{"ConnectionStrings": {"Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoiceauth50staging;Password=Vnis@12A", "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoice50staging;Password=Vnis@12A", "VnisCoreMongoDbAuditLogging": "*************************************************************************************************", "VnisCoreMongoDb": "***************************************************************************************"}, "Service": {"Name": "Tvan.Vnpay.Invoice03WithoutCode", "Title": "Tvan.Vnpay.Invoice03WithoutCode", "BaseUrl": "send-invoice03-withoutcode", "AuthApiName": "Tvan.Vnpay.Invoice03WithoutCode"}, "TvanVnpayInvoice": {"EndPoint": "https://tvan-stg.vnpaytest.vn/", "ApiKey": "505451C4FDAA4148A053BB6187F4A857", "Timeout": 5}, "Redis": {"IsUsing": "true", "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14", "Database": 14}, "Minio": {"Endpoint": "**************:9000", "AccessKey": "vnis", "SecretKey": "Vnis@12A", "Region": null, "SessionToken": null, "BucketName": "staging-core50"}, "TimePeriod": 30, "MaxSize": 50, "Logging": {"RootFolder": {"Folder": "D:\\Projects\\Vnis\\VnInvoice50\\Microservices\\VnisCore\\Tvan\\VnpayInvoice\\Tvan.Vnpay.Invoice03WithoutCode"}}}