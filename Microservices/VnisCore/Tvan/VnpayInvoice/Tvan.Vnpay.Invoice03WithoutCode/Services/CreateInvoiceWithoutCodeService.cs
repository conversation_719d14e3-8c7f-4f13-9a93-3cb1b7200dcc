using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Tvan.Vnpay.Invoice03WithoutCode.Repositories;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using Core.Tvan.Vnpay.Interfaces;
using Core.Shared.Cached;
using Core.Shared.Services;

namespace Tvan.Vnpay.Invoice03WithoutCode.Services
{
    public interface ICreateInvoiceWithoutCodeService
    {
        Task SendToTvanAsync();
    }

    public class CreateInvoiceWithoutCodeService : ICreateInvoiceWithoutCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly ITvanVnpayService _tvanVnpayService;
        private readonly IInvoiceTemplateRepository _invoiceTemplateRepository;
        private readonly IInvoice03HeaderRepository _invoice03HeaderRepository;
        private readonly IInvoice03XmlRepository _invoice03XmlRepository;
        private readonly IDownloadFileService _downloadFileService;
        private readonly INewRegistrationRepository _newRegistrationRepository;
        private readonly Repositories.ITenantRepository _tenantRepository;
        private readonly ITenantCacheBusiness _tenantCacheBusiness;
        private readonly ISettingWorkerService _settingWorkerService;

        public CreateInvoiceWithoutCodeService(IAppFactory appFactory,
                                               ITvanVnpayService tvanVnpayService,
                                               IInvoiceTemplateRepository invoiceTemplateRepository,
                                               IInvoice03HeaderRepository invoice03HeaderRepository,
                                               IInvoice03XmlRepository invoice03XmlRepository,
                                               IDownloadFileService downloadFileService,
                                               INewRegistrationRepository newRegistrationRepository,
                                               Repositories.ITenantRepository tenantRepository,
                                               ITenantCacheBusiness tenantCacheBusiness,
                                               ISettingWorkerService settingWorkerService)
        {
            _appFactory = appFactory;
            _tvanVnpayService = tvanVnpayService;
            _invoiceTemplateRepository = invoiceTemplateRepository;
            _invoice03HeaderRepository = invoice03HeaderRepository;
            _invoice03XmlRepository = invoice03XmlRepository;
            _downloadFileService = downloadFileService;
            _newRegistrationRepository = newRegistrationRepository;
            _tenantRepository = tenantRepository;
            _tenantCacheBusiness = tenantCacheBusiness;
            _settingWorkerService = settingWorkerService;
        }

        public async Task SendToTvanAsync()
        {
            #region Kiểm tra Cấu hình gửi Tvan
            List<string> ignoreTaxCodes = new List<string>();
            var tenants = await _tenantCacheBusiness.GetAllTenantAsync();

            foreach (var tenant in tenants)
            {
                var isSetting = await _settingWorkerService.GetByCodeAsync(tenant.Id, SettingKey.ConfigSendTvan.ToString(), tenant.TaxCode);
                if (isSetting == 0)
                {
                    ignoreTaxCodes.Add(tenant.TaxCode);
                }
            }

            // TH Tắt Luồng Tvan
            if (tenants.Count == ignoreTaxCodes.Count)
            {
                Log.Information("Hệ thống ngưng gửi lên TVAN");
                return;
            }

            tenants = tenants.Where(x => !ignoreTaxCodes.Contains(x.TaxCode)).ToList();
            #endregion
            if (tenants == null)
            {
                Log.Error("KHÔNG TÌM THẤY THÔNG TIN TENANT!");
                return;
            }

            foreach (var tenant in tenants)
            {
                //var rawTenantId = BitConverter.ToString((tenant.Id.ToByteArray())).Replace("-", "");
                var rawTenantId = OracleExtension.ConvertGuidToRaw(tenant.Id);

                var registrationSendInvoiceMethod = await _newRegistrationRepository.GetNewRegistrationAsync(rawTenantId);
                if (registrationSendInvoiceMethod == null)
                    continue;

                if (registrationSendInvoiceMethod.InvoiceHasCode || (!registrationSendInvoiceMethod.InvoiceHasCode && registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendBothMethod.GetHashCode()
                     && registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendInvoice.GetHashCode()))
                    continue;

                // Lấy tất cả các invoice template không mã (SerialNo bắt đầu bằng ký tự K)
                var invoiceTemplates = await _invoiceTemplateRepository.QueryByTenantAsync(rawTenantId);
                if (!invoiceTemplates.Any())
                {
                    //Log.Error($"****** KHÔNG CÓ MẪU HÓA ĐƠN KHÔNG MÃ NÀO CÓ TENANTID = {tenant.Id}, taxcode = {tenant.TaxCode} TRONG CƠ SỞ DỮ LIỆU ******");
                    continue;
                }

                var idsTemplate = invoiceTemplates.Select(x => x.Id).ToList();

                // lấy tất cả các hóa đơn có trạng thái chưa gửi lên TVAN
                var invoiceHeaders = await _invoice03HeaderRepository.GetListByIdsTemplateAsync(rawTenantId, idsTemplate);
                if (!invoiceHeaders.Any())
                {
                    //Log.Error($"****** KHÔNG TÌM THẤY HÓA ĐƠN NÀO CỦA CÔNG TY{tenant.TaxCode} GỬI LÊN TVAN ******");
                    continue;
                }

                var idsInvoiceHeader = invoiceHeaders.ToDictionary(x => x.Id, x => x);

                await BuildAndSendXmlToTvan(tenant.Id, tenant.TaxCode, rawTenantId, idsInvoiceHeader);
            }
        }

        private async Task<List<long>> BuildAndSendXmlToTvan(Guid tenantId, string sellerTaxcode, string rawTenantId, Dictionary<long, Invoice03HeaderEntity> invoiceHeader)
        {
            var invoiceXmls = await _invoice03XmlRepository.GetByInvoiceHeaderIdsAsync(rawTenantId, invoiceHeader.Keys.ToList());
            if (!invoiceXmls.Any())
                return new List<long>();

            var invoiceRepos = _appFactory.Repository<Invoice03HeaderEntity, long>();

            var invoiceXmlGroups = from element in invoiceXmls
                                   group element by element.InvoiceHeaderId
                       into groups
                                   select groups.OrderByDescending(p => p.Id).First();

            //gom lại max 400 hóa đơn
            var maxSize = int.Parse(_appFactory.Configuration.GetSection("MaxSize").Value);
            invoiceXmls = invoiceXmlGroups.OrderBy(x => x.Id).ToList();
            var totalTimes = invoiceXmls.Count() / maxSize;
            if (invoiceXmls.Count() % maxSize != 0)
            {
                totalTimes++;
            }

            for (int i = 1; i <= totalTimes; i++)
            {
                var invoiceXmlPerPages = invoiceXmls.Skip(maxSize * (i - 1)).Take(maxSize).ToList();
                var xmlGroup = "";

                try
                {
                    var invoiceHeaderIds = new List<long>();
                    foreach (var invoiceXmlEntity in invoiceXmlPerPages)
                    {
                        try
                        {
                            var fileDto = await _downloadFileService.DownloadAsync(tenantId, invoiceXmlEntity);

                            var xmlHDon = Encoding.UTF8.GetString(fileDto.FileBytes);
                            //bỏ ký tự <xml ../> ở đầu
                            var indexHdon = xmlHDon.IndexOf("<HDon>");
                            xmlGroup += xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);

                            invoiceHeaderIds.Add(invoiceXmlEntity.InvoiceHeaderId);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, $"Download Invoice03Xml id = {invoiceXmlEntity.Id} Error: {ex.Message}");
                            continue;
                        }

                    }

                    if (!invoiceHeaderIds.Any())
                        continue;

                    try
                    {
                        var responseTvan = await _tvanVnpayService.SendInvoice03WithoutCodeAsync(tenantId, sellerTaxcode, invoiceHeaderIds, xmlGroup);

                        if (responseTvan != null && responseTvan.Code == "00")
                        {
                            await _invoice03HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.Sended);
                        }
                        else
                        {
                            await _invoice03HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                        }
                    }
                    catch (Exception ex)
                    {
                        await _invoice03HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                        Log.Error(ex, ex.Message);
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"****** LỖI: {ex.Message} ******");
                    continue;
                }
            }
            return invoiceXmls.Select(x => x.InvoiceHeaderId).ToList();
        }
    }
}
