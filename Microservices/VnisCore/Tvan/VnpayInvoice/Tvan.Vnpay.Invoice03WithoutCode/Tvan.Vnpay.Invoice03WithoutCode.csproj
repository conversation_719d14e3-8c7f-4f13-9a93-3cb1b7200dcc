<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net5.0</TargetFramework>
      <Version>5.30.0</Version>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="5.0.*" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="3.1.0" />
      <PackageReference Include="Serilog.Sinks.Async" Version="1.4.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
      <PackageReference Include="Serilog.Sinks.File" Version="4.1.0" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="appsettings.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.Application.Contracts\VnisCore.AuthDatabase.Oracle.Application.Contracts.csproj" />
      <ProjectReference Include="..\..\..\..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.Domain\VnisCore.AuthDatabase.Oracle.Domain.csproj" />
      <ProjectReference Include="..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Application.Contracts\VnisCore.Core.Oracle.Application.Contracts.csproj" />
      <ProjectReference Include="..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Domain\VnisCore.Core.Oracle.Domain.csproj" />
      <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
      <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Shared\Core.Shared.csproj" />
      <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Tvan.Vnpay\Core.Tvan.Vnpay.csproj" />
      <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Tvan\Core.Tvan.csproj" />
	  <ProjectReference Include="..\..\..\..\..\Framework\Core\Core.Autofac\Core.Autofac.csproj" />
	  <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
    </ItemGroup>

</Project>
