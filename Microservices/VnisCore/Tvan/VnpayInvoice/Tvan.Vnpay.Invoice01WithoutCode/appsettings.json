{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "Service": {
    "Name": "Tvan.Vnpay.Invoice01WithoutCode",
    "Title": "Tvan.Vnpay.Invoice01WithoutCode",
    "BaseUrl": "send-invoice01-withoutcode",
    "AuthApiName": "Tvan.Vnpay.Invoice01WithoutCode"
  },
  "TvanVnpayInvoice": {
    "EndPoint": "https://tvan-stg.vnpaytest.vn/",
    "ApiKey": "bf1976d99b384703a0636ce8d09ae3a7",
    "Timeout": 5
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14",
    "Database": 14
  },
  "Minio": {
    "Endpoint": "efin-minio.vnpaytest.vn",
    //"Endpoint": "10.22.18.233:9000",
    "AccessKey": "minioadmin",
    "SecretKey": "Minio@123",
    "Region": null,
    "SessionToken": null,
    "BucketName": "invoice-mass-v5",
    "TrustAllCerts": "true"
  },
  "Settings": {
    "MaximunSizeXml": 1.8,
    "Group0": {
      "MaxSize": 50,
      "TenantGroup": "0,1.2,2.1,2.2,2.3,2.4,2.5,2.6"
    },
    "GroupX": {
      "MaxSize": 50,
      "TenantGroup": "3.1,3.2,3.3,3.4,3.5,3.6,4.1,4.2,4.3,4.4,4.5,4.6,5.1,5.2,5.3,5.4,5.5,5.6"
    },
    "GroupFamilyMart": {
      "MaxSize": 1,
      "TenantGroup": "1.3",
      "MaximunSizeXml": 2
    }
  },
  "TimePeriod": 5,
  "Logging": {
    "RootFolder": {
      "Folder": "D:\\Projects\\Vnis\\VnInvoice50\\Microservices\\VnisCore\\Tvan\\VnpayInvoice\\Tvan.Vnpay.Invoice01WithoutCode"
    }
  }
}