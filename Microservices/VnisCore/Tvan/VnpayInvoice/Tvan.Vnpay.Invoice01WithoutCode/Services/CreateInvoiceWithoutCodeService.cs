using AutoMapper;
using Core;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Core.Tvan.Vnpay.Interfaces;
using Microsoft.Extensions.Configuration;
using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Tvan.Vnpay.Invoice01WithoutCode.Repositories;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Tvan.Vnpay.Invoice01WithoutCode.Services
{
    public interface ICreateInvoiceWithoutCodeService
    {
        Task SendToTvanAsync(List<decimal> tenantGroup, int maxSize);
    }

    public class CreateInvoiceWithoutCodeService : ICreateInvoiceWithoutCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly ITvanVnpayService _tvanVnpayService;
        private readonly IInvoiceTemplateRepository _invoiceTemplateRepository;
        private readonly IInvoice01HeaderRepository _invoice01HeaderRepository;
        private readonly IInvoice01XmlRepository _invoice01XmlRepository;
        private readonly IDownloadFileService _downloadFileService;
        private readonly INewRegistrationRepository _newRegistrationRepository;
        private readonly Repositories.ITenantRepository _tenantRepository;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IVnisCoreMongoXmlInvoice01SignedRepository _mongoXmlInvoice01SignedRepository;
        private readonly IConfiguration _configuration;
        private readonly ITenantCacheBusiness _tenantCacheBusiness;
        private readonly ISettingWorkerService _settingWorkerService;

        public CreateInvoiceWithoutCodeService(IAppFactory appFactory,
                                               ITvanVnpayService tvanVnpayService,
                                               IInvoiceTemplateRepository invoiceTemplateRepository,
                                               IInvoice01HeaderRepository invoice01HeaderRepository,
                                               IInvoice01XmlRepository invoice01XmlRepository,
                                               IDownloadFileService downloadFileService,
                                               INewRegistrationRepository newRegistrationRepository,
                                               Repositories.ITenantRepository tenantRepository,
                                               IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
                                               IVnisCoreMongoXmlInvoice01SignedRepository mongoXmlInvoice01SignedRepository,
                                               IConfiguration configuration,
                                               ITenantCacheBusiness tenantCacheBusiness,
                                               ISettingWorkerService settingWorkerService)
        {
            _appFactory = appFactory;
            _tvanVnpayService = tvanVnpayService;
            _invoiceTemplateRepository = invoiceTemplateRepository;
            _invoice01HeaderRepository = invoice01HeaderRepository;
            _invoice01XmlRepository = invoice01XmlRepository;
            _downloadFileService = downloadFileService;
            _newRegistrationRepository = newRegistrationRepository;
            _tenantRepository = tenantRepository;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _mongoXmlInvoice01SignedRepository = mongoXmlInvoice01SignedRepository;
            _configuration = configuration;
            _tenantCacheBusiness = tenantCacheBusiness;
            _settingWorkerService = settingWorkerService;
        }

        public async Task SendToTvanAsync(List<decimal> tenantGroup, int maxSize)
        {
            #region Kiểm tra Cấu hình gửi Tvan
            List<string> ignoreTaxCodes = new List<string>();
            var tenants = await _tenantCacheBusiness.GetAllTenantAsync();
            tenants = tenants.Where(x=> tenantGroup.Contains(x.Group)).ToList();

            foreach (var tenant in tenants)
            {
                var isSetting = await _settingWorkerService.GetByCodeAsync(tenant.Id, SettingKey.ConfigSendTvan.ToString(), tenant.TaxCode);
                if (isSetting == 0)
                {
                    ignoreTaxCodes.Add(tenant.TaxCode);
                }
            }

            // TH Tắt Luồng Tvan
            if (tenants.Count == ignoreTaxCodes.Count)
            {
                Log.Information("Hệ thống ngưng gửi lên TVAN");
                return;
            }

            tenants = tenants.Where(x=> !ignoreTaxCodes.Contains(x.TaxCode)).ToList();
            #endregion

            if (tenants.IsNullOrEmpty())
            {
                Log.Error("KHÔNG TÌM THẤY THÔNG TIN TENANT!");
                return;
            }

            foreach (var tenant in tenants)
            {
                //var rawTenantId = BitConverter.ToString((tenant.Id.ToByteArray())).Replace("-", "");
                var rawTenantId = OracleExtension.ConvertGuidToRaw(tenant.Id);

                var registrationSendInvoiceMethod = await _newRegistrationRepository.GetNewRegistrationAsync(rawTenantId);
                if (registrationSendInvoiceMethod == null)
                    continue;

                //if (registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendBothMethod.GetHashCode()
                //     && registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendInvoice.GetHashCode())
                //    continue;

                if (registrationSendInvoiceMethod.InvoiceHasCode || (!registrationSendInvoiceMethod.InvoiceHasCode && registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendBothMethod.GetHashCode()
                     && registrationSendInvoiceMethod.SendInvoiceMethod != (short)RegistrationSendInvoiceMethod.SendInvoice.GetHashCode()))
                    continue;

                // Lấy tất cả các invoice template không mã (SerialNo bắt đầu bằng ký tự K)
                var invoiceTemplates = await _invoiceTemplateRepository.QueryByTenantAsync(rawTenantId);
                if (!invoiceTemplates.Any())
                {
                    //Log.Error($"****** KHÔNG CÓ MẪU HÓA ĐƠN KHÔNG MÃ NÀO CÓ TENANTID = {tenant.Id}, taxcode = {tenant.TaxCode} TRONG CƠ SỞ DỮ LIỆU ******");
                    continue;
                }

                var idsTemplate = invoiceTemplates.Select(x => x.Id).ToList();

                // lấy tất cả các hóa đơn có trạng thái chưa gửi lên TVAN
                var invoiceHeaders = await _invoice01HeaderRepository.GetListByIdsTemplateAsync(rawTenantId, idsTemplate);
                if (!invoiceHeaders.Any())
                {
                    var invoicesMongo = await _mongoInvoice01Repository.GetListByIdsTemplateAsync(tenant.Id, idsTemplate);
                    if (!invoicesMongo.Any())
                    {
                        //Log.Error($"****** KHÔNG TÌM THẤY HÓA ĐƠN NÀO CỦA CÔNG TY{tenant.TaxCode} GỬI LÊN TVAN ******");
                        continue;
                    }
                    else
                    {
                        var config = new MapperConfiguration(cfg =>
                        {
                            cfg.CreateMap<Invoice01HeaderEntity, MongoInvoice01Entity>().ReverseMap();
                            cfg.CreateMap<Invoice01DetailEntity, global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>().ReverseMap();
                            cfg.CreateMap<Invoice01TaxBreakdownEntity, global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>().ReverseMap();
                            cfg.CreateMap<List<Invoice01DetailEntity>, List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>>().ReverseMap();
                            cfg.CreateMap<List<Invoice01TaxBreakdownEntity>, List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>>().ReverseMap();
                        });

                        var mapper = new Mapper(config);
                        
                        invoiceHeaders = mapper.Map<List<MongoInvoice01Entity>, List<Invoice01HeaderEntity>>(invoicesMongo);
                    }
                }

                var idsInvoiceHeader = invoiceHeaders.ToDictionary(x => x.Id, x => x);

                await BuildAndSendXmlToTvan(tenant.Id, tenant.TaxCode, rawTenantId, idsInvoiceHeader, maxSize);
            }
        }

        private async Task<List<long>> BuildAndSendXmlToTvan(Guid tenantId, string sellerTaxcode, string rawTenantId, Dictionary<long, Invoice01HeaderEntity> invoiceHeader, int maxSize)
        {
            var maximunSizeXml = double.Parse(_configuration["Settings:MaximunSizeXml"]);
            if (maximunSizeXml <= 0)
            {
                maximunSizeXml = 1.9;//2 MB
            }
            var maximunSizeXmlBytes = maximunSizeXml * 1024 * 1024;
            var invoiceXmls = await _invoice01XmlRepository.GetByInvoiceHeaderIdsAsync(rawTenantId, invoiceHeader.Keys.ToList());
            if (!invoiceXmls.Any())
            {
                var invoicesXmlMongo = await _mongoXmlInvoice01SignedRepository.GetListByInvoiceHeaderId(invoiceHeader.Keys.ToList());
                if (!invoicesXmlMongo.Any())
                    return new List<long>();
                else
                {
                    var invoiceXmlGroups = from element in invoicesXmlMongo
                                           group element by element.InvoiceHeaderId
                                            into groups
                                           select groups.OrderByDescending(p => p.Id).First();

                    //var maxSize = int.Parse(_appFactory.Configuration.GetSection("MaxSize").Value);

                    invoicesXmlMongo = invoiceXmlGroups.OrderBy(x => x.Id).ToList();

                    var invoiceHeaderIds = new List<long>();
                    var xmlGroup = "";

                    for (int i = 0; i < invoicesXmlMongo.Count; i++)
                    {
                        try
                        {
                            var xmlHDon = Encoding.UTF8.GetString(Convert.FromBase64String(invoicesXmlMongo[i].Xml));
                            var sizeBytes = ASCIIEncoding.Unicode.GetByteCount(xmlHDon);
                            if(sizeBytes > maximunSizeXmlBytes)
                            {
                                // th file xml lon hon 2MB
                                throw new UserFriendlyException("File xml " + invoicesXmlMongo[i].FileName + " lon hon 2MB");
                            }
                            //bỏ ký tự <xml ../> ở đầu
                            var indexHdon = xmlHDon.IndexOf("<HDon>");
                            xmlGroup += xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);

                            var xmlGroupCheckSize = xmlGroup + xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);

                            // Tinh toan so luong bytes file xml gop > 2M
                            sizeBytes = ASCIIEncoding.Unicode.GetByteCount(xmlGroupCheckSize);
                            if (sizeBytes > maximunSizeXmlBytes)
                            {
                                // Neu kich thuoc lon hon 2MB => Gui len thue file group trc khi lon hon 2MB
                                if (!invoiceHeaderIds.Any())
                                    continue;

                                try
                                {
                                    var responseTvan = await _tvanVnpayService.SendInvoice01WithoutCodeAsync(tenantId, sellerTaxcode, invoiceHeaderIds, xmlGroup);

                                    if (responseTvan != null && responseTvan.Code == "00")
                                    {
                                        await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.Sended);
                                        await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.Sended);
                                    }
                                    else
                                    {
                                        await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                        await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                    await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                    Log.Error(ex, ex.Message);
                                    continue;
                                }

                                // Reset lai
                                xmlGroup = "";
                                invoiceHeaderIds = new List<long>();
                                i--;
                            }
                            else
                            {
                                // Neu < 2MB => Tiep tuc them file xml vao file gop
                                xmlGroup += xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);
                                invoiceHeaderIds.Add(invoicesXmlMongo[i].InvoiceHeaderId);
                                // Neu da den phan tu cuoi cung
                                if (i == invoicesXmlMongo.Count - 1)
                                {
                                    try
                                    {
                                        var responseTvan = await _tvanVnpayService.SendInvoice01WithoutCodeAsync(tenantId, sellerTaxcode, invoiceHeaderIds, xmlGroup);

                                        if (responseTvan != null && responseTvan.Code == "00")
                                        {
                                            await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.Sended);
                                            await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.Sended);
                                        }
                                        else
                                        {
                                            await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                            await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                        await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                        Log.Error(ex, ex.Message);
                                        continue;
                                    }
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, $"Download 01 xml id = {invoicesXmlMongo[i].Id} Error: {ex.Message}");
                            continue;
                        }
                    }
                    return invoicesXmlMongo.Select(x => x.InvoiceHeaderId).ToList();
                }
            }
            else
            {
                var invoiceRepos = _appFactory.Repository<Invoice01HeaderEntity, long>();

                var invoiceXmlGroups = from element in invoiceXmls
                                       group element by element.InvoiceHeaderId
                           into groups
                                       select groups.OrderByDescending(p => p.Id).First();

                //gom lại max 400 hóa đơn
                //var maxSize = int.Parse(_appFactory.Configuration.GetSection("MaxSize").Value);
                invoiceXmls = invoiceXmlGroups.OrderBy(x => x.Id).ToList();
                var invoiceHeaderIds = new List<long>();
                var xmlGroup = "";

                for (int i = 0; i < invoiceXmls.Count; i++)
                {
                    try
                    {
                        var fileDto = await _downloadFileService.DownloadAsync(tenantId, invoiceXmls[i]);

                        var xmlHDon = Encoding.UTF8.GetString(fileDto.FileBytes);
                        var sizeBytes = ASCIIEncoding.Unicode.GetByteCount(xmlHDon);
                        if (sizeBytes > maximunSizeXmlBytes)
                        {
                            // th file xml lon hon 2MB
                            throw new UserFriendlyException("File xml " + invoiceXmls[i].FileName + " lon hon 2MB");
                        }
                        //bỏ ký tự <xml ../> ở đầu
                        var indexHdon = xmlHDon.IndexOf("<HDon>");

                        var xmlGroupCheckSize = xmlGroup + xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);

                        // Tinh toan so luong bytes file xml gop > 2M
                        sizeBytes = ASCIIEncoding.Unicode.GetByteCount(xmlGroupCheckSize);
                        if (sizeBytes > maximunSizeXmlBytes)
                        {
                            // Neu kich thuoc lon hon 2MB => Gui len thue file group trc khi lon hon 2MB
                            if (!invoiceHeaderIds.Any())
                                continue;

                            try
                            {
                                var responseTvan = await _tvanVnpayService.SendInvoice01WithoutCodeAsync(tenantId, sellerTaxcode, invoiceHeaderIds, xmlGroup);

                                if (responseTvan != null && responseTvan.Code == "00")
                                {
                                    await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.Sended);
                                    await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.Sended);
                                }
                                else
                                {
                                    await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                    await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                }
                            }
                            catch (Exception ex)
                            {
                                await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                Log.Error(ex, ex.Message);
                                continue;
                            }

                            // Reset lai
                            xmlGroup = "";
                            invoiceHeaderIds = new List<long>();
                            i--;
                        }
                        else
                        {
                            // Neu < 2MB => Tiep tuc them file xml vao file gop
                            xmlGroup += xmlHDon.Substring(indexHdon, xmlHDon.Length - indexHdon);
                            invoiceHeaderIds.Add(invoiceXmls[i].InvoiceHeaderId);
                            // Neu da den phan tu cuoi cung
                            if (i == invoiceXmls.Count - 1)
                            {
                                try
                                {
                                    var responseTvan = await _tvanVnpayService.SendInvoice01WithoutCodeAsync(tenantId, sellerTaxcode, invoiceHeaderIds, xmlGroup);

                                    if (responseTvan != null && responseTvan.Code == "00")
                                    {
                                        await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.Sended);
                                        await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.Sended);
                                    }
                                    else
                                    {
                                        await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                        await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    await _invoice01HeaderRepository.UpdateStatusTvanAsync(invoiceHeaderIds, TvanStatus.SendError);
                                    await _mongoInvoice01Repository.UpdateManyStatusTvan(invoiceHeaderIds, (short)TvanStatus.SendError);
                                    Log.Error(ex, ex.Message);
                                    continue;
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, $"Download 01 xml id = {invoiceXmls[i].Id} Error: {ex.Message}");
                        continue;
                    }
                }

                return invoiceXmls.Select(x => x.InvoiceHeaderId).ToList();
            }
        }
    }
}
