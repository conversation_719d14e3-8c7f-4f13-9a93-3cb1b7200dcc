using Core;
using Core.Application;
using Core.Autofac;
using Core.BackgroundWorkers;
using Core.Caching.StackExchangeRedis;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.FileManager.Models;
using Core.Shared.FileManager.Services;
using Core.Tvan;
using Core.Tvan.Interfaces;
using Core.Tvan.Services;
using Core.Tvan.Vnpay;
using Core.VaultSharp;
using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Minio;
using System;
using System.Collections.Generic;
using System.Net.Http;
using Tvan.Vnpay.TicketWithoutCode.Repositories;
using Tvan.Vnpay.TicketWithoutCode.Services;

using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.Domain;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace Tvan.Vnpay.TicketWithoutCode
{

    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpDddApplicationModule),
        typeof(AbpAutofacModule),
        typeof(TvanModule),
        typeof(TvanVnpayModule),
        typeof(AbpCachingStackExchangeRedisModule),
        typeof(SharedModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(VnisCoreAuthDatabaseOracleDomainModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule)
    )]
    public class TicketWithoutCodeModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            // Repository
            context.Services.AddScoped<IInvoiceTemplateRepository, InvoiceTemplateRepository>();
            context.Services.AddScoped<ITicketXmlRepository, TicketXmlRepository>();
            context.Services.AddScoped<ITicketHeaderRepository, TicketHeaderRepository>();
            context.Services.AddScoped<ITenantRepository, TenantRepository>();
            context.Services.AddScoped<INewRegistrationRepository, NewRegistrationRepository>();

            // Service
            context.Services.AddScoped<ITvanTicketWithoutCodeService, TvanTicketWithoutCodeService>();
            context.Services.AddScoped<IDownloadFileService, DownloadFileService>();
            context.Services.AddScoped<ITvanInvoiceService, TvanInvoiceService>();
            
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<TicketWithoutCodeWorker>();
        }
    }
}
