using Core;
using Core.Application;
using Core.Autofac;
using Core.BackgroundWorkers;
using Core.Caching.StackExchangeRedis;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Tvan;
using Core.Tvan.Interfaces;
using Core.Tvan.Services;
using Core.Tvan.Vnpay;
using Core.VaultSharp;
using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using Tvan.Vnpay.Invoice04WithoutCode.Repositories;
using Tvan.Vnpay.Invoice04WithoutCode.Services;

using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.Domain;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace Tvan.Vnpay.Invoice04WithoutCode
{

    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpDddApplicationModule),
        typeof(AbpAutofacModule),
        typeof(TvanModule),
        typeof(TvanVnpayModule),
        typeof(AbpCachingStackExchangeRedisModule),
        typeof(SharedModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(VnisCoreAuthDatabaseOracleDomainModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule)
    )]
    public class Invoice04WithoutCodeModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddHostedService<Invoice04WithoutCodeHostedService>();

            // Repository
            context.Services.AddScoped<IInvoiceTemplateRepository, InvoiceTemplateRepository>();
            context.Services.AddScoped<IInvoice04XmlRepository, Invoice04XmlRepository>();
            context.Services.AddScoped<IInvoice04HeaderRepository, Invoice04HeaderRepository>();
            context.Services.AddScoped<IDownloadFileService, DownloadFileService>();
            context.Services.AddScoped<ITenantRepository, TenantRepository>();
            context.Services.AddScoped<INewRegistrationRepository, NewRegistrationRepository>();

            // Service
            context.Services.AddScoped<ICreateInvoiceWithoutCodeService, CreateInvoiceWithoutCodeService>();
            context.Services.AddScoped<ITvanInvoiceService, TvanInvoiceService>();
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<Invoice04WithoutCodeWorker>();
        }
    }
}
