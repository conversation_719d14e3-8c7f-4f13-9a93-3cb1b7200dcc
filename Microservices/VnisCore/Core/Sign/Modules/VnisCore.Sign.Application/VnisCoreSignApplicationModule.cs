using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Core.Tvan;
using Core.Tvan.MInvoice;
using Core.Tvan.Vnpay;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Linq;
using System.Reflection;

using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Invoice01.Infrastructure;
using VnisCore.PILetter.Infrastructure;
using VnisCore.PITDeductionDocument.Infrastructure;
using VnisCore.Sign.Application.Business;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models;
using VnisCore.Sign.Application.Repositories;
using VnisCore.Sign.Application.Repositories.Invoice01;
using VnisCore.Sign.Application.Repositories.Invoice02;
using VnisCore.Sign.Application.Repositories.Invoice03;
using VnisCore.Sign.Application.Repositories.Invoice04;
using VnisCore.Sign.Application.Repositories.Ticket;
using VnisCore.Sign.Application.Services;
using VnisCore.Sign.Application.Services.Elastics;
using VnisCore.Sign.Application.Services.Factories;
using VnisCore.Sign.Application.Services.SignClient;
using VnisCore.Sign.Application.Services.SignServer.Invoice01;
using VnisCore.Sign.Application.Services.SignServer.Invoice02;
using VnisCore.Sign.Application.Services.SignServer.Invoice03;
using VnisCore.Sign.Application.Services.SignServer.Invoice04;
using VnisCore.Sign.Application.Services.SignServer.Ticket;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice01.Business;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice01.Interfaces;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice02.Business;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice02.Interfaces;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice03.Business;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice03.Interfaces;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice04.Business;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice04.Interfaces;
using VnisCore.Sign.Application.SignClient.Invoices.Ticket.Business;
using VnisCore.Sign.Application.SignClient.Invoices.Ticket.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice01.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice01.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice02.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice02.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice03.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice03.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice04.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Invoice04.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.PITDeductionDocument.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.PITDeductionDocument.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Tbss.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Tbss.Interfaces;
using VnisCore.Sign.Application.SignClient.InvoicesError.Ticket.Business;
using VnisCore.Sign.Application.SignClient.InvoicesError.Ticket.Interfaces;
using VnisCore.Sign.Application.SignClient.PitDocument.Business;
using VnisCore.Sign.Application.SignClient.PitDocument.Interfaces;
using VnisCore.Sign.Application.SignClient.PitLetter.Business;
using VnisCore.Sign.Application.SignClient.PitLetter.Interfaces;
using VnisCore.Sign.Application.SignClient.TaxReport.TaxReport01.Business;
using VnisCore.Sign.Application.SignClient.TaxReport.TaxReport01.Interfaces;
using VnisCore.Sign.Application.SignServer.Invoice01.Business;
using VnisCore.Sign.Application.SignServer.Invoice01.Interfaces;
using VnisCore.Sign.Application.SignServer.Invoice02.Business;
using VnisCore.Sign.Application.SignServer.Invoice02.Interfaces;
using VnisCore.Sign.Application.SignServer.Invoice03.Business;
using VnisCore.Sign.Application.SignServer.Invoice03.Interfaces;
using VnisCore.Sign.Application.SignServer.Invoice04.Business;
using VnisCore.Sign.Application.SignServer.Invoice04.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.Common.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice01.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice01.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice02.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice02.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice03.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice03.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice04.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice04.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.PITDeductionDocument.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.PITDeductionDocument.Interfaces;
using VnisCore.Sign.Application.SignServer.InvoicesError.Ticket.Business;
using VnisCore.Sign.Application.SignServer.InvoicesError.Ticket.Interfaces;
using VnisCore.Sign.Application.SignServer.PitDocument.Business;
using VnisCore.Sign.Application.SignServer.PITLetter.Business;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Business;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Interfaces;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport03.Business;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport03.Interfaces;
using VnisCore.Sign.Application.SignServer.Ticket.Business;
using VnisCore.Sign.Application.SignServer.Ticket.Interfaces;

namespace VnisCore.Sign.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreMongoDbModule),
        typeof(SharedModule),
        typeof(TvanModule),
        typeof(TvanMInvoiceModule),
        typeof(TvanVnpayModule),
        typeof(VnisCorePITDeductionDocumentInfrastructureModule),
        typeof(VnisCorePITLetterInfrastructureModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
    )]
    public class VnisCoreSignApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());


            var configuration = context.Services.GetConfiguration();
            //context.Services.Configure<UseCMTMTTienOption>(configuration.GetSection("UseCMTMTTien"));
            Configure<UseCMTMTTienOption>(options =>
            {
                options.TaxCodes = configuration["UseCMTMTTien:TaxCodes"]?.Split(',').ToList();
            });

            context.Services.AddScoped<ISignService, SignService>();
            context.Services.AddScoped<ICertificateService, CertificateService>();
            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();

            context.Services.AddScoped<ISendMailHttpClientService, SendMailHttpClientService>();

            //Repositories
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));

            //Invoice01
            context.Services.AddScoped<IInvoice01DetailRepository, Invoice01DetailRepository>();
            context.Services.AddScoped<IInvoice01HeaderRepository, Invoice01HeaderRepository>();
            context.Services.AddScoped<IInvoice01DetailFieldRepository, Invoice01DetailFieldRepository>();
            context.Services.AddScoped<IInvoice01HeaderFieldRepository, Invoice01HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice01TaxBreakdownRepository, Invoice01TaxBreakdownRepository>();

            //Invoice02
            context.Services.AddScoped<IInvoice02DetailRepository, Invoice02DetailRepository>();
            context.Services.AddScoped<IInvoice02HeaderRepository, Invoice02HeaderRepository>();
            context.Services.AddScoped<IInvoice02DetailFieldRepository, Invoice02DetailFieldRepository>();
            context.Services.AddScoped<IInvoice02HeaderFieldRepository, Invoice02HeaderFieldRepository>();

            //Invoice03
            context.Services.AddScoped<IInvoice03DetailRepository, Invoice03DetailRepository>();
            context.Services.AddScoped<IInvoice03HeaderRepository, Invoice03HeaderRepository>();
            context.Services.AddScoped<IInvoice03DetailFieldRepository, Invoice03DetailFieldRepository>();
            context.Services.AddScoped<IInvoice03HeaderFieldRepository, Invoice03HeaderFieldRepository>();

            //Invoice04
            context.Services.AddScoped<IInvoice04DetailRepository, Invoice04DetailRepository>();
            context.Services.AddScoped<IInvoice04HeaderRepository, Invoice04HeaderRepository>();
            context.Services.AddScoped<IInvoice04DetailFieldRepository, Invoice04DetailFieldRepository>();
            context.Services.AddScoped<IInvoice04HeaderFieldRepository, Invoice04HeaderFieldRepository>();

            //Ticket
            context.Services.AddScoped<ITicketDetailRepository, TicketDetailRepository>();
            context.Services.AddScoped<ITicketHeaderRepository, TicketHeaderRepository>();
            context.Services.AddScoped<ITicketTaxBreakdownRepository, TicketTaxBreakdownRepository>();
            context.Services.AddScoped<ITicketHeaderFieldRepository, TicketHeaderFieldRepository>();

            // PIT Document
            context.Services.AddScoped<ISign123TicketBusiness, Sign123TicketBusiness>();
            context.Services.AddScoped<ISignServerPitDocumentBusiness, SignServerPitDocumentBusiness>();
            context.Services.AddScoped<IGenerateNumberPITBusiness, GenerateNumberPITBusiness>();
            context.Services.AddSingleton<LockerStore, LockerStore>();

            // PIT Letter
            context.Services.AddScoped<ISignServerPitLetterBusiness, SignServerPitLetterBusiness>();

            //Services
            context.Services.AddScoped<ISignService, SignService>();
            context.Services.AddScoped<IElasticService, ElasticService>();
            context.Services.AddScoped<ISignServerService<Invoice01HeaderEntity>, Invoice01SignServerService>();
            context.Services.AddScoped<ISignServerService<Invoice02HeaderEntity>, Invoice02SignServerService>();
            context.Services.AddScoped<ISignServerService<Invoice03HeaderEntity>, Invoice03SignServerService>();
            context.Services.AddScoped<ISignServerService<Invoice04HeaderEntity>, Invoice04SignServerService>();
            context.Services.AddScoped<ISignServerService<TicketHeaderEntity>, TicketSignServerService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice01ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice02ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice03ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice04ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, TicketErrorService>();
            // context.Services.AddScoped<IInvoiceErrorService, PITDeductionDocumentErrorService>();
            context.Services.AddScoped<IInvoiceErrorFactory, InvoiceErrorFactory>();

            //Sign Client services
            context.Services.AddScoped<ISignClientFactory, SignClientFactory>();
            context.Services.AddScoped<ISignClientService, Invoice01SignClientService>();
            context.Services.AddScoped<ISignClientService, Invoice02SignClientService>();
            context.Services.AddScoped<ISignClientService, Invoice03SignClientService>();
            context.Services.AddScoped<ISignClientService, Invoice04SignClientService>();
            context.Services.AddScoped<ISignClientService, TicketSignClientService>();
            context.Services.AddScoped<ISignClientService, RegistrationSignClientService>();
            context.Services.AddScoped<ISignClientService, PITDeductionDocumentDeclarationSignClientService>();

            // business
            //context.Services.AddScoped<ISignServerInvoice01Business, SignServerInvoice01Business>();
            context.Services.AddScoped<ISignServerInvoice01Business, SignServerInvoice01ExtendBusiness>();
            context.Services.AddScoped<ISignServerInvoice02Business, SignServerInvoice02Business>();
            context.Services.AddScoped<ISignServerInvoice03Business, SignServerInvoice03Business>();
            context.Services.AddScoped<ISignServerInvoice04Business, SignServerInvoice04Business>();
            context.Services.AddScoped<ISignServerTicketBusiness, SignServerTicketBusiness>();

            context.Services.AddScoped<ISign123Invoice01Business, Sign123Invoice01Business>();
            context.Services.AddScoped<ISign123Invoice02Business, Sign123Invoice02Business>();
            context.Services.AddScoped<ISign123Invoice03Business, Sign123Invoice03Business>();
            context.Services.AddScoped<ISign123Invoice04Business, Sign123Invoice04Business>();
            context.Services.AddScoped<ISign123TicketBusiness, Sign123TicketBusiness>();

            context.Services.AddScoped<ISignInvoiceError01Business, SignInvoiceError01Business>();
            context.Services.AddScoped<ISignInvoiceError02Business, SignInvoiceError02Business>();
            context.Services.AddScoped<ISignInvoiceError03Business, SignInvoiceError03Business>();
            context.Services.AddScoped<ISignInvoiceError04Business, SignInvoiceError04Business>();
            context.Services.AddScoped<ISignTicketErrorBusiness, SignTicketErrorBusiness>();
            context.Services.AddScoped<ISignInvoiceErrorPITDeductionDocumentBusiness, SignInvoiceErrorPITDeductionDocumentBusiness>();

            context.Services.AddScoped<ISendToTvanInvoice01ErrorBusiness, SendToTvanInvoice01ErrorBusiness>();
            context.Services.AddScoped<ISendToTvanInvoice02ErrorBusiness, SendToTvanInvoice02ErrorBusiness>();
            context.Services.AddScoped<ISendToTvanPITDeductionDocumentErrorBusiness, SendToTvanPITDeductionDocumentErrorBusiness>();

            context.Services.AddScoped<ISign123Invoice01ErrorBusiness, Sign123Invoice01ErrorBusiness>();
            context.Services.AddScoped<ISign123Invoice02ErrorBusiness, Sign123Invoice02ErrorBusiness>();
            context.Services.AddScoped<ISign123Invoice03ErrorBusiness, Sign123Invoice03ErrorBusiness>();
            context.Services.AddScoped<ISign123Invoice04ErrorBusiness, Sign123Invoice04ErrorBusiness>();
            context.Services.AddScoped<ISign123TicketErrorBusiness, Sign123TicketErrorBusiness>();
            context.Services.AddScoped<ISign123PITDeductionDocumentErrorBusiness, Sign123PITDeductionDocumentErrorBusiness>();

            context.Services.AddScoped<IInvoiceErrorCommonBusiness, InvoiceErrorCommonBusiness>();


            // sign-client
            context.Services.AddScoped<ISignClientInvoice01Business, SignClientInvoice01Business>();
            context.Services.AddScoped<ISignClientInvoice02Business, SignClientInvoice02Business>();
            context.Services.AddScoped<ISignClientInvoice03Business, SignClientInvoice03Business>();
            context.Services.AddScoped<ISignClientInvoice04Business, SignClientInvoice04Business>();
            context.Services.AddScoped<ISignClientTicketBusiness, SignClientTicketBusiness>();
            context.Services.AddScoped<ISignClientPitDocumentBusiness, SignClientPitDocumentBusiness>();
            context.Services.AddScoped<ISignClientPitLetterBusiness, SignClientPitLetterBusiness>();

            context.Services.AddScoped<ISignClientInvoice01SaveXmlBusiness, SignClientInvoice01SaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice02SaveXmlBusiness, SignClientInvoice02SaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice03SaveXmlBusiness, SignClientInvoice03SaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice04SaveXmlBusiness, SignClientInvoice04SaveXmlBusiness>();
            context.Services.AddScoped<ISignClientTicketSaveXmlBusiness, SignClientTicketSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientPitDocumentSaveXmlBusiness, SignClientPitDocumentSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientPitLetterSaveXmlBusiness, SignClientPitLetterSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientTbssSaveXmlBusiness, SignClientTbssSaveXmlBusiness>(); // lưu xml sau khi ký = signclient của TBSS hóa đơn ngoài hệ thống (TT32...)

            context.Services.AddScoped<ISignClientInvoice01ErrorBusiness, SignClientInvoice01ErrorBusiness>();
            context.Services.AddScoped<ISignClientInvoice02ErrorBusiness, SignClientInvoice02ErrorBusiness>();
            context.Services.AddScoped<ISignClientInvoice03ErrorBusiness, SignClientInvoice03ErrorBusiness>();
            context.Services.AddScoped<ISignClientInvoice04ErrorBusiness, SignClientInvoice04ErrorBusiness>();
            context.Services.AddScoped<ISignClientTicketErrorBusiness, SignClientTicketErrorBusiness>();
            context.Services.AddScoped<ISignClientPITDeductionDocumentErrorBusiness, SignClientPITDeductionDocumentErrorBusiness>();
            context.Services.AddScoped<ISignClientTbssBusiness, SignClientTbssBusiness>(); // ký TBSS hóa đơn ngoài hệ thống (TT32...)

            context.Services.AddScoped<ISignClientInvoice01ErrorSaveXmlBusiness, SignClientInvoice01ErrorSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice02ErrorSaveXmlBusiness, SignClientInvoice02ErrorSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice03ErrorSaveXmlBusiness, SignClientInvoice03ErrorSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientInvoice04ErrorSaveXmlBusiness, SignClientInvoice04ErrorSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientTicketErrorSaveXmlBusiness, SignClientTicketErrorSaveXmlBusiness>();
            context.Services.AddScoped<ISignClientPITDeductionDocumentErrorSaveXmlBusiness, SignClientPITDeductionDocumentErrorSaveXmlBusiness>();

            context.Services.AddScoped<ISignServerTaxReport01Business, SignServerTaxReport01Business>();
            context.Services.AddScoped<ISignServerTaxReport03Business, SignServerTaxReport03Business>();

            context.Services.AddScoped<ISign123TaxReport01Business, Sign123TaxReport01Business>();
            context.Services.AddScoped<ISign123TaxReport03Business, Sign123TaxReport03Business>();

            context.Services.AddScoped<ISignTaxReport01BusinessFactory, SignTaxReport01BusinessFactory>();
            context.Services.AddScoped<ISignTaxReport01BusinessService, SignTaxReport01BusinessService>();
            context.Services.AddScoped<ISignTaxReport01BusinessService, NSHNSignTaxReport01BusinessService>();

            context.Services.AddScoped<ISignClientTaxReport01Business, SignClientTaxReport01Business>();
            context.Services.AddScoped<ISignClientTaxReport01SaveXmlBusiness, SignClientTaxReport01SaveXmlBusiness>();

            //AddRabbitMqServices(context.Services, configuration);
            // elastic search 
            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddProfile<VnisCoreSignApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreSignApplicationModule).GetTypeInfo().Assembly);
        }
    }
}