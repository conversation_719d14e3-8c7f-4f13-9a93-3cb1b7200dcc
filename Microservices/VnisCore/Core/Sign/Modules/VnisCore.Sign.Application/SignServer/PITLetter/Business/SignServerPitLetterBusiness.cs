using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITLetter;
using VnisCore.PITLetter.Infrastructure.IRepository;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.Invoices.PitLetter;

namespace VnisCore.Sign.Application.SignServer.PITLetter.Business
{
    public interface ISignServerPitLetterBusiness
    {
        Task SignAsync(List<long> ids);
    }

    public class SignServerPitLetterBusiness : ISignServerPitLetterBusiness
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly ISignService _signService;
        private readonly ISettingService _settingService;
        private readonly IConfiguration _configuration;
        private readonly IFileService _fileService;
        private readonly IPITLetterRepository _pITLetterRepository;

        public SignServerPitLetterBusiness(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            ISignService signService,
            ISettingService settingService,
            IConfiguration configuration,
            IFileService fileService,
            IPITLetterRepository pITLetterRepository)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _signService = signService;
            _settingService = settingService;
            _configuration = configuration;
            _fileService = fileService;
            _pITLetterRepository = pITLetterRepository;
        }

        public async Task SignAsync(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            if (!ids.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.PITLetter.PITLetterIsEmpty"]);

            var pitLetters = await _pITLetterRepository.GetByIdsAsync(ids);
            if (!pitLetters.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.PITLetter.PITLetterIsEmpty"]);

            var pitLettersToSign = pitLetters.Where(x => x.LetterStatus != (short)PITInvoiceStatus.DaHuy 
                                                    && (x.SignStatus == (short)PITSignStatus.KySoLoi || x.SignStatus == (short)PITSignStatus.ChuaKy))
                                            .ToList();
            if (pitLettersToSign.Any())
            {
                foreach (var pitLetter in pitLettersToSign)
                {
                    // Ký
                    var pitLetterSigned = await Sign123PITLetterDocumentBusiness(tenantId, userId, pitLetter);
                    if (pitLetterSigned.SignStatus != (short)PITSignStatus.DaKySo)
                    {
                        Log.Error($@"Ký thư chứng từ {pitLetterSigned.TemplateNo}-{pitLetterSigned.TaxPayerLegalName} không thành công");
                        continue;
                    }
                }
            }
        }

        public async Task<PITLetterEntity> Sign123PITLetterDocumentBusiness(Guid tenantId, Guid idUser, PITLetterEntity pitLetter)
        {
            var stopwatch = new Stopwatch();

            #region Lấy và kiểm tra chứng thư số
            var certificate = await _signService.GetCertificateAsync(idUser, tenantId, null);
            if (certificate == null)
                throw new UserFriendlyException("Chứng thư số chưa được đăng ký sử dụng, vui lòng đăng ký sử dụng chứng thư số với cơ quan thuế");
            #endregion

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);

            //Ký 
            if (setting == null)
                throw new UserFriendlyException("Không tìm thấy chứng thư số ký chứng từ");

            //Lấy cấu hình ngày ký
            var signBackDate = false;
            var settingSignDate = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignBackDate.ToString());
            if (settingSignDate != null && settingSignDate.Value == "1")
                signBackDate = true;

            //lấy cấu hình ký hsm
            var isSignHsm = await _signService.IsSignHsmAsync(tenantId);
            stopwatch.Restart();

            var token = await _signService.GetTokenAsync(setting, isSignHsm);

            stopwatch.Stop();
            Log.Fatal($"Lay token: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            //Tạo batch
            Log.Debug($"{pitLetter.TemplateNo}-{pitLetter.TaxPayerLegalName}: Start create batch from signserver");
            await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
            Log.Debug($"{pitLetter.TemplateNo}-{pitLetter.TaxPayerLegalName}: End create batch from signserver");

            stopwatch.Stop();
            Log.Fatal($"Tao batch: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            try
            {
                var uriSign = "api/batch-signer/xmlnd123/txntnurl";
                var msttcgp = _configuration.GetSection("MSTTCGP").Value;
                var model = PITLetterXml123Model.Parse(pitLetter, false);
                
                var xml = await _signService.Sign123Async(setting.Host, token, uriSign, _signService.NewObjToXml(model), pitLetter.Id.ToString());

                stopwatch.Stop();
                Log.Fatal($"Ky: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();

                if (xml != null)
                {
                    //Bỏ ký tự đặc biệt
                    //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                    if (xml[0] != '<')
                    {
                        xml = xml.Substring(1, xml.Length - 1);
                    }

                    // bỏ header của xml
                    XmlDocument doc = new XmlDocument();
                    doc.LoadXml(xml);

                    foreach (XmlNode node in doc)
                    {
                        if (node.NodeType == XmlNodeType.XmlDeclaration)
                        {
                            doc.RemoveChild(node);
                        }
                    }

                    xml = doc.InnerXml;
                }
               
                await SavePitLetterAfterSignedAsync(tenantId, idUser, pitLetter.OrganizationLegalName, xml, pitLetter, signBackDate);

                stopwatch.Stop();
                Log.Fatal($"Luu xml: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();
            }
            finally
            {
                try
                {
                    //Xóa batch
                    await _signService.RemoveBatch123Async(setting.Host, token);

                    stopwatch.Stop();
                    Log.Fatal($"Xoa Batch: {stopwatch.ElapsedMilliseconds}");
                    stopwatch.Restart();

                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }
            }

            return pitLetter;
        }

        #region private
        private async Task<FileDto> CallApiGenerateFilePdfAsync(PITLetterEntity unSignInvoice)
        {
            try
            {
                var ids = new List<long> { unSignInvoice.Id };

                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var clientHandler = new HttpClientHandler();
                clientHandler.ServerCertificateCustomValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;

                var client = new HttpClient(clientHandler);
                var httpContent = new StringContent(JsonConvert.SerializeObject(ids), Encoding.UTF8, "application/json");

                var host = _configuration.GetSection("Microservices:ExportPdf:Endpoint");
                var request = new HttpRequestMessage(HttpMethod.Post, host.Value + $"api/ExportPdf/InvoiceUnOfficial/pitletter");
                request.Content = httpContent;
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var result = await client.SendAsync(request);
                if (result.IsSuccessStatusCode)
                {
                    var data = await result.Content.ReadAsStringAsync();
                    var uploadFileModel = JsonConvert.DeserializeObject<FileDto>(data);
                    return uploadFileModel;
                }    
                else
                {
                    throw new UserFriendlyException(await result.Content.ReadAsStringAsync());
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException("Có lỗi trong quá trình sinh số");
            }
        }

        private async Task SavePitLetterAfterSignedAsync(Guid tenantId, Guid idUser, string userFullName, string xml, PITLetterEntity invoice, bool signBackDate)
        {
            if (xml == null)
            {
                invoice.SignStatus = (short)PITSignStatus.KySoLoi;
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }
            else
            {
                var name = $"Thu_XNTN_{invoice.PublishDate.Value.ToString("yyyyMMdd")}_{DateTime.Now.ToString("yyyyMMdd")}.xml";
                var fileName = $"Thu_XNTN_{invoice.PublishDate.Value.ToString("yyyyMMdd")}_{DateTime.Now.ToString("yyyyMMdd")}.xml";

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.PITLetterXml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu vào bảng xml trước
                var invoiceXml = new PITLetterXmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = name,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    InvoiceHeaderId = invoice.Id,
                    CreationTime = createdAt,
                    PathFolder = pathFileMinio
                };

                var repoInvoiceXml = _appFactory.Repository<PITLetterXmlEntity, long>();
                await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                //lưu lại vào hóa đơn
                invoice.SignStatus = (short)PITSignStatus.DaKySo;
                invoice.SignedTime = signBackDate ? invoice.PublishDate.Value : DateTime.Now;
                invoice.OrganizationFullNameSigned = userFullName;
                invoice.OrganizationSignedId = idUser;

                var date = signBackDate ? invoice.PublishDate : DateTime.Now;

                try
                {
                    var query = $@"UPDATE  ""{DatabaseExtension<PITLetterEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {PITSignStatus.DaKySo.GetHashCode()}, 
                                        ""SignedTime"" = '{date.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""OrganizationFullNameSigned"" = '{userFullName}', 
                                        ""OrganizationSignedId"" = '{OracleExtension.ConvertGuidToRaw(idUser)}'
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw new UserFriendlyException(ex.Message);
                }
            }
        }

        #endregion
    }
}
