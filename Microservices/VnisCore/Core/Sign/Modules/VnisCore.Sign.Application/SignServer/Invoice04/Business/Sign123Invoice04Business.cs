using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Sign.Application.Dto.Invoice04;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.Invoices.Invoice04;
using VnisCore.Sign.Application.Shared.Dtos;
using VnisCore.Sign.Application.Shared.Models;
using VnisCore.Sign.Application.SignServer.Invoice04.Interfaces;

namespace VnisCore.Sign.Application.SignServer.Invoice04.Business
{
    public class Sign123Invoice04Business : ISign123Invoice04Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISignService _signService;
        private readonly ISettingService _settingService;
        private readonly IFileService _fileService;
        private readonly IConfiguration _configuration;

        protected IObjectMapper _objectMapper { get; }

        public Sign123Invoice04Business(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISignService signService,
            ISettingService settingService,
            IFileService fileService,
            IObjectMapper objectMapper,
            IConfiguration configuration)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _signService = signService;
            _settingService = settingService;
            _fileService = fileService;
            _objectMapper = objectMapper;
            _configuration = configuration;
        }

        public async Task<Invoice04HeaderEntity> SignInvoices123(Guid tenantId, Guid idUser, string userFullName, short templateNo, string serialNo, string invoiceNo)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();

            var queryGetInvoice = @$"SELECT JSON_OBJECT (
                             KEY 'invoices' VALUE(
                               SELECT JSON_ARRAYAGG(
                                        JSON_OBJECT(
                                          a.*,
                                          KEY 'InvoiceDetail' VALUE(
                                            SELECT JSON_ARRAYAGG(
                                                     JSON_OBJECT(
                                                       b.*
                                                     ) RETURNING CLOB
                                                   )
                                            FROM   ""Invoice04Detail"" b
                                            WHERE  b.""InvoiceHeaderId"" = a.""Id""
                                          ),
                                          KEY 'InvoiceReference' VALUE(
                                            SELECT JSON_ARRAYAGG(
                                                     JSON_OBJECT(
                                                       b.*
                                                     ) RETURNING CLOB
                                                   )
                                            FROM   ""Invoice04Reference"" b
                                            WHERE  b.""InvoiceHeaderId"" = a.""Id""
                                          ),
                                          KEY 'InvoiceDocumentInfo' VALUE(
                                            SELECT JSON_ARRAYAGG(
                                                     JSON_OBJECT(
                                                       b.*
                                                     ) RETURNING CLOB
                                                   )
                                            FROM   ""Invoice04DocumentInfo"" b
                                            WHERE  b.""InvoiceHeaderId"" = a.""Id""
                                          ) RETURNING CLOB
                                        ) RETURNING CLOB

                                      )
                               FROM ""Invoice04Header"" a
                               where a.""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' 
                               AND ""SerialNo"" = '{serialNo}'
                               AND ""Number"" = {int.Parse(invoiceNo)}
                               AND rownum <= 1
                             ) RETURNING CLOB
                           ) AS invoices
                    FROM dual";

            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<string>(queryGetInvoice);
            var invoiceModel = data.JsonDeserialize<InvoicesModel>();

            stopwatch.Stop();
            Log.Fatal($"Time query data: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            if (invoiceModel == null || invoiceModel.Invoices == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.NotFound", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}" }]);

            var invoiceHeaderDtos = JsonConvert.DeserializeObject<List<InvoiceHeaderDto>>(invoiceModel.Invoices.ToString());
            if (invoiceHeaderDtos == null || !invoiceHeaderDtos.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.NotFound", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}" }]);

            var invoiceHeaderDto = invoiceHeaderDtos.FirstOrDefault();

            var invoiceDetails = new List<Invoice04DetailEntity>();
            Invoice04ReferenceEntity invoiceReference = null;
            Invoice04DocumentInfoEntity invoiceFile = null;

            Invoice04HeaderDto invoice04HeaderDto = _objectMapper.Map<InvoiceHeaderDto, Invoice04HeaderDto>(invoiceHeaderDto);
            var invoiceHeader = _objectMapper.Map<Invoice04HeaderDto, Invoice04HeaderEntity>(invoice04HeaderDto);

            if (!(invoiceHeader.SignStatus == SignStatus.ChoKy.GetHashCode()
               || invoiceHeader.SignStatus == SignStatus.KyLoi.GetHashCode()))
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.CannotSignInvoice", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}", EnumExtension.ToEnum<SignStatus>(invoiceHeader.SignStatus).GetName() }]);

            if (invoiceHeaderDto.InvoiceDetail != null && invoiceHeaderDto.InvoiceDetail.Any())
            {
                foreach (var item in invoiceHeaderDto.InvoiceDetail)
                {
                    invoiceDetails.Add(_objectMapper.Map<Invoice04DetailDto, Invoice04DetailEntity>(_objectMapper.Map<InvoiceDetailDto, Invoice04DetailDto>(item)));
                }
            }

            if (invoiceHeaderDto.InvoiceReference != null && invoiceHeaderDto.InvoiceReference.Any())
            {
                invoiceReference = _objectMapper.Map<InvoiceReferenceDto, Invoice04ReferenceEntity>(invoiceHeaderDto.InvoiceReference.FirstOrDefault());
            }

            if (invoiceHeaderDto.InvoiceDocumentInfo != null && invoiceHeaderDto.InvoiceDocumentInfo.Any())
            {
                invoiceFile = _objectMapper.Map<InvoiceDocumentInfo, Invoice04DocumentInfoEntity>(invoiceHeaderDto.InvoiceDocumentInfo.FirstOrDefault());
            }

            var dictionDetails = invoiceDetails.GroupBy(x => x.InvoiceHeaderId)
                                 .ToDictionary(x => x.Key, x => x.ToList());

            stopwatch.Stop();
            Log.Fatal($"Convert Data: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            // lấy thông tin chứng thư số trên server
            var certificate = await _signService.GetCertificateAsync(idUser, tenantId, invoiceHeader.InvoiceTemplateId);
            if (certificate == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.CertificateNotFound", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}" }]);

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);
            Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Got setting : {JsonConvert.SerializeObject(setting)}");

            //Ký 
            if (setting == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.SettingSignInvoiceNotFound", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}" }]);

            //Lấy cấu hình ngày ký
            var signBackDate = false;
            var settingSignDate = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignBackDate.ToString());
            if (settingSignDate != null && settingSignDate.Value == "1")
                signBackDate = true;

            //lấy cấu hình ký hsm
            var isSignHsm = await _signService.IsSignHsmAsync(tenantId);

            stopwatch.Stop();
            Log.Fatal($"Lay cau hinh, cts: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Start get token from signserver");
            var token = await _signService.GetTokenAsync(setting, isSignHsm);
            Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: End get token from signserver : {token}");

            stopwatch.Stop();
            Log.Fatal($"Lay token: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            var isHeader = EnumExtension.ToEnum<InvoiceStatus>(invoiceHeader.InvoiceStatus) == InvoiceStatus.DieuChinhDinhDanh;

            var isDetail = EnumExtension.ToEnum<InvoiceStatus>(invoiceHeader.InvoiceStatus) == InvoiceStatus.DieuChinhTangGiam
                && invoiceHeader.TotalPaymentAmount != 0;

            if (!isHeader && !isDetail && !dictionDetails.ContainsKey(invoiceHeader.Id))
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceDetailsIsEmpty", new string[] { $"{templateNo}-{serialNo}-{invoiceNo}" }]);

            //Tạo batch
            Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Start create batch from signserver");
            await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
            Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: End create batch from signserver");

            stopwatch.Stop();
            Log.Fatal($"Tao batch: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            try
            {
                var uriSign = "api/batch-signer/xmlnd123/invhdcmneurl";
                var msttcgp = _configuration.GetSection("MSTTCGP").Value;
                var model = Invoice04Xml123Model.Parse(invoiceHeader, invoiceDetails, invoiceReference, invoiceFile, signBackDate, msttcgp);
                Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Start sign from signserver");
                var xml = await _signService.Sign123Async(setting.Host, token, uriSign, _signService.NewObjToXml(model), invoiceHeader.Id.ToString());
                Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: End sign from signserver : {xml}");

                stopwatch.Stop();
                Log.Fatal($"Ky: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();

                if (xml != null)
                {
                    //Bỏ ký tự đặc biệt
                    //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                    if (xml[0] != '<')
                    {
                        xml = xml.Substring(1, xml.Length - 1);
                    }
                }

                Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Start save xml");
                await SaveInvoiceAfterSignedAsync(tenantId, idUser, userFullName, xml, invoiceHeader, signBackDate);
                Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: End save xml");

                stopwatch.Stop();
                Log.Fatal($"Luu xml: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();
            }
            finally
            {
                try
                {
                    //Xóa batch
                    Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: Start remove batch");
                    await _signService.RemoveBatch123Async(setting.Host, token);
                    Log.Debug($"{templateNo}-{serialNo}-{invoiceNo}: End remove batch");

                    stopwatch.Stop();
                    Log.Fatal($"Xoa Batch: {stopwatch.ElapsedMilliseconds}");
                    stopwatch.Restart();

                }
                catch (Exception ex)
                {
                    Log.Debug(ex, $"{templateNo}-{serialNo}-{invoiceNo}: {ex.Message}");
                }
            }

            //Nếu tất cả đều thành công thì trả về true, có 1 cái false thì trả về false
            return invoiceHeader;
        }

        private async Task SaveInvoiceAfterSignedAsync(Guid tenantId, Guid idUser, string userFullName, string xml, Invoice04HeaderEntity invoice, bool signBackDate)
        {
            if (xml == null)
            {
                invoice.SignStatus = (short)SignStatus.KyLoi;
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }
            else
            {
                var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
                var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice04Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu vào bảng xml trước
                var invoiceXml = new Invoice04XmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = name,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    InvoiceHeaderId = invoice.Id,
                    CreationTime = createdAt
                };

                var repoInvoiceXml = _appFactory.Repository<Invoice04XmlEntity, long>();
                var entity = await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                //Elastic
                //var data = new Invoice04XmlEventSendData((InvoiceXmlRequest)entity);
                //await _distributedEventBus.PublishAsync(data);

                //lưu lại vào hóa đơn
                invoice.SignStatus = (short)SignStatus.DaKy;
                invoice.SellerSignedTime = signBackDate ? invoice.InvoiceDate : DateTime.Now;
                invoice.SellerFullNameSigned = userFullName;
                invoice.SellerSignedId = idUser;

                var date = signBackDate ? invoice.InvoiceDate : DateTime.Now;

                try
                {
                    var query = $@"UPDATE  ""{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.DaKy.GetHashCode()}, 
                                        ""SellerSignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""SellerFullNameSigned"" = '{userFullName}', 
                                        ""SellerSignedId"" = '{ OracleExtension.ConvertGuidToRaw(idUser) }'
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }
        }
    }
}
