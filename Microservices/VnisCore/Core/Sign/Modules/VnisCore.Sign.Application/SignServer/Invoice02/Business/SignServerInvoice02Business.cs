using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Elasticsearch.Net;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Sign.Application.Dto.Invoice02;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.Invoices.Invoice02;
using VnisCore.Sign.Application.Repositories.Invoice02;
using VnisCore.Sign.Application.SignServer.Invoice02.Interfaces;

namespace VnisCore.Sign.Application.SignServer.Invoice02.Business
{
    public class SignServerInvoice02Business : ISignServerInvoice02Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IVnisCoreMongoXmlInvoice02SignedRepository _mongoXmlInvoice02SignedRepository;
        private readonly IVnisCoreMongoInvoice02LogRepository _mongoInvoice02LogRepository;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly ISignService _signService;
        private readonly IFileService _fileService;
        private readonly string _uriSign = "api/batch-signer/xmlnd123/invhdcmneurl";
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly ISettingService _settingService;
        private readonly IInvoice02HeaderFieldRepository _invoice02HeaderFieldRepository;

        public SignServerInvoice02Business(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IVnisCoreMongoXmlInvoice02SignedRepository mongoXmlInvoice02SignedRepository,
            IVnisCoreMongoInvoice02LogRepository mongoInvoice02LogRepository,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            ISignService signService,
            IFileService fileService,
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            ISettingService settingService,
            IInvoice02HeaderFieldRepository invoice02HeaderFieldRepository
            )
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _mongoXmlInvoice02SignedRepository = mongoXmlInvoice02SignedRepository;
            _mongoInvoice02LogRepository = mongoInvoice02LogRepository;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _signService = signService;
            _fileService = fileService;
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _settingService = settingService;
            _invoice02HeaderFieldRepository = invoice02HeaderFieldRepository;
        }

        public async Task SignAsync(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var tenantGroup = _appFactory.CurrentTenant.Group;
            var userId = _appFactory.CurrentUser.Id.Value;
            var userFullName = _appFactory.CurrentUser.Name;
            var userName = _appFactory.CurrentUser.UserName;
            if (!ids.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            var mongoInvoice02Entities = await _mongoInvoice02Repository.GetInvoiceToSignByIdsAsync(ids);
            if (!mongoInvoice02Entities.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            // lấy thông tin chứng thư số trên server
            var certificates = await _signService.GetAllCertificateAsync(new List<Guid> { tenantId });
            if (!certificates.Any())
                throw new UserFriendlyException("Không có chứng thư số nào");

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);
            
            if (setting == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.SettingSignInvoiceNotFound", new string[] { $"{string.Join(", ", mongoInvoice02Entities.Select(x => $"{x.TemplateNo}-{x.SerialNo}-{x.InvoiceNo}"))}" }]);

            //Log.Fatal($"Cau hinh server ky: {setting.Host}, {setting.UserName}, {setting.Password}");
            //lấy quyền sử dụng cts
            var atts = await _signService.GetAccountTokenTemplatesAsync(new List<Guid> { tenantId });
            var msttcgp = _appFactory.Configuration.GetSection("MSTTCGP").Value;

            // Lấy cấu hình thể TPhi
            var settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
            var headerFields = await _invoice02HeaderFieldRepository.QueryByTenantCodeRawAsync(tenantId);

            var groupByTemplates = mongoInvoice02Entities.GroupBy(x => x.InvoiceTemplateId);
            var certUsingByUsers = atts.Where(x => x.UsbTokenId.HasValue && x.UserId.HasValue && x.UserId.Value == userId).Select(x => x.UsbTokenId.Value).ToList();

            foreach (var groupByTemplate in groupByTemplates)
            {
                //lấy cts dc ký mẫu này
                var certUsingTemplates = atts.Where(x => x.TemplateId.HasValue && x.TemplateId == groupByTemplate.Key && x.UsbTokenId.HasValue).ToList();
                if (!certUsingTemplates.Any())
                {
                    Log.Error($"Không có chứng thư số nào ký mẫu Id = {groupByTemplate.Key}");
                    continue;
                }

                //lấy cts được dùng
                var idUsbTokens = certUsingTemplates.Select(x => x.UsbTokenId.Value).Intersect(certUsingByUsers);

                var certificate = certificates.FirstOrDefault(x => idUsbTokens.Contains(x.Id));
                if (certificate == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.CertificateNotAuthorized"]);

                var token = await _signService.GetTokenAsync(setting);
                var isSignHsm = await _signService.IsSignHsmAsync(tenantId);

                await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
                int.TryParse(_appFactory.Configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                try
                {
                    foreach (var item in groupByTemplate.ToList())
                    {
                        var signStatus = item.SignStatus;
                        try
                        {
                            Invoice02ReferenceDto invoiceReference = null;
                            if (item.InvoiceReference != null)
                            {
                                invoiceReference = _appFactory.ObjectMapper.Map<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceDto, Invoice02ReferenceDto>(item.InvoiceReference);

                            }
                            else if (item.InvoiceReferenceOld != null)
                            {
                                invoiceReference = _appFactory.ObjectMapper.Map<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceOldDto, Invoice02ReferenceDto>(item.InvoiceReferenceOld);
                            }

                            var model = Invoice02Xml123Model.Parse(item, invoiceReference, setting.IsSignBackDate, msttcgp, isEnableMongoDbLocalTime, settingConfigTPhi.Value, headerFields);
                            var xml = await _signService.Sign123Async(setting.Host, token, _uriSign, _signService.NewObjToXml(model), item.Id.ToString());

                            if (xml != null)
                            {
                                //Bỏ ký tự đặc biệt
                                //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                                if (xml[0] != '<')
                                {
                                    xml = xml.Substring(1, xml.Length - 1);
                                }

                                signStatus = (short)SignStatus.DaKy.GetHashCode();
                            }
                            else
                            {
                                signStatus = (short)SignStatus.KyLoi.GetHashCode();
                            }

                            await SaveInvoiceAfterSignedAsync(tenantId, tenantGroup, userId, userName, userFullName, xml, item, setting.IsSignBackDate, isEnableMongoDbLocalTime);

                        }
                        catch (Exception ex)
                        {
                            signStatus = (short)SignStatus.KyLoi.GetHashCode();
                            Log.Error(ex, ex.Message);
                            throw;
                        }
                        finally
                        {
                            // update trạng thái ký lên ES
                            await UpdateEsAsync(new List<long> { item.Id }, signStatus);

                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw;
                }
                finally
                {
                    await _signService.RemoveBatch123Async(setting.Host, token);
                }
            }
        }

        private async Task SaveInvoiceAfterSignedAsync(Guid tenantId, decimal tenantGroup, Guid idUser, string userName, string userFullName, string xml, MongoInvoice02Entity invoice, bool signBackDate, int isEnableMongoDbLocalTime)
        {
            if (xml == null)
            {
                invoice.SignStatus = (short)SignStatus.KyLoi;
                invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                invoice.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
                await _mongoInvoice02Repository.UpdateAsync(invoice);
                //await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                try
                {
                    var query = $@"UPDATE  ""{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()} 
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }
            else
            {
                var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
                var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice02Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                // userFullName trong DB đang cho phép null
                var sellerFullNameSigned = string.IsNullOrEmpty(userFullName) ? userName : userFullName;

                //lưu lại vào hóa đơn
                var date = signBackDate ? invoice.InvoiceDate : DateTime.Now;
                invoice.SignStatus = (short)SignStatus.DaKy;
                invoice.SellerSignedTime = isEnableMongoDbLocalTime > 0 ? date.AddHours(7) : date;
                invoice.SellerFullNameSigned = sellerFullNameSigned;
                invoice.SellerSignedId = idUser;
                invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                invoice.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();

                await _mongoInvoice02Repository.UpdateAsync(invoice, true);

                await _mongoXmlInvoice02SignedRepository.InsertAsync(new MongoXmlInvoice02SignedEntity
                {
                    BuyerEmail = invoice.BuyerEmail,
                    CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                    FileName = name,
                    FullNameCreator = userFullName,
                    Id = Guid.NewGuid(),
                    InvoiceHeaderId = invoice.Id,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = invoice.InvoiceStatus,
                    IsGeneratedContentMail = 0,
                    IsSynced = 2,
                    PhysicalFileName = fileName,
                    SellerTaxCode = invoice.SellerTaxCode,
                    SerialNo = invoice.SerialNo,
                    TemplateNo = invoice.TemplateNo,
                    TenantGroup = tenantGroup,
                    TenantId = invoice.TenantId,
                    UserId = idUser,
                    UserNameCreator = userName,
                    Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml))
                });

                // insert log
                var invoice02LogEntity = new MongoInvoice02LogEntity
                {
                    InvoiceHeaderId = invoice.Id,
                    TenantId = tenantId,
                    UserId = idUser,
                    UserName = userName,
                    Action = (short)ActionLogInvoice.Sign.GetHashCode(),
                    CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                    InvoiceType = (short)VnisType._02GTTT.GetHashCode(),
                    Id = Guid.NewGuid(),
                    Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                };

                await _mongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                try
                {
                    //lưu vào bảng xml trước
                    var invoiceXml = new Invoice02XmlEntity
                    {
                        ContentType = ContentType.Xml,
                        FileName = name,
                        PhysicalFileName = fileName,
                        TenantId = tenantId,
                        InvoiceHeaderId = invoice.Id,
                        CreationTime = createdAt
                    };

                    var repoInvoiceXml = _appFactory.Repository<Invoice02XmlEntity, long>();
                    await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                    var query = $@"UPDATE  ""{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.DaKy.GetHashCode()}, 
                                        ""SellerSignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""SellerFullNameSigned"" = '{sellerFullNameSigned}', 
                                        ""SellerSignedId"" = '{ OracleExtension.ConvertGuidToRaw(idUser) }'
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(query);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }

        }

        private async Task UpdateEsAsync(List<long> ids, short signStatus)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var group = _appFactory.CurrentTenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            try
            {
                foreach (var id in ids)
                {
                    var res = await client.UpdateAsync<object>(id, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}", signStatus)
                                )
                            ).Refresh(Refresh.True)
                        );

                    if (res.IsValid)
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.Synced);
                    }
                    else
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.PendingSyncSign);
                    }

                    Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        #region old
        //public async Task SignAsync(List<long> ids)
        //{
        //    var tenantId = _appFactory.CurrentTenant.Id.Value;
        //    var userId = _appFactory.CurrentUser.Id.Value;
        //    var userFullName = _appFactory.CurrentUser.FullName;
        //    var userName = _appFactory.CurrentUser.UserName;

        //    if (!ids.Any())
        //        throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

        //    var queryInvoice = $@"SELECT ""Id"",
        //                                 ""TemplateNo"",
        //                                 ""SerialNo"",
        //                                 ""InvoiceNo"",
        //                                 ""TenantId"",
        //                                 ""SignStatus"",
        //                                 ""InvoiceStatus"",
        //                                 ""ApproveStatus""
        //                          FROM ""Invoice02Header"" 
        //                          WHERE ""Id"" IN ({string.Join(",", ids)})
        //                                      AND ""IsDeleted"" = 0";

        //    var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice02HeaderEntity>(queryInvoice);
        //    if (!invoices.Any())
        //        throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

        //    var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
        //    foreach (var invoice in invoices)
        //    {
        //        if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode()
        //            || invoice.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()
        //            || invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
        //            continue;

        //        await distributedEventBus.PublishAsync(new SignInvoice02EventSendData(new SignInvoiceRequestModel
        //        {
        //            TenantId = tenantId,
        //            UserId = userId,
        //            UserFullName = userFullName,
        //            InvoiceHeaderId = invoice.Id,
        //            InvoiceNo = invoice.InvoiceNo,
        //            TemplateNo = invoice.TemplateNo,
        //            SerialNo = invoice.SerialNo
        //        }));

        //        //Insert Log
        //        await distributedEventBus.PublishAsync(new Invoice02LogEventSendData
        //        {
        //            InvoiceHeaderId = invoice.Id,
        //            TenantId = invoice.TenantId,
        //            UserId = userId,
        //            UserName = userName,
        //            InvoiceType = EnumExtension.ToEnum<VnisType>(invoice.TemplateNo).GetHashCode(),
        //            Action = ActionLogInvoice.Sign.GetHashCode(),
        //            Partition = long.Parse(invoice.InvoiceDate.ToString("yyyyMMddHHmm")),
        //            ActionTime = DateTime.Now
        //        });
        //    }
        //}
        #endregion
    }
}
