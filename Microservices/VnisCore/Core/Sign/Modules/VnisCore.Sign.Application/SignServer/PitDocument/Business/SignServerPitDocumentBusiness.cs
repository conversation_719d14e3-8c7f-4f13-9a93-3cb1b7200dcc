using Core;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.PITDeductionDocument.Infrastructure.IRepository;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.PitDocument;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.PITDeductionDocument;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.TaxReport01;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Dtos;
using static Stimulsoft.Report.StiOptions.Export;

namespace VnisCore.Sign.Application.SignServer.PitDocument.Business
{
    public interface ISignServerPitDocumentBusiness
    {
        Task SignAsync(List<long> ids);
    }

    public class SignServerPitDocumentBusiness : ISignServerPitDocumentBusiness
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly ISignService _signService;
        private readonly ISettingService _settingService;
        private readonly IConfiguration _configuration;
        private readonly IFileService _fileService;
        private readonly IPITDeductionDocumentRepository _iPITDeductionDocumentRepository;
        private readonly IDistributedEventBus _distributedEventBus;

        public SignServerPitDocumentBusiness(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            ISignService signService,
            ISettingService settingService,
            IConfiguration configuration,
            IFileService fileService,
            IPITDeductionDocumentRepository iPITDeductionDocumentRepository,
            IDistributedEventBus distributedEventBus)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _signService = signService;
            _settingService = settingService;
            _configuration = configuration;
            _fileService = fileService;
            _iPITDeductionDocumentRepository = iPITDeductionDocumentRepository;
            _distributedEventBus = distributedEventBus;
        }

        public async Task SignAsync(List<long> ids)
        {
            // lọc được hóa đơn nào ký lỗi để ký lại và hóa đơn nào chờ ký
            // hóa đơn ký lại: đã sinh số (bỏ qua bước sinh số)
            // hóa đơn chờ ký: sinh số rồi mới ký

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            if (!ids.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.PIT.PITIsEmpty"]);

            var invoices = await _iPITDeductionDocumentRepository.GetByIdsAsync(ids);
            if (!invoices.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.PIT.PITIsEmpty"]);

            var invoicesReSign = invoices.Where(x => x.Number != 0
                                            && !string.IsNullOrEmpty(x.DocumentNumber)
                                            && (x.SignStatus == (short)PITSignStatus.KySoLoi || x.SignStatus == (short)PITSignStatus.ChuaKy)).ToList();
            if (invoicesReSign.Any())
            {
                foreach (var invoice in invoicesReSign)
                {
                    // Ký
                    var header = await Sign123PITDocumentBusiness(tenantId, userId, invoice);
                    if (header.SignStatus != (short)PITSignStatus.DaKySo)
                    {
                        Log.Error($@"Ký hóa đơn {header.TemplateNo}-{header.SerialNo}-{header.DocumentNumber} không thành công");
                        continue;
                    }
                }
            }
        }
        private async Task<List<PITDeductionDocumentEntity>> CallApiGenerateNumberAsync(Guid tenantId, List<PITDeductionDocumentEntity> unSignInvoice)
        {
            try
            {

                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var clientHandler = new HttpClientHandler();
                clientHandler.ServerCertificateCustomValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;

                var client = new HttpClient(clientHandler);
                var httpContent = new StringContent(unSignInvoice.JsonSerialize(), Encoding.UTF8, "application/json");

                var host = _configuration.GetSection("Microservices:PitGenerateNumber:Endpoint");
                var request = new HttpRequestMessage(HttpMethod.Post, host.Value + $"api/pitGenerateNumber/GenerateNumberPIT/GenerateNumber/{tenantId}");
                request.Content = httpContent;
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var result = await client.SendAsync(request);

                var data = await result.Content.ReadAsStringAsync();
                var uploadFileModel = JsonConvert.DeserializeObject<List<PITDeductionDocumentEntity>>(data);
                return uploadFileModel;
            }
            catch (Exception ex)
            {

                throw new UserFriendlyException("Có lỗi trong quá trình sinh số");
            }
        }
        public async Task<PITDeductionDocumentEntity> Sign123PITDocumentBusiness(Guid tenantId, Guid idUser, PITDeductionDocumentEntity invoice)
        {
            var stopwatch = new Stopwatch();

            var certificate = await _signService.GetCertificateAsync(idUser, tenantId, null);
            if (certificate == null)
                throw new UserFriendlyException("Chứng thư số chưa được đăng ký sử dụng, vui lòng đăng ký sử dụng chứng thư số với cơ quan thuế");
            //throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.CertificateNotFound", new string[] { $"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}" }]);

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);

            //Ký 
            if (setting == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.SettingSignInvoiceNotFound", new string[] { $"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}" }]);

            //Lấy cấu hình ngày ký
            var signBackDate = false;
            var settingSignDate = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignBackDate.ToString());
            if (settingSignDate != null && settingSignDate.Value == "1")
                signBackDate = true;

            //lấy cấu hình ký hsm
            var isSignHsm = await _signService.IsSignHsmAsync(tenantId);
            stopwatch.Restart();

            var token = await _signService.GetTokenAsync(setting, isSignHsm);

            stopwatch.Stop();
            Log.Fatal($"Lay token: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            //Tạo batch
            Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: Start create batch from signserver");
            await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
            Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: End create batch from signserver");

            stopwatch.Stop();
            Log.Fatal($"Tao batch: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            try
            {
                PITDeductionDocumentEntity invoiceReference = null;

                if (invoice.Status == (short)PITDeductionStatus.DieuChinh || invoice.Status == (short)PITDeductionStatus.ThayThe)
                {
                    if (invoice.ReferenceId.HasValue)
                    {
                        invoiceReference = await _iPITDeductionDocumentRepository.FirstOrDefaultAsync(x => x.Id == invoice.ReferenceId.Value);
                    }
                }

                var uriSign = "api/batch-signer/xmlnd123/invktttncnneurl";
                var msttcgp = _configuration.GetSection("MSTTCGP").Value;
                var model = PITDocumentXml123Model.Parse(invoice, invoiceReference, signBackDate);
                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: Start sign from signserver");
                var xml = await _signService.Sign123Async(setting.Host, token, uriSign, _signService.ObjToXml(model), invoice.Id.ToString());
                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: End sign from signserver : {xml}");

                stopwatch.Stop();
                Log.Fatal($"Ky: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();

                if (xml != null)
                {
                    //Bỏ ký tự đặc biệt
                    //Kiểm tra nếu ký tự đầu tiên ko phải<thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                    if (xml[0] != '<')
                    {
                        xml = xml.Substring(1, xml.Length - 1);
                    }
                }

                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: Start save xml");
                await SaveInvoiceAfterSignedAsync(tenantId, idUser, invoice.OrganizationLegalName, xml, invoice, signBackDate);
                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: End save xml");

                stopwatch.Stop();
                Log.Fatal($"Luu xml: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Restart();
            }
            finally
            {
                try
                {
                    //Xóa batch
                    Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: Start remove batch");
                    await _signService.RemoveBatch123Async(setting.Host, token);
                    Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: End remove batch");

                    stopwatch.Stop();
                    Log.Fatal($"Xoa Batch: {stopwatch.ElapsedMilliseconds}");
                    stopwatch.Restart();

                }
                catch (Exception ex)
                {
                    Log.Debug(ex, $"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}: {ex.Message}");
                }
            }
            return invoice;
        }

        private async Task SaveInvoiceAfterSignedAsync(Guid tenantId, Guid idUser, string userFullName, string xml, PITDeductionDocumentEntity invoice, bool signBackDate)
        {
            if (xml == null)
            {
                invoice.SignStatus = (short)PITSignStatus.KySoLoi;
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }
            else
            {
                var name = $"{invoice.SerialNo?.Replace("/", "")}-{invoice.DocumentNumber}-{invoice.DocumentDate.ToString("yyyyMMdd")}.xml";
                //var fileName = $"{invoice.OrganizationTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.DocumentNumber}-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var fileName = $"{invoice.SerialNo?.Replace("/", "")}-{invoice.DocumentNumber}-{invoice.DocumentDate.ToString("yyyyMMdd")}.xml";

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.PITXml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu vào bảng xml trước
                var invoiceXml = new PITDeductionDocumentXmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = name,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    InvoiceHeaderId = invoice.Id,
                    CreationTime = createdAt
                };

                var repoInvoiceXml = _appFactory.Repository<PITDeductionDocumentXmlEntity, long>();
                await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                //lưu lại vào hóa đơn
                invoice.SignStatus = (short)PITSignStatus.DaKySo;
                invoice.SignedTime = signBackDate ? invoice.DocumentDate : DateTime.Now;
                invoice.OrganizationFullNameSigned = userFullName;
                invoice.OrganizationSignedId = idUser;

                var date = signBackDate ? invoice.DocumentDate : DateTime.Now;

                try
                {
                    var query = $@"UPDATE  ""{DatabaseExtension<PITDeductionDocumentEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {PITSignStatus.DaKySo.GetHashCode()}, 
                                        ""SignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""OrganizationFullNameSigned"" = '{userFullName}', 
                                        ""OrganizationSignedId"" = '{OracleExtension.ConvertGuidToRaw(idUser)}'
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    await SendToTvanAsync(invoice, xml);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }
        }

        private async Task SendToTvanAsync(PITDeductionDocumentEntity pITDeductionDocumentEntity, string xml)
        {
            //publish mess để gửi lên tvan
            await _distributedEventBus.PublishAsync(new PITDeductionDocumentSendTvanEventSendData
            {
                Id = pITDeductionDocumentEntity.Id,
                TenantId = pITDeductionDocumentEntity.TenantId,
                SellerTaxCode = _appFactory.CurrentTenant.TaxCode,
                TimeRetry = 0,
                Xml = xml
            });
        }
    }
}
