using Core.Application.Dtos;
using Core.Shared.Attributes;
using Core.Shared.Invoice.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Dtos
{
    public class TaxReport01DetailDto : EntityDto<long>, IBaseTaxReport01DetailDecreeNo70Dto
    {
        public decimal ExchangeRate { get; set; }

        public long InvoiceReferenceId { get; set; }

        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long InvoiceHeaderId { get; set; }

        [Required(ErrorMessage = "Ký hiệu mẫu hóa đơn không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [MaxLength(50, ErrorMessage = "Ký hiệu tối đa 50 ký tự")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// cho hóa đơn 03
        /// </summary>
        public string ReceiverName { get; set; }

        public string BuyerFullName { get; set; }
        /// <summary>
        /// cho hóa đơn 04
        /// </summary>
        public string BuyerName { get; set; }
        public string BuyerTaxCode { get; set; }

        public DateTime InvoiceDate { get; set; }

        public string InvoiceNo { get; set; }

        /// <summary>
        /// Số hóa đơn 
        /// </summary>
        [Range(1, 99999999, ErrorMessage = "Số hóa đơn không đúng. Số hóa đơn trong khoảng 1-99999999")]
        public int Number { get; set; }
        public int Index { get; set; }

        public decimal TotalAmount { get; set; }

        public decimal TotalVatAmount { get; set; }

        public decimal TotalPaymentAmount { get; set; }

        public string Status { get; set; }

        public short InvoiceStatus { get; set; }

        public long InvoiceTemplateId { get; set; }

        public string InvoiceReferenceData { get; set; }
        public string InvoiceReferenceDataSplit { get; set; }

        public short ReferenceInvoiceType { get; set; }

        /// <summary>
        /// loại hóa đơn
        /// </summary>
        public short InvoiceType { get; set; }

        public string ProductId { get; set; }
        public string UnitId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string UnitName { get; set; }

        public decimal Amount { get; set; }

        public string Note { get; set; }

        public short? VatPercent { get; set; }

        public decimal PaymentAmount { get; set; }

        public decimal? Quantity { get; set; }

        public long PageSize { get; set; }
        public long RowNumber { get; set; }
        public decimal TotalItems { get; set; }

        //FK
        public long TaxReportHeaderId { get; set; }
        public long TaxReport01DetailMappingId { get; set; }
        public string ExtraProperties { get; set; }
        public decimal? TTPhi { get; set; }
        public string BudgetUnitCode { get; set; }
    }

    public class TaxReport01DetailGroupDto : EntityDto<long>, IBaseTaxReport01DetailDecreeNo70Dto
    {
        public decimal ExchangeRate { get; set; }
        public long InvoiceReferenceId { get; set; }

        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long InvoiceHeaderId { get; set; }

        [Required(ErrorMessage = "Ký hiệu mẫu hóa đơn không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [MaxLength(50, ErrorMessage = "Ký hiệu tối đa 50 ký tự")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// cho hóa đơn 03
        /// </summary>
        public string ReceiverName { get; set; }

        public string BuyerFullName { get; set; }
        /// <summary>
        /// cho hóa đơn 04
        /// </summary>
        public string BuyerName { get; set; }
        public string BuyerTaxCode { get; set; }

        public DateTime InvoiceDate { get; set; }

        public string InvoiceNo { get; set; }

        /// <summary>
        /// Số hóa đơn 
        /// </summary>
        [Range(1, 99999999, ErrorMessage = "Số hóa đơn không đúng. Số hóa đơn trong khoảng 1-99999999")]
        public int Number { get; set; }
        public int Index { get; set; }

        public decimal TotalAmount { get; set; }

        public decimal TotalVatAmount { get; set; }

        public decimal TotalPaymentAmount { get; set; }

        public string Status { get; set; }

        public short InvoiceStatus { get; set; }

        public long InvoiceTemplateId { get; set; }

        public string InvoiceReferenceData { get; set; }
        public string InvoiceReferenceDataSplit { get; set; }

        public short ReferenceInvoiceType { get; set; }

        /// <summary>
        /// loại hóa đơn
        /// </summary>
        public short InvoiceType { get; set; }

        public string ProductId { get; set; }
        public string UnitId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string UnitName { get; set; }

        public decimal Amount { get; set; }

        public string Note { get; set; }

        public short? VatPercent { get; set; }

        public decimal PaymentAmount { get; set; }

        public decimal? Quantity { get; set; }

        public long PageSize { get; set; }
        public long RowNumber { get; set; }
        public decimal TotalItems { get; set; }

        //FK
        public long TaxReportHeaderId { get; set; }
        public long TaxReport01DetailMappingId { get; set; }
        public string ExtraProperties { get; set; }
        public decimal? TTPhi { get; set; }

        public List<TaxReport01DetailDto> ReferenceInvoices { get; set; }
        public string BudgetUnitCode { get; set; }
    }
}
