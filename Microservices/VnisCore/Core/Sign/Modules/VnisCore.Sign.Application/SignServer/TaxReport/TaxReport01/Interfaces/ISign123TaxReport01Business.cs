using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Sign.Application.Models.SignServer;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Dtos;

namespace VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Interfaces
{
    public interface ISign123TaxReport01Business
    {
        Task<string> Sign123TaxReport01Async(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos);

        /// <summary>
        /// Xăng dầu
        /// </summary>
        /// <param name="taxReport"></param>
        /// <param name="taxReport01DetailMapping"></param>
        /// <param name="taxReportDetailDtos"></param>
        /// <returns></returns>
        Task<string> Sign123TaxReport01XangDauAsync(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos);

        Task<string> Sign123TaxReport01Async(TaxReport01HeaderEntity taxReport,
            TaxReport01DetailMappingEntity taxReport01DetailMapping,
            List<TaxReport01DetailDto> taxReportDetailDtos,
            SignServerInfomationModel signServerInfomation);

        Task<string> Sign123TaxReport01BoSungAsync(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos);

        Task<string> Sign123TaxReport01ByXmlAsync(Guid tenantId, string tag, string xml);

    }
}
