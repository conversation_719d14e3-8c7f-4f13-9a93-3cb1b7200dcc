using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.ReportInvoice;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport03.Interfaces;

namespace VnisCore.Sign.Application.SignServer.TaxReport.TaxReport03.Business
{
    public class Sign123TaxReport03Business : ISign123TaxReport03Business
    {
        private readonly IAppFactory _appFactory;

        public Sign123TaxReport03Business(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<string> Sign123TaxReport03Async(TaxReport03HeaderEntity taxReport)
        {
            try
            {
                var model = ToXmlModel(_appFactory.CurrentTenant, taxReport);
                //var xml = DecorationXml(XmlExtension.ObjToXml(model));
                var xml = XmlExtension.ObjToXml(model);

                //gửi lên signserver
                var signService = _appFactory.GetServiceDependency<ISignService>();
                var certificate = await signService.GetCertificateAsync(_appFactory.CurrentUser.Id.Value, _appFactory.CurrentTenant.Id.Value);
                var setting = await signService.GetSettingRawAsync(taxReport.TenantId);
                var token = await signService.GetTokenAsync(setting);

                //Tạo batch
                await signService.CreateBatch123Async(setting.Host, token, certificate);
                try
                {
                    var uri = "api/batch-signer/xmlnd123/invbthdlbrneurl";
                    xml = await signService.Sign123Async(setting.Host, token, uri, xml, taxReport.Id.ToString());

                    if (xml != null)
                    {
                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }
                    }
                }
                catch (Exception ex)
                {
                    //Tạo batch
                    await signService.RemoveBatch123Async(setting.Host, token);

                    throw ex;
                }

                var registrationInvoiceXml = await SaveFileXmlAsync(taxReport, xml);

                var repoTaxReport03Xml = _appFactory.Repository<ReportXmlEntity, long>();
                await repoTaxReport03Xml.InsertAsync(registrationInvoiceXml);

                taxReport.SignStatus = (short)SignStatus.DaKy.GetHashCode();
                taxReport.SignedTime = DateTime.Now;
                taxReport.SignerId = _appFactory.CurrentUser.Id.Value;
                taxReport.FullNameSigner = _appFactory.CurrentUser.Name;

                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                return xml;
            }
            catch (Exception ex)
            {
                taxReport.SignStatus = (short)RegistrationInvoiceSignStatus.KyLoi.GetHashCode();
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                throw;
            }
        }


        #region helpers
        private TKhaiReportInvoice03 ToXmlModel(global::Core.MultiTenancy.ICurrentTenant currentTenant, TaxReport03HeaderEntity taxHeader)
        {
            var taxes = StaticData.TaxDefaults.Values.ToDictionary(x => x.Item1, x => x.Item5);
            var tThaiTBaos = Enum.GetValues(typeof(TThaiTBao)).Cast<TThaiTBao>().ToDictionary(x => x.ToDisplayName(), x => x.GetHashCode());

            var details = taxHeader.TaxReportDetails?.OrderBy(x => x.Id).ToList();
            var taxXmls = new List<TTSuatReportInvoice03Model>();
            for (int i = 0; i < details.Count; i++)
            {
                var item = details.ElementAt(i);

                var invoices = new List<HDonReportInvoice03Model>();

                if (item.TaxReportDetailDatas != null && item.TaxReportDetailDatas.Any())
                {
                    for (int j = 0; j < item.TaxReportDetailDatas.Count; j++)
                    {
                        var data = item.TaxReportDetailDatas.ElementAt(j);

                        invoices.Add(new HDonReportInvoice03Model
                        {
                            STT = j + 1,
                            KHMSHDon = data.TemplateNo.ToString(),
                            KHHDon = data.SerialNo,
                            SHDon = data.Number.ToString("00000000"),
                            NLap = data.InvoiceDate.ToString("yyyy-MM-dd"),
                            TNMua = data.BuyerFullName,
                            MSTNMua = data.BuyerTaxCode,
                            DTCThue = data.Amount,
                            TGTGT = data.VatAmount,
                            GChu = data.Note
                        });
                    }
                }


                var taxXml = new TTSuatReportInvoice03Model
                {
                    TgDTCThue = item.SumAmount,
                    TgTGTGT = item.SumVatAmount,
                    TSuat = taxes.ContainsKey(item.VatPercent) ? taxes[item.VatPercent] : $"KHAC:{string.Format(CultureInfo.InvariantCulture, "{0:00.00}", item.VatPercent)}%",
                    DSHDon = new DSHDonReportInvoice03Model
                    {
                        HDon = invoices
                    }
                };

                taxXmls.Add(taxXml);
            }


            var result = new TKhaiReportInvoice03
            {
                DLTKhai = new DLTKhaiReportInvoice03Model
                {
                    //Data = "data",
                    Data = $"Id-{taxHeader.Id}",
                    TTChung = new TTChungDLTKhai
                    {
                        PBan = TvanInvoiceStaticData.PBan,
                        MSo = Mso._01TH_HDDT.ToDisplayName(),
                        Ten = Mso._01TH_HDDT.GetDescription(),
                        LKTThue = taxHeader.ReportMonth == 0 ? "Q" : "T",
                        KTThue = taxHeader.ReportMonth == 0 ? $"{taxHeader.ReportQuarter:00}/{taxHeader.ReportYear}" : $"{taxHeader.ReportMonth:00}/{taxHeader.ReportYear}",
                        DDanh = taxHeader.Place,
                        NLap = taxHeader.ReportDate.ToString("yyyy-MM-dd"),
                        TNNT = _appFactory.CurrentTenant.FullNameVi,
                        MST = _appFactory.CurrentTenant.TaxCode,
                        TDLThue = taxHeader.FullNameAgency,
                        MSTDLThue = taxHeader.TaxCodeAgency,
                        DVTTe = taxHeader.Currency
                    },
                    NDTKhai = new NDTKhai
                    {
                        TTSuat = taxXmls,
                        TgDThu = taxHeader.SumTotalAmount,
                        TgThue = taxHeader.SumTotalVatAmount
                    }
                },
                DSCKS = new DSCKSReportInvoice03Model
                {
                    NNT = new CKSNNTModel
                    {
                        Signature = new SignatureNNTModel
                        {
                            //Id = "NNTSignature",
                            Id = $"NNT-{taxHeader.Id}",
                            Object = new ObjectNNTModel
                            {
                                //Id = $"SigningTime-{BitConverter.ToString((Guid.NewGuid().ToByteArray())).Replace("-", "")}",
                                Id = $"SigningTime-{taxHeader.Id}",
                                SignatureProperties = new SignaturePropertiesModel
                                {
                                    SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new SignaturePropertyModel
                                        {
                                            //Id = "NTTSignTimeStamp",
                                            Target = "signatureProperties",
                                            SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            return result;
        }

        private string DecorationXml(string xml)
        {
            if (xml != null)
            {
                //Bỏ ký tự \ bị duplicate
                xml = xml.Replace(@"\n", "");
                xml = xml.Replace("\\\"", "\"");

                //Nếu đầu cuối là dấu " thì cắt chuỗi bỏ đi
                if (xml[0] == '"' && xml[xml.Length - 1] == '"')
                {
                    xml = xml.Substring(1, xml.Length - 2);
                }

                //Bỏ ký tự đặc biệt
                //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                if (xml[0] != '<')
                {
                    xml = xml.Substring(1, xml.Length - 1);
                }
            }

            return xml;
        }

        private async Task<ReportXmlEntity> SaveFileXmlAsync(TaxReport03HeaderEntity taxReport, string xml)
        {
            // Save to Minio
            var fileName = $"Baocaothue03-VnisRequest-{taxReport.FromDate}_{taxReport.ToDate}-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

            var bytes = Encoding.UTF8.GetBytes(xml);
            var createdAt = DateTime.Now;
            var pathFileMinio = $"{MediaFileType.TvanTaxReportXml}/{taxReport.TenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            var fileService = _appFactory.GetServiceDependency<IFileService>();
            await fileService.UploadAsync(pathFileMinio, bytes);

            return new ReportXmlEntity()
            {
                ReportHeaderId = taxReport.Id,
                ContentType = ContentType.Xml,
                TenantId = taxReport.TenantId,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                CreationTime = createdAt,
                Type = (short)ReportFileType.TaxReport03.GetHashCode(),
                CreatorId = _appFactory.CurrentUser.Id.Value
            };
        }
        #endregion
    }
}
