using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Shared.Url;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.ReportInvoice;
using Helpers;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.SignServer;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Dtos;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Interfaces;

namespace VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Business
{
    public class Sign123TaxReport01Business : ISign123TaxReport01Business
    {
        private readonly IAppFactory _appFactory;
        private readonly ISettingService _settingService;
        public Sign123TaxReport01Business(
            IAppFactory appFactory,
            ISettingService settingService)
        {
            _appFactory = appFactory;
            _settingService = settingService;
        }

        public async Task<string> Sign123TaxReport01Async(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos)
        {
            try
            {
                //var itemFirst = taxReportDetailDtos.FirstOrDefault();

                #region Lấy cấu hình thể TTPhi
                if (taxReport01DetailMapping.InvoiceType == 1)
                {
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice01.ToString());
                    //var headerFields = await _invoice01HeaderFieldRepository.QueryByTenantIdRawAsync(_appFactory.CurrentTenant.Id.Value);

                    var tPhiConfig = settingConfigTPhi.Value;

                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 2)
                {
                    // HD 02 Khong co VAT PERCENT
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 5)
                {
                    // Ticket
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                var model = ToXmlModel(_appFactory.CurrentTenant, taxReport, taxReport01DetailMapping, taxReportDetailDtos);
                //var xml = DecorationXml(XmlExtension.ObjToXmlWithoutRemoveXml(model));
                var xml = XmlExtension.ObjToXmlWithoutRemoveXml(model);

                //gửi lên signserver
                var signService = _appFactory.GetServiceDependency<ISignService>();
                var certificate = await signService.GetCertificateAsync(_appFactory.CurrentUser.Id.Value, _appFactory.CurrentTenant.Id.Value);
                var setting = await signService.GetSettingRawAsync(taxReport.TenantId);
                var token = await signService.GetTokenAsync(setting);

                var isSignHsm = await signService.IsSignHsmAsync(taxReport.TenantId);

                //Tạo batch
                await signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
                try
                {
                    var uri = "api/batch-signer/xmlnd123/invbthdlneurl";
                    xml = await signService.Sign123Async(setting.Host, token, uri, xml, taxReport01DetailMapping.Id.ToString());

                    if (xml != null)
                    {
                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }

                        await SaveFileXmlAsync(taxReport, taxReport01DetailMapping, xml);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw ex;
                }
                finally
                {
                    //remove batch
                    await signService.RemoveBatch123Async(setting.Host, token);
                }

                return xml;
            }
            catch (Exception ex)
            {
                Log.Error($"Lỗi tạo báo cáo {taxReport.Id} detailMapping {taxReport01DetailMapping.Id} với MST {_appFactory.CurrentTenant.TaxCode} {ex.Message}");
                throw ex;
            }
        }

        public async Task<string> Sign123TaxReport01XangDauAsync(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos)
        {
            try
            {
                //var itemFirst = taxReportDetailDtos.FirstOrDefault();

                #region Lấy cấu hình thể TTPhi
                if (taxReport01DetailMapping.InvoiceType == 1)
                {
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice01.ToString());
                    //var headerFields = await _invoice01HeaderFieldRepository.QueryByTenantIdRawAsync(_appFactory.CurrentTenant.Id.Value);

                    var tPhiConfig = settingConfigTPhi.Value;

                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 2)
                {
                    // HD 02 Khong co VAT PERCENT
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 5)
                {
                    // Ticket
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                var model = ToXmlXangDauModel(_appFactory.CurrentTenant, taxReport, taxReport01DetailMapping, taxReportDetailDtos);
                var xml = DecorationXml(XmlExtension.ObjToXmlWithoutRemoveXml(model));

                //gửi lên signserver
                var signService = _appFactory.GetServiceDependency<ISignService>();
                var certificate = await signService.GetCertificateAsync(_appFactory.CurrentUser.Id.Value, _appFactory.CurrentTenant.Id.Value);
                var setting = await signService.GetSettingsAsync(taxReport.TenantId);
                var token = await signService.GetTokenAsync(setting);

                var isSignHsm = await signService.IsSignHsmAsync(taxReport.TenantId);

                //Tạo batch
                await signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
                try
                {
                    var uri = "api/batch-signer/xmlnd123/invbthdlneurl";
                    xml = await signService.Sign123Async(setting.Host, token, uri, xml, taxReport01DetailMapping.Id.ToString());

                    if (xml != null)
                    {
                        //xml = JsonConvert.DeserializeObject<string>(xml);

                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }

                        // bỏ header của xml
                        XmlDocument doc = new XmlDocument();
                        doc.LoadXml(xml);

                        foreach (XmlNode node in doc)
                        {
                            if (node.NodeType == XmlNodeType.XmlDeclaration)
                            {
                                doc.RemoveChild(node);
                            }
                        }

                        xml = doc.InnerXml;

                        await SaveFileXmlAsync(taxReport, taxReport01DetailMapping, xml);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw ex;
                }
                finally
                {
                    //remove batch
                    await signService.RemoveBatch123Async(setting.Host, token);
                }

                return xml;
            }
            catch (Exception ex)
            {
                Log.Error($"Lỗi tạo báo cáo {taxReport.Id} detailMapping {taxReport01DetailMapping.Id} với MST {_appFactory.CurrentTenant.TaxCode} {ex.Message}");
                throw ex;
            }
        }

        public async Task<string> Sign123TaxReport01Async(TaxReport01HeaderEntity taxReport, 
            TaxReport01DetailMappingEntity taxReport01DetailMapping, 
            List<TaxReport01DetailDto> taxReportDetailDtos, 
            SignServerInfomationModel signServerInfomation)
        {
            try
            {
                // Lấy thông tin cần thiết để thực hiện ký Sign Server
                var signService = _appFactory.GetServiceDependency<ISignService>();
                var certificate = signServerInfomation.UsbToken;
                var setting = signServerInfomation.SignServerSetting;
                var token = signServerInfomation.Token;
                var isSignHsm = signServerInfomation.IsSignHsm;

                #region Lấy cấu hình thể TTPhi
                if (taxReport01DetailMapping.InvoiceType == 1)
                {
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice01.ToString());
                    //var headerFields = await _invoice01HeaderFieldRepository.QueryByTenantIdRawAsync(_appFactory.CurrentTenant.Id.Value);

                    var tPhiConfig = settingConfigTPhi.Value;

                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 2)
                {
                    // HD 02 Khong co VAT PERCENT
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if (taxReport01DetailMapping.InvoiceType == 5)
                {
                    // Ticket
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                            taxReportDetailDtos[index].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                var model = ToXmlModel(_appFactory.CurrentTenant, taxReport, taxReport01DetailMapping, taxReportDetailDtos);
                //var xml = DecorationXml(XmlExtension.ObjToXmlWithoutRemoveXml(model));
                var xml = XmlExtension.ObjToXmlWithoutRemoveXml(model);

                try
                {
                    var uri = SignServerUrl.SignXml;
                    xml = await signService.Sign123Async(setting.Host, token, uri, xml, taxReport01DetailMapping.Id.ToString());

                    if (xml != null)
                    {
                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }

                        await SaveFileXmlAsync(taxReport, taxReport01DetailMapping, xml);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw ex;
                }

                return xml;
            }
            catch (Exception ex)
            {
                Log.Error($"Lỗi tạo báo cáo {taxReport.Id} detailMapping {taxReport01DetailMapping.Id} với MST {_appFactory.CurrentTenant.TaxCode} {ex.Message}");
                throw ex;
            }
        }

        public async Task<string> Sign123TaxReport01ByXmlAsync(Guid tenantId, string tag, string xml)
        {
            try
            {
                //gửi lên signserver
                var signService = _appFactory.GetServiceDependency<ISignService>();
                var certificate = await signService.GetCertificateAsync(_appFactory.CurrentUser.Id.Value, _appFactory.CurrentTenant.Id.Value);
                var setting = await signService.GetSettingRawAsync(tenantId);
                var token = await signService.GetTokenAsync(setting);

                var isSignHsm = await signService.IsSignHsmAsync(tenantId);

                //Tạo batch
                await signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
                try
                {
                    var uri = "api/batch-signer/xmlnd123/invbthdlneurl";
                    xml = await signService.Sign123Async(setting.Host, token, uri, xml, tag);

                    if (xml != null)
                    {
                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw ex;
                }
                finally
                {
                    //remove batch
                    await signService.RemoveBatch123Async(setting.Host, token);
                }

                return xml;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        #region helpers
        private BTHDLieuReportInvoice01 ToXmlModel(global::Core.MultiTenancy.ICurrentTenant currentTenant, TaxReport01HeaderEntity taxHeader, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos)
        {
            try
            {
                var result = new BTHDLieuReportInvoice01
                {
                    DLBTHop = new DLBTHopReportInvoice01Model
                    {
                        //Data = "data",
                        Data = $"Id-{taxReport01DetailMapping.Id}",
                        TTChung = new TTChungDLBTHopReportInvoice01Model
                        {
                            PBan = TvanInvoiceStaticData.PBanTaxReport01,
                            MSo = Mso._01TH_HDDT.ToDisplayName(),
                            Ten = Mso._01TH_HDDT.GetDescription(),
                            SBTHDLieu = taxReport01DetailMapping.Index,
                            LKDLieu = taxHeader.TypeReport == (short)ReportType.Day ? "N" : (taxHeader.TypeReport == (short)ReportType.Month ? "T" : "Q"),
                            KDLieu = taxHeader.TypeReport == (short)ReportType.Day ? $"{taxHeader.DataPeriod.Value.ToString("dd/MM/yyyy")}" : (taxHeader.TypeReport == (short)ReportType.Month ? $"{taxHeader.ReportMonth:00}/{taxHeader.ReportYear}" : $"{taxHeader.ReportQuarter:00}/{taxHeader.ReportYear}"),
                            //LKDLieu = taxHeader.ReportMonth == 0 ? "Q" : "T", //TODO: xem lại có cả kỳ theo ngày
                            //KDLieu = taxHeader.ReportMonth == 0 ? $"{taxHeader.ReportQuarter:00}/{taxHeader.ReportYear}" : $"{taxHeader.ReportMonth:00}/{taxHeader.ReportYear}",
                            LDau = taxHeader.IsFirstTimeInPeriod ? 1 : 0,
                            BSLThu = taxHeader.AdditionalTimes,
                            NLap = taxHeader.ReportDate.ToString("yyyy-MM-dd"),
                            TNNT = currentTenant.FullNameVi,
                            MST = currentTenant.TaxCode,
                            HDDIn = 0,
                            LHHoa = taxHeader.TypeMerchandise, //khác
                            DVTTe = taxHeader.CurrencyUnit,
                        },
                        NDBTHDLieu = new NDBTHDLieuReportInvoice01Model
                        {
                            DSDLieu = new DSDLieuTaxReport01Model
                            {
                                DLieu = new List<DLieuTaxReport01Model>()
                            }
                        }
                    },
                    DSCKS = new DSCKSReportInvoice01Model
                    {
                        NNT = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                //Id = "NNTSignature",
                                Id = $"NNT-{taxReport01DetailMapping.Id}",
                                Object = new ObjectNNTModel
                                {
                                    //Id = $"SigningTime-{BitConverter.ToString((Guid.NewGuid().ToByteArray())).Replace("-", "")}",
                                    Id = $"SigningTime-{taxReport01DetailMapping.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                        {
                                            new SignaturePropertyModel
                                            {
                                                //Id = "NTTSignTimeStamp",
                                                Target = "signatureProperties",
                                                SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };

                var taxes = StaticData.TaxDefaults.Values.ToDictionary(x => x.Item1, x => x.Item5);
                var tThaiTBaos = Enum.GetValues(typeof(TThaiTBao)).Cast<TThaiTBao>().ToDictionary(x => x.ToDisplayName(), x => x.GetHashCode());


                var details = taxReportDetailDtos?.OrderBy(x => x.Id).ToList();
                for (int i = 0; i < details.Count; i++)
                {
                    var item = details.ElementAt(i);
                    string referenceTemplateNo = null;
                    string referenceSerialNo = null;
                    string referenceInvoiceNo = null;
                    DateTime? referenceInvoiceDate = null;
                    bool hasReference = false;

                    if (!string.IsNullOrEmpty(item.InvoiceReferenceDataSplit))
                    {
                        hasReference = true;
                        var splits = item.InvoiceReferenceDataSplit.Split("_");

                        referenceTemplateNo = splits[0];
                        referenceSerialNo = splits[1];
                        referenceInvoiceNo = splits[2];
                        referenceInvoiceDate = DateTime.Parse(splits[3]);
                    }

                    try
                    {
                        var detail = new DLieuTaxReport01Model
                        {
                            STT = i + 1,
                            KHMSHDon = item.TemplateNo.ToString(),
                            KHHDon = item.SerialNo,
                            SHDon = item.Number.ToString("00000000"),
                            NLap = item.InvoiceDate.ToString("yyyy-MM-dd"),
                            TNMua = item.BuyerFullName,
                            MSTNMua = item.BuyerTaxCode,
                            MDVQHNSach = item.BudgetUnitCode?.StandardizedValueOfTag(),
                            MKHang = null,
                            MHHDVu = null,
                            THHDVu = item.ProductName,
                            DVTinh = null,
                            SLuong = item.Quantity,
                            TTCThue = item.TotalAmount,
                            TSuat = !item.VatPercent.HasValue ? null : (taxes.ContainsKey(item.VatPercent.Value) ? taxes[item.VatPercent.Value] : $"KHAC:{String.Format(CultureInfo.InvariantCulture, "{0:00.00}", item.VatPercent)}%"),
                            TgTThue = item.TotalVatAmount,
                            TgTTToan = item.TotalPaymentAmount,
                            TThai = !string.IsNullOrEmpty(item.Status) && tThaiTBaos.ContainsKey(item.Status) ? tThaiTBaos[item.Status] : 0,
                            KHMSHDCLQuan = referenceTemplateNo,
                            KHHDCLQuan = referenceSerialNo,
                            SHDCLQuan = referenceInvoiceNo,
                            LHDCLQuan = hasReference ? (item.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : item.ReferenceInvoiceType) : null,
                            LKDLDChinh = hasReference ? (taxHeader.TypeReport == (short)ReportType.Day ? "N" : (taxHeader.TypeReport == (short)ReportType.Month ? "T" : "Q")) : null, // là giá trị kỳ của hóa đơn lquan
                            KDLDChinh = hasReference ? (taxHeader.TypeReport == (short)ReportType.Day ? $"{referenceInvoiceDate.Value.ToString("dd/MM/yyyy")}" : (taxHeader.TypeReport == (short)ReportType.Month ? $"{taxHeader.ReportMonth:00}/{referenceInvoiceDate.Value.Year}" : $"{referenceInvoiceDate.Value.GetQuarter():00}/{referenceInvoiceDate.Value.Year}")) : null,  //là giá trị kỳ của hóa đơn lquan
                            // LKDLDChinh = hasReference ? taxHeader.ReportMonth == 0 ? "Q" : "T" : null, // là giá trị kỳ của hóa đơn lquan
                            // KDLDChinh = hasReference ? (taxHeader.ReportMonth == 0 ? $"{referenceInvoiceDate.Value.GetQuarter():00}/{referenceInvoiceDate.Value.Year}" : $"{taxHeader.ReportMonth:00}/{referenceInvoiceDate.Value.Year}") : null,  //là giá trị kỳ của hóa đơn lquan
                            STBao = null, //lấy từ xml thông báo hóa đơn
                            NTBao = null,
                            GChu = item.Note,
                            TTPhi = item.TTPhi
                        };
                        detail.TGia = item.ExchangeRate;
                        #region Bo qua khi Don vi tien te la VND
                        //if (taxHeader.CurrencyUnit != "VND")
                        //{
                        //    detail.TGia = item.ExchangeRate;
                        //} else
                        //{
                        //    detail.TGia = null;
                        //}
                        #endregion
                        result.DLBTHop.NDBTHDLieu.DSDLieu.DLieu.Add(detail);
                    }
                    catch (Exception ex)
                    {

                        throw;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {

                throw;
            }
            
        }

        private BTHDLieuReportInvoice01 ToXmlXangDauModel(global::Core.MultiTenancy.ICurrentTenant currentTenant, TaxReport01HeaderEntity taxHeader, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos)
        {
            try
            {
                var result = new BTHDLieuReportInvoice01
                {
                    DLBTHop = new DLBTHopReportInvoice01Model
                    {
                        //Data = "data",
                        Data = $"Id-{taxReport01DetailMapping.Id}",
                        TTChung = new TTChungDLBTHopReportInvoice01Model
                        {
                            PBan = TvanInvoiceStaticData.PBan,
                            MSo = Mso._01TH_HDDT.ToDisplayName(),
                            Ten = Mso._01TH_HDDT.GetDescription(),
                            SBTHDLieu = taxReport01DetailMapping.Index,
                            LKDLieu = taxHeader.TypeReport == (short)ReportType.Day ? "N" : (taxHeader.TypeReport == (short)ReportType.Month ? "T" : "Q"),
                            KDLieu = taxHeader.TypeReport == (short)ReportType.Day ? $"{taxHeader.DataPeriod.Value.ToString("dd/MM/yyyy")}" : (taxHeader.TypeReport == (short)ReportType.Month ? $"{taxHeader.ReportMonth:00}/{taxHeader.ReportYear}" : $"{taxHeader.ReportQuarter:00}/{taxHeader.ReportYear}"),
                            LDau = taxHeader.IsFirstTimeInPeriod ? 1 : 0,
                            BSLThu = taxHeader.AdditionalTimes,
                            NLap = taxHeader.ReportDate.ToString("yyyy-MM-dd"),
                            TNNT = currentTenant.FullNameVi,
                            MST = currentTenant.TaxCode,
                            HDDIn = 0,
                            LHHoa = taxHeader.TypeMerchandise,
                            DVTTe = taxHeader.CurrencyUnit,
                        },
                        NDBTHDLieu = new NDBTHDLieuReportInvoice01Model
                        {
                            DSDLieu = new DSDLieuTaxReport01Model
                            {
                                DLieu = new List<DLieuTaxReport01Model>()
                            }
                        }
                    },
                    DSCKS = new DSCKSReportInvoice01Model
                    {
                        NNT = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                //Id = "NNTSignature",
                                Id = $"NNT-{taxReport01DetailMapping.Id}",
                                Object = new ObjectNNTModel
                                {
                                    //Id = $"SigningTime-{BitConverter.ToString((Guid.NewGuid().ToByteArray())).Replace("-", "")}",
                                    Id = $"SigningTime-{taxReport01DetailMapping.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                        {
                                            new SignaturePropertyModel
                                            {
                                                //Id = "NTTSignTimeStamp",
                                                Target = "signatureProperties",
                                                SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };

                var taxes = StaticData.TaxDefaults.Values.ToDictionary(x => x.Item1, x => x.Item5);
                var tThaiTBaos = Enum.GetValues(typeof(TThaiTBao)).Cast<TThaiTBao>().ToDictionary(x => x.ToDisplayName(), x => x.GetHashCode());


                var details = taxReportDetailDtos?.OrderBy(x => x.Id).ToList();
                for (int i = 0; i < details.Count; i++)
                {
                    var item = details.ElementAt(i);
                    string referenceTemplateNo = null;
                    string referenceSerialNo = null;
                    string referenceInvoiceNo = null;
                    DateTime? referenceInvoiceDate = null;
                    bool hasReference = false;

                    if (!string.IsNullOrEmpty(item.InvoiceReferenceDataSplit))
                    {
                        hasReference = true;
                        var splits = item.InvoiceReferenceDataSplit.Split("_");

                        referenceTemplateNo = splits[0];
                        referenceSerialNo = splits[1];
                        referenceInvoiceNo = splits[2];
                        referenceInvoiceDate = DateTime.Parse(splits[3]);
                    }

                    try
                    {
                        var detail = new DLieuTaxReport01Model
                        {
                            STT = i + 1,
                            KHMSHDon = item.TemplateNo.ToString(),
                            KHHDon = item.SerialNo,
                            SHDon = item.Number.ToString("00000000"),
                            NLap = item.InvoiceDate.ToString("yyyy-MM-dd"),
                            TNMua = item.BuyerFullName,
                            MSTNMua = item.BuyerTaxCode,
                            MKHang = null, // không bắt buộc
                            MHHDVu = item.ProductCode, // bắt buốc đối vs XĂNG DẦU
                            THHDVu = item.ProductName,
                            DVTinh = item.UnitName, // bắt buốc đối vs XĂNG DẦU
                            SLuong = item.Quantity, // bắt buốc đối vs XĂNG DẦU
                            TTCThue = item.TotalAmount,
                            TSuat = !item.VatPercent.HasValue ? null : (taxes.ContainsKey(item.VatPercent.Value) ? taxes[item.VatPercent.Value] : $"KHAC:{String.Format(CultureInfo.InvariantCulture, "{0:00.00}", item.VatPercent)}%"),
                            TgTThue = item.TotalVatAmount,
                            TgTTToan = item.TotalPaymentAmount,
                            TThai = !string.IsNullOrEmpty(item.Status) && tThaiTBaos.ContainsKey(item.Status) ? tThaiTBaos[item.Status] : 0,
                            KHMSHDCLQuan = referenceTemplateNo,
                            KHHDCLQuan = referenceSerialNo,
                            SHDCLQuan = referenceInvoiceNo,
                            LHDCLQuan = hasReference ? (item.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : item.ReferenceInvoiceType) : null,
                            LKDLDChinh = hasReference ? (taxHeader.TypeReport == (short)ReportType.Day ? "N" : (taxHeader.TypeReport == (short)ReportType.Month ? "T" : "Q")) : null, // là giá trị kỳ của hóa đơn lquan
                            KDLDChinh = hasReference ? (taxHeader.TypeReport == (short)ReportType.Day ? $"{referenceInvoiceDate.Value.ToString("dd/MM/yyyy")}" : (taxHeader.TypeReport == (short)ReportType.Month ? $"{taxHeader.ReportMonth:00}/{referenceInvoiceDate.Value.Year}" : $"{referenceInvoiceDate.Value.GetQuarter():00}/{referenceInvoiceDate.Value.Year}")) : null,  //là giá trị kỳ của hóa đơn lquan
                            //KDLDChinh = hasReference ? (taxHeader.ReportMonth == 0 ? $"{referenceInvoiceDate.Value.GetQuarter():00}/{referenceInvoiceDate.Value.Year}" : $"{taxHeader.ReportMonth:00}/{referenceInvoiceDate.Value.Year}") : null,  //là giá trị kỳ của hóa đơn lquan
                            STBao = null, //lấy từ xml thông báo hóa đơn
                            NTBao = null,
                            GChu = item.Note,
                            TTPhi = item.TTPhi
                        };
                        detail.TGia = item.ExchangeRate;
                        #region Bo qua khi Don vi tien te la VND
                        //if (taxHeader.CurrencyUnit != "VND")
                        //{
                        //    detail.TGia = item.ExchangeRate;
                        //} else
                        //{
                        //    detail.TGia = null;
                        //}
                        #endregion
                        result.DLBTHop.NDBTHDLieu.DSDLieu.DLieu.Add(detail);
                    }
                    catch (Exception ex)
                    {

                        throw;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        private async Task SaveFileXmlAsync(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, string xml)
        {
            // Save to Minio
            var fileName = $"Baocaothue01-{taxReport.FromDate}_{taxReport.ToDate}-{taxReport01DetailMapping.Index}-{DateTime.UtcNow.Ticks}-{taxReport01DetailMapping.Index}.xml".Replace("/", "-");

            var bytes = Encoding.UTF8.GetBytes(xml);
            var createdAt = DateTime.Now;
            var pathFileMinio = $"{MediaFileType.ReportXml}/{taxReport.TenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            var fileService = _appFactory.GetServiceDependency<IFileService>();
            await fileService.UploadAsync(pathFileMinio, bytes);

            var repoReportXml = _appFactory.Repository<ReportXmlEntity, long>();
            var result = new ReportXmlEntity()
            {
                ReportHeaderId = taxReport.Id,
                TaxReport01DetailMappingId = taxReport01DetailMapping.Id,
                ContentType = ContentType.Xml,
                TenantId = taxReport.TenantId,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                CreationTime = createdAt,
                Type = (short)ReportFileType.TaxReport01.GetHashCode(),
                CreatorId = _appFactory.CurrentUser.Id.Value
            };
            await repoReportXml.InsertAsync(result);
        }

        private string DecorationXml(string xml)
        {
            if (xml != null)
            {
                //Bỏ ký tự \ bị duplicate
                xml = xml.Replace(@"\n", "");
                xml = xml.Replace("\\\"", "\"");

                //Nếu đầu cuối là dấu " thì cắt chuỗi bỏ đi
                if (xml[0] == '"' && xml[xml.Length - 1] == '"')
                {
                    xml = xml.Substring(1, xml.Length - 2);
                }

                //Bỏ ký tự đặc biệt
                //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                if (xml[0] != '<')
                {
                    xml = xml.Substring(1, xml.Length - 1);
                }
            }

            return xml;
        }

        public async Task<string> Sign123TaxReport01BoSungAsync(TaxReport01HeaderEntity taxReport, TaxReport01DetailMappingEntity taxReport01DetailMapping, List<TaxReport01DetailDto> taxReportDetailDtos)
        {
            #region Lấy cấu hình thể TTPhi
            if (taxReport01DetailMapping.InvoiceType == 1)
            {
                var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice01.ToString());
                //var headerFields = await _invoice01HeaderFieldRepository.QueryByTenantIdRawAsync(_appFactory.CurrentTenant.Id.Value);

                var tPhiConfig = settingConfigTPhi.Value;

                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                        taxReportDetailDtos[index].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else if (taxReport01DetailMapping.InvoiceType == 2)
            {
                // HD 02 Khong co VAT PERCENT
                var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                var tPhiConfig = settingConfigTPhi.Value;
                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                        taxReportDetailDtos[index].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else if (taxReport01DetailMapping.InvoiceType == 5)
            {
                // Ticket
                var settingConfigTPhi = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SettingKey.ConfigLPhiForInvoice02.ToString());
                var tPhiConfig = settingConfigTPhi.Value;
                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = taxReportDetailDtos.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var index = taxReportDetailDtos.IndexOf(maxVatPercent);
                                        taxReportDetailDtos[index].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            var model = ToXmlModel(_appFactory.CurrentTenant, taxReport, taxReport01DetailMapping, taxReportDetailDtos);
            var xml = DecorationXml(XmlExtension.ObjToXmlWithoutRemoveXml(model));

            return xml;
        }
        #endregion
    }
}
