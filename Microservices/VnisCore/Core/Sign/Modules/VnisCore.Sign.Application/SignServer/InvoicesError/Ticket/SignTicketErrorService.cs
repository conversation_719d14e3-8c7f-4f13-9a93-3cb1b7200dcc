using Core;
using Core.Application.Services;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceExtra;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Ticket;
using VnisCore.Sign.Application.SignServer.InvoicesError.Ticket.Interfaces;

namespace VnisCore.Sign.Application.SignServer.InvoicesError.Ticket
{
    [Authorize]
    public class SignTicketErrorService : ApplicationService
    {
        private readonly ISignTicketErrorBusiness _signTicketErrorBusiness;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IRepository<TicketErrorEntity, long> _repoError;

        public SignTicketErrorService(
            ISignTicketErrorBusiness signTicketErrorBusiness,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IRepository<TicketErrorEntity, long> repoError)
        {
            _signTicketErrorBusiness = signTicketErrorBusiness;
            _appFactory = appFactory;
            _localizer = localizer;
            _repoError = repoError;
        }

        [HttpPost(Utilities.ApiUrlBaseSignServer + "Ticket/TicketError")]
        public async Task SignTicketError([FromBody] List<long> groupCodes)
        {
            await _signTicketErrorBusiness.SignAsync(groupCodes);
        }

        [Authorize(InvoiceErrorPermissions.InvoiceError.SendTvan)]
        [HttpPost(Utilities.ApiUrlBaseSignServer + "ticket/send-tvan-error")]
        public async Task SendTicketErrorToTvan([FromBody] List<long> groupCodes)
        {
            if (!CurrentTenant.IsAvailable)
                throw new UserFriendlyException("Không tìm thấy thông tin công ty");

            if (!CurrentUser.Id.HasValue)
                throw new UserFriendlyException("Không tìm thấy thông tin user");

            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var userId = _appFactory.CurrentUser.Id.Value;
            var userFullName = _appFactory.CurrentUser.Name;
            var sellerTaxCode = _appFactory.CurrentTenant.TaxCode;

            if (!groupCodes.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            var invoiceErrors = await _repoError.Where(x => groupCodes.Contains(x.GroupCode)
                                                        && x.SignStatus == (short)SignStatus.DaKy
                                                        && x.TvanStatus == (short)TvanStatus.SendError).ToListAsync();

            if (!invoiceErrors.Any())
                throw new UserFriendlyException("Không thấy TBSS chưa gửi hoặc gửi lỗi lên TVAN");

            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            foreach (var groupCode in groupCodes)
            {
                await distributedEventBus.PublishAsync(new SignTicketErrorEventTvanData
                {
                    TenantId = tenantId,
                    GroupCode = groupCode,
                    TaxCode = sellerTaxCode,
                    UserFullName = userFullName,
                    UserId = userId,
                });
            }
        }
    }
}
