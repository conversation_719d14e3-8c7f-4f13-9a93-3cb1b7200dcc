using Core;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.Invoices.Invoice01;

using Dapper;

using Microsoft.EntityFrameworkCore;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Sign.Application.Dto;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Common;
using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice02.Interfaces;

namespace VnisCore.Sign.Application.SignServer.InvoicesError.Invoice02.Business
{
    public class Sign123Invoice02ErrorBusiness : ISign123Invoice02ErrorBusiness
    {
        private readonly ISignService _signService;
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly ISettingService _settingService;

        public Sign123Invoice02ErrorBusiness(
            ISignService signService,
            IAppFactory appFactory,
            ISettingService settingService,
            IFileService fileService)
        {
            _signService = signService;
            _fileService = fileService;
            _settingService = settingService;
            _appFactory = appFactory;
        }

        public async Task<InvoiceErrorModel> SignInvoiceError123Async(Guid tenantId, Guid userId, string userFullName, long groupCode)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var queryInvoiceError = $@" SELECT * FROM ""Invoice02Error"" WHERE ""GroupCode"" = {groupCode} AND ""IsDeleted"" = 0 AND ""TenantId"" = '{rawTenantId}' ";
            var invoice02Errors = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice02ErrorEntity>(queryInvoiceError)).ToList();
            if (!invoice02Errors.Any())
                throw new UserFriendlyException("Không tìm thấy thông báo sai sót nào để ký");

            if (!(invoice02Errors.Any(x => x.SignStatus == SignStatus.ChoKy.GetHashCode())
               || invoice02Errors.Any(x => x.SignStatus == SignStatus.KyLoi.GetHashCode())))
                throw new UserFriendlyException("Không tìm thấy thông báo sai sót có trạng thái chờ ký hoặc ký lỗi");

            //Lấy thông tin chứng thư số (trên server)
            var certificate = await _signService.GetCertificateAsync(userId, tenantId);
            if (certificate == null)
                throw new UserFriendlyException("Không tìm thấy thông tin chứng tư số trên server");

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);
            Log.Logger.Debug($"Got setting : {JsonConvert.SerializeObject(setting)}");

            if (setting == null)
                throw new UserFriendlyException("Không tìm thấy cấu hình server ký");

            //Lấy cấu hình ngày ký
            var signBackDate = false;
            var settingSignDate = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignBackDate.ToString());
            if (settingSignDate != null && settingSignDate.Value == "1")
                signBackDate = true;

            //lấy cấu hình ký hsm
            var isSignHsm = await _signService.IsSignHsmAsync(tenantId);
            Log.Logger.Debug("Start get token from signserver");
            var token = await _signService.GetTokenAsync(setting, isSignHsm);
            Log.Logger.Debug("End get token from signserver");

            //Tạo batch
            Log.Logger.Debug("Start create batch from signserver");
            await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
            Log.Logger.Debug("End create batch from signserver");

            var sql = @$"SELECT ""FullNameVi"", ""TaxCode"" FROM ""VnisTenants"" WHERE ""IsDeleted"" = 0 AND ""Id"" = '{rawTenantId}' fetch first 1 row only";
            var tenant = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<VnisTenantDto>(sql);

            try
            {
                var uriSign = "api/batch-signer/xmlnd123/invtbhddtcssneurl";
                var model = GetXmlModel(invoice02Errors, tenant.FullNameVi, tenant.TaxCode);
                Log.Logger.Debug("Start sign from signserver");
                var xml = await _signService.Sign123Async(setting.Host, token, uriSign, _signService.NewObjToXml(model), groupCode.ToString());
                Log.Logger.Debug("End sign from signserver");

                if (xml != null)
                {
                    //Bỏ ký tự đặc biệt
                    //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                    if (xml[0] != '<')
                    {
                        xml = xml.Substring(1, xml.Length - 1);
                    }
                }

                Log.Logger.Debug("Start save xml");
                await SaveInvoiceAfterSignedAsync(tenantId, userId, userFullName, xml, invoice02Errors, signBackDate, groupCode);
                Log.Logger.Debug("End save xml");
            }
            finally
            {
                try
                {
                    //Xóa batch
                    Log.Logger.Debug($"Start remove batch");
                    await _signService.RemoveBatch123Async(setting.Host, token);
                    Log.Logger.Debug($"End remove batch");
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, $"{ex.Message}");
                }
            }

            return new InvoiceErrorModel
            {
                TaxCode = tenant.TaxCode,
                GroupCode = groupCode,
                TenantId = tenantId
            };
        }

        private DLieuInvoiceErrorRequestModel.TBaoModel GetXmlModel(List<Invoice02ErrorEntity> invoice02Errors, string sellerFullName, string taxCode)
        {
            var hdons = new List<DLieuInvoiceErrorRequestModel.HDonModel>();
            var index = invoice02Errors.ToDictionary(x => x.Id, x => x);
            var invoice02Error = invoice02Errors.FirstOrDefault();

            for (int i = 0; i < invoice02Errors.Count; i++)
            {
                var invoice = invoice02Errors.ElementAt(i);
                hdons.Add(new DLieuInvoiceErrorRequestModel.HDonModel
                {
                    STT = (short)(i + 1),
                    MCCQT = invoice.VerificationCode,
                    KHMSHDon = invoice.TemplateNo.ToString(),
                    KHHDon = invoice.SerialNo,
                    SHDon = invoice.InvoiceNo,
                    Ngay = invoice.InvoiceDate.ToString("yyyy-MM-dd"),
                    LADHDDT = (short)LADHDDTu.Loai1.GetHashCode(),
                    //TCTBao = (short)index[invoice.Id].Action,
                    LDo = index[invoice.Id].Reason
                });
            }

            return new DLieuInvoiceErrorRequestModel.TBaoModel
            {
                DLTBao = new DLieuInvoiceErrorRequestModel.DLTBaoModel
                {
                    Data = $"Id-{invoice02Error.GroupCode}",
                    PBan = TvanInvoiceStaticData.PBanTBSS,
                    MSo = Mso._04SS_HDDT.GetName(),
                    Ten = Mso._04SS_HDDT.GetDescription(),
                    Loai = (short)LoaiTBSSot.Loai1.GetHashCode(),
                    So = null,
                    NTBCCQT = null,
                    MCQT = invoice02Error?.CodeTaxDepartment,
                    TCQT = invoice02Error?.TaxDepartment,
                    TNNT = sellerFullName,
                    MST = taxCode,
                    MDVQHNSach = invoice02Error?.BudgetUnitCode,
                    DDanh = invoice02Error?.PlaceName,
                    NTBao = DateTime.Now.ToString("yyyy-MM-dd"),
                    DSHDon = new DLieuInvoiceErrorRequestModel.DSHDon
                    {
                        HDon = hdons
                    }
                },
                DSCKS = new DLieuInvoiceErrorRequestModel.DSCKS
                {
                    CCKSKhac = null,
                    NNT = new CKSNNTModel
                    {
                        Signature = new SignatureNNTModel
                        {
                            Id = $"NNT-{invoice02Error.GroupCode}",
                            Object = new ObjectNNTModel
                            {
                                Id = $"SigningTime-{invoice02Error.GroupCode}",
                                SignatureProperties = new SignaturePropertiesModel
                                {
                                    SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new SignaturePropertyModel
                                        {
                                            //Id = "NTTSignTimeStamp",
                                            //Target = "#NNTSignature",
                                            Target = "signatureProperties",
                                            SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }

        private async Task SaveInvoiceAfterSignedAsync(Guid tenantId, Guid idUser, string userFullName, string xml, List<Invoice02ErrorEntity> invoiceErrors, bool signBackDate, long groupCode)
        {
            if (xml == null)
            {
                // update ký lỗi vào bảng Invoice02Error
                var sql = @$"UPDATE ""Invoice02Error"" SET ""SignStatus"" = {(short)SignStatus.KyLoi.GetHashCode()} WHERE ""GroupCode"" = {groupCode}";
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
            }
            else
            {
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice02ErrorXml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu vào bảng xml 
                var invoiceXml = new Invoice02ErrorXmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    Length = bytes.Length,
                    CreationTime = createdAt,
                    GroupCode = groupCode
                };

                var repoTvanInvoiceErrorXml = _appFactory.Repository<Invoice02ErrorXmlEntity, long>();
                await repoTvanInvoiceErrorXml.InsertAsync(invoiceXml, true);
                //await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                // update đã ký vào bảng Invoice02Error
                var sql = @$"UPDATE ""Invoice02Error"" SET ""SignStatus"" = {(short)SignStatus.DaKy.GetHashCode()} WHERE ""GroupCode"" = {groupCode}";
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
            }
        }
    }
}
