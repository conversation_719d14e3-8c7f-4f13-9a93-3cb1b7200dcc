using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Sign.Application.Dto.InvoiceError;

namespace VnisCore.Sign.Application.SignServer.InvoicesError.Common.Business
{
    public interface IInvoiceErrorCommonBusiness
    {
        /// <summary>
        /// Check TBSS trước khi ký
        /// </summary>
        /// <returns></returns>
        Task<InvoiceErrorResponseDto> CheckInvoiceErrorAsync(InvoiceErrorRequestDto request);
    }

    public class InvoiceErrorCommonBusiness : IInvoiceErrorCommonBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IRepository<Invoice02HeaderEntity, long> _repoInvoice02Header;
        private readonly IRepository<Invoice03HeaderEntity, long> _repoInvoice03Header;
        private readonly IRepository<Invoice04HeaderEntity, long> _repoInvoice04Header;
        private readonly IRepository<TicketHeaderEntity, long> _repoTicketHeader;
        private readonly IRepository<PITDeductionDocumentEntity, long> _repoPITDeductionDocumentHeader;
        private readonly IRepository<Invoice01ErrorEntity, long> _repoInvoice01Err;
        private readonly IRepository<Invoice02ErrorEntity, long> _repoInvoice02Err;
        private readonly IRepository<Invoice03ErrorEntity, long> _repoInvoice03Err;
        private readonly IRepository<Invoice04ErrorEntity, long> _repoInvoice04Err;
        private readonly IRepository<TicketErrorEntity, long> _repoTicketErr;
        private readonly IRepository<PITDeductionDocumentErrorEntity, long> _repoPITDeductionDocumentErr;
        private readonly IRepository<TbssHeaderEntity, long> _repoTbssHeader;

        public InvoiceErrorCommonBusiness(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IRepository<Invoice02HeaderEntity, long> repoInvoice02Header,
            IRepository<Invoice03HeaderEntity, long> repoInvoice03Header,
            IRepository<Invoice04HeaderEntity, long> repoInvoice04Header,
            IRepository<TicketHeaderEntity, long> repoTicketHeader,
            IRepository<PITDeductionDocumentEntity, long> repoPITDeductionDocumentHeader,
            IRepository<Invoice01ErrorEntity, long> repoInvoice01Err,
            IRepository<Invoice02ErrorEntity, long> repoInvoice02Err,
            IRepository<Invoice03ErrorEntity, long> repoInvoice03Err,
            IRepository<Invoice04ErrorEntity, long> repoInvoice04Err,
            IRepository<TicketErrorEntity, long> repoTicketErr,
            IRepository<PITDeductionDocumentErrorEntity, long> repoPITDeductionDocumentErr,
            IRepository<TbssHeaderEntity, long> repoTbssHeader)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoice02Header = repoInvoice02Header;
            _repoInvoice03Header = repoInvoice03Header;
            _repoInvoice04Header = repoInvoice04Header;
            _repoTicketHeader = repoTicketHeader;
            _repoPITDeductionDocumentHeader = repoPITDeductionDocumentHeader;
            _repoInvoice01Err = repoInvoice01Err;
            _repoInvoice02Err = repoInvoice02Err;
            _repoInvoice03Err = repoInvoice03Err;
            _repoInvoice04Err = repoInvoice04Err;
            _repoTicketErr = repoTicketErr;
            _repoPITDeductionDocumentErr = repoPITDeductionDocumentErr;
            _repoTbssHeader = repoTbssHeader;
        }

        /// <summary>
        /// Implement
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<InvoiceErrorResponseDto> CheckInvoiceErrorAsync(InvoiceErrorRequestDto request)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            switch (request.InvoiceType)
            {
                case (short)VnisType._01GTKT:
                    return await Invoice01ErrorAsync(request, tenantId);
                case (short)VnisType._02GTTT:
                    return await Invoice02ErrorAsync(request, tenantId);
                case (short)VnisType._03XKNB:
                    return await Invoice03ErrorAsync(request, tenantId);
                case (short)VnisType._04HGDL:
                    return await Invoice04ErrorAsync(request, tenantId);
                case (short)VnisType._05TVDT:
                    return await TicketErrorAsync(request, tenantId);
                case (short)VnisType._6TNCN:
                    return await PITDeductionDocumentErrorAsync(request, tenantId);
                case (short)VnisType._11HĐNHT:
                    return await TbssErrorAsync(request, tenantId);
                default:
                    throw new UserFriendlyException("Loại hoá đơn không đúng!");
            }    
        }

        /// <summary>
        /// TBSS cho HĐ 01 Có trong Hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> Invoice01ErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoInvoice01Err.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            var invoiceHeaderIds = invoiceErrs.Select(x => x.InvoiceHeaderId).ToList();
            var invoiceHeaderByIds = await _repoInvoice01Header.AsNoTracking().Where(x => invoiceHeaderIds.Contains(x.Id)
                                                                                    && x.SerialNo.StartsWith("K")
                                                                                    && x.IsDeclared == false)
                                                                .ToListAsync();

            // TH lập TBSS cho hoá đơn không mã chưa kê khai thì
            if (invoiceHeaderByIds.Any())
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = false,
                    Invoices = invoiceHeaderByIds.Select(x => new InvoiceDto { Number = x.Number.Value, SerialNo = x.SerialNo }).ToList()
                };
            }
            else
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = true,
                    Invoices = new List<InvoiceDto>()
                };
            }
        }

        /// <summary>
        /// TBSS cho HĐ 02 Có trong Hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> Invoice02ErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoInvoice02Err.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            var invoiceHeaderIds = invoiceErrs.Select(x => x.InvoiceHeaderId).ToList();
            var invoiceHeaderByIds = await _repoInvoice02Header.AsNoTracking().Where(x => invoiceHeaderIds.Contains(x.Id)
                                                                                    && x.SerialNo.StartsWith("K")
                                                                                    && x.IsDeclared == false)
                                                                .ToListAsync();

            // TH lập TBSS cho hoá đơn không mã chưa kê khai thì
            if (invoiceHeaderByIds.Any())
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = false,
                    Invoices = invoiceHeaderByIds.Select(x => new InvoiceDto { Number = x.Number.Value, SerialNo = x.SerialNo }).ToList()
                };
            }
            else
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = true,
                    Invoices = new List<InvoiceDto>()
                };
            }
        }

        /// <summary>
        /// TBSS cho HĐ 03 Có trong Hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> Invoice03ErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoInvoice03Err.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            var invoiceHeaderIds = invoiceErrs.Select(x => x.InvoiceHeaderId).ToList();
            var invoiceHeaderByIds = await _repoInvoice03Header.AsNoTracking().Where(x => invoiceHeaderIds.Contains(x.Id)
                                                                                    && x.SerialNo.StartsWith("K")
                                                                                    && x.IsDeclared == false)
                                                                .ToListAsync();

            // TH lập TBSS cho hoá đơn không mã chưa kê khai thì
            if (invoiceHeaderByIds.Any())
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = false,
                    Invoices = invoiceHeaderByIds.Select(x => new InvoiceDto { Number = x.Number.Value, SerialNo = x.SerialNo }).ToList()
                };
            }
            else
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = true,
                    Invoices = new List<InvoiceDto>()
                };
            }
        }

        /// <summary>
        /// TBSS cho HĐ 04 Có trong Hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> Invoice04ErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoInvoice04Err.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            var invoiceHeaderIds = invoiceErrs.Select(x => x.InvoiceHeaderId).ToList();
            var invoiceHeaderByIds = await _repoInvoice04Header.AsNoTracking().Where(x => invoiceHeaderIds.Contains(x.Id)
                                                                                    && x.SerialNo.StartsWith("K")
                                                                                    && x.IsDeclared == false)
                                                                .ToListAsync();

            // TH lập TBSS cho hoá đơn không mã chưa kê khai thì
            if (invoiceHeaderByIds.Any())
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = false,
                    Invoices = invoiceHeaderByIds.Select(x => new InvoiceDto { Number = x.Number.Value, SerialNo = x.SerialNo }).ToList()
                };
            }
            else
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = true,
                    Invoices = new List<InvoiceDto>()
                };
            }
        }

        /// <summary>
        /// TBSS cho Ticket Có trong Hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> TicketErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoTicketErr.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            var invoiceHeaderIds = invoiceErrs.Select(x => x.InvoiceHeaderId).ToList();
            var invoiceHeaderByIds = await _repoTicketHeader.AsNoTracking().Where(x => invoiceHeaderIds.Contains(x.Id)
                                                                                    && x.SerialNo.StartsWith("K")
                                                                                    && x.IsDeclared == false)
                                                                .ToListAsync();

            // TH lập TBSS cho hoá đơn không mã chưa kê khai thì
            if (invoiceHeaderByIds.Any())
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = false,
                    Invoices = invoiceHeaderByIds.Select(x => new InvoiceDto { Number = x.Number.Value, SerialNo = x.SerialNo }).ToList()
                };
            }
            else
            {
                return new InvoiceErrorResponseDto
                {
                    IsSuccess = true,
                    Invoices = new List<InvoiceDto>()
                };
            }
        }

        /// <summary>
        /// TBSS cho chứng từ khấu trừ thuế TNCN có trong hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> PITDeductionDocumentErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var documentErrs = await _repoPITDeductionDocumentErr.AsNoTracking().Where(x => request.GroupCodes.Contains(x.GroupCode)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!documentErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }
            
            return new InvoiceErrorResponseDto
            {
                IsSuccess = true,
                Invoices = new List<InvoiceDto>()
            };
        }

        /// <summary>
        /// TBSS cho HĐ ngoài hệ thống
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<InvoiceErrorResponseDto> TbssErrorAsync(InvoiceErrorRequestDto request, Guid tenantId)
        {
            var invoiceErrs = await _repoTbssHeader.AsNoTracking().Where(x => request.GroupCodes.Contains(x.Id)
                                                                            && (x.SignStatus == (short)SignStatus.ChoKy.GetHashCode()
                                                                            || x.SignStatus == (short)SignStatus.KyLoi.GetHashCode())
                                                                            && x.TenantId == tenantId).ToListAsync();
            if (!invoiceErrs.Any())
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);
            }

            return new InvoiceErrorResponseDto
            {
                IsSuccess = true,
                Invoices = new List<InvoiceDto>()
            };
        }
    }
}
