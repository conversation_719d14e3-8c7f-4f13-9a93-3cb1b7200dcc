using Core;
using Core.Application.Services;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.Invoices.Invoice01;
using Core.Tvan.Vnpay.Interfaces;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceExtra;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss;
using VnisCore.Sign.Application.Interfaces;

namespace VnisCore.Sign.Application.SignServer.InvoicesError
{
    /// <summary>
    /// Ký TBSS cho hóa đơn ngoài hệ thống
    /// </summary>
    public class SignTbssService : ApplicationService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<TbssHeaderEntity, long> _repoTbssHeader;
        private readonly IRepository<TbssDetailEntity, long> _repoTbssDetail;
        private readonly IRepository<TbssXmlEntity, long> _repoTbssXml;
        private readonly ISignService _signService;
        private readonly ISettingService _settingService;
        private readonly IFileService _fileService;

        public SignTbssService(
            IAppFactory appFactory,
            IRepository<TbssHeaderEntity, long> repoTbssHeader,
            IRepository<TbssDetailEntity, long> repoTbssDetail,
            IRepository<TbssXmlEntity, long> repoTbssXml,
            ISignService signService,
            ISettingService settingService,
            IFileService fileService)
        {
            _appFactory = appFactory;
            _repoTbssDetail = repoTbssDetail;
            _repoTbssHeader = repoTbssHeader;
            _signService = signService;
            _settingService = settingService;
            _fileService = fileService;
            _repoTbssXml = repoTbssXml;
        }

        /// <summary>
        /// ký tbss hóa đơn ngoài hệ thống theo lô
        /// để invoice01/tbss => vì cho giống convension với các api crud và giống api ký các loại trên hệ thống 
        /// => theo yêu cầu của FE
        /// </summary>
        /// <param name="ids">ids tbss bảng TbssHeader</param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBaseSignServer + "tbss")]
        public async Task SignTBSSAsync(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenantFullNameVi = _appFactory.CurrentTenant.FullNameVi;
            var userFullNameVi = _appFactory.CurrentUser.UserName;
            var taxCode = _appFactory.CurrentTenant.TaxCode;

            var tbssHeaderByIds = await _repoTbssHeader.Where(x => ids.Contains(x.Id)
                                                        && (x.SignStatus == (short)SignStatus.ChoKy || x.SignStatus == (short)SignStatus.KyLoi)
                                                        && x.TenantId == tenantId)
                                                .ToListAsync();
            if (!tbssHeaderByIds.Any())
            {
                throw new UserFriendlyException("Không tìm thấy thông báo sai sót để ký");
            }

            var tbssHeaderIds = tbssHeaderByIds.Select(x => x.Id).ToList();
            var tbssDetailsByHeaderId = await _repoTbssDetail.Where(x => tbssHeaderIds.Contains(x.TbssHeaderId) && x.TenantId == tenantId).ToListAsync();
            if (!tbssDetailsByHeaderId.Any())
            {
                throw new UserFriendlyException("Không tìm thấy hóa đơn trong thông báo sai sót để ký");
            }

            foreach (var tbss in tbssHeaderByIds)
            {
                var certificate = await _signService.GetCertificateAsync(userId, tenantId);
                if (certificate == null)
                {
                    throw new UserFriendlyException("Không tìm thấy thông tin chứng tư số trên server");
                }

                //Lấy setting
                var setting = await _signService.GetSettingRawAsync(tenantId);
                if (setting == null)
                {
                    throw new UserFriendlyException("Không tìm thấy cấu hình server ký");
                }

                //Lấy cấu hình ngày ký
                var signBackDate = false;
                var settingSignDate = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignBackDate.ToString());
                if (settingSignDate != null && settingSignDate.Value == "1")
                    signBackDate = true;

                //lấy cấu hình ký hsm
                var isSignHsm = await _signService.IsSignHsmAsync(tenantId);
                var token = await _signService.GetTokenAsync(setting, isSignHsm);

                // tạo batch ký
                await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);

                try
                {
                    var uriSign = "api/batch-signer/xmlnd123/invtbhddtcssneurl";
                    var tbssDetails = tbssDetailsByHeaderId.Where(x => x.TbssHeaderId == tbss.Id && x.TenantId == tenantId).ToList();
                    var model = GetXmlModel(tbss, tbssDetails, tenantFullNameVi, taxCode);

                    var xml = await _signService.Sign123Async(setting.Host, token, uriSign, _signService.NewObjToXml(model), tbss.Id.ToString());

                    if (xml != null)
                    {
                        //Bỏ ký tự \ bị duplicate
                        xml = xml.Replace(@"\n", "");
                        xml = xml.Replace("\\\"", "\"");

                        //Nếu đầu cuối là dấu " thì cắt chuỗi bỏ đi
                        if (xml[0] == '"' && xml[xml.Length - 1] == '"')
                        {
                            xml = xml.Substring(1, xml.Length - 2);
                        }

                        //Bỏ ký tự đặc biệt
                        //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                        if (xml[0] != '<')
                        {
                            xml = xml.Substring(1, xml.Length - 1);
                        }

                        // bỏ header của xml
                        XmlDocument doc = new XmlDocument();
                        doc.LoadXml(xml);

                        foreach (XmlNode node in doc)
                        {
                            if (node.NodeType == XmlNodeType.XmlDeclaration)
                            {
                                doc.RemoveChild(node);
                            }
                        }

                        xml = doc.InnerXml;
                    }

                    var idTbssXml = await SaveTbssAfterSignedAsync(tenantId, tbss, xml);

                    // gửi lên TVAN

                    await SendTbssToTvanAync(tbss, tbssDetails, idTbssXml, xml, tenantId);

                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }
                finally
                {
                    try
                    {
                        await _signService.RemoveBatch123Async(setting.Host, token);
                    }
                    catch (Exception ex)
                    {
                        Log.Logger.Error(ex, $"{ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// gửi tbss hóa đơn ngoài hệ thống theo lô lên Tvan
        /// </summary>
        /// <param name="ids">ids tbss bảng TbssHeader</param>
        /// <returns></returns>
        [Authorize(InvoiceErrorPermissions.InvoiceError.SendTvan)]
        [HttpPost(Utilities.ApiUrlBaseSignServer + "tbss/send-tvan-error")]
        public async Task SendTbssToTvanAsync(List<long> ids)
        {
            if (!CurrentTenant.IsAvailable)
                throw new UserFriendlyException("Không tìm thấy thông tin công ty");

            if (!CurrentUser.Id.HasValue)
                throw new UserFriendlyException("Không tìm thấy thông tin user");

            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var userId = _appFactory.CurrentUser.Id.Value;
            var userFullName = _appFactory.CurrentUser.Name;

            var tbssHeaderByIds = await _repoTbssHeader.Where(x => ids.Contains(x.Id)
                                                       && x.SignStatus == (short)SignStatus.DaKy
                                                       && (x.TvanStatus == (short)TvanStatus.SendError || x.TvanStatus == (short)TvanStatus.UnSent)
                                                       && x.TenantId == tenantId)
                                               .ToListAsync();
            if (!tbssHeaderByIds.Any())
            {
                throw new UserFriendlyException("Không tìm thấy thông báo sai sót để gửi lên TVAN");
            }

            var tbssHeaderIds = tbssHeaderByIds.Select(x => x.Id).ToList();
            var tbssDetailsByHeaderId = await _repoTbssDetail.Where(x => tbssHeaderIds.Contains(x.TbssHeaderId) && x.TenantId == tenantId).ToListAsync();
            if (!tbssDetailsByHeaderId.Any())
            {
                throw new UserFriendlyException("Không tìm thấy hóa đơn trong thông báo sai sót để gửi lên TVAN");
            }

            foreach (var tbssHeader in tbssHeaderByIds)
            {
                var tbssXml = await _repoTbssXml.FirstOrDefaultAsync(x => x.TbssHeaderId == tbssHeader.Id && x.TenantId == tenantId);
                if (tbssXml == null)
                {
                    throw new UserFriendlyException("Không tìm thấy XML thông báo sai sót để gửi lên TVAN");
                }

                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var pathFileMinio = $"{MediaFileType.TbssXml}/{tbssXml.TenantId}/{tbssXml.CreationTime.Year}/{tbssXml.CreationTime.Month:00}/{tbssXml.CreationTime.Day:00}/{tbssXml.CreationTime.Hour:00}/{tbssXml.PhysicalFileName}";
                var bytes = await fileService.DownloadAsync(pathFileMinio);

                var xml = Encoding.UTF8.GetString(bytes);
                if (xml == null)
                {
                    throw new UserFriendlyException("Không tìm thấy XML thông báo sai sót để gửi lên TVAN");
                }

                var tbssDetails = tbssDetailsByHeaderId.Where(x => x.TenantId == tbssHeader.TenantId && x.TbssHeaderId == tbssHeader.Id).ToList();

                await SendTbssToTvanAync(tbssHeader, tbssDetails, tbssXml.Id, xml, tenantId);
            }
        }

        private async Task SendTbssToTvanAync(TbssHeaderEntity tbssHeader, List<TbssDetailEntity> tbssDetails, long idTbssXml, string xml, Guid tenantId)
        {
            var taxCode = _appFactory.CurrentTenant.TaxCode;
            var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();
           
            try
            {
                var responseTvan = await tvanVnpayInvoice.SendTbssAsync(taxCode, xml, tenantId, tbssHeader, tbssDetails, idTbssXml);
                if (responseTvan != null && responseTvan.Code == "00")
                {
                    tbssHeader.TvanStatus = (short)TvanStatus.Sended;
                    tbssHeader.MessageCode = responseTvan.Data?.MaThongdiep;
                }
                else
                {
                    tbssHeader.TvanStatus = (short)TvanStatus.SendError;
                    tbssHeader.MessageCode = responseTvan.Data?.MaThongdiep;
                }

                
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                tbssHeader.TvanStatus = (short)TvanStatus.SendError;
            }
            finally
            {
                await _repoTbssHeader.UpdateAsync(tbssHeader);

                foreach (var tbssDetail in tbssDetails)
                {
                    tbssDetail.TvanStatus = tbssHeader.TvanStatus;
                }

                await _repoTbssDetail.UpdateManyAsync(tbssDetails);
            }
        }

        private async Task<long> SaveTbssAfterSignedAsync(Guid tenantId, TbssHeaderEntity tbssHeader, string xml)
        {
            var signStatus = SignStatus.ChoKy;
            if (xml == null)
            {
                tbssHeader.SignStatus = (short)SignStatus.KyLoi.GetHashCode();
                await _repoTbssHeader.UpdateAsync(tbssHeader);

                signStatus = SignStatus.KyLoi;

                return 0;
            }
            else
            {
                var fileName = $"HoaDonCoSaiSotNgoaiHeThong-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TbssXml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu vào bảng xml 
                var invoiceXml = new TbssXmlEntity
                {
                    TbssHeaderId = tbssHeader.Id,
                    MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    Length = bytes.Length,
                    CreationTime = createdAt,
                };

                var idTbssXml = await _repoTbssXml.InsertAsync(invoiceXml, true);

                tbssHeader.SignStatus = (short)SignStatus.DaKy.GetHashCode();
                await _repoTbssHeader.UpdateAsync(tbssHeader);

                return idTbssXml.Id;
            }
        }

        private DLieuInvoiceErrorRequestModel.TBaoModel GetXmlModel(TbssHeaderEntity tbssHeader, List<TbssDetailEntity> tbssDetails, string sellerFullName, string taxCode)
        {
            var hdons = new List<DLieuInvoiceErrorRequestModel.HDonModel>();

            short i = 0;
            foreach (var tbssDetail in tbssDetails)
            {
                i++;

                hdons.Add(new DLieuInvoiceErrorRequestModel.HDonModel
                {
                    STT = i,
                    MCCQT = tbssDetail.VerificationCode,
                    KHMSHDon = tbssDetail.TemplateNo.ToString(),
                    KHHDon = tbssDetail.SerialNo,
                    SHDon = tbssDetail.InvoiceNo,
                    Ngay = tbssDetail.InvoiceDate.ToString("yyyy-MM-dd"),
                    LADHDDT = tbssDetail.InvoiceType,
                    //TCTBao = tbssDetail.Action,
                    LDo = tbssDetail.Reason
                });
            }

            return new DLieuInvoiceErrorRequestModel.TBaoModel
            {
                DLTBao = new DLieuInvoiceErrorRequestModel.DLTBaoModel
                {
                    Data = $"Id-{tbssHeader.Id}",
                    PBan = TvanInvoiceStaticData.PBanTBSS,
                    MSo = Mso._04SS_HDDT.GetName(),
                    Ten = Mso._04SS_HDDT.GetDescription(),
                    Loai = (short)LoaiTBSSot.Loai1.GetHashCode(),
                    So = null,
                    NTBCCQT = null,
                    MCQT = tbssHeader?.CodeTaxDepartment,
                    TCQT = tbssHeader?.TaxDepartment,
                    TNNT = sellerFullName,
                    MST = taxCode,
                    MDVQHNSach = tbssHeader?.BudgetUnitCode,
                    DDanh = tbssHeader?.PlaceName,
                    NTBao = DateTime.Now.ToString("yyyy-MM-dd"),
                    DSHDon = new DLieuInvoiceErrorRequestModel.DSHDon
                    {
                        HDon = hdons
                    }
                },
                DSCKS = new DLieuInvoiceErrorRequestModel.DSCKS
                {
                    CCKSKhac = null,
                    NNT = new CKSNNTModel
                    {
                        Signature = new SignatureNNTModel
                        {
                            Id = $"NNT-{tbssHeader.Id}",
                            Object = new ObjectNNTModel
                            {
                                Id = $"SigningTime-{tbssHeader.Id}",
                                SignatureProperties = new SignaturePropertiesModel
                                {
                                    SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new SignaturePropertyModel
                                        {
                                            //Id = "NTTSignTimeStamp",
                                            //Target = "#NNTSignature",
                                            Target = "signatureProperties",
                                            SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
    }
}
