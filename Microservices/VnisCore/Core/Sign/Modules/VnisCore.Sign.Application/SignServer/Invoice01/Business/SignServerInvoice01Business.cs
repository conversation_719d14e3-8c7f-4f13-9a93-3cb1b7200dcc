using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Elasticsearch.Net;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Infrastructure.Dapper;
using VnisCore.Invoice01.Infrastructure.IRepository;
using VnisCore.Sign.Application.Dto.Invoice01;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.Invoices.Invoice01;
using VnisCore.Sign.Application.Models.Requests.Commands;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice01;
using VnisCore.Sign.Application.Repositories.Invoice01;
using VnisCore.Sign.Application.SignServer.Invoice01.Interfaces;
using VnisCore.Sign.Application.Util;
using IInvoice01HeaderRepository = VnisCore.Sign.Application.Repositories.Invoice01.IInvoice01HeaderRepository;
using Invoice01ReferenceDto = VnisCore.Sign.Application.Dto.Invoice01.Invoice01ReferenceDto;

namespace VnisCore.Sign.Application.SignServer.Invoice01.Business
{
    public class SignServerInvoice01Business : ISignServerInvoice01Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;


        public SignServerInvoice01Business(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _localizer = localizer;
        }

        public virtual async Task SignAsync(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var userFullName = _appFactory.CurrentUser.Name;
            var userName = _appFactory.CurrentUser.UserName;
            if (!ids.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            var repoInvoiceHeader = _appFactory.GetServiceDependency<IInvoice01HeaderRepository>();
            var invoices = await repoInvoiceHeader.GetInvoiceHeadersRawAsync(tenantId, ids);

            if (!invoices.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            foreach (var invoice in invoices)
            {
                if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode()
                    || invoice.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()
                    || invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
                    continue;

                await distributedEventBus.PublishAsync(new SignInvoice01EventSendData(new SignInvoiceRequestModel
                {
                    TenantId = tenantId,
                    UserId = userId,
                    UserFullName = userFullName,
                    InvoiceHeaderId = invoice.Id,
                    InvoiceNo = invoice.InvoiceNo,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo
                }));

                //Insert Log
                await distributedEventBus.PublishAsync(new Invoice01LogEventSendData(new Invoice01LogModel
                {
                    InvoiceHeaderId = invoice.Id,
                    TenantId = invoice.TenantId,
                    UserId = userId,
                    UserName = userName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(invoice.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Sign.GetHashCode(),
                    Partition = long.Parse(invoice.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));
            }
        }
    }

    public class SignServerInvoice01ExtendBusiness : SignServerInvoice01Business, ISignServerInvoice01Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IVnisCoreMongoXmlInvoice01SignedRepository _mongoXmlInvoice01SignedRepository;
        private readonly IVnisCoreMongoInvoice01LogRepository _mongoInvoice01LogRepository;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly ISignService _signService;
        private readonly IFileService _fileService;
        private readonly IConfiguration _configuration;
        private readonly ElasticSearch _elasticSearch;
        private readonly string _uriSign = "api/batch-signer/xmlnd123/invhdcmneurl";
        private readonly ISettingService _settingService;
        private readonly IInvoice01HeaderFieldRepository _invoice01HeaderFieldRepository;
        private readonly IInvoicePrintNoteRepository _invoicePrintNoteRepository;
        private readonly IInvoice01TaxBreakDownRepository _invoice01TaxBreakdownRepository;
        private readonly IInvoice01Dapper _invoice01Dapper;

        public SignServerInvoice01ExtendBusiness(IStringLocalizer<CoreLocalizationResource> localizer,
            IVnisCoreMongoXmlInvoice01SignedRepository mongoXmlInvoice01SignedRepository,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
            IVnisCoreMongoInvoice01LogRepository mongoInvoice01LogRepository,
            ISignService signService,
            IFileService fileService,
            IAppFactory appFactory,
            IConfiguration configuration,
            ElasticSearch elasticSearch,
            ISettingService settingService,
            IInvoice01HeaderFieldRepository invoice01HeaderFieldRepository,
            IInvoicePrintNoteRepository invoicePrintNoteRepository,
            IInvoice01TaxBreakDownRepository invoice01TaxBreakdownRepository,
            IInvoice01Dapper invoice01Dapper)
            : base(localizer, appFactory)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _mongoXmlInvoice01SignedRepository = mongoXmlInvoice01SignedRepository;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _signService = signService;
            _fileService = fileService;
            _mongoInvoice01LogRepository = mongoInvoice01LogRepository;
            _configuration = configuration;
            _elasticSearch = elasticSearch;
            _settingService = settingService;
            _invoice01HeaderFieldRepository = invoice01HeaderFieldRepository;
            _invoicePrintNoteRepository = invoicePrintNoteRepository;
            _invoice01TaxBreakdownRepository = invoice01TaxBreakdownRepository;
            _invoice01Dapper = invoice01Dapper;
        }

        public override async Task SignAsync(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var tenantGroup = _appFactory.CurrentTenant.Group;
            var userId = _appFactory.CurrentUser.Id.Value;
            var userFullName = _appFactory.CurrentUser.Name;
            var userName = _appFactory.CurrentUser.UserName;
            if (!ids.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            var mongoInvoice01Entities = await _mongoInvoice01Repository.GetInvoiceToSignByIdsAsync(ids);
            if (!mongoInvoice01Entities.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

            // lấy thông tin chứng thư số trên server
            var certificates = await _signService.GetAllCertificateAsync(new List<Guid> { tenantId });
            if (!certificates.Any())
                throw new UserFriendlyException("Không có chứng thư số nào");

            //Lấy setting
            var setting = await _signService.GetSettingRawAsync(tenantId);

            if (setting == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.SettingSignInvoiceNotFound", new string[] { $"{string.Join(", ", mongoInvoice01Entities.Select(x => $"{x.TemplateNo}-{x.SerialNo}-{x.InvoiceNo}"))}" }]);

            //Log.Fatal($"Cau hinh server ky: {setting.Host}, {setting.UserName}, {setting.Password}");
            //lấy quyền sử dụng cts
            var atts = await _signService.GetAccountTokenTemplatesAsync(new List<Guid> { tenantId });
            var msttcgp = _appFactory.Configuration.GetSection("MSTTCGP").Value;

            var groupByTemplates = mongoInvoice01Entities.GroupBy(x => x.InvoiceTemplateId);
            var certUsingByUsers = atts.Where(x => x.UsbTokenId.HasValue && x.UserId.HasValue && x.UserId.Value == userId).Select(x => x.UsbTokenId.Value).ToList();

            foreach (var groupByTemplate in groupByTemplates)
            {
                //lấy cts dc ký mẫu này
                var certUsingTemplates = atts.Where(x => x.TemplateId.HasValue && x.TemplateId == groupByTemplate.Key && x.UsbTokenId.HasValue).ToList();
                if (!certUsingTemplates.Any())
                {
                    Log.Error($"Không có chứng thư số nào ký mẫu Id = {groupByTemplate.Key}");
                    continue;
                }
                // Lấy cấu hình thể TPhi
                var settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                var headerFields = await _invoice01HeaderFieldRepository.QueryByTenantIdRawAsync(tenantId);

                //lấy cts được dùng
                var idUsbTokens = certUsingTemplates.Select(x => x.UsbTokenId.Value).Intersect(certUsingByUsers);

                var certificate = certificates.FirstOrDefault(x => idUsbTokens.Contains(x.Id));
                if (certificate == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.CertificateNotAuthorized"]);

                var token = await _signService.GetTokenAsync(setting);
                var isSignHsm = await _signService.IsSignHsmAsync(tenantId);

                await _signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);
                int.TryParse(_appFactory.Configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                try
                {
                    foreach (var item in groupByTemplate.ToList())
                    {
                        var signStatus = item.SignStatus;
                        try
                        {
                            Invoice01ReferenceDto invoiceReference = null;
                            if (item.InvoiceReference != null)
                            {
                                invoiceReference = _appFactory.ObjectMapper.Map<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceDto, Invoice01ReferenceDto>(item.InvoiceReference);
                                invoiceReference.Note = null;
                                if(item.InvoiceStatus == InvoiceStatus.DieuChinh.GetHashCode() || item.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
                                {
                                    // Lấy dữ liệu ghi chú
                                    var printNoteEntity = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(item.Id, (short)VnisType._01GTKT.GetHashCode(), tenantId);
                                    if(printNoteEntity != null)
                                    {
                                        invoiceReference.Note = printNoteEntity.Note;
                                    }
                                }
                            }
                            else if (item.InvoiceReferenceOld != null)
                            {
                                invoiceReference = _appFactory.ObjectMapper.Map<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceOldDto, Invoice01ReferenceDto>(item.InvoiceReferenceOld);
                            }

                            var model = Invoice01Xml123Model.Parse(item, invoiceReference, setting.IsSignBackDate, msttcgp, isEnableMongoDbLocalTime, settingConfigTPhi.Value, headerFields);
                            var xml = await _signService.Sign123Async(setting.Host, token, _uriSign, _signService.NewObjToXml(model), item.Id.ToString());

                            if (xml != null)
                            {
                                //Bỏ ký tự đặc biệt
                                //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                                if (xml[0] != '<')
                                {
                                    xml = xml.Substring(1, xml.Length - 1);
                                }

                                signStatus = (short)SignStatus.DaKy.GetHashCode();
                            }
                            else
                            {
                                signStatus = (short)SignStatus.KyLoi.GetHashCode();
                            }

                            await SaveInvoiceAfterSignedAsync(tenantId, tenantGroup, userId, userName, userFullName, xml, item, setting.IsSignBackDate, isEnableMongoDbLocalTime);

                        }
                        catch (Exception ex)
                        {
                            signStatus = (short)SignStatus.KyLoi.GetHashCode();
                            Log.Error(ex, ex.Message);
                            throw;
                        }
                        finally
                        {
                            // update trạng thái ký lên ES
                            await UpdateEsAsync(new List<long> { item.Id}, signStatus);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw;
                }
                finally
                {
                    await _signService.RemoveBatch123Async(setting.Host, token);
                }
            }
        }


        private async Task SaveInvoiceAfterSignedAsync(Guid tenantId, decimal tenantGroup, Guid idUser, string userName, string userFullName, string xml, MongoInvoice01Entity invoice, bool signBackDate, int isEnableMongoDbLocalTime)
        {
            if (xml == null)
            {
                var signStatus = (short)SignStatus.KyLoi;
                var isSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                var isSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
                await _mongoInvoice01Repository.UpdateInvoieAfterSignAsync(invoice.Id, signStatus, isSyncSignTocore, isSyncedToElasticSearch);

                try
                {
                    var query = $@"UPDATE  ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()} 
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }
            else
            {
                var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
                var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");

                //ghi file vào minio trước
                var bytes = Encoding.UTF8.GetBytes(xml);
                var createdAt = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice01Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";
                await _fileService.UploadAsync(pathFileMinio, bytes);

                //lưu lại vào hóa đơn
                var date = signBackDate ? invoice.InvoiceDate : DateTime.Now;
                var signStatus = (short)SignStatus.DaKy;
                DateTime sellerSignedTime = isEnableMongoDbLocalTime > 0 ? date.AddHours(7) : date;
                string sellerFullNameSigned = userFullName;
                short isSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                short isSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();

                await _mongoInvoice01Repository.UpdateInvoieAfterSignAsync(invoice.Id, signStatus, sellerSignedTime, sellerFullNameSigned, idUser, isSyncSignTocore, isSyncedToElasticSearch);

                await _mongoXmlInvoice01SignedRepository.InsertAsync(new MongoXmlInvoice01SignedEntity
                {
                    BuyerEmail = invoice.BuyerEmail,
                    CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                    FileName = name,
                    FullNameCreator = userFullName,
                    Id = Guid.NewGuid(),
                    InvoiceHeaderId = invoice.Id,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = invoice.InvoiceStatus,
                    IsGeneratedContentMail = 0,
                    IsSynced = 2,
                    PhysicalFileName = fileName,
                    SellerTaxCode = invoice.SellerTaxCode,
                    SerialNo = invoice.SerialNo,
                    TemplateNo = invoice.TemplateNo,
                    TenantGroup = tenantGroup,
                    TenantId = invoice.TenantId,
                    UserId = idUser,
                    UserNameCreator = userName,
                    Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml))
                });

                // insert log
                var invoice01LogEntity = new MongoInvoice01LogEntity
                {
                    InvoiceHeaderId = invoice.Id,
                    TenantId = tenantId,
                    UserId = idUser,
                    UserName = userName,
                    Action = (short)ActionLogInvoice.Sign.GetHashCode(),
                    CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                    InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                    Id = Guid.NewGuid(),
                    Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                };

                await _mongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                try
                {
                    //lưu vào bảng xml trước
                    var invoiceXml = new Invoice01XmlEntity
                    {
                        ContentType = ContentType.Xml,
                        FileName = name,
                        PhysicalFileName = fileName,
                        TenantId = tenantId,
                        InvoiceHeaderId = invoice.Id,
                        CreationTime = createdAt
                    };

                    var repoInvoiceXml = _appFactory.Repository<Invoice01XmlEntity, long>();
                    await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                    var query = $@"UPDATE  ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.DaKy.GetHashCode()}, 
                                        ""SellerSignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""SellerFullNameSigned"" = '{userFullName}', 
                                        ""SellerSignedId"" = '{OracleExtension.ConvertGuidToRaw(idUser)}'
                                WHERE   ""Id""= {invoice.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                }
                catch (Exception ex)
                {
                    throw new UserFriendlyException(ex.Message);
                }
            }
        }


        private async Task UpdateEsAsync(List<long> ids, short signStatus)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var group = _appFactory.CurrentTenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            try
            {
                foreach (var id in ids)
                {
                    var res = await client.UpdateAsync<object>(id, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}", signStatus)
                                )
                            ).Refresh(Refresh.True)
                        );

                    if (res.IsValid)
                    {
                        await _mongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(id, (short)SyncElasticSearchStatus.Synced);
                    }
                    else
                    {
                        await _mongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(id, (short)SyncElasticSearchStatus.PendingSyncSign);
                    }

                    Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        /// <summary>
        /// Tinh toan lai chi tiet thue suat
        /// </summary>
        /// <param name="invoice"></param>
        private async Task<MongoInvoice01Entity> ReCalculateTaxBreakdown(MongoInvoice01Entity invoice)
        {
            // Check Chi tiết thuế suất
            if (!invoice.InvoiceTaxBreakdowns.IsNullOrEmpty())
            {
                DiscountType discountType = ReCaculateInvoiceUtil.GetAutoDiscountType(invoice);
                var taxBreakDownEntities = await _invoice01TaxBreakdownRepository.getTaxBreakDownById(invoice.Id, invoice.TenantId);
                // Case: Loại chiết khấu = Chiết khấu tổng
                if (discountType == DiscountType.TongChietKhau)
                {
                    foreach (var taxBreakdown in invoice.InvoiceTaxBreakdowns)
                    {
                        if (taxBreakdown.Amount == 0)
                        {
                            // Tính toán lại
                            taxBreakdown.Amount = invoice.InvoiceDetails.Where(detail => detail.VatPercent == taxBreakdown.VatPercent).Sum(detail => detail.Amount) - invoice.TotalDiscountAmountBeforeTax;
                            var taxBreakDownEntity = taxBreakDownEntities.Where(taxBreakdown => taxBreakdown.VatPercent == taxBreakdown.VatPercent).FirstOrDefault();
                            taxBreakDownEntity.Amount = taxBreakdown.Amount;
                        }
                    }
                }
                else
                {
                    // Case: Loại chiết khấu = Chiết khấu hàng hóa
                    // Case: Loại chiết khấu = Không chiết khấu
                    foreach (var taxBreakdown in invoice.InvoiceTaxBreakdowns)
                    {
                        if (taxBreakdown.Amount == 0)
                        {
                            // Tính toán lại
                            // Thanh tein khac CKTM - Thanh tien CKTM
                            taxBreakdown.Amount = invoice.InvoiceDetails.Where(detail => detail.VatPercent == taxBreakdown.VatPercent && detail.ProductType != ProductType.ChietKhauThuongMai.GetHashCode()).Sum(detail => detail.Quantity * detail.UnitPrice - detail.DiscountAmountBeforeTax)
                                - invoice.InvoiceDetails.Where(detail => detail.VatPercent == taxBreakdown.VatPercent && detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Sum(detail => detail.Quantity * detail.UnitPrice - detail.DiscountAmountBeforeTax);
                            var taxBreakDownEntity = taxBreakDownEntities.Where(t => t.VatPercent == taxBreakdown.VatPercent).FirstOrDefault();
                            taxBreakDownEntity.Amount = taxBreakdown.Amount;
                        }
                    }
                }
                await _invoice01TaxBreakdownRepository.UpdateManyAsync(taxBreakDownEntities);

                if(invoice.DiscountType != discountType.GetHashCode())
                {
                    invoice.DiscountType = (short) discountType.GetHashCode();

                    // Cap nhat Loai CK Oracle
                    await _invoice01Dapper.UpdateDiscountTypeById(invoice.Id, invoice.DiscountType);
                }
            }
            return invoice;
        }
    }
}
