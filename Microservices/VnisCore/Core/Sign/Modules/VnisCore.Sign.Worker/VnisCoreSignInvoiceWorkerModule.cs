using Core;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Data;
using Core.DependencyInjection;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Threading;
using Core.VaultSharp;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Reflection;
using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Invoice01.Infrastructure;
using VnisCore.Sign.Application.Repositories.Invoice01;
using VnisCore.Sign.Application.Repositories.Invoice02;
using VnisCore.Sign.Worker.BackgroundWorkers;
using VnisCore.Sign.Worker.Business;
using VnisCore.Sign.Worker.Interface;

namespace VnisCore.Sign.Worker
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),
        typeof(VnisCoreMongoDbModule),


        typeof(AbpAutofacModule),
        typeof(AbpCachingModule),

        typeof(AbpEntityFrameworkCoreOracleModule),

        typeof(VnisCoreAuthDatabaseOracleEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),

        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),

        typeof(AbpCachingStackExchangeRedisModule),

        typeof(SharedModule),
        typeof(SharedDtoModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
     )]
    public class VnisCoreSignInvoiceWorkerModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCoreSignInvoiceWorkerModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreSignInvoiceWorkerAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreSignInvoiceWorkerModule).GetTypeInfo().Assembly);

            context.Services.AddScoped<ISignInvoiceBusiness, SignInvoice01Business>();
            context.Services.AddScoped<ISignInvoice02Business, SignInvoice02Business>();
            context.Services.AddScoped<ISignService, SignService>();

            // Repository
            context.Services.AddScoped<IInvoice01HeaderFieldRepository, Invoice01HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice02HeaderFieldRepository, Invoice02HeaderFieldRepository>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            Configure<AbpDistributedCacheOptions>(options =>
            {
                options.KeyPrefix = "einvoice:";
            });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<SignInvoiceBackgroundWorker>();
            context.AddBackgroundWorker<SignInvoice02BackgroundWorker>();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();

                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreOracleDbContext>();
            });
        }
    }
}
