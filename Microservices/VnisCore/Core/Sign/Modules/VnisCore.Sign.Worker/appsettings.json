{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "MSTTCGP": "0101352495",
  "Service": {
    "Name": "VnisCore.Sign.Worker",
    "Title": "VnisCore.Sign.Worker",
    "BaseUrl": "sign-worker",
    "AuthApiName": "VnisCore.Sign.Worker"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14",
    "Database": 14
  },
  "AuthServer": {
    "Authority": "https://massinvoice-auth.vnpaytest.vn",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_Swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "Elasticsearch": {
    "IsEnable": 1,
    "MaxResultWindow": 20000,
    "TenantGroupsPrivate": "1.1,1.2,1.3",
    "Urls": "https://10.22.18.234:9200",
    "Username": "elastic",
    "Password": "GyHasF+gFd4iysODzRjI",
    "TrustAllCerts": "true"
  },
  "Settings": {
    "TenantGroupsPrivate": "1.1,1.2,1.3",
    //"TenantGroup": "0,1.3",//"0,1.1,1.2,1.3,1.4,1.5,1.6,2.1,2.2,2.3,2.4,2.5,2.6,3.1,3.2,3.3,3.4,3.5,3.6,4.1,4.2,4.3,4.4,4.5,4.6,5.1,5.2,5.3,5.4,5.5,5.6",
    "TenantGroup": "0,1.1,1.2,1.3,1.4,1.5,1.6,2.1,2.2,2.3,2.4,2.5,2.6,3.1,3.2,3.3,3.4,3.5,3.6,4.1,4.2,4.3,4.4,4.5,4.6,5.1,5.2,5.3,5.4,5.5,5.6",
    "InvoicesGroup": "0,1,2,3,4,5,6,7,8,9,10",
    "TimePeriod": 1,
    "InvoiceTakeToSign": 100,
    "IsEnableGenerateInvoice01NumberWorker": 1,
    "IsEnableMongoDbLocalTime": 1,
    "ErrorRetry": 5
  },
  "Logging": {
    "RootFolder": {
      "Folder": "/var/logs/invoice/mass"
    }
  }
}