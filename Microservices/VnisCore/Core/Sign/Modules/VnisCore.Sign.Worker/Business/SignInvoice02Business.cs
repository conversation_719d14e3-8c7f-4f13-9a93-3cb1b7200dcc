using Core;
using Core.Dto.Shared.Invoices.Invoice02;
using Core.MultiTenancy;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.TenantManagement;

using Dapper;

using Elasticsearch.Net;

using Microsoft.Extensions.Configuration;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Sign.Application.Repositories.Invoice02;
using VnisCore.Sign.Worker.Interface;
using VnisCore.Sign.Worker.Models;

namespace VnisCore.Sign.Worker.Business
{
    public class SignInvoice02Business : ISignInvoice02Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoXmlInvoice02SignedRepository _mongoXmlInvoice02SignedRepository;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IVnisCoreMongoInvoice02LogRepository _mongoInvoice02LogRepository;
        private readonly IConfiguration _configuration;
        private readonly ISignService _signService;
        private readonly ISettingService _settingService;
        private readonly string _uriSign = "api/batch-signer/xmlnd123/invhdcmneurl";
        private readonly ElasticSearch _elasticSearch;
        private readonly IInvoice02HeaderFieldRepository _invoice02HeaderFieldRepository;
        private readonly IRedisCacheService _redisCacheService;

        public SignInvoice02Business(
            IVnisCoreMongoXmlInvoice02SignedRepository mongoXmlInvoice02SignedRepository,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IVnisCoreMongoInvoice02LogRepository mongoInvoice02LogRepository,
            IAppFactory appFactory,
            ISignService signService,
            ISettingService settingService,
            IConfiguration configuration,
            ElasticSearch elasticSearch,
            IInvoice02HeaderFieldRepository invoice02HeaderFieldRepository,
            IRedisCacheService redisCacheService)
        {
            _mongoXmlInvoice02SignedRepository = mongoXmlInvoice02SignedRepository;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _mongoInvoice02LogRepository = mongoInvoice02LogRepository;
            _configuration = configuration;
            _appFactory = appFactory;
            _signService = signService;
            _settingService = settingService;
            _elasticSearch = elasticSearch;
            _invoice02HeaderFieldRepository = invoice02HeaderFieldRepository;
            _redisCacheService = redisCacheService;
        }

        public async Task SignInvoiceAsync()
        {
            //lấy hóa đơn ra để ký
            //lấy theo group được cấu hình
            // nếu không có cấu hình tenantGroup thì mặc đinh lấy theo group 0
            var tenantGroup = _configuration["Settings:TenantGroup"];

            // Số lần ký lại HD
            if (!int.TryParse(_configuration["Settings:ErrorRetry"], out int errorRetry))
                errorRetry = 5;

            var invoiceTakeToSign = 100;
            if (int.TryParse(_configuration["Settings:InvoiceTakeToSign"], out int take))
                invoiceTakeToSign = take;

            var groups = !string.IsNullOrEmpty(tenantGroup) ? tenantGroup.Split(",").Select(decimal.Parse).ToList() : new List<decimal> { 0 };

            var configInvoiceGroup = _configuration["Settings:InvoicesGroup"];

            var invoiceGroups = !string.IsNullOrEmpty(configInvoiceGroup)
                ? configInvoiceGroup.Split(",").Select(int.Parse).ToList()
                : new List<int> { 0 };

            //lấy danh sách các tenant
            var tenants = await GetTenantAsync(groups);
            var tenantIds = tenants.Select(x => x.Id).ToList();

            //lấy cấu hình ký tự động
            var settingAutoSigns = await GetSettingAutoSignAsync(tenantIds);
            if (!settingAutoSigns.Any())
                return;

            tenantIds = tenantIds.Intersect(settingAutoSigns.Select(x => x.TenantId).ToList()).ToList();

            short isSyncToCore = 1;
            var data = await _mongoInvoice02Repository.GetListUnSignedAsync(tenantIds, groups, invoiceGroups, isSyncToCore, invoiceTakeToSign);

            if (!data.Any())
                return;

            //group theo batch id và mẫu để ký
            var invoiceGroupByTemplates = data.GroupBy(x => new { x.InvoiceTemplateId });
            tenantIds = data.Select(x => x.TenantId).Distinct().ToList();

            // lấy thông tin chứng thư số trên server
            var certificates = await _signService.GetAllCertificateAsync(tenantIds);
            if (!certificates.Any())
                throw new UserFriendlyException("Không có chứng thư số nào");

            //lấy quyền sử dụng cts
            var atts = await _signService.GetAccountTokenTemplatesAsync(tenantIds);

            //Lấy setting
            var settings = await _signService.GetSettingsAsync(tenantIds);

            //Ký 
            if (!settings.Any())
                throw new UserFriendlyException("Không có setting ký nào");

            var msttcgp = _configuration.GetSection("MSTTCGP").Value;
            int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

            //TODO: group thêm theo userId
            foreach (var invoiceGroupByTemplate in invoiceGroupByTemplates)
            {
                try
                {
                    //check cấu hình ký signserver của tenant này
                    var tenantId = invoiceGroupByTemplate.FirstOrDefault().TenantId;
                    var group = (await GetTenantByIdAsync(tenantId)).Group;

                    var setting = settings.FirstOrDefault(x => x.TenantId == tenantId);
                    if (setting == null || string.IsNullOrEmpty(setting.Host) || setting.Host == "#")
                    {
                        Log.Error($"Không có cấu hình ký signserver hoặc cấu hình không đúng để ký mẫu Id = {invoiceGroupByTemplate.Key}");
                        continue;
                    }

                    //lấy cts dc ký mẫu này
                    var certUsingTemplates = atts.Where(x => x.TemplateId.HasValue && x.TemplateId == invoiceGroupByTemplate.Key.InvoiceTemplateId && x.UsbTokenId.HasValue).ToList();
                    if (!certUsingTemplates.Any())
                    {
                        Log.Error($"Không có chứng thư số nào ký mẫu Id = {invoiceGroupByTemplate.Key}");
                        continue;
                    }

                    // Lấy cấu hình thể TPhi
                    var settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                    var headerFields = await _invoice02HeaderFieldRepository.QueryByTenantCodeRawAsync(tenantId);

                    Log.Information($"{setting.TenantId}: {setting.Host}, {setting.UserName}, {setting.Password}, {setting.IsSignBackDate}");
                    var token = await _signService.GetTokenAsync(setting);

                    try
                    {
                        var idUsbTokens = certUsingTemplates.Select(x => x.UsbTokenId.Value).ToList();
                        var firstInvoice = invoiceGroupByTemplate.FirstOrDefault();

                        //lấy cts được dùng bởi tài khoản này
                        //TODO: test nên để thế này, dùng thật bỏ comment dòng này
                        idUsbTokens = atts.Where(x => x.UserId == firstInvoice.CreatorId && x.UsbTokenId.HasValue && idUsbTokens.Contains(x.UsbTokenId.Value)).Select(x => x.UsbTokenId.Value).ToList();
                        if (!idUsbTokens.Any())
                        {
                            Log.Error($"Tài khoản {invoiceGroupByTemplate.FirstOrDefault().UserNameCreator} không có quyền ký hóa đơn {firstInvoice.SellerTaxCode}, {firstInvoice.TemplateNo}{firstInvoice.SerialNo}-{firstInvoice.InvoiceNo}");
                            continue;
                        }

                        var certificate = certificates.FirstOrDefault(x => idUsbTokens.Contains(x.Id));
                        if (certificate == null)
                        {
                            Log.Error($"Tài khoản {firstInvoice.UserNameCreator} không có quyền ký hóa đơn {firstInvoice.SellerTaxCode}, {firstInvoice.TemplateNo}{firstInvoice.SerialNo}-{firstInvoice.InvoiceNo}");
                            continue;
                        }

                        await _signService.CreateBatch123Async(setting.Host, token, certificate, setting.IsSignBackDate);

                        foreach (var invoice in invoiceGroupByTemplate.ToList())
                        {
                            try
                            {

                                InvoiceReferenceDto invoice02Reference = null;
                                if (invoice.InvoiceReference != null)
                                {
                                    invoice02Reference = _appFactory.ObjectMapper.Map<Invoice02ReferenceDto, InvoiceReferenceDto>(invoice.InvoiceReference);
                                }
                                else if (invoice.InvoiceReferenceOld != null)
                                {
                                    invoice02Reference = _appFactory.ObjectMapper.Map<Invoice02ReferenceOldDto, InvoiceReferenceDto>(invoice.InvoiceReferenceOld);
                                }

                                var model = Invoice02Xml123Model.Parse(invoice, invoice02Reference, setting.IsSignBackDate, msttcgp, isEnableMongoDbLocalTime, settingConfigTPhi.Value, headerFields);
                                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}: Start sign from signserver");
                                Log.Error($"HD 02 chuan bi ky: {invoice.Id}");
                                var xml = await _signService.Sign123Async(setting.Host, token, _uriSign, _signService.ObjToXml(model), invoice.Id.ToString());
                                Log.Error($"HD 02 ky thanh cong: {invoice.Id}");
                                Log.Debug($"{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}: End sign from signserver : {xml}");

                                var invoiceXml = _appFactory.ObjectMapper.Map<MongoInvoice02Entity, MongoXmlInvoice02SignedEntity>(invoice);

                                if (xml != null)
                                {
                                    //Bỏ ký tự đặc biệt
                                    //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
                                    if (xml[0] != '<')
                                    {
                                       xml = xml.Substring(1, xml.Length - 1);
                                    }

                                    invoiceXml.CreationTime = DateTime.Now;
                                    invoiceXml.Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml));
                                    invoiceXml.FileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}{invoice.SerialNo}-{invoice.InvoiceNo}.xml";
                                    invoiceXml.PhysicalFileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}{invoice.SerialNo}-{invoice.InvoiceNo}-{DateTime.Now.Ticks}.xml";

                                    invoice.SignStatus = (short)SignStatus.DaKy.GetHashCode();
                                    invoice.SellerSignedId = invoice.CreatorId;
                                    invoice.SellerFullNameSigned = invoice.FullNameCreator;
                                    invoice.SellerSignedTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;

                                    var signDate = setting.IsSignBackDate ? invoice.InvoiceDate : DateTime.Now;

                                    invoice.SellerSignedTime = isEnableMongoDbLocalTime > 0 ? signDate.AddHours(7) : signDate;
                                }
                                else
                                {
                                    invoice.SignStatus = (short)SignStatus.KyLoi.GetHashCode();
                                }

                                invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.Success.GetHashCode();

                                //update về core
                                await UpdateToCoreAsync(invoice, setting.IsSignBackDate);

                                //lưu vào bảng xml mongo
                                if (xml != null)
                                {
                                    await _mongoXmlInvoice02SignedRepository.InsertAsync(invoiceXml);

                                    // insert log
                                    var invoice02LogEntity = new MongoInvoice02LogEntity
                                    {
                                        InvoiceHeaderId = invoice.Id,
                                        TenantId = tenantId,
                                        UserId = invoice.CreatorId.Value,
                                        UserName = invoice.UserNameCreator,
                                        Action = (short)ActionLogInvoice.Sign.GetHashCode(),
                                        CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                                        InvoiceType = (short)VnisType._02GTTT.GetHashCode(),
                                        Id = Guid.NewGuid(),
                                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                                    };

                                    await _mongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                                }
                            }
                            catch (Exception ex)
                            {
                                // Lưu số lần ký lỗi hóa đơn
                                var signErrorkey = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, invoice.SellerTaxCode, CacheKeyPath.SignAuto, CacheKeyPath.Invoice02, "KyLoi_" + DateTime.Now.ToString("ddMMyyyy"), invoice.Id.ToString());

                                TimeSpan timeSpan = new TimeSpan(24, 0, 0);
                                var value = _redisCacheService.Increment(signErrorkey, timeSpan);

                                // Ký tối đa 5 lần
                                if (value < errorRetry)
                                {
                                    invoice.SignStatus = (short)SignStatus.ChoKy.GetHashCode();
                                }
                                else
                                {
                                    invoice.SignStatus = (short)SignStatus.KyLoi.GetHashCode();
                                }

                                invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();

                                Log.Error($"Hóa đơn {CacheKeyPath.Invoice02} Id = {invoice.Id} ký lỗi lần thử {value}");
                                Log.Error(ex, ex.Message);
                            }
                            finally
                            {
                                //update bangr header
                                //invoice.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
                                await _mongoInvoice02Repository.UpdateAsync(invoice, true);
                                await UpdateEsAsync(new List<long> { invoice.Id }, invoice.SignStatus, group);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                    finally
                    {
                        await _signService.RemoveBatch123Async(setting.Host, token);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }
            }
        }

        private async Task<List<Tenant>> GetTenantAsync(List<decimal> groups)
        {
            try
            {
                var query = $@"
                                                    SELECT 
                                                        ""Id"",
                                                        ""Group""
                                                    FROM ""VnisTenants"" 
                                                    WHERE ""Group"" IN ({string.Join(", ", groups)})
                                                    AND ""IsDeleted"" = 0";
                var settings = (await _appFactory.AuthDatabase.Connection.QueryAsync<Tenant>(query)).ToList();
                return settings;
            }
            catch (Exception e)
            {
                Log.Error(e, e.Message);
                Log.Error(e, e.StackTrace);
                throw new UserFriendlyException(e.Message);
            }
        }

        private async Task<Tenant> GetTenantByIdAsync(Guid tenantId)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            try
            {
                var query = $@"
                                                    SELECT 
                                                        ""Id"",
                                                        ""Group""
                                                    FROM ""VnisTenants"" 
                                                    WHERE ""Id"" = '{rawTenantId}' AND ""IsDeleted"" = 0 FETCH FIRST 1 ROWS ONLY ";
                var settings = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<Tenant>(query);
                return settings;
            }
            catch (Exception e)
            {
                Log.Error(e, e.Message);
                Log.Error(e, e.StackTrace);
                throw new UserFriendlyException(e.Message);
            }
        }

        private async Task UpdateEsAsync(List<long> ids, short signStatus, decimal group)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            try
            {
                foreach (var id in ids)
                {
                    var res = await client.UpdateAsync<object>(id, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}", signStatus)
                                )
                            ).Refresh(Refresh.True)
                        );

                    if (res.IsValid)
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.Synced);
                    }
                    else
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.PendingSyncSign);
                    }

                    Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        private async Task UpdateToCoreAsync(MongoInvoice02Entity invoice, bool isSignBackDate)
        {
            try
            {
                var sellerSignedTime = isSignBackDate ? invoice.InvoiceDate : DateTime.Now;

                var sql = $@"Update ""Invoice02Header"" SET ""SignStatus"" = {invoice.SignStatus}
                            {(!invoice.SellerSignedTime.HasValue ? "" : @$",""SellerSignedTime"" = '{invoice.SellerSignedTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ")}
                            {(!invoice.SellerSignedId.HasValue ? "" : @$",""SellerSignedId"" = '{OracleExtension.ConvertGuidToRaw(invoice.SellerSignedId.Value)}' ")}
                            {(!string.IsNullOrEmpty(invoice.SellerFullNameSigned) ? "" : @$",""SellerFullNameSigned"" = '{invoice.SellerFullNameSigned}' ")}
                             WHERE ""Id"" = { invoice.Id} 
                ";
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
            }
            catch (Exception e)
            {
                Log.Error(e, e.Message);
                Log.Error(e, e.StackTrace);
                throw new UserFriendlyException(e.Message);
            }
        }

        private async Task<List<TenantSetting>> GetSettingAutoSignAsync(List<Guid> tenantIds)
        {
            try
            {
                var result = new List<TenantSetting>();

                foreach (var tenantId in tenantIds)
                {
                    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());

                    if (setting?.Value == "1")
                    {
                        setting.TenantId = tenantId;
                        result.Add(setting);
                    }
                }

                return result;
            }
            catch (Exception e)
            {
                Log.Error(e, e.Message);
                Log.Error(e, e.StackTrace);
                throw new UserFriendlyException(e.Message);
            }
        }
    }
}
