using Core;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Data;
using Core.DependencyInjection;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.EventBus.RabbitMq;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Threading;
using Core.VaultSharp;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;
using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Invoice01.Infrastructure;
using VnisCore.Sign.Application.Repositories.Invoice01;
using VnisCore.Sign.Application.Repositories.Invoice02;
using VnisCore.Sign.WorkerSchedule.BackgroundWorkers;
using VnisCore.Sign.WorkerSchedule.Business;
using VnisCore.Sign.WorkerSchedule.Business.Invoice03;
using VnisCore.Sign.WorkerSchedule.Interface;
using VnisCore.Sign.WorkerSchedule.Models;
using VnisCore.Sign.WorkerSchedule.Services;

namespace VnisCore.Sign.WorkerSchedule
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),
        typeof(VnisCoreMongoDbModule),


        typeof(AbpAutofacModule),
        typeof(AbpCachingModule),

        typeof(AbpEntityFrameworkCoreOracleModule),

        typeof(VnisCoreAuthDatabaseOracleEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),

        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),

        typeof(AbpCachingStackExchangeRedisModule),

        typeof(AbpEventBusRabbitMqModule),
        typeof(SharedModule),
        typeof(SharedDtoModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
     )]
    public class VnisCoreSignInvoiceWorkerModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCoreSignInvoiceWorkerModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreSignInvoiceWorkerAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreSignInvoiceWorkerModule).GetTypeInfo().Assembly);

            var configuration = context.Services.GetConfiguration();
            context.Services.Configure<TenantSignOption>(configuration.GetSection("TenantSign"));

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            context.Services.AddScoped<ISignInvoiceBusiness, SignInvoice01Business>();
            context.Services.AddScoped<ISignInvoiceBusiness, SignInvoice02Business>();
            context.Services.AddScoped<ISignInvoiceBusiness, SignInvoice03Business>();
            context.Services.AddScoped<ISignService, SignService>();
            context.Services.AddScoped<ISignServerHttpClient, SignServerHttpClient>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();
            context.Services.AddScoped<ISign123Invoice03Business, Sign123Invoice03Business>();

            // Repository
            context.Services.AddScoped<IInvoice01HeaderFieldRepository, Invoice01HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice02HeaderFieldRepository, Invoice02HeaderFieldRepository>();

            //HttpClients
            context.Services.AddHttpClient("signserver", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:Sign:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:Sign:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<SignInvoice01BackgroundWorker>();
            context.AddBackgroundWorker<SignInvoice02BackgroundWorker>();
            context.AddBackgroundWorker<SignInvoice03BackgroundWorker>();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();

                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreOracleDbContext>();
            });
        }


    }
}
