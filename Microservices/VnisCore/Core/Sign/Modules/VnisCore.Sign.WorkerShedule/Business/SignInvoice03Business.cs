using Core.EventBus.Distributed;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Sign.WorkerSchedule.Business.Invoice03;
using VnisCore.Sign.WorkerSchedule.Interface;
using VnisCore.Sign.WorkerSchedule.Models;

namespace VnisCore.Sign.WorkerSchedule.Business
{
    public class SignInvoice03Business : ISignInvoiceBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;
        private readonly ISign123Invoice03Business _sign123Invoice03Business;

        public SignInvoice03Business(
            IConfiguration configuration,
            IAppFactory appFactory,
            ISign123Invoice03Business sign123Invoice03Business)
        {
            _appFactory = appFactory;
            _configuration = configuration;
            _sign123Invoice03Business = sign123Invoice03Business;
        }


        public async Task SignInvoiceAsync(string sellerTaxCode, List<short> invoiceSources)
        {
            try
            {
                //lấy hóa đơn ra để ký
                var invoiceTakeToSign = 100;
                if (int.TryParse(_configuration["Settings:InvoiceTakeToSign"], out int take))
                    invoiceTakeToSign = take;

                //laay hoa don chua ky
                var query = @$"SELECT ""Id"", ""TenantId"", ""CreatorId"", ""FullNameCreator"", ""TemplateNo"", ""SerialNo"", ""InvoiceNo"", ""UserNameCreator""
                          FROM ""Invoice03Header"" 
                          WHERE ""IsDeleted"" = 0 AND ""SellerTaxCode"" = '{sellerTaxCode}' 
                                AND (""SignStatus"" = {SignStatus.ChoKy.GetHashCode()})
                                AND ""InvoiceStatus"" != {InvoiceStatus.XoaHuy.GetHashCode()}
                                AND ""ApproveStatus"" != {ApproveStatus.ChoDuyet.GetHashCode()}
                                {((invoiceSources != null && invoiceSources.Any()) ? $@" AND ""Source"" in ({string.Join(",", invoiceSources)}) " : "")}
                                AND ROWNUM  <= {invoiceTakeToSign}  
                                ORDER BY ""Id""";

                var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice03HeaderEntity>(query.ToString());
                if (!invoices.Any())
                {
                    Log.Information($"{CacheKeyPath.Invoice03}: Không còn hóa đơn nào để ký của {sellerTaxCode}");
                    return;
                }

                var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();

                foreach (var invoice in invoices)
                {
                    await _sign123Invoice03Business.SignInvoices123(invoice.TenantId, invoice.CreatorId.Value, invoice.FullNameCreator, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo);
                    
                    //Insert Log
                    await distributedEventBus.PublishAsync(new Invoice03LogEventSendData
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = invoice.TenantId,
                        UserId = invoice.CreatorId.Value,
                        UserName = invoice.UserNameCreator,
                        InvoiceType = EnumExtension.ToEnum<VnisType>(invoice.TemplateNo).GetHashCode(),
                        Action = ActionLogInvoice.Sign.GetHashCode(),
                        Partition = long.Parse(invoice.InvoiceDate.ToString("yyyyMMddHHmm")),
                        ActionTime = DateTime.Now
                    });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }


        public async Task OldSignInvoiceAsync(string sellerTaxCode, string token, List<short> invoiceSources)
        {
            try
            {
                //lấy hóa đơn ra để ký
                var invoiceTakeToSign = 100;
                if (int.TryParse(_configuration["Settings:InvoiceTakeToSign"], out int take))
                    invoiceTakeToSign = take;

                //laay hoa don chua ky
                var query = @$"SELECT ""Id"" 
                          FROM ""Invoice03Header"" 
                          WHERE ""IsDeleted"" = 0 AND ""SellerTaxCode"" = '{sellerTaxCode}' 
                                AND (""SignStatus"" = {SignStatus.ChoKy.GetHashCode()} OR ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()})
                                AND ""InvoiceStatus"" != {InvoiceStatus.XoaHuy.GetHashCode()}
                                {((invoiceSources != null && invoiceSources.Any()) ? $@" AND ""Source"" in ({string.Join(",", invoiceSources)}) " : "")}
                                AND ROWNUM  <= {invoiceTakeToSign}  
                                ORDER BY ""Id""";

                var idInvoiceHeaders = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(query.ToString());
                if (!idInvoiceHeaders.Any())
                {
                    Log.Information($"Không còn hóa đơn nào để ký của {sellerTaxCode}");
                    return;
                }

                var service = _appFactory.GetServiceDependency<ISignServerHttpClient>();
                await service.SignInvoice03Async(token, idInvoiceHeaders.ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        public Task SignInvoiceAsync(string[] taxCodes, List<short> invoiceSources)
        {
            throw new System.NotImplementedException();
        }

        /// <summary>
        /// Task 949
        /// Sửa luồng ký hóa đơn của signschedulewoker tăng thread ký lên
        /// </summary>
        /// <param name="taxCodes"></param>
        /// <param name="invoiceSources"></param>
        /// <returns></returns>
        public Task SignInvoiceV2Async(string[] taxCodes, List<short> invoiceSources)
        {
            throw new NotImplementedException();
        }
    }
}