using Core.Dto.Shared.Invoices.Invoice02;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Models;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.Invoices.Base;
using Core.Tvan.Models.Xmls.Invoices.Invoice02;
using Core.Tvan.Services;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;

namespace VnisCore.Sign.WorkerSchedule.Models
{
    public class Invoice02Xml123Model
    {
        public static HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>> Parse(
            Invoice02HeaderEntity invoiceHeader,
            List<Invoice02DetailEntity> invoiceDetails,
            Invoice02ReferenceDto invoiceReference,
            Invoice02DocumentInfoEntity invoiceFile,
            bool signBackDate,
            string taxCode,
            string tPhiConfig,
            List<Invoice02HeaderFieldEntity> headerFields)
        {
            var paymentMethods = Enum.GetValues(typeof(PaymentMethod)).Cast<PaymentMethod>();
            var extraHeaders = invoiceHeader.ExtraProperties == null || !invoiceHeader.ExtraProperties.ContainsKey("invoiceHeaderExtras") ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoiceHeader.ExtraProperties["invoiceHeaderExtras"].ToString());

            #region Lay the TPhi tu cau hinh
            List<LPhi> lPhiArray = new List<LPhi>();
            if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
            {
                // Khong xu ly
            }
            else
            {
                if (!extraHeaders.IsNullOrEmpty())
                {
                    var tPhiArray = tPhiConfig.Split(';');
                    foreach (var tPhi in tPhiArray)
                    {
                        if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                        {
                            var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();
                            var headerField = headerFields.Where(x => x.FieldName == tPhi).FirstOrDefault();
                            var lePhi = new LPhi()
                            {
                                TLPhi = headerField.DisplayName,
                                TPhi = decimal.Parse(extraHeader.FieldValue.Replace('.', ','))
                            };

                            // Them header extra vao the LPhi
                            lPhiArray.Add(lePhi);

                            // Xoa Header extra duoc dua vao the LPhi
                            extraHeaders.Remove(extraHeader);
                        }
                    }
                }
            }
            #endregion

            if (!string.IsNullOrEmpty(invoiceHeader.TransactionId))
            {
                extraHeaders.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = "Mã bảo mật",
                    FieldValue = invoiceHeader.TransactionId
                });
            }

            if (!string.IsNullOrEmpty(invoiceHeader.Note))
            {
                extraHeaders.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = "Ghi chú",
                    FieldValue = invoiceHeader.Note
                });
            }

            // TH là hóa đơn điều chỉnh định danh: 
            // hóa đơn dcdd đang lưu vẫn có detail, tiền....(clone từ hóa đơn gốc sang)
            // thực tế nghiệp vụ thì như thế là sai vì nếu thế khi báo cáo lên thuế thì 2 hóa đơn bị điều chỉnh định danh và hóa đơn đcdd đều có tiền => cộng lại bị x2 lên
            // => chỉ có 1 hóa đơn có giá trị tiền
            // => xml với hóa đơn dcdd thì không cho tiền với thông tin detail vào xml nữa
            var email = invoiceHeader.BuyerEmail.IsNullOrEmpty() ? null : invoiceHeader.BuyerEmail.Split(";").FirstOrDefault();
            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
            {
                var model = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>
                {
                    DLHDon = new DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>
                    {
                        //Data = "data",
                        Data = $"Id-{invoiceHeader.Id}",
                        TTChung = new TTChungDLHDonInvoice02Model
                        {
                            PBan = TvanInvoiceStaticData.PBan,
                            THDon = "HÓA ĐƠN BÁN HÀNG",
                            KHMSHDon = invoiceHeader.TemplateNo,
                            KHHDon = invoiceHeader.SerialNo,
                            SHDon = invoiceHeader.Number.ToString(),
                            MHSo = null,
                            NLap = invoiceHeader.InvoiceDate.ToString("yyyy-MM-dd"),
                            HDDCKPTQuan = 0,
                            SBKe = null, //TODO: thêm số bảng kê
                            NBKe = null,  //TODO: thêm số bảng kê
                            DVTTe = invoiceHeader.ToCurrency,
                            TGia = invoiceHeader.ExchangeRate,
                            HTTToan = invoiceHeader.PaymentMethod,
                            MSTDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            TDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            DCDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            MSTTCGP = taxCode,
                            TTHDLQuan = invoiceReference == null ? null : new TTHDLQuanDLHDon
                            {
                                GChu = invoiceReference.Note,
                                KHMSHDCLQuan = invoiceReference.TemplateNoReference.ToString(),
                                KHHDCLQuan = invoiceReference.SerialNoReference,
                                LHDCLQuan = invoiceHeader.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : invoiceHeader.ReferenceInvoiceType,
                                SHDCLQuan = invoiceReference.NumberReference.ToString(),
                                NLHDCLQuan = invoiceReference.InvoiceDateReference.ToString("yyyy-MM-dd"),
                                TCHDon = EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus).ToString().Contains("ThayThe") ? 1 : 2
                            }
                        },
                        NDHDon = new NDHDonInvoice02Model
                        {
                            NBan = new NBan
                            {
                                DChi = invoiceHeader.SellerAddressLine,
                                Ten = invoiceHeader.SellerFullName,
                                DCTDTu = email,
                                Fax = invoiceHeader.SellerFaxNumber,
                                MST = invoiceHeader.SellerTaxCode,
                                SDThoai = invoiceHeader.SellerPhoneNumber,
                                STKNHang = invoiceHeader.SellerBankAccount,
                                TNHang = invoiceHeader.SellerBankName,
                                Website = null, //TODO
                            },
                            NMua = new NMua
                            {
                                MKHang = invoiceHeader.BuyerCode,
                                DChi = invoiceHeader.BuyerAddressLine,
                                Ten = invoiceHeader.BuyerFullName,
                                DCTDTu = email,
                                HVTNMHang = invoiceHeader.BuyerLegalName,
                                MST = invoiceHeader.BuyerTaxCode,
                                SDThoai = invoiceHeader.BuyerPhoneNumber,
                                STKNHang = invoiceHeader.BuyerBankAccount,
                                TNHang = invoiceHeader.BuyerBankName,
                            },
                            TTKhac = new TTKhacModel
                            {
                                //Id = extraHeaders.Any() ? $"TTKhac-{invoiceHeader.Id}" : null,
                                TTin = extraHeaders?.Select(x => new TTin
                                {
                                    DLieu = x.FieldValue,
                                    KDLieu = "string",
                                    TTruong = x.FieldName
                                }).ToList()
                            }
                        }
                    },
                    DLQRCode = GetQrCode(invoiceHeader),
                    DSCKS = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>.DSCKSModel
                    {
                        NBan = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                //Id = "NBanSignature",
                                Id = $"NBan-{invoiceHeader.Id}",
                                Object = new ObjectNNTModel
                                {
                                    Id = $"SigningTime-{invoiceHeader.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                        {
                                            new SignaturePropertyModel
                                            {
                                                Target = "signatureProperties",
                                                SigningTime = signBackDate ? invoiceHeader.InvoiceDate.ToString("yyyy-MM-ddTHH:mm:ss") : DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        CCKSKhac = new object { }
                    }
                };

                var details = new List<HHDVu>();
                var detail = new HHDVu
                {
                    STT = 1,
                    DGia = 0,
                    DVTinh = "DVT",
                    MHHDVu = "DV",
                    SLuong = 0,
                    STCKhau = 0,
                    TChat = TChat.Note.GetHashCode(),
                    THHDVu = $"Hóa đơn điều chỉnh nội dung thông tin người mua cho hóa đơn số {invoiceReference.InvoiceNoReference} - {invoiceReference.SerialNoReference} - {invoiceReference.InvoiceDateReference.ToString("dd/MM/yyyy")}",
                    ThTien = 0,
                    TLCKhau = 0,
                    TTKhac = new TTKhacModel { }
                };

                details.Add(detail);

                model.DLHDon.NDHDon.DSHHDVu = new DSHHDVu
                {
                    ListDSHHDVu = details
                };

                return model;
            }
            else
            {
                var model = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>
                {
                    DLHDon = new DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>
                    {
                        //Data = "data",
                        Data = $"Id-{invoiceHeader.Id}",
                        TTChung = new TTChungDLHDonInvoice02Model
                        {
                            PBan = TvanInvoiceStaticData.PBan,
                            THDon = "HÓA ĐƠN BÁN HÀNG",
                            KHMSHDon = invoiceHeader.TemplateNo,
                            KHHDon = invoiceHeader.SerialNo,
                            SHDon = invoiceHeader.Number.ToString(),
                            MHSo = null,
                            NLap = invoiceHeader.InvoiceDate.ToString("yyyy-MM-dd"),
                            HDDCKPTQuan = 0,
                            SBKe = null, //TODO: thêm số bảng kê
                            NBKe = null,  //TODO: thêm số bảng kê
                            DVTTe = invoiceHeader.ToCurrency,
                            TGia = invoiceHeader.ExchangeRate,
                            HTTToan = invoiceHeader.PaymentMethod,
                            MSTDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            TDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            DCDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            MSTTCGP = taxCode,
                            TTHDLQuan = invoiceReference == null ? null : new TTHDLQuanDLHDon
                            {
                                GChu = invoiceReference.Note,
                                KHMSHDCLQuan = invoiceReference.TemplateNoReference.ToString(),
                                KHHDCLQuan = invoiceReference.SerialNoReference,
                                LHDCLQuan = invoiceHeader.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : invoiceHeader.ReferenceInvoiceType,
                                SHDCLQuan = invoiceReference.NumberReference.ToString(),
                                NLHDCLQuan = invoiceReference.InvoiceDateReference.ToString("yyyy-MM-dd"),
                                TCHDon = EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus).ToString().Contains("ThayThe") ? 1 : 2
                            }
                        },
                        NDHDon = new NDHDonInvoice02Model
                        {
                            NBan = new NBan
                            {
                                DChi = invoiceHeader.SellerAddressLine,
                                Ten = invoiceHeader.SellerFullName,
                                DCTDTu = email,
                                Fax = invoiceHeader.SellerFaxNumber,
                                MST = invoiceHeader.SellerTaxCode,
                                SDThoai = invoiceHeader.SellerPhoneNumber,
                                STKNHang = invoiceHeader.SellerBankAccount,
                                TNHang = invoiceHeader.SellerBankName,
                                Website = null, //TODO
                            },
                            NMua = new NMua
                            {
                                MKHang = invoiceHeader.BuyerCode,
                                DChi = invoiceHeader.BuyerAddressLine,
                                Ten = invoiceHeader.BuyerFullName,
                                DCTDTu = email,
                                HVTNMHang = invoiceHeader.BuyerLegalName,
                                MST = invoiceHeader.BuyerTaxCode,
                                SDThoai = invoiceHeader.BuyerPhoneNumber,
                                STKNHang = invoiceHeader.BuyerBankAccount,
                                TNHang = invoiceHeader.BuyerBankName,
                            },
                            TToan = new TToan
                            {
                                TgTTTBChu = invoiceHeader.PaymentAmountWords,
                                TgTTTBSo = invoiceHeader.TotalPaymentAmount,
                                TTCKTMai = invoiceHeader.TotalDiscountAmount,
                                DSLPhi = new DSLPhi()
                                {
                                    LPhi = lPhiArray,
                                }
                            },
                            TTKhac = new TTKhacModel
                            {
                                //Id = extraHeaders.Any() ? $"TTKhac-{invoiceHeader.Id}" : null,
                                TTin = extraHeaders?.Select(x => new TTin
                                {
                                    DLieu = x.FieldValue,
                                    KDLieu = "string",
                                    TTruong = x.FieldName
                                }).ToList()
                            }
                        }
                    },
                    DLQRCode = GetQrCode(invoiceHeader),
                    DSCKS = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>.DSCKSModel
                    {
                        NBan = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                Id = $"NBan-{invoiceHeader.Id}",
                                Object = new ObjectNNTModel
                                {
                                    Id = $"SigningTime-{invoiceHeader.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new SignaturePropertyModel
                                        {
                                            //Id = "NBanSignTimeStamp",
                                            //Target = "#NBanSignature",
                                            Target = "signatureProperties",
                                            SigningTime = signBackDate ? invoiceHeader.InvoiceDate.ToString("yyyy-MM-ddTHH:mm:ss") : DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                    }
                                }
                            }
                        },
                        CCKSKhac = new object { }
                    }
                };

                var details = new List<HHDVu>();
                foreach (var item in invoiceDetails)
                {
                    var extraDetails = item.ExtraProperties == null || !item.ExtraProperties.ContainsKey("invoiceDetailExtras") ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceDetailExtras"].ToString());
                    
                    if (!string.IsNullOrEmpty(item.Note))
                    {
                        extraDetails.Add(new InvoiceHeaderExtraModel
                        {
                            FieldName = "Ghi chú",
                            FieldValue = item.Note
                        });
                    }

                    var detail = new HHDVu
                    {
                        STT = item.Index,
                        DGia = item.UnitPrice,
                        DVTinh = item.UnitName,
                        MHHDVu = item.ProductCode,
                        SLuong = item.Quantity,
                        STCKhau = item.DiscountAmount,
                        TChat = item.ProductType,
                        THHDVu = item.ProductName,
                        ThTien = item.Amount,
                        TLCKhau = item.DiscountPercent,
                        TTKhac = new TTKhacModel
                        {
                            //Id = extraDetails.Any() ? $"IdDetail-{item.Id}" : null,
                            TTin = extraDetails?.Select(x => new TTin
                            {
                                DLieu = x.FieldValue,
                                KDLieu = "string",
                                TTruong = x.FieldName
                            }).ToList()
                        }
                    };

                    details.Add(detail);
                }

                model.DLHDon.NDHDon.DSHHDVu = new DSHHDVu
                {
                    ListDSHHDVu = details
                };

                return model;
            }
        }

        public static HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>> Parse(MongoInvoice02Entity invoiceHeader, InvoiceReferenceDto invoiceReference, bool signBackDate, string msttcgp, string tPhiConfig,
            List<Invoice02HeaderFieldEntity> headerFields)
        {
            var paymentMethods = Enum.GetValues(typeof(PaymentMethod)).Cast<PaymentMethod>();
            var extraHeaders = invoiceHeader.ExtraProperties == null || !invoiceHeader.ExtraProperties.ContainsKey("invoiceHeaderExtras") ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoiceHeader.ExtraProperties["invoiceHeaderExtras"].ToString());

            #region Lay the TPhi tu cau hinh
            List<LPhi> lPhiArray = new List<LPhi>();
            if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
            {
                // Khong xu ly
            }
            else
            {
                if (!extraHeaders.IsNullOrEmpty())
                {
                    var tPhiArray = tPhiConfig.Split(';');
                    foreach (var tPhi in tPhiArray)
                    {
                        if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                        {
                            var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();
                            var headerField = headerFields.Where(x => x.FieldName == tPhi).FirstOrDefault();
                            var lePhi = new LPhi()
                            {
                                TLPhi = headerField.DisplayName,
                                TPhi = decimal.Parse(extraHeader.FieldValue.Replace('.', ','))
                            };

                            // Them header extra vao the LPhi
                            lPhiArray.Add(lePhi);

                            // Xoa Header extra duoc dua vao the LPhi
                            extraHeaders.Remove(extraHeader);
                        }
                    }
                }
            }
            #endregion

            if (!string.IsNullOrEmpty(invoiceHeader.TransactionId))
            {
                extraHeaders.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = "Mã bảo mật",
                    FieldValue = invoiceHeader.TransactionId
                });
            }

            if (!string.IsNullOrEmpty(invoiceHeader.Note))
            {
                extraHeaders.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = "Ghi chú",
                    FieldValue = invoiceHeader.Note
                });
            }

            // TH là hóa đơn điều chỉnh định danh: 
            // hóa đơn dcdd đang lưu vẫn có detail, tiền....(clone từ hóa đơn gốc sang)
            // thực tế nghiệp vụ thì như thế là sai vì nếu thế khi báo cáo lên thuế thì 2 hóa đơn bị điều chỉnh định danh và hóa đơn đcdd đều có tiền => cộng lại bị x2 lên
            // => chỉ có 1 hóa đơn có giá trị tiền
            // => xml với hóa đơn dcdd thì không cho tiền với thông tin detail vào xml nữa
            var email = invoiceHeader.BuyerEmail.IsNullOrEmpty() ? null : invoiceHeader.BuyerEmail.Split(";").FirstOrDefault();
            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
            {
                var model = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>
                {
                    DLHDon = new DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>
                    {
                        //Data = "data",
                        Data = $"Id-{invoiceHeader.Id}",
                        TTChung = new TTChungDLHDonInvoice02Model
                        {
                            PBan = TvanInvoiceStaticData.PBanHD02,
                            THDon = "HÓA ĐƠN BÁN HÀNG",
                            KHMSHDon = invoiceHeader.TemplateNo,
                            KHHDon = invoiceHeader.SerialNo,
                            SHDon = invoiceHeader.Number.ToString(),
                            MHSo = null,
                            NLap = invoiceHeader.InvoiceDate.ToString("yyyy-MM-dd"),
                            HDDCKPTQuan = 0,
                            SBKe = null, //TODO: thêm số bảng kê
                            NBKe = null,  //TODO: thêm số bảng kê
                            DVTTe = invoiceHeader.ToCurrency,
                            TGia = invoiceHeader.ExchangeRate,
                            HTTToan = invoiceHeader.PaymentMethod,
                            MSTDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            TDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            DCDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            MSTTCGP = msttcgp,
                            TTHDLQuan = invoiceReference == null ? null : new TTHDLQuanDLHDon
                            {
                                GChu = invoiceReference.Note,
                                KHMSHDCLQuan = invoiceReference.TemplateNoReference.ToString(),
                                KHHDCLQuan = invoiceReference.SerialNoReference,
                                LHDCLQuan = invoiceHeader.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : invoiceHeader.ReferenceInvoiceType,
                                SHDCLQuan = invoiceReference.NumberReference.ToString(),
                                NLHDCLQuan = invoiceReference.InvoiceDateReference.ToString("yyyy-MM-dd"),
                                TCHDon = EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus).ToString().Contains("ThayThe") ? 1 : 2
                            }
                        },
                        NDHDon = new NDHDonInvoice02Model
                        {
                            NBan = new NBan
                            {
                                DChi = invoiceHeader.SellerAddressLine,
                                Ten = invoiceHeader.SellerFullName,
                                DCTDTu = email,
                                Fax = invoiceHeader.SellerFaxNumber,
                                MST = invoiceHeader.SellerTaxCode,
                                MCHang = invoiceHeader.StoreCode,
                                TCHang = invoiceHeader.StoreName,
                                SDThoai = invoiceHeader.SellerPhoneNumber,
                                STKNHang = invoiceHeader.SellerBankAccount,
                                TNHang = invoiceHeader.SellerBankName,
                                Website = null, //TODO
                            },
                            NMua = new NMua
                            {
                                MKHang = invoiceHeader.BuyerCode,
                                DChi = invoiceHeader.BuyerAddressLine,
                                Ten = invoiceHeader.BuyerFullName,
                                DCTDTu = email,
                                HVTNMHang = invoiceHeader.BuyerLegalName,
                                MST = invoiceHeader.BuyerTaxCode,
                                MDVQHNSach = invoiceHeader.BudgetUnitCode,
                                SDThoai = invoiceHeader.BuyerPhoneNumber,
                                CCCDan = invoiceHeader.BuyerIDNumber,
                                SHChieu = invoiceHeader.BuyerPassportNumber,
                                STKNHang = invoiceHeader.BuyerBankAccount,
                                TNHang = invoiceHeader.BuyerBankName,
                            },
                            TTKhac = new TTKhacModel
                            {
                                //Id = extraHeaders.Any() ? $"TTKhac-{invoiceHeader.Id}" : null,
                                TTin = extraHeaders?.Select(x => new TTin
                                {
                                    DLieu = x.FieldValue,
                                    KDLieu = "string",
                                    TTruong = x.FieldName
                                }).ToList()
                            }
                        }
                    },
                    DLQRCode = GetQrCode(invoiceHeader.SellerTaxCode, invoiceHeader.TemplateNo, invoiceHeader.SerialNo, invoiceHeader.Number.Value, invoiceHeader.InvoiceDate, invoiceHeader.TotalPaymentAmount),
                    DSCKS = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>.DSCKSModel
                    {
                        NBan = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                //Id = "NBanSignature",
                                Id = $"NBan-{invoiceHeader.Id}",
                                Object = new ObjectNNTModel
                                {
                                    Id = $"SigningTime-{invoiceHeader.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                        {
                                            new SignaturePropertyModel
                                            {
                                                Target = "signatureProperties",
                                                SigningTime = signBackDate ? invoiceHeader.InvoiceDate.ToString("yyyy-MM-ddTHH:mm:ss") : DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        CCKSKhac = new object { }
                    }
                };

                var details = new List<HHDVu>();
                var detail = new HHDVu
                {
                    STT = 1,
                    DGia = 0,
                    DVTinh = "DVT",
                    MHHDVu = "DV",
                    SLuong = 0,
                    STCKhau = 0,
                    TChat = TChat.Note.GetHashCode(),
                    THHDVu = $"Hóa đơn điều chỉnh nội dung thông tin người mua cho hóa đơn số {invoiceReference.InvoiceNoReference} - {invoiceReference.SerialNoReference} - {invoiceReference.InvoiceDateReference.ToString("dd/MM/yyyy")}",
                    ThTien = 0,
                    TLCKhau = 0,
                    TTKhac = new TTKhacModel { }
                };

                details.Add(detail);

                model.DLHDon.NDHDon.DSHHDVu = new DSHHDVu
                {
                    ListDSHHDVu = details
                };

                return model;
            }
            else
            {
                var model = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>
                {
                    DLHDon = new DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>
                    {
                        //Data = "data",
                        Data = $"Id-{invoiceHeader.Id}",
                        TTChung = new TTChungDLHDonInvoice02Model
                        {
                            PBan = TvanInvoiceStaticData.PBanHD02,
                            THDon = "HÓA ĐƠN BÁN HÀNG",
                            KHMSHDon = invoiceHeader.TemplateNo,
                            KHHDon = invoiceHeader.SerialNo,
                            SHDon = invoiceHeader.Number.ToString(),
                            MHSo = null,
                            NLap = invoiceHeader.InvoiceDate.ToString("yyyy-MM-dd"),
                            HDDCKPTQuan = 0,
                            SBKe = null, //TODO: thêm số bảng kê
                            NBKe = null,  //TODO: thêm số bảng kê
                            DVTTe = invoiceHeader.ToCurrency,
                            TGia = invoiceHeader.ExchangeRate,
                            HTTToan = invoiceHeader.PaymentMethod,
                            MSTDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            TDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            DCDVNUNLHDon = null, //TODO: lấy từ dkph ra nếu có
                            MSTTCGP = msttcgp,
                            TTHDLQuan = invoiceReference == null ? null : new TTHDLQuanDLHDon
                            {
                                GChu = invoiceReference.Note,
                                KHMSHDCLQuan = invoiceReference.TemplateNoReference.ToString(),
                                KHHDCLQuan = invoiceReference.SerialNoReference,
                                LHDCLQuan = invoiceHeader.ReferenceInvoiceType == LHDCLQuan.Loai0.GetHashCode() ? LHDCLQuan.Loai1.GetHashCode() : invoiceHeader.ReferenceInvoiceType,
                                SHDCLQuan = invoiceReference.NumberReference.ToString(),
                                NLHDCLQuan = invoiceReference.InvoiceDateReference.ToString("yyyy-MM-dd"),
                                TCHDon = EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus).ToString().Contains("ThayThe") ? 1 : 2
                            }
                        },
                        NDHDon = new NDHDonInvoice02Model
                        {
                            NBan = new NBan
                            {
                                DChi = invoiceHeader.SellerAddressLine,
                                Ten = invoiceHeader.SellerFullName,
                                DCTDTu = email,
                                Fax = invoiceHeader.SellerFaxNumber,
                                MST = invoiceHeader.SellerTaxCode,
                                MCHang = invoiceHeader.StoreCode,
                                TCHang = invoiceHeader.StoreName,
                                SDThoai = invoiceHeader.SellerPhoneNumber,
                                STKNHang = invoiceHeader.SellerBankAccount,
                                TNHang = invoiceHeader.SellerBankName,
                                Website = null, //TODO
                            },
                            NMua = new NMua
                            {
                                MKHang = invoiceHeader.BuyerCode,
                                DChi = invoiceHeader.BuyerAddressLine,
                                Ten = invoiceHeader.BuyerFullName,
                                DCTDTu = email,
                                HVTNMHang = invoiceHeader.BuyerLegalName,
                                MST = invoiceHeader.BuyerTaxCode,
                                MDVQHNSach = invoiceHeader.BudgetUnitCode,
                                SDThoai = invoiceHeader.BuyerPhoneNumber,
                                CCCDan = invoiceHeader.BuyerIDNumber,
                                SHChieu = invoiceHeader.BuyerPassportNumber,
                                STKNHang = invoiceHeader.BuyerBankAccount,
                                TNHang = invoiceHeader.BuyerBankName,
                            },
                            TToan = new TToan
                            {
                                TgTTTBChu = invoiceHeader.PaymentAmountWords,
                                TgTTTBSo = invoiceHeader.TotalPaymentAmount,
                                TTCKTMai = invoiceHeader.TotalDiscountAmount,
                                DSLPhi = new DSLPhi()
                                {
                                    LPhi = lPhiArray,
                                }
                            },
                            TTKhac = new TTKhacModel
                            {
                                //Id = extraHeaders.Any() ? $"TTKhac-{invoiceHeader.Id}" : null,
                                TTin = extraHeaders?.Select(x => new TTin
                                {
                                    DLieu = x.FieldValue,
                                    KDLieu = "string",
                                    TTruong = x.FieldName
                                }).ToList()
                            }
                        }
                    },
                    DLQRCode = GetQrCode(invoiceHeader.SellerTaxCode, invoiceHeader.TemplateNo, invoiceHeader.SerialNo, invoiceHeader.Number.Value, invoiceHeader.InvoiceDate, invoiceHeader.TotalPaymentAmount),
                    DSCKS = new HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice02Model, NDHDonInvoice02Model>>.DSCKSModel
                    {
                        NBan = new CKSNNTModel
                        {
                            Signature = new SignatureNNTModel
                            {
                                Id = $"NBan-{invoiceHeader.Id}",
                                Object = new ObjectNNTModel
                                {
                                    Id = $"SigningTime-{invoiceHeader.Id}",
                                    SignatureProperties = new SignaturePropertiesModel
                                    {
                                        SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new SignaturePropertyModel
                                        {
                                            //Id = "NBanSignTimeStamp",
                                            //Target = "#NBanSignature",
                                            Target = "signatureProperties",
                                            SigningTime = signBackDate ? invoiceHeader.InvoiceDate.ToString("yyyy-MM-ddTHH:mm:ss") : DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                    }
                                }
                            }
                        },
                        CCKSKhac = new object { }
                    }
                };

                var details = new List<HHDVu>();
                foreach (var item in invoiceHeader.InvoiceDetails)
                {
                    var extraDetails = item.ExtraProperties == null || !item.ExtraProperties.ContainsKey("invoiceDetailExtras") ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceDetailExtras"].ToString());

                    if (!string.IsNullOrEmpty(item.Note))
                    {
                        extraDetails.Add(new InvoiceHeaderExtraModel
                        {
                            FieldName = "Ghi chú",
                            FieldValue = item.Note
                        });
                    }

                    var detail = new HHDVu
                    {
                        STT = item.Index,
                        DGia = item.UnitPrice,
                        DVTinh = item.UnitName,
                        MHHDVu = item.ProductCode,
                        SLuong = item.Quantity,
                        STCKhau = item.DiscountAmount,
                        TChat = item.ProductType,
                        THHDVu = item.ProductName,
                        ThTien = item.Amount,
                        TLCKhau = item.DiscountPercent,
                        TTHHDTrung = new TTHHDTrungModel
                        {
                            TTin = item.InvoiceSpecificProductExtras?
                                .Select(p => new TTHHDTrungTTinModel
                                {
                                    LHHDTrung = p.Type,
                                    TTruong = p.FieldName,
                                    DLieu = p.FieldValue
                                })
                                .ToList()
                        },
                        TTKhac = new TTKhacModel
                        {
                            //Id = extraDetails.Any() ? $"IdDetail-{item.Id}" : null,
                            TTin = extraDetails?.Select(x => new TTin
                            {
                                DLieu = x.FieldValue,
                                KDLieu = "string",
                                TTruong = x.FieldName
                            }).ToList()
                        }
                    };

                    details.Add(detail);
                }

                model.DLHDon.NDHDon.DSHHDVu = new DSHHDVu
                {
                    ListDSHHDVu = details
                };

                return model;
            }
        }

        private static string GetQrCode(string sellerTaxCode, short templateNo, string serialNo, int number, DateTime invoiceDate, decimal totalPaymentAmount)
        {
            var gTriSoHieuDonViChapNhanThanhToan3Sub1 = "A000000775";
            var giaTriThongTinHoaDonSub1 = "A000000775";
            var giaTriThongTinBoSung = $"01{number.ToString().Length:00}{number}0307{"CTYVNIS"}0706{"XYZ001"}";
            var giaTriThongTinHoaDon2 = $"00{giaTriThongTinHoaDonSub1.Length:00}{giaTriThongTinHoaDonSub1}01{sellerTaxCode.Replace("-", "").Length:00}{sellerTaxCode.Replace("-", "")}0201{templateNo}0306{serialNo}04{number.ToString().Length:00}{number}0508{invoiceDate:yyyyMMdd}06{totalPaymentAmount.ToString().Length:00}{totalPaymentAmount}";

            var pBanDacTa = "000201";
            var pThucKhoiTao = "010212";
            var soHieuDonViChapNhanThanhToan1 = "021426578954265489"; //visa
            var soHieuDonViChapNhanThanhToan2 = "041415682654895154"; //mastercard
            var soHieuDonViChapNhanThanhToan3 = $"26{gTriSoHieuDonViChapNhanThanhToan3Sub1.Length + 18}00{gTriSoHieuDonViChapNhanThanhToan3Sub1.Length:00}{gTriSoHieuDonViChapNhanThanhToan3Sub1}0110{"0107001729"}";
            var maDanhMucChapNhanThanhToan = "52045499";
            var maTienTe = $"5303{"704"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị với loại tiền khác
            var soTienGiaoDich = $"54{totalPaymentAmount.ToString().Length:00}{totalPaymentAmount}"; //TODO: ThuVT HỎi lại a Tùng xem lại với tiền là tiền số thập phân
            var maQuocGia = "5802VN";
            var tenDonViChapNhanThanhToan = $"5905{$"VNPAY"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị
            var thanhPhoDonViChapNhanThanhToan = $"6005{"HANOI"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị
            var thongTinBoSung = $"62{giaTriThongTinBoSung.Length:00}{giaTriThongTinBoSung}";
            var thongTinHoaDon = $"99{giaTriThongTinHoaDon2.Length:00}{giaTriThongTinHoaDon2}";

            var result = $"{pBanDacTa}{pThucKhoiTao}{soHieuDonViChapNhanThanhToan1}{soHieuDonViChapNhanThanhToan2}{soHieuDonViChapNhanThanhToan3}{maDanhMucChapNhanThanhToan}{maTienTe}{soTienGiaoDich}{maQuocGia}{tenDonViChapNhanThanhToan}{thanhPhoDonViChapNhanThanhToan}{thongTinBoSung}{thongTinHoaDon}6304";

            var crc = CrcExtensions.CalcCRC16(Encoding.UTF8.GetBytes(result));
            return $"{result}{crc}";
        }

        private static string GetQrCode(Invoice02HeaderEntity invoiceHeader)
        {
            var gTriSoHieuDonViChapNhanThanhToan3Sub1 = "A000000775";
            var giaTriThongTinHoaDonSub1 = "A000000775";
            var giaTriThongTinBoSung = $"01{invoiceHeader.Number.ToString().Length:00}{invoiceHeader.Number}0307{"CTYVNIS"}0706{"XYZ001"}";
            var giaTriThongTinHoaDon2 = $"00{giaTriThongTinHoaDonSub1.Length:00}{giaTriThongTinHoaDonSub1}01{invoiceHeader.SellerTaxCode.Replace("-", "").Length:00}{invoiceHeader.SellerTaxCode.Replace("-", "")}0201{invoiceHeader.TemplateNo}0306{invoiceHeader.SerialNo}04{invoiceHeader.Number.ToString().Length:00}{invoiceHeader.Number}0508{invoiceHeader.InvoiceDate:yyyyMMdd}06{invoiceHeader.TotalPaymentAmount.ToString().Length:00}{invoiceHeader.TotalPaymentAmount}";

            var pBanDacTa = "000201";
            var pThucKhoiTao = "010212";
            var soHieuDonViChapNhanThanhToan1 = "021426578954265489"; //visa
            var soHieuDonViChapNhanThanhToan2 = "041415682654895154"; //mastercard
            var soHieuDonViChapNhanThanhToan3 = $"26{gTriSoHieuDonViChapNhanThanhToan3Sub1.Length + 18}00{gTriSoHieuDonViChapNhanThanhToan3Sub1.Length:00}{gTriSoHieuDonViChapNhanThanhToan3Sub1}0110{"0107001729"}";
            var maDanhMucChapNhanThanhToan = "52045499";
            var maTienTe = $"5303{"704"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị với loại tiền khác
            var soTienGiaoDich = $"54{invoiceHeader.TotalPaymentAmount.ToString().Length:00}{invoiceHeader.TotalPaymentAmount}"; //TODO: ThuVT HỎi lại a Tùng xem lại với tiền là tiền số thập phân
            var maQuocGia = "5802VN";
            var tenDonViChapNhanThanhToan = $"5905{$"VNPAY"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị
            var thanhPhoDonViChapNhanThanhToan = $"6005{"HANOI"}"; //TODO: ThuVT Hỏi lại a Tùng giá trị
            var thongTinBoSung = $"62{giaTriThongTinBoSung.Length:00}{giaTriThongTinBoSung}";
            var thongTinHoaDon = $"99{giaTriThongTinHoaDon2.Length:00}{giaTriThongTinHoaDon2}";

            var result = $"{pBanDacTa}{pThucKhoiTao}{soHieuDonViChapNhanThanhToan1}{soHieuDonViChapNhanThanhToan2}{soHieuDonViChapNhanThanhToan3}{maDanhMucChapNhanThanhToan}{maTienTe}{soTienGiaoDich}{maQuocGia}{tenDonViChapNhanThanhToan}{thanhPhoDonViChapNhanThanhToan}{thongTinBoSung}{thongTinHoaDon}6304";

            var crc = CrcExtensions.CalcCRC16(Encoding.UTF8.GetBytes(result));
            return $"{result}{crc}";
        }
    }
}
