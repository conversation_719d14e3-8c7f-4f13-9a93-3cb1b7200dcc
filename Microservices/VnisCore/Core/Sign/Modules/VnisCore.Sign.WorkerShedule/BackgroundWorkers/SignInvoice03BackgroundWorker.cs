using System;
using System.Linq;
using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Serilog;
using VnisCore.Sign.WorkerSchedule.Business;
using VnisCore.Sign.WorkerSchedule.Interface;
using VnisCore.Sign.WorkerSchedule.Models;

namespace VnisCore.Sign.WorkerSchedule.BackgroundWorkers
{
    public class SignInvoice03BackgroundWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public SignInvoice03BackgroundWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            Timer.Period = 100; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;

        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                var signTenantOptions = workerContext.ServiceProvider.GetService<IOptions<TenantSignOption>>().Value;

                if (signTenantOptions.Invoice03s == null || !signTenantOptions.Invoice03s.Any())
                {
                    return;
                }

                var services = workerContext.ServiceProvider.GetServices<ISignInvoiceBusiness>();
                var service = services.FirstOrDefault(x => x.GetType().Name == nameof(SignInvoice03Business));

                if (service == null)
                    return;

                foreach (var tenant in signTenantOptions.Invoice03s)
                {

                    try
                    {
                        var now = DateTime.Now;
                        var isSign = true;
                        if (tenant.FromHour > tenant.ToHour)
                        {
                            if (!((tenant.FromHour <= now.Hour && now.Hour < 24)
                                || (0 <= now.Hour && now.Hour < tenant.ToHour)))
                            {
                                isSign = false;
                            }
                        }
                        else
                        {
                            if (!(tenant.FromHour <= now.Hour && now.Hour < tenant.ToHour))
                            {
                                isSign = false;
                            }
                        }

                        if (isSign)
                        {
                            await service.SignInvoiceAsync(tenant.TaxCode, tenant.InvoiceSources);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}