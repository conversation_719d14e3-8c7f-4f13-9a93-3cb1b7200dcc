using System.Collections.Generic;
using System.Threading.Tasks;

namespace VnisCore.Sign.WorkerSchedule.Interface
{
    public interface ISignInvoiceBusiness
    {
        Task SignInvoiceAsync(string[] taxCodes, List<short> invoiceSources);

        /// <summary>
        /// Task 949
        /// S<PERSON>a luồng ký hóa đơn của signschedulewoker tăng thread ký lên
        /// </summary>
        /// <param name="taxCodes"></param>
        /// <param name="invoiceSources"></param>
        /// <returns></returns>
        Task SignInvoiceV2Async(string[] taxCodes, List<short> invoiceSources);

        Task SignInvoiceAsync(string taxCode, List<short> invoiceSources);
    }
}