<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net5.0</TargetFramework>
    <Version>5.30.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="5.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="3.2.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="1.4.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.Application.Contracts\VnisCore.AuthDatabase.Oracle.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.EntityFrameworkCore\VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Databases\VnisCore\Core\MongoDB\VnisCore.Core.MongoDB\VnisCore.Core.MongoDB.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.Autofac\Core.Autofac.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.BackgroundWorkers\Core.BackgroundWorkers.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.Caching.StackExchangeRedis\Core.Caching.StackExchangeRedis.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.EventBus.RabbitMQ\Core.EventBus.RabbitMQ.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Dto.Shared\Core.Dto.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Shared\Core.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.Dapper\Core.Dapper.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core\Core.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Tvan\Core.Tvan.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
    <ProjectReference Include="..\..\..\Invoice01\Modules\VnisCore.Invoice01.Infrastructure\VnisCore.Invoice01.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
