{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "MSTTCGP": "0101352495",
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "************",
        "Port": 5672,
        "UserName": "admin",
        "Password": "admin@123",
        "VirtualHost": "/massv5-invoice"
      }
    },
    "EventBus": {
      "ClientName": "einvoice.sign-schedule",
      "ExchangeName": "einvoice"
    }
  },
  "Minio": {
    "Endpoint": "efin-minio.vnpaytest.vn",
    //"Endpoint": "************:9000",
    "AccessKey": "minioadmin",
    "SecretKey": "Minio@123",
    "Region": null,
    "SessionToken": null,
    "BucketName": "invoice-mass-v5",
    "TrustAllCerts": "true"
  },
  "Settings": {
    "IsEnableTenantGroupPrivate": 0,
    "TenantGroupsPrivate": "1.1,1.2,1.3"
  },
  "Elasticsearch": {
    "Index": "dev.core50.invoice01",
    "Url": "https://************:9200",
    "Urls": "https://************:9200",
    "Username": "elastic",
    "Password": "GyHasF+gFd4iysODzRjI",
    "TrustAllCerts": "true"
  },
  "TenantSign": {
    "Invoice01s": [
      {
        // "0100106225"
        "TaxCodes": [],
        "FromHour": 8,
        "ToHour": 21
      }
    ],
    "Invoice02s": [
      {
        "TaxCodes": [],
        "FromHour": 10,
        "ToHour": 11
      }
    ],
    "Invoice03s": [
      {
        "TaxCode": "0102182292-998",
        "FromHour": 2,
        "ToHour": 20,
        "Token": ""
      }
    ]
  },
  "Service": {
    "Name": "VnisCore.Sign.WorkerSchedule",
    "Title": "VnisCore.Sign.WorkerSchedule",
    "BaseUrl": "sign-worker-schedule",
    "AuthApiName": "VnisCore.Sign.WorkerSchedule"
  },
  "Redis": {
    "IsUsing": "true",
    //"Configuration": "10.22.18.230,allowAdmin=true,password=vnpayredis@123,defaultDatabase=14"
    "Configuration": "10.22.18.230:6380,10.22.18.230:6381,10.22.18.230:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14"
  },
  "AuthServer": {
    "Authority": "https://auth.v50.staging.vninvoice.net",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_Swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "Settings": {
    "TimeFlushToDiskInterval": 10000, // 10s
    "IsEnableTenantGroupPrivate": 0,
    "QuantityOfInvoicesInMultiThread": 10,
    "TenantGroupsPrivate": "1.1,1.2,1.3",
    "InvoicesGroup": "0,1,2,3,4",
    "TimePeriod": 1,
    "InvoiceTakeToSign": 100,
    "IsEnableGenerateInvoice01NumberWorker": 1,
    "IsEnableMongoDbLocalTime": 1
  },
  "Logging": {
    "RootFolder": {
      "Folder": "/var/logs/invoice/mass"
    }
  }
}