{
  //"App": {
  //  "SelfUrl": "https://localhost:6014",
  //  "CorsOrigins": "http://localhost:6789",
  //  "RedirectAllowedUrls": "http://localhost:4200"
  //},
  //"AppSelfUrl": "https://localhost:6014/",
  //"ConnectionStrings": {
  //  "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoiceauth;Password=Vnis@12A",
  //  "VnisCoreSqlServer": "Server=************;Database=Einvoice;persist security info=True;user id=vnisadmin;password=*************;MultipleActiveResultSets=True;",
  //  "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=einvoice50dev)))';User Id=einvoice50dev;Password=Vnis@12A",
  //  "VnisCoreMongoDbAuditLogging": "***************************************************************************************"
  //},
  //"MaxTimesOfTvanDisconnected": 3, // số lần tối đa ko kết nối đc đến 1 nhà Van
  //"MaxTimeCaching": 2, // thời gian (giờ) cached thông tin ko kết nối đc đến 1 nhà Van
  //"MSTTCGP": "0101352495",
  //"Service": {
  //  "Name": "VnisCore.Sign.Host",
  //  "Title": "VnisCore.Sign.Host",
  //  "BaseUrl": "sign",
  //  "AuthApiName": "VnisCore.Sign.Host"
  //},
  //"TvanInvoice": {
  //  "Endpoint": "http://**************:5000/",
  //  "ApiKey": "2e850b565b8c4bf39dde990972b71572",
  //  "Timeout": 5
  //},
  //"TvanMInvoice": {
  //  "EndPoint": "https://testmtvan.minvoice.net/",
  //  "UserName": "VnisTest50",
  //  "Password": "AdminVnisTest500@123",
  //  "TenantCode": "VNIS", //ma_dvcs,
  //  "TaxCode": "0101352495", //mst don v? cung c?p gi?i pháp,
  //  "Timeout": 5
  //},
  //"TvanSupplier": {
  //  "VnPayInvoice": 1,
  //  "MInvoice": 2
  //},
  //"Redis": {
  //  "IsUsing": "true",
  //  "Configuration": "**************,allowAdmin=true"
  //},
  //"AuthServer": {
  //  "Authority": "http://localhost:6868",
  //  "RequireHttpsMetadata": "false",
  //  "ApiName": "einvoice",
  //  "SwaggerClientId": "einvoice_Swagger",
  //  "SwaggerClientSecret": "Vnis@12A"
  //},
  //"Minio": {
  //  "Endpoint": "**************:9000",
  //  "AccessKey": "vnis",
  //  "SecretKey": "Vnis@12A",
  //  "Region": null,
  //  "SessionToken": null,
  //  "BucketName": "dev-core50"
  //},
  //"RabbitMQ": {
  //  "Connections": {
  //    "Default": {
  //      "HostName": "localhost",
  //      "Port": 5672,
  //      "UserName": "guest",
  //      "Password": "guest",
  //      "VirtualHost": "/",
  //      "Workers": {
  //        "Einvoice": {
  //          "ConnectionName": "Einvoice",
  //          "NumberOfConsumer": 1
  //        },
  //        "GenerateSignServer": {
  //          "ConnectionName": "GenerateSignServer",
  //          "NumberOfConsumer": 1
  //        }
  //      }
  //    }
  //  },
  //  "EventBus": {
  //    "ClientName": "einvoice.sign",
  //    "ExchangeName": "einvoice"
  //  }
  //},
  //"Elasticsearch": {
  //  "Invoice01": {
  //    "Index": "dev.core50.invoice01"
  //  },
  //  "Invoice02": {
  //    "Index": "dev.core50.invoice02"
  //  },
  //  "Invoice04": {
  //    "Index": "dev.core50.invoice04"
  //  },
  //  "Url": "http://**************:9200"
  //},
  //"Microservices": {
  //  "SendMail": {
  //    "Endpoint": "http://localhost:6013/",
  //    "Timeout": 5
  //  }
  //}
}