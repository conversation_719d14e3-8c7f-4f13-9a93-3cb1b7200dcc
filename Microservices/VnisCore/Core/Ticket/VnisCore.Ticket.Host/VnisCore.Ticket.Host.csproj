<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Version>5.31.4</Version>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\Framework\Core\Core.EventBus.RabbitMQ\Core.EventBus.RabbitMQ.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Core\Core.Swashbuckle\Core.Swashbuckle.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Core\Core\Core.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
    <ProjectReference Include="..\Modules\VnisCore.Ticket.Application\VnisCore.Ticket.Application.csproj" />
  </ItemGroup>

</Project>
