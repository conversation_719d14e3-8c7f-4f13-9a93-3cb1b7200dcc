using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Dapper;

using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;

namespace VnisCore.Ticket.SendToTvan.Repositories
{
    public interface ITicketHeaderRepository
    {
        Task<List<TicketHeaderEntity>> GetInvoicesUnSentAsync();

        Task<List<TicketHeaderEntity>> GetInvoicesUnSentAsync(List<string> ignoreTaxCodes);

        Task<List<TicketHeaderEntity>> GetInvoicesSendErrorAsync();

        Task<List<TicketHeaderEntity>> GetInvoicesSendErrorAsync(List<string> ignoreTaxCodes);
    }

    public class TicketHeaderRepository : ITicketHeaderRepository
    {
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;

        public TicketHeaderRepository(
            IConfiguration configuration,
            IAppFactory appFactory)
        {
            _configuration = configuration;
            _appFactory = appFactory;
        }

        public async Task<List<TicketHeaderEntity>> GetInvoicesSendErrorAsync()
        {
            var isTake = int.TryParse(_configuration.GetSection("InvoicesTake").Value, out int invoiceTake);
            if (!isTake)
                invoiceTake = 100;

            var sql = $@" SELECT ih.""Id"",
                                ih.""TenantId"",
                                ih.""InvoiceNo"",
                                ih.""SerialNo"",
                                ih.""TemplateNo"",
                                ih.""StatusTvan"",
                                ih.""CreationTime"",
                                ih.""SellerTaxCode"" FROM ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" ih
                            LEFT JOIN ""{DatabaseExtension<TvanInfoInvoice04HasCodeEntity>.GetTableName()}"" tiihc
                            ON ih.""Id"" = tiihc.""InvoiceHeaderId""
                WHERE ih.""SerialNo"" LIKE 'C%' 
                    AND ih.""SignStatus"" = {(short)SignStatus.DaKy} 
                    AND ih.""StatusTvan"" = {(short)TvanStatus.SendError}
                    AND ih.""SellerSignedTime"" IS NOT NULL
                    AND ih.""IsDeleted"" = 0
                    AND tiihc.""MessageCode"" IS NULL
                OFFSET 0 ROWS FETCH NEXT {invoiceTake} ROWS ONLY ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderEntity>(sql.ToString())).ToList();
        }

        public async Task<List<TicketHeaderEntity>> GetInvoicesSendErrorAsync(List<string> ignoreTaxCodes)
        {
            var isTake = int.TryParse(_configuration.GetSection("InvoicesTake").Value, out int invoiceTake);
            if (!isTake)
                invoiceTake = 100;

            var condition = "";
            if (!ignoreTaxCodes.IsNullOrEmpty())
            {
                condition = @$" AND ih.""SellerTaxCode"" NOT IN ({string.Join(", ", ignoreTaxCodes.Select(x => $"'{x}'"))})";
            }

            var sql = $@" SELECT ih.""Id"",
                                ih.""TenantId"",
                                ih.""InvoiceNo"",
                                ih.""SerialNo"",
                                ih.""TemplateNo"",
                                ih.""StatusTvan"",
                                ih.""CreationTime"",
                                ih.""SellerTaxCode"" FROM ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" ih
                            LEFT JOIN ""{DatabaseExtension<TvanInfoInvoice04HasCodeEntity>.GetTableName()}"" tiihc
                            ON ih.""Id"" = tiihc.""InvoiceHeaderId""
                WHERE ih.""SerialNo"" LIKE 'C%' 
                    AND ih.""SignStatus"" = {(short)SignStatus.DaKy} 
                    AND ih.""StatusTvan"" = {(short)TvanStatus.SendError}
                    AND ih.""SellerSignedTime"" IS NOT NULL
                    AND ih.""IsDeleted"" = 0
                    AND tiihc.""MessageCode"" IS NULL {condition}
                OFFSET 0 ROWS FETCH NEXT {invoiceTake} ROWS ONLY ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderEntity>(sql.ToString())).ToList();
        }

        public async Task<List<TicketHeaderEntity>> GetInvoicesUnSentAsync()
        {
            var isTake = int.TryParse(_configuration.GetSection("InvoicesTake").Value, out int invoiceTake);
            if (!isTake)
                invoiceTake = 100;

            var sql = $@" SELECT ih.""Id"",
                                ih.""TenantId"",
                                ih.""InvoiceNo"",
                                ih.""SerialNo"",
                                ih.""TemplateNo"",
                                ih.""StatusTvan"",
                                ih.""CreationTime"",
                                ih.""SellerTaxCode"" FROM ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" ih
                        LEFT JOIN ""{DatabaseExtension<TvanInfoInvoice04HasCodeEntity>.GetTableName()}"" tiihc
                        ON ih.""Id"" = tiihc.""InvoiceHeaderId""
                WHERE ih.""SerialNo"" LIKE 'C%' 
                    AND ih.""SignStatus"" = {(short)SignStatus.DaKy} 
                    AND ih.""StatusTvan"" = {(short)TvanStatus.UnSent}
                    AND ih.""SellerSignedTime"" IS NOT NULL
                    AND ih.""IsDeleted"" = 0
                    AND tiihc.""MessageCode"" IS NULL
                OFFSET 0 ROWS FETCH NEXT {invoiceTake} ROWS ONLY ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderEntity>(sql.ToString())).ToList();
        }

        public async Task<List<TicketHeaderEntity>> GetInvoicesUnSentAsync(List<string> ignoreTaxCodes)
        {
            var isTake = int.TryParse(_configuration.GetSection("InvoicesTake").Value, out int invoiceTake);
            if (!isTake)
                invoiceTake = 100;

            var condition = "";
            if (!ignoreTaxCodes.IsNullOrEmpty())
            {
                condition = @$" AND ih.""SellerTaxCode"" NOT IN ({string.Join(", ", ignoreTaxCodes.Select(x=>$"'{x}'"))})";
            }

            var sql = $@" SELECT ih.""Id"",
                                ih.""TenantId"",
                                ih.""InvoiceNo"",
                                ih.""SerialNo"",
                                ih.""TemplateNo"",
                                ih.""StatusTvan"",
                                ih.""CreationTime"",
                                ih.""SellerTaxCode"" FROM ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" ih
                        LEFT JOIN ""{DatabaseExtension<TvanInfoInvoice04HasCodeEntity>.GetTableName()}"" tiihc
                        ON ih.""Id"" = tiihc.""InvoiceHeaderId""
                WHERE ih.""SerialNo"" LIKE 'C%' 
                    AND ih.""SignStatus"" = {(short)SignStatus.DaKy} 
                    AND ih.""StatusTvan"" = {(short)TvanStatus.UnSent}
                    AND ih.""SellerSignedTime"" IS NOT NULL
                    AND ih.""IsDeleted"" = 0
                    AND tiihc.""MessageCode"" IS NULL {condition}
                OFFSET 0 ROWS FETCH NEXT {invoiceTake} ROWS ONLY ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderEntity>(sql.ToString())).ToList();
        }
    }
}
