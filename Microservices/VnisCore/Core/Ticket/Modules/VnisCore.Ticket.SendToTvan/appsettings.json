{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "TvanVnpayInvoice": {
    "EndPoint": "https://tvan-api.vnpaytest.vn/v2/",
    "ApiKey": "bf1976d99b384703a0636ce8d09ae3a7",
    "Timeout": 5
  },
  "Service": {
    "Name": "VnisCore.Ticket.SendToTvan",
    "Title": "VnisCore.Ticket.SendToTvan",
    "BaseUrl": "send-ticket-hascode",
    "AuthApiName": "VnisCore.Ticket.SendToTvan"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14",
    "Database": 14
  },
  "Minio": {
    "Endpoint": "efin-minio.vnpaytest.vn",
    //"Endpoint": "10.22.18.233:9000",
    "AccessKey": "minioadmin",
    "SecretKey": "Minio@123",
    "Region": null,
    "SessionToken": null,
    "BucketName": "invoice-mass-v5",
    "TrustAllCerts": "true"
  },
  "Settings": {
    "IsResendInvoicesError": 0,
    "TimePeriodResendInvoiceError": 30,
    "TimePeriod": 1
  },
  "InvoicesTake": 100,
  "MaxSize": 100,
  "TimePeriod": 1,
  "Logging": {
    "RootFolder": {
      "Folder": "/var/logs/invoice"
    }
  }
}