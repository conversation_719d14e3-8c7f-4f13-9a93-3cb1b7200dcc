using Core;
using Core.Application;
using Core.Autofac;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Modularity;
using Core.MultiTenancy;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.FileManager.Models;
using Core.Shared.FileManager.Services;
using Core.Shared.Services;
using Core.TenantManagement;
using Core.Tvan;
using Core.Tvan.Vnpay;
using Core.VaultSharp;
using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.Domain;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Ticket.SendToTvan.BackgroundWorkers;
using VnisCore.Ticket.SendToTvan.Business;
using VnisCore.Ticket.SendToTvan.Repositories;

namespace VnisCore.Ticket.SendToTvan
{

    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpDddApplicationModule),
        typeof(AbpAutofacModule),
        typeof(TvanModule),
        typeof(AbpCachingStackExchangeRedisModule),
        typeof(SharedModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(VnisCoreAuthDatabaseOracleDomainModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),
        typeof(TvanVnpayModule),
        typeof(VnisCoreMongoDbModule)
    )]
    public class TicketSendToTvanModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            // business
            context.Services.AddScoped<ISendInvoiceHasCodeToTvanBusiness, SendInvoiceHasCodeToTvanBusiness>();
            context.Services.AddScoped<IResendInvoiceHasCodeToTvanBusiness, ResendInvoiceHasCodeToTvanBusiness>();
            context.Services.AddScoped<ITicketHeaderRepository, TicketHeaderRepository>();
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<ResendTicketHascodeBackgroundWorker>();
            context.AddBackgroundWorker<SendTicketHasCodeBackgroundWorker>();
        }
    }
}
