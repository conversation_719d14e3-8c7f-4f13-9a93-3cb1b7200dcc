// using Core.Host.Shared;
using Core.VaultSharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

using Serilog;

using System;

namespace VnisCore.Ticket.SendToTvan
{
    public class Program
    {
        public static int Main(string[] args)
        {
            // ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting VnisCore.Ticket.SendToTvan.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "VnisCore.Ticket.SendToTvan terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }

        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            try
            {
                return VaultConfigure.CreateHostBuilder(args)
                                    .ConfigureServices((hostContext, services) =>
                                    {
                                        ConfigureServices(services, hostContext.Configuration);
                                        services.AddHostedService<TicketSendToTvanHostedService>();
                                    })
                                    .UseAutofac()
                                    .UseSerilog();
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }

        private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddApplication<TicketSendToTvanModule>();

            return services.BuildServiceProvider();
        }

        #region
        //public static int Main(string[] args)
        //{
        //    // ConfigureLogging.Configure();

        //    try
        //    {
        //        Log.Information("Starting console host.");
        //        CreateHostBuilder(args).Build().Run();
        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Fatal(ex, "Host terminated unexpectedly!");
        //        return 1;
        //    }
        //    finally
        //    {
        //        Log.CloseAndFlush();
        //    }

        //}

        //public static IHostBuilder CreateHostBuilder(string[] args) =>
        //        Host.CreateDefaultBuilder(args)
        //        .ConfigureServices((hostContext, services) =>
        //        {
        //            ConfigureServices(services, hostContext.Configuration);
        //            services.AddHostedService<TicketSendToTvanHostedService>();
        //        })
        //        .UseAutofac()
        //        .UseSerilog();

        //private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        //{
        //    services.AddApplication<TicketSendToTvanModule>();

        //    return services.BuildServiceProvider();
        //}
        #endregion
    }
}
