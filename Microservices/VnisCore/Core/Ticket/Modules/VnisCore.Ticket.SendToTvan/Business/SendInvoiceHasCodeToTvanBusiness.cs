using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Core.Tvan.Vnpay.Interfaces;

using Dapper;

using Serilog;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.SendToTvan.Repositories;

namespace VnisCore.Ticket.SendToTvan.Business
{
    public interface ISendInvoiceHasCodeToTvanBusiness
    {
        Task SendInvoiceToTvanAsync();
    }

    public class SendInvoiceHasCodeToTvanBusiness : ISendInvoiceHasCodeToTvanBusiness
    {
        private readonly ITicketHeaderRepository _TicketHeaderRepository;
        private readonly IAppFactory _appFactory;
        private readonly ITenantCacheBusiness _tenantCacheBusiness;
        private readonly ISettingWorkerService _settingWorkerService;

        public SendInvoiceHasCodeToTvanBusiness(
            ITicketHeaderRepository TicketHeaderRepository,
            IAppFactory appFactory,
            ITenantCacheBusiness tenantCacheBusiness,
            ISettingWorkerService settingWorkerService)
        {
            _TicketHeaderRepository = TicketHeaderRepository;
            _appFactory = appFactory;
            _tenantCacheBusiness = tenantCacheBusiness;
            _settingWorkerService = settingWorkerService;
        }

        public async Task SendInvoiceToTvanAsync()
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();

            List<string> ignoreTaxCodes = new List<string>();
            var tenants = await _tenantCacheBusiness.GetAllTenantAsync();
            foreach (var tenant in tenants)
            {
                var isSetting = await _settingWorkerService.GetByCodeAsync(tenant.Id, SettingKey.ConfigSendTvan.ToString(), tenant.TaxCode);
                if (isSetting == 0)
                {
                    ignoreTaxCodes.Add(tenant.TaxCode);
                }
            }
            // TH Tắt Luồng Tvan
            if (tenants.Count == ignoreTaxCodes.Count)
            {
                Log.Information("Hệ thống ngưng gửi lên TVAN");
                return;
            }

            var invoiceHasCodeUnsent = await _TicketHeaderRepository.GetInvoicesUnSentAsync(ignoreTaxCodes);

            stopwatch.Stop();
            Log.Fatal($"*** TIME QUERY ***: {stopwatch.ElapsedMilliseconds}");
            stopwatch.Restart();

            if (!invoiceHasCodeUnsent.Any())
                return;

            foreach (var invoice in invoiceHasCodeUnsent)
            {
                var xml = string.Empty;
                var queryInvoiceXml = $@" SELECT ""TenantId"",
                                                 ""CreationTime"",
                                                 ""PhysicalFileName""
                                          FROM ""{DatabaseExtension<TicketXmlEntity>.GetTableName()}""
                                          WHERE ""InvoiceHeaderId"" = {invoice.Id} 
                                            AND ""IsDeleted"" = 0 
                                          Order By ""Id"" DESC
                                          OFFSET 0 ROWS
                                          FETCH NEXT 1 ROWS ONLY";
                
                var invoiceXml = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<TicketXmlEntity>(queryInvoiceXml);
                if (invoiceXml == null)
                    continue;

                try
                {
                    var fileService = _appFactory.GetServiceDependency<IFileService>();
                    var pathFileMinio = $"{MediaFileType.TicketXml}/{invoiceXml.TenantId}/{invoiceXml.CreationTime.Year}/{invoiceXml.CreationTime.Month:00}/{invoiceXml.CreationTime.Day:00}/{invoiceXml.CreationTime.Hour:00}/{invoiceXml.PhysicalFileName}";
                    var bytes = await fileService.DownloadAsync(pathFileMinio);

                    xml = Encoding.UTF8.GetString(bytes);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Download xml invoiceHeaderId = {invoice.Id} Error: {ex.Message}");
                    continue;
                }
                

                if (string.IsNullOrEmpty(xml))
                    continue;

                var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();

                try
                {
                    var responseTvan = await tvanVnpayInvoice.SendTicketHasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
                    if (responseTvan != null && responseTvan.Code == "00")
                    {
                        invoice.StatusTvan = (short)TvanStatus.Sended;
                    }
                    else
                    {
                        invoice.StatusTvan = (short)TvanStatus.SendError;
                    }

                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {invoice.Id} and ""StatusTvan"" != {invoice.StatusTvan}";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
                }
                catch (Exception ex)
                {
                    invoice.StatusTvan = (short)TvanStatus.SendError;
                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {invoice.Id} and ""StatusTvan"" != {invoice.StatusTvan}";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
                    Log.Error(ex, ex.Message);
                    continue;
                }
                //finally
                //{
                //    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {invoice.Id} and ""StatusTvan"" != {invoice.StatusTvan}";
                //    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
                //}
            }
        }
    }
}
