using Core.Application.Dtos;
using Core.Data;
using Core.Domain.Repositories;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls.TCTResponse;
using Core.Uow;
using Dapper;
using Dapper.Oracle;
using Nest;
using Newtonsoft.Json;

using Oracle.ManagedDataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Ticket.Application.Factories.Models;
using VnisCore.Ticket.Application.Factories.Repositories;
using VnisCore.Ticket.Application.Ticket.Dto;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Commands;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Queries;
using VnisCore.Ticket.Application.Ticket.Models.Responses.Queries;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CreateByGroupCustomerTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CreateTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CreateReplaceTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateReplaceTicket.MessageEventData;
using VnisCore.Ticket.Application.StoredProcedure.Procedures;
using static Core.Tvan.Models.Xmls.TCTResponse.DLieuInvoiceErrorResponseModel;
using static VnisCore.Ticket.Application.Factories.Constants.CommonTicketConst;

namespace VnisCore.Ticket.Application.Factories.Services
{
    public class TicketService : ITicketService
    {
        private readonly IAppFactory _appFactory;
        private readonly IElasticClient _elasticClient;
        private readonly IRepository<TicketHeaderEntity, long> _repoTicketHeader;
        private readonly IRepository<TicketDetailEntity, long> _repoTicketDetail;
        private readonly IRepository<TicketTaxBreakdownEntity, long> _repoTicketTaxBreakdown;
        private readonly IRepository<TicketReferenceEntity, long> _repoTicketReference;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        protected IObjectMapper _objectMapper { get; }

        public TicketService(
            IAppFactory appFactory,
            IElasticClient elasticClient,
            IRepository<TicketHeaderEntity, long> repoTicketHeader,
            IRepository<TicketDetailEntity, long> repoTicketDetail,
            IRepository<TicketTaxBreakdownEntity, long> repoTicketTaxBreakdown,
            IRepository<TicketReferenceEntity, long> repoTicketReference,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            IObjectMapper objectMapper
            )
        {
            _appFactory = appFactory;
            _repoTicketHeader = repoTicketHeader;
            _repoTicketDetail = repoTicketDetail;
            _repoTicketTaxBreakdown = repoTicketTaxBreakdown;
            _repoTicketReference = repoTicketReference;
            _invoiceService = invoiceService;
            _elasticClient = elasticClient;
            _objectMapper = objectMapper;
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string SequenceName)
        {
            var sql = $@"   SELECT ""{SequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }


        public async Task<PagedResultDto<PagingTicketResponse>> GetListElasticAsync(Guid tenantId, PagingTicketRequest input)
        {
            var datas = new List<PagingTicketResponse>();
            var pagedResponse = await PagingElasticSearchAsync(tenantId, input);

            var paged = pagedResponse.Documents.ToList();

            foreach (var item in paged)
            {
                PagingTicketResponse model = new PagingTicketResponse
                {
                    Id = item.Id,
                    ErpId = item.ErpId,
                    TransactionId = item.TransactionId,
                    TemplateNo = item.TemplateNo,
                    SerialNo = item.SerialNo,
                    InvoiceNo = item.InvoiceNo,
                    Number = item.Number,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(item.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(item.SignStatus),
                    ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveStatus),
                    ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveCancelStatus),
                    ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveDeleteStatus),
                    InvoiceDate = item.InvoiceDate,
                    TotalAmount = item.TotalAmount,
                    TotalPaymentAmount = item.TotalPaymentAmount,
                    TotalVatAmount = item.TotalVatAmount,
                    BuyerFullName = item.BuyerFullName,
                    BuyerEmail = item.BuyerEmail,
                    BuyerCode = item.BuyerCode,
                    PrintedTime = item.PrintedTime,
                    UserNameCreator = item.UserNameCreator,
                    CreatorErp = item.CreatorErp,
                    Note = item.Note,
                    Source = EnumExtension.ToEnum<InvoiceSource>(item.Source),
                    IsViewed = item.IsViewed,
                    IsOpened = item.IsOpened,
                    BuyerBankAccount = item.BuyerBankAccount,
                    IsDeclared = item.IsDeclared,
                    VerificationCode = item.VerificationCode
                };
                datas.Add(model);
            }

            var result = new PagedResultDto<PagingTicketResponse>
            {
                Items = datas,
                //Page = request.Page ?? 1,
                //Q = request.Q,
                //Size = request.Size ?? 10,
                //TotalItems = Convert.ToInt32(pagedResponse.Total),
                TotalCount = pagedResponse.Total
            };
            return result;
        }
        public async Task<decimal> CaculateTotalPaymentAmount(Guid tenantId, Guid userId, PagingTicketRequest input)
        {
            var query = GeneratCaculatePaymentAmountQuery(tenantId, userId, input);

            try
            {
                var sum = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<decimal>(query);
                return sum;
            }
            catch (DataException ex)
            {

                return 0;
            }
        }
        public async Task<PagedResultDto<PagingTicketResponse>> GetListAsync(Guid tenantId, Guid userId, PagingTicketRequest input)
        {
            var query = await GenerateDrawGetListQuery(tenantId, userId, input);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingTicketResponse>(query);

            var result = new PagedResultDto<PagingTicketResponse>();
            if (data != null && data.Any())
            {
                data.ToList().ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingTicketResponse.PagingTicketHeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }

                    //x.GDTResponseStatus = 0;

                    //if (x.SerialNo.StartsWith('C'))
                    //{
                    //    x.GDTResponseStatus = x.TvanInfoInvoiceErrorStatus;
                    //}
                    //else if (!string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) || !string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason))
                    //{
                    //    x.GDTResponseStatus = -1;
                    //}

                    var errorsMess = new string[] { @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceErrorReason) ? "" : $"Lý do sai sót: {string.Join("; ", JsonConvert.DeserializeObject<List<LDoHDonModel>>(x.TvanInfoInvoiceErrorReason)?.Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn có mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceHasCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceWithoutCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn không mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceWithoutCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason) ? "" : $"Thông báo về hóa đơn cần rà soát: {x.TvanInfoCheckInvoiceReason}")}" };

                    x.InvoiceErrorMessage = string.Join("\n", errorsMess.Where(x => !string.IsNullOrEmpty(x)));
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = data.ToList();
            }

            return result;
        }

        public async Task<PagedResultDto<PagingTicketResponse>> GetListByImportFileAsync(Guid tenantId, Guid userId, PagedFullRequestDto input, IEnumerable<ConditionToSendProcedure> conditions)
        {
            var param = new OracleDynamicParameters();
            param.Add("json_conditions", JsonConvert.SerializeObject(conditions), OracleMappingType.NClob, ParameterDirection.Input);
            param.Add("tenantRawId", OracleExtension.ConvertGuidToRaw(tenantId), OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add("skipCount", input.SkipCount, OracleMappingType.Int32, ParameterDirection.Input);
            param.Add("maxResultCount", input.MaxResultCount, OracleMappingType.Int32, ParameterDirection.Input);
            param.Add(name: "output_data", value: DBNull.Value, dbType: OracleMappingType.RefCursor, direction: ParameterDirection.Output);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingTicketResponse>(
                SystemProcedureName.TicketGetPagingByImportFile,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            var result = new PagedResultDto<PagingTicketResponse>();
            if (data != null && data.Any())
            {
                var items = data.ToList();
                items.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingTicketResponse.PagingTicketHeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = items;
            }

            return result;
        }

        private async Task<ISearchResponse<ElastichSearchInvoiceModel>> PagingElasticSearchAsync(Guid tenantCode, PagingTicketRequest query)
        {
            //Full text search - multiMatch
            var multiMatch = new List<Func<MultiMatchQueryDescriptor<ElastichSearchInvoiceModel>, IMultiMatchQuery>>();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                //int.TryParse(query.Q, out int outInvoiceNo);
                //var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(query.Q);
                //var signStatus = EnumExtension.TryToEnum<SignStatus>(query.Q);

                multiMatch.Add(m => m.Fields(f => f
               .Field(p => p.UserNameCreator)
               .Field(p => p.BuyerEmail)
               .Field(p => p.BuyerFullName)
               .Field(p => p.PaymentMethod)
               .Field(p => p.BuyerPhoneNumber)
               .Field(p => p.SerialNo)
               .Field(p => p.TemplateNo)
               .Field(p => p.ErpId)
               .Field(p => p.TransactionId)
               .Field(p => p.CreatorErp)
               .Field(p => p.BuyerId)
               .Field(p => p.BuyerBankAccount)
               )
                .Query(query.Keyword)
                .Operator(Operator.And)
               );
            }
            //               .Field(p => p.Number)
            //   .Field(p => p.InvoiceStatus)
            //   .Field(p => p.SignStatus)
            //
            //Keyword customize
            var filters = new List<Func<QueryContainerDescriptor<ElastichSearchInvoiceModel>, QueryContainer>>();

            if (query.CancelFromDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.CancelTime).GreaterThanOrEquals(query.CancelFromDate)));


            //lấy hóa đơn có số hoặc không số
            if (query.IsNullInvoice)
                filters.Add(f => !f.Exists(t => t.Field(f => f.Number)));
            else
                filters.Add(f => f.Exists(t => t.Field(f => f.Number)));

            if (query.CancelToDate.HasValue)
            {
                filters.Add(f => f.DateRange(t => t.Field(f => f.CancelTime).LessThanOrEquals(query.CancelToDate)));
            }

            if (query.IssuedTime.HasValue)
            {
                var dateNextIssuedAt = query.IssuedTime.Value.AddDays(1);
                filters.Add(f => f.DateRange(t => t.Field(f => f.IssuedTime).LessThan(dateNextIssuedAt)) &&
                f.DateRange(t => t.Field(f => f.IssuedTime).GreaterThanOrEquals(query.IssuedTime)));
            }

            //Full text search - multiMatch
            var multiCustomers = new List<Func<MultiMatchQueryDescriptor<ElastichSearchInvoiceModel>, IMultiMatchQuery>>();

            if (!string.IsNullOrEmpty(query.Customers))
            {
                multiCustomers.Add(m => m.Fields(f => f
                .Field(p => p.BuyerId)
                .Field(p => p.BuyerLegalName)
                //.Field(p => p.BuyerFullName)
                .Field(p => p.BuyerEmail)
                //.Field(p => p.BuyerTaxCode)
                )
                 .Query(query.Customers)
                 .Operator(Operator.And)
                );
            }

            if (query.InvoiceNo != null)
            {
                int.TryParse(query.InvoiceNo, out int outInvoiceNo);
                filters.Add(f => f.Terms(t => t.Field(f => f.InvoiceNo).Terms(outInvoiceNo.ToString("0000000"))));
            }

            if (query.ApproveStatuses != null && query.ApproveStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.ApproveStatus).Terms(query.ApproveStatuses)));

            if (query.InvoiceStatuses != null && query.InvoiceStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.InvoiceStatus.GetHashCode()).Terms(query.InvoiceStatuses)));

            if (query.SignStatuses != null && query.SignStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.SignStatus).Terms(query.SignStatuses)));

            if (!string.IsNullOrEmpty(query.TransactionId))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.TransactionId).Query(query.TransactionId)));

            //if (!string.IsNullOrEmpty(query.Buyê))
            //    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerTaxCode).Query(query.BuyerTaxCode)));

            //if (!string.IsNullOrEmpty(query.BuyerPhoneNumber))
            //    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerPhoneNumber).Query(query.BuyerPhoneNumber)));

            //if (!string.IsNullOrEmpty(query.BuyerBankName))
            //    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerBankName).Query(query.BuyerBankName)));

            //if (!string.IsNullOrEmpty(query.BuyerEmail))
            //    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerEmail).Query(query.BuyerEmail)));

            if (!string.IsNullOrEmpty(query.ErpId))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.ErpId).Query(query.ErpId)));

            if (query.HeaderExtras != null && query.HeaderExtras.Count > 0)
            {
                foreach (var item in query.HeaderExtras)
                {
                    var split = item.Split("-");
                    var fieldName = split.FirstOrDefault();
                    var fieldValue = item.Substring(fieldName.Length + 1, item.Length - fieldName.Length - 1);
                    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldName)).Query(fieldName.ToLower())) &&
                    f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldValue)).Query(fieldValue.ToLower())));
                }
            }

            if (query.CreateFromDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.InvoiceDate).GreaterThanOrEquals(query.CreateFromDate)));

            if (query.CreateToDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.InvoiceDate).LessThanOrEquals(query.CreateToDate)));

            if (query.FromNumber.HasValue)
                filters.Add(f => f.Range(t => t.Field(f => f.Number).GreaterThanOrEquals(query.FromNumber)));

            if (query.ToNumber.HasValue)
                filters.Add(f => f.Range(t => t.Field(f => f.Number).LessThanOrEquals(query.ToNumber)));

            if (!string.IsNullOrEmpty(query.UserNameCreator))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.UserNameCreator).Query(query.UserNameCreator)));

            if (!string.IsNullOrEmpty(query.UserNameCreatorErp))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.CreatorErp).Query(query.UserNameCreatorErp)));

            if (!string.IsNullOrEmpty(query.BuyerBankAccount))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerBankAccount).Query(query.BuyerBankAccount)));

            if (!string.IsNullOrEmpty(query.BuyerCode))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.BuyerCode).Query(query.BuyerCode)));

            //Searching query
            //.From(filters.Count > 1 ? 0 : ((query.SkipCount - 1) * 10))
            var searchResponse = await _elasticClient.SearchAsync<ElastichSearchInvoiceModel>(s => s
                .From(query.SkipCount)
                .Size(query.MaxResultCount)
                .Query(q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                .Field(f => f.TenantId)
                                .Query(tenantCode.ToString())),
                                //bm => bm.DateRange(p => p
                                //    .Field(f => f.InvoiceDate)
                                //    .GreaterThanOrEquals(query.CreateFromDate)
                                //    .LessThanOrEquals(query.CreateToDate)),
                                bm => bm.MultiMatch(multiMatch.FirstOrDefault()),
                                bm => bm.MultiMatch(multiCustomers.FirstOrDefault())
                                //bm => bm.Bool(b => b
                                //    .Must(
                                //    s => MatchAny(s, s => s.TemplateCode, query.AllReadTemplateCodes.Select(x => x.ToString()).ToArray()),
                                //    s => MatchAny(s, s => s.TemplateCode, query.CodeInvoiceTemplates?.Select(x => x.ToString()).ToArray()))
                                ////s => MatchAny(s, s => s.Code, query.Codes?.Select(x => x.ToString()).ToArray()))
                                //)
                                )
                            .Filter(filters)
                            ))
                .Sort(s => s.Descending(f => f.InvoiceDate).Descending(f => f.Number).Descending(f => f.CreationTime))
                );


            return searchResponse;
        }


        public async Task<GetTicketResponse> GetByIdAsync(Guid tenantId, long invoiceId)
        {
            var query = await GenerateDrawGetByIdQuery(tenantId, invoiceId);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<GetTicketResponse>(query);

            if (data != null)
            {
                if (!String.IsNullOrEmpty(data.InvoiceHeaderExtrasJson))
                    data.InvoiceHeaderExtras = JsonConvert.DeserializeObject<List<GetTicketResponse.GetTicketHeaderExtraResponseModel>>(data.InvoiceHeaderExtrasJson);

                if (!String.IsNullOrEmpty(data.InvoiceTaxBreakdownsJson))
                    data.InvoiceTaxBreakdowns = JsonConvert.DeserializeObject<List<GetTicketResponse.GetTicketTaxBreakdownResponseModel>>(data.InvoiceTaxBreakdownsJson);

                if (!String.IsNullOrEmpty(data.InvoiceReferenceJson))
                {
                    var referenceEntity = JsonConvert.DeserializeObject<TicketReferenceEntity>(data.InvoiceReferenceJson);
                    data.InvoiceReferenceId = referenceEntity.InvoiceReferenceId;
                    data.TransactionData = $"{data.SellerTaxCode}|{referenceEntity.TemplateNoReference}|{referenceEntity.SerialNoReference}|{referenceEntity.InvoiceNoReference}|{referenceEntity.InvoiceDateReference:yyyy-MM-dd}";
                    data.InvoiceDateReference = referenceEntity.InvoiceDateReference;
                    data.Content = referenceEntity.Note;
                }

                if (!String.IsNullOrEmpty(data.InvoiceDetailsJson))
                {
                    var invoiceDetails = JsonConvert.DeserializeObject<List<GetTicketResponse.GetTicketDetailResponseModel>>(data.InvoiceDetailsJson);

                    invoiceDetails.ForEach(x =>
                    {
                        if (!String.IsNullOrEmpty(x.InvoiceDetailExtrasJson))
                            x.InvoiceDetailExtras = JsonConvert.DeserializeObject<List<GetTicketResponse.GetTicketDetailExtraResponseModel>>(x.InvoiceDetailExtrasJson);
                    });

                    data.InvoiceDetails = invoiceDetails;
                }
            }


            return data;
        }

        public async Task<GetTicketResponse> GetByIdElasticAsync(Guid tenantId, long invoiceId)
        {
            var data = (await _elasticClient.SearchAsync<GetTicketResponse>(s => s
            .Query(q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                .Field(f => f.TenantId)
                                .Query(tenantId.ToString())),
                                bm => bm.Match(p => p
                                .Field(f => f.Id)
                                .Query(invoiceId.ToString()))
                                )
                            ))))
            .Documents.FirstOrDefault();

            return data;
        }

        public async Task<long> CreateAsync(CreateTicketHeaderDto request, InvoiceSource resource, ContextModel context, InvoiceStatus invoiceStatus, SignStatus signStatus, ApproveStatus approveStatus, TenantInfo tenantInfo, Dictionary<TenantMetaData, MetadataModel> tenantInfoMetadata, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = context.TenantId,
                CreationTime = DateTime.Now,
                CreatorId = context.UserId,

                Source = (short)resource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = _invoiceService.GetTransactionId(resource, request.TransactionId),
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,

                InvoiceStatus = (short)invoiceStatus.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = tenantInfo.Id,
                SellerTaxCode = tenantInfo.TaxCode,
                SellerAddressLine = tenantInfo.Address,
                SellerCountryCode = tenantInfo.Country,
                SellerDistrictName = tenantInfo.District,
                SellerCityName = tenantInfo.City,
                SellerPhoneNumber = tenantInfo.Phone,
                SellerFaxNumber = tenantInfo.Fax,
                SellerEmail = tenantInfo.Emails,
                SellerBankName = tenantInfo.BankName,
                SellerBankAccount = tenantInfo.BankAccount,
                SellerLegalName = tenantInfo.LegalName,
                SellerFullName = tenantInfo.FullNameVi,

                RoundingCurrency = toCurrency.Rounding,
                FromCurrency = fromCurrency.CurrencyCode,
                CurrencyConversion = toCurrency.Conversion,
                ToCurrency = toCurrency.CurrencyCode,
                ExchangeRate = request.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = context.Session.UserInfo.FullName,
                UserNameCreator = context.Session.UserName,

                BuyerId = null,
                BuyerCode = request.BuyerCode?.Trim(),
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail,
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,

                Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            };

            await _repoTicketHeader.InsertAsync(header);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //TODO: NamLD xem lại
            ////Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!headerFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = headerFields[item.FieldName];
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = context.TenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        await _repoTicketHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}



            //Details
            var details = new List<TicketDetailEntity>();
            foreach (var item in request.InvoiceDetails)
            {
                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim())
                                ? null
                                : ((products != null && products.ContainsKey(item.ProductCode?.Trim())) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value)) ? productTypes[product.ProductTypeId.Value] : null;

                details.Add(new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = context.TenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product == null ? -1 : product.Id,
                    ProductName = item.ProductName,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = unit == null ? -1 : unit.Id,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    RoundingUnit = unit == null ? 4 : unit.Rounding,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                });
            }

            await _repoTicketDetail.InsertManyAsync(details);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //TODO: NamLD xem lại

            //Detail Extras
            //Sau khi SaveChanges, các Id của bảng InvoiceDetail sẽ có dữ liệu
            //Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
            var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    var items = indexes[detail.Index];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        //Nếu ko có DetailField thì bỏ qua, ko insert
            //        if (!detailFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = detailFields[item.FieldName];
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            TenantId = context.TenantId,
            //            InvoiceDetailId = detail.Id,
            //            InvoiceDetailFieldId = field.Id,
            //            Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    await _repoTicketDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = tax.Item1,
                    TenantId = context.TenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = item.VatAmountBackUp,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                await _repoTicketTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            await transaction.CompleteAsync();

            return header.Id;
        }

        public async Task<List<long>> CreateBatchAsync(CreateTicketByGroupCustomerRequest request, List<CustomerEntity> Customers, InvoiceSource resource, ContextModel context, InvoiceStatus invoiceStatus, SignStatus signStatus, ApproveStatus approveStatus, TenantInfo tenantInfo, Dictionary<TenantMetaData, MetadataModel> tenantInfoMetadata, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                IsTransactional = true
            }, true);

            var invoiceIds = new List<long>();
            var detailIds = new List<long>();

            var lastInvoiceId = await _repoTicketHeader.MaxAsync(x => x.Id);
            var lastDetailId = await _repoTicketDetail.MaxAsync(x => x.Id);

            for (var i = 0; i < Customers.Count; i++)
            {
                invoiceIds.Add(lastInvoiceId++);
            }

            var detailNum = Customers.Count * request.InvoiceDetails.Count;
            for (var i = 0; i < detailNum; i++)
            {
                detailIds.Add(lastDetailId++);
            }

            var j = 0;
            var headers = new List<TicketHeaderEntity>();
            foreach (var customer in Customers)
            {
                //Header
                headers.Add(new TicketHeaderEntity
                {
                    Id = invoiceIds[j],
                    TenantId = context.TenantId,
                    CreationTime = DateTime.Now,
                    CreatorId = context.UserId,

                    Source = (short)resource.GetHashCode(),
                    BatchId = Guid.NewGuid(),
                    ErpId = request.IdErp,
                    CreatorErp = request.CreatorErp,
                    TransactionId = _invoiceService.GetTransactionId(resource, request.IdTransaction),
                    InvoiceTemplateId = template.Id,
                    TemplateNo = template.TemplateNo,
                    SerialNo = template.SerialNo,
                    Note = request.Note,
                    InvoiceDate = request.InvoiceDate,
                    InvoiceStatus = (short)invoiceStatus.GetHashCode(),
                    SignStatus = (short)signStatus.GetHashCode(),
                    ApproveStatus = (short)approveStatus.GetHashCode(),

                    //Lưu lúc sinh số
                    RegistrationHeaderId = null,
                    RegistrationDetailId = null,
                    Number = null,
                    InvoiceNo = null,

                    SellerId = tenantInfo.Id,
                    SellerTaxCode = tenantInfo.TaxCode,
                    SellerAddressLine = tenantInfo.Address,
                    SellerCountryCode = tenantInfo.Country,
                    SellerDistrictName = tenantInfo.District,
                    SellerCityName = tenantInfo.City,
                    SellerPhoneNumber = tenantInfo.Phone,
                    SellerFaxNumber = tenantInfo.Fax,
                    SellerEmail = tenantInfo.Emails,
                    SellerBankName = tenantInfo.BankName,
                    SellerBankAccount = tenantInfo.BankAccount,
                    SellerLegalName = tenantInfo.LegalName,
                    SellerFullName = tenantInfo.FullNameVi,

                    RoundingCurrency = toCurrency.Rounding,
                    FromCurrency = fromCurrency.CurrencyCode,
                    CurrencyConversion = toCurrency.Conversion,
                    ToCurrency = toCurrency.CurrencyCode,
                    ExchangeRate = request.ExchangeRate,
                    PaymentMethod = request.PaymentMethod,
                    PaymentDate = request.InvoiceDate,
                    PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                    PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                    TotalAmount = request.TotalAmount,
                    TotalPaymentAmount = request.TotalPaymentAmount,
                    FullNameCreator = context.Session.UserInfo.FullName,
                    UserNameCreator = context.Session.UserName,

                    BuyerId = customer.Id,
                    BuyerCode = customer.CustomerCode,
                    BuyerFullName = customer.FullName,
                    BuyerLegalName = customer.LegalName,
                    BuyerTaxCode = customer.TaxCode,
                    BuyerAddressLine = customer.Address,
                    BuyerDistrictName = customer.District,
                    BuyerCityName = customer.City,
                    BuyerPhoneNumber = customer.Phone,
                    BuyerFaxNumber = customer.Fax,
                    BuyerEmail = customer.Email,
                    BuyerBankName = customer.BankName,
                    BuyerBankAccount = customer.BankAccount,

                    TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                    TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                    TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                    TotalVatAmount = request.TotalVatAmount
                });

                j++;
            }

            //var headerExtras = new List<TicketHeaderExtraEntity>();
            //var firstHeaderExtras = new List<TicketHeaderExtraEntity>();

            var details = new List<TicketDetailEntity>();
            var firstDetails = new List<TicketDetailEntity>();

            //var detailExtras = new List<TicketDetailExtraEntity>();
            //var firstDetailExtras = new List<TicketDetailExtraEntity>();

            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();

            var k = 0;
            var d = 0;
            foreach (var header in headers)
            {
                //TODO: NamLD xem lại
                ////Header Extras
                //if (k == 0)
                //{
                //    if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
                //    {
                //        foreach (var item in request.InvoiceHeaderExtras)
                //        {
                //            //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                //            if (!headerFields.ContainsKey(item.FieldName))
                //                continue;

                //            var field = headerFields[item.FieldName];
                //            headerExtras.Add(new TicketHeaderExtraEntity
                //            {
                //                FieldValue = item.FieldValue,
                //                InvoiceHeaderFieldId = field.Id,
                //                InvoiceHeaderId = header.Id,
                //                TenantId = context.TenantId,
                //            });
                //        }

                //        if (headerExtras != null && headerExtras.Any())
                //            firstHeaderExtras = headerExtras;
                //    }
                //}
                //else
                //{
                //    if (firstHeaderExtras != null && firstHeaderExtras.Any())
                //    {
                //        firstHeaderExtras.ForEach(x => x.InvoiceHeaderId = header.Id);
                //        headerExtras.AddRange(firstHeaderExtras);
                //    }
                //}


                //Details
                if (k == 0)
                {
                    foreach (var item in request.InvoiceDetails)
                    {
                        var unit = units.ContainsKey(item.UnitName?.Trim()) ? units[item.UnitName?.Trim()] : null;
                        var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                        ProductTypeEntity productType = null;
                        if (product != null && product.ProductTypeId != null)
                            productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                        details.Add(new TicketDetailEntity
                        {
                            Id = detailIds[d],
                            Index = item.Index,
                            TenantId = context.TenantId,
                            InvoiceHeaderId = header.Id,
                            Amount = item.Amount,
                            DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                            DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                            Note = item.Note,
                            PaymentAmount = item.PaymentAmount,
                            ProductId = product == null ? -1 : product.Id,
                            ProductName = item.ProductName,
                            ProductCode = item.ProductCode?.Trim(),
                            Quantity = item.Quantity,
                            UnitId = unit == null ? -1 : unit.Id,
                            UnitName = item.UnitName?.Trim(),
                            UnitPrice = item.UnitPrice,
                            RoundingUnit = unit == null ? 4 : unit.Rounding,
                            HideQuantity = productType == null ? false : productType.HideQuantity,
                            HideUnit = productType == null ? false : productType.HideUnit,
                            HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                            VatAmount = item.VatAmount,
                            VatPercent = item.VatPercent,
                            VatPercentDisplay = taxes[item.VatPercent].Item2
                        });

                        d++;
                    }

                    firstDetails = details;
                }
                else
                {
                    firstDetails.ForEach(x =>
                    {
                        x.Id = detailIds[d];
                        x.InvoiceHeaderId = header.Id;
                        d++;
                    });

                    details.AddRange(firstDetails);
                }


                //TODO: NamLD xem lại
                ////Detail Extras
                ////Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
                //var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
                //if (k == 0)
                //{
                //    foreach (var detail in details)
                //    {
                //        var items = indexes[detail.Index];

                //        //Nếu ko có DetailExtra thì bỏ qua, ko insert
                //        if (items == null || items.Count == 0)
                //            continue;

                //        foreach (var item in items)
                //        {
                //            //Nếu ko có DetailField thì bỏ qua, ko insert
                //            if (!detailFields.ContainsKey(item.FieldName))
                //                continue;

                //            var field = detailFields[item.FieldName];
                //            detailExtras.Add(new TicketDetailExtraEntity
                //            {
                //                FieldValue = item.FieldValue,
                //                TenantId = context.TenantId,
                //                InvoiceDetailId = detail.Id,
                //                InvoiceDetailFieldId = field.Id
                //            });
                //        }

                //        if (detailExtras != null && detailExtras.Any())
                //            firstDetailExtras = detailExtras;
                //    }
                //}
                //else
                //{
                //    if (firstDetailExtras != null && firstDetailExtras.Any())
                //    {
                //        for (var m = 1; m < details.Count; m++)
                //        {
                //            firstDetailExtras.ForEach(x => x.InvoiceDetailId = details[m].Id);
                //            detailExtras.AddRange(firstDetailExtras);
                //        }
                //    }
                //}



                //TaxBreakdowns
                foreach (var item in request.InvoiceTaxBreakdowns)
                {
                    var tax = taxes[item.VatPercent];
                    taxBreakdowns.Add(new TicketTaxBreakdownEntity
                    {
                        InvoiceHeaderId = header.Id,
                        Name = tax.Item1,
                        TenantId = context.TenantId,
                        VatAmount = item.VatAmount,
                        VatAmountBackUp = item.VatAmountBackUp,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = tax.Item2
                    });
                }

                k++;
            }

            await _repoTicketHeader.InsertManyAsync(headers);

            if (taxBreakdowns.Count > 0)
                await _repoTicketTaxBreakdown.InsertManyAsync(taxBreakdowns);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();

            return invoiceIds;
        }

        public async Task<long> ReplaceAsync(CreateReplaceTicketRequest request, TicketHeaderEntity rootInvoice, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource resource, ContextModel context, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                IsTransactional = true
            }, true);

            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = context.TenantId,
                CreationTime = DateTime.Now,
                CreatorId = context.UserId,

                Source = (short)resource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = rootInvoice.TransactionId,
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceStatus = (short)InvoiceStatus.ThayThe.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = rootInvoice.SellerId,
                SellerTaxCode = rootInvoice.SellerTaxCode,
                SellerAddressLine = rootInvoice.SellerAddressLine,
                SellerCountryCode = rootInvoice.SellerCountryCode,
                SellerDistrictName = rootInvoice.SellerDistrictName,
                SellerCityName = rootInvoice.SellerCityName,
                SellerPhoneNumber = rootInvoice.SellerPhoneNumber,
                SellerFaxNumber = rootInvoice.SellerFaxNumber,
                SellerEmail = rootInvoice.SellerEmail,
                SellerBankName = rootInvoice.SellerBankName,
                SellerBankAccount = rootInvoice.SellerBankAccount,
                SellerLegalName = rootInvoice.SellerLegalName,
                SellerFullName = rootInvoice.SellerFullName,

                RoundingCurrency = toCurrency.Rounding,
                FromCurrency = fromCurrency.CurrencyCode,
                CurrencyConversion = toCurrency.Conversion,
                ToCurrency = toCurrency.CurrencyCode,
                ExchangeRate = request.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.ThayThe),
                PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(context.TenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.ThayThe),
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = context.Session.UserInfo.FullName,
                UserNameCreator = context.Session.UserName,

                BuyerId = null,
                BuyerCode = request.BuyerCode?.Trim(),
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail,
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,

                //DocumentDate = request.DocumentDate,
                //DocumentNo = request.DocumentNo,
                //DocumentReason = request.DocumentReason,
                //IdFileDocument = request.IdFileDocument,

                //TransactionData = $"{rootInvoice.SellerTaxCode}|{rootInvoice.TemplateNo}|{rootInvoice.SerialNo}|{rootInvoice.InvoiceNo}|{rootInvoice.InvoiceDate:yyyy-MM-dd}",
            };

            await _repoTicketHeader.InsertAsync(header);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //TODO: NamLD xem lại
            ////Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!headerFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = headerFields[item.FieldName];
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = context.TenantId,
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        await _repoTicketHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}

            //Details
            var details = new List<TicketDetailEntity>();
            foreach (var item in request.InvoiceDetails)
            {
                var unit = units.ContainsKey(item.UnitName?.Trim()) ? units[item.UnitName?.Trim()] : null;
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                details.Add(new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = context.TenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product == null ? -1 : product.Id,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    UnitId = unit == null ? -1 : unit.Id,
                    UnitPrice = item.UnitPrice,
                    UnitName = item.UnitName?.Trim(),
                    RoundingUnit = unit == null ? 4 : unit.Rounding,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2
                });
            }

            await _repoTicketDetail.InsertManyAsync(details);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Detail Extras
            //Sau khi SaveChanges, các Id của bảng InvoiceDetail sẽ có dữ liệu
            //Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
            var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            
            //TODO: NamLD xem lại
            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    var items = indexes[detail.Index];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        //Nếu ko có DetailField thì bỏ qua, ko insert
            //        if (!detailFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = detailFields[item.FieldName];
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceDetailId = detail.Id,
            //            InvoiceDetailFieldId = field.Id,
            //            TenantId = context.TenantId
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    await _repoTicketDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = tax.Item1,
                    TenantId = context.TenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = item.VatAmountBackUp,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                await _repoTicketTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            //FileDocument
            //var fileDocument = new TicketDocumentInfo
            //{
            //    FileCode = request.IdFileDocument,
            //    InvoiceCode = code,
            //    TenantId = context.TenantId,
            //    DocumentDate = request.DocumentDate,
            //    DocumentNo = request.DocumentNo,
            //    DocumentReason = request.DocumentReason,
            //};
            //var repoFile = _ticketUoW.GetRepository<ITicketDocumentInfoRepository>();
            //await repoFile.InsertAsync(fileDocument);
            //await _ticketUoW.CommitAsync();

            //InvoiceReference
            var invoiceReference = new TicketReferenceEntity
            {
                CreationTime = DateTime.Now,
                InvoiceDateReference = rootInvoice.InvoiceDate,
                InvoiceHeaderId = request.Id, // TODO: Xem lại logic
                InvoiceNoReference = rootInvoice.InvoiceNo,
                InvoiceReferenceId = rootInvoice.Id,
                NumberReference = rootInvoice.Number ?? 0,
                SerialNoReference = rootInvoice.SerialNo,
                TemplateNoReference = rootInvoice.TemplateNo,
                TenantId = context.TenantId,
                InvoiceStatus = InvoiceStatus.ThayThe.GetHashCode(),
                Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                Note = request.Content
            };
            await _repoTicketReference.InsertAsync(invoiceReference);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();

            return header.Id;
        }

        public async Task<long> UpdateAsync(UpdateTicketHeaderDto request, TicketHeaderEntity header, List<TicketDetailEntity> details, List<TicketTaxBreakdownEntity> taxBreakdowns, CurrencyEntity toCurrency, Dictionary<string, long> headerFields, Dictionary<string, long> detailFields, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                IsTransactional = true
            }, true);

            header.ToCurrency = toCurrency.CurrencyCode;
            header.InvoiceDate = request.InvoiceDate;
            header.Note = request.Note;
            header.PaymentMethod = request.PaymentMethod;
            header.RoundingCurrency = toCurrency.Rounding;
            header.CurrencyConversion = toCurrency.Conversion;
            header.ExchangeRate = request.ExchangeRate;
            header.TotalAmount = request.TotalAmount;
            header.TotalVatAmount = request.TotalVatAmount;
            header.TotalPaymentAmount = request.TotalPaymentAmount;
            header.TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax;
            header.TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax;
            header.TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax;
            header.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(header.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus)));
            header.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(header.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus)));
            //header.BuyerId = request.BuyerId?.Trim();
            header.BuyerCode = request.BuyerCode;
            header.BuyerEmail = request.BuyerEmail;
            header.BuyerFullName = request.BuyerFullName;
            header.BuyerLegalName = request.BuyerLegalName;
            header.BuyerTaxCode = request.BuyerTaxCode?.Trim();
            header.BuyerAddressLine = request.BuyerAddressLine;
            header.BuyerDistrictName = request.BuyerDistrictName;
            header.BuyerCityName = request.BuyerCityName;
            header.BuyerPhoneNumber = request.BuyerPhoneNumber;
            header.BuyerFaxNumber = request.BuyerFaxNumber;
            header.BuyerBankAccount = request.BuyerBankAccount;
            header.BuyerBankName = request.BuyerBankName;

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    TenantId = header.TenantId,
                    InvoiceHeaderId = header.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        TenantId = header.TenantId,
                        InvoiceDetailFieldId = detailFields[y.FieldName],
                        FieldValue = y.FieldValue,
                    }).ToList()
                });
            }

            var commandHeaderExtras = request.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            {
                TenantId = header.TenantId,
                InvoiceHeaderId = header.Id,
                FieldValue = x.FieldValue,
                InvoiceHeaderFieldId = headerFields[x.FieldName],
            })
            .ToList();

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = header.TenantId,
                InvoiceHeaderId = header.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmountBackUp,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();


            await UpdateInvoiceDetailAsync(details, commandInvoiceDetails);
            //await UpdateInvoiceHeaderExtraAsync(headerExtras, commandHeaderExtras); //TODO: NamLD xem lại
            await UpdateInvoiceTaxbreakdownAsync(taxBreakdowns, commandTaxBreakDowns);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();

            return header.Id;
        }

        public async Task UpdateReplaceAsync(TicketHeaderEntity entityHeader, List<TicketDetailEntity> entityInvoiceDetails, List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, UpdateReplaceTicketRequest request, CurrencyEntity toCurrency, Dictionary<string, long> headerFields, Dictionary<string, long> detailFields, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                IsTransactional = true
            }, true);

            entityHeader.ToCurrency = toCurrency.CurrencyCode;
            entityHeader.InvoiceDate = request.InvoiceDate;
            entityHeader.Note = request.Note;
            entityHeader.PaymentMethod = request.PaymentMethod;
            entityHeader.RoundingCurrency = toCurrency.Rounding;
            entityHeader.CurrencyConversion = toCurrency.Conversion;
            entityHeader.ExchangeRate = request.ExchangeRate;
            entityHeader.TotalAmount = request.TotalAmount;
            entityHeader.TotalVatAmount = request.TotalVatAmount;
            entityHeader.TotalPaymentAmount = request.TotalPaymentAmount;
            entityHeader.TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax;
            entityHeader.TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax;
            entityHeader.TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax;
            entityHeader.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.BuyerCode = request.BuyerCode?.Trim();
            entityHeader.BuyerEmail = request.BuyerEmail;
            entityHeader.BuyerFullName = request.BuyerFullName;
            entityHeader.BuyerLegalName = request.BuyerLegalName;
            entityHeader.BuyerTaxCode = request.BuyerTaxCode?.Trim();
            entityHeader.BuyerAddressLine = request.BuyerAddressLine;
            entityHeader.BuyerDistrictName = request.BuyerDistrictName;
            entityHeader.BuyerCityName = request.BuyerCityName;
            entityHeader.BuyerPhoneNumber = request.BuyerPhoneNumber;
            entityHeader.BuyerFaxNumber = request.BuyerFaxNumber;
            entityHeader.BuyerBankAccount = request.BuyerBankAccount;
            entityHeader.BuyerBankName = request.BuyerBankName;

            var commandInvoiceDetails = new List<TicketDetailModel>();

            foreach (var item in request.InvoiceDetails)
            {
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    ProductCode = item.ProductCode?.Trim(),
                    Index = item.Index,
                    InvoiceHeaderId = entityHeader.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = entityHeader.TenantId,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue,
                        InvoiceDetailFieldId = detailFields[y.FieldName],
                        TenantId = entityHeader.TenantId
                    }).ToList()
                });

            }

            var commandHeaderExtras = request.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            {
                FieldValue = x.FieldValue,
                InvoiceHeaderId = entityHeader.Id,
                TenantId = entityHeader.TenantId,
                InvoiceHeaderFieldId = headerFields[x.FieldName],
            })
            .ToList();

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = entityHeader.TenantId,
                InvoiceHeaderId = entityHeader.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmountBackUp,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();


            await UpdateInvoiceDetailAsync(entityInvoiceDetails, commandInvoiceDetails);
            //await UpdateInvoiceHeaderExtraAsync(entityInvoiceHeaderExtras, commandHeaderExtras); //TODO: NamLD xem lại
            await UpdateInvoiceTaxbreakdownAsync(entityInvoiceTaxBreakDowns, commandTaxBreakDowns);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();
        }

        public async Task<InfosNeedCreateTicketModel> GetInfosBeforePublishCreateTicket(Guid tenantId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{ String.Join("','", productCodes) }') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Where(x => !string.IsNullOrEmpty(x)).Select(x => x.ToUpper()?.Replace("'", "''")).ToList();

                if (unitNames.Any())
                    andUnitName.Append($@" AND ""NormalizedName"" IN('{ String.Join("','", unitNames) }') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                  SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                  FROM ""Product""                                                                     
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andProductCode}                                                                 
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              ProductTypes AS(                                                                         
                                  SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                  FROM ""ProductType""                                                                 
                                  WHERE                                                                                
                                      ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              Units AS(                                                                                
                                  SELECT ""Id"", ""Name"", ""Rounding""                                                
                                  FROM ""Unit""                                                                        
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andUnitName}                                                                    
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              HeaderFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""TicketHeaderField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              ),                                                                                       
                              DetailFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""TicketDetailField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              )                                                                                        
                              SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                              UNION ALL                                                                                                    
                              SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                              UNION ALL                                                                                                    
                              SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Units       
                              UNION ALL                                                                                                    
                              SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                              UNION ALL                                                                                                    
                              SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedCreateTicketModel();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var units = data.First(x => x.Type == 3);
            var headerFields = data.First(x => x.Type == 4);
            var detailFields = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(units.Data))
                result.Units = JsonConvert.DeserializeObject<List<UnitEntity>>(units.Data).ToDictionary(x => x.Name, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<TicketHeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<TicketDetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            return result;
        }

        public async Task<InfosNeedUpdateTicketModel> GetInfosBeforePublishUpdateTicket(Guid tenantId, long invoiceId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{ String.Join("','", productCodes) }') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Where(x => !string.IsNullOrEmpty(x)).Select(x => x.ToUpper()?.Replace("'", "''")).ToList();

                if (unitNames.Any())
                    andUnitName.Append($@" AND ""NormalizedName"" IN('{ String.Join("','", unitNames) }') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                FROM ""Product""                                                                     
                                WHERE                                                                                
                                    ""TenantId"" = '{rawTenantId}'                                                   
                                    {andProductCode}                                                                 
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            ProductTypes AS(                                                                         
                                SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                FROM ""ProductType""                                                                 
                                WHERE                                                                                
                                    ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            HeaderFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""TicketHeaderField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),                                                                                       
                            DetailFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""TicketDetailField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),
                            Details AS(                                                                         
                                SELECT *                                                         
                                FROM ""TicketDetail""                                                        
                                WHERE ""InvoiceHeaderId"" = {invoiceId}                                               
                            ),
                            TaxBreakdowns AS(                                                                         
                                SELECT *                                                        
                                FROM ""TicketTaxBreakdown""                                                        
                                WHERE ""InvoiceHeaderId"" = {invoiceId}                                             
                            )
                            SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                            UNION ALL                                                                                                    
                            SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                            UNION ALL                                                                                                    
                            SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                            UNION ALL                                                                                                    
                            SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields
                            UNION ALL
                            SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM TaxBreakdowns 
                            UNION ALL
                            SELECT 6 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Details
                    ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedUpdateTicketModel();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var headerFields = data.First(x => x.Type == 3);
            var detailFields = data.First(x => x.Type == 4);
            var taxBreakdowns = data.First(x => x.Type == 5);
            var details = data.First(x => x.Type == 6);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<TicketHeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<TicketDetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(taxBreakdowns.Data))
                result.TaxBreakdowns = JsonConvert.DeserializeObject<List<TicketTaxBreakdownEntity>>(taxBreakdowns.Data);

            if (!String.IsNullOrEmpty(details.Data))
            {
                var detailInfos = JsonConvert.DeserializeObject<List<TicketDetailInfo>>(details.Data);
                if (detailInfos != null && detailInfos.Any())
                {
                    result.Details = detailInfos.Select(x => new TicketDetailEntity
                    {
                        Id = x.Id,
                        Amount = x.Amount,
                        DiscountAmountBeforeTax = x.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = x.DiscountPercentBeforeTax,
                        Index = x.Index,
                        Note = x.Note,
                        InvoiceHeaderId = x.InvoiceHeaderId,
                        RoundingUnit = x.RoundingUnit,
                        IsPromotion = x.IsPromotion,
                        ProductId = x.ProductId,
                        ProductType = x.ProductType,
                        PaymentAmount = x.PaymentAmount,
                        ProductCode = x.ProductCode?.Trim(),
                        ProductName = x.ProductName,
                        Partition = x.Partition,
                        Quantity = x.Quantity,
                        UnitName = x.UnitName?.Trim(),
                        VatAmount = x.VatAmount,
                        VatPercent = x.VatPercent,
                        VatPercentDisplay = x.VatPercentDisplay,
                        HideQuantity = x.HideQuantity,
                        HideUnit = x.HideUnit,
                        HideUnitPrice = x.HideUnitPrice,
                        UnitPrice = x.UnitPrice,
                        ExtraProperties = !string.IsNullOrEmpty(x.ExtraProperties) ? new ExtraPropertyDictionary(JsonConvert.DeserializeObject<Dictionary<string, object>>(x.ExtraProperties)) : null
                    }).ToList();
                }
            }

            return result;
        }

        private async Task UpdateInvoiceDetailAsync(IEnumerable<TicketDetailEntity> entityInvoiceDetails, List<TicketDetailModel> modelDetails)
        {
            var removeDetails = new List<TicketDetailEntity>();
            //var removeDetailExtras = new List<TicketDetailExtraEntity>();

            //var detailExtras = (await _repoInvoiceDetailExtra.QueryByInvoiceDetailIdsAsync(entityInvoiceDetails.Select(x => x.Id).ToList()))
            //                   .GroupBy(x => x.InvoiceDetailId)
            //                   .ToDictionary(x => x.Key, x => x.ToList());

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityInvoiceDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityInvoiceDetails)
            {
                if (duplicate.Contains(item.Index)) // sửa
                {
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    item.Amount = detail.Amount;
                    item.DiscountAmountBeforeTax = detail.DiscountAmountBeforeTax;
                    item.DiscountPercentBeforeTax = detail.DiscountPercentBeforeTax;
                    item.Index = detail.Index;
                    item.Note = detail.Note;
                    item.PaymentAmount = detail.PaymentAmount;
                    item.ProductCode = detail.ProductCode?.Trim();
                    item.ProductName = detail.ProductName;
                    item.ProductType = detail.ProductType;
                    item.Quantity = detail.Quantity;
                    item.UnitName = detail.UnitName?.Trim();
                    item.VatAmount = detail.VatAmount;
                    item.VatPercent = detail.VatPercent;
                    item.VatPercentDisplay = detail.VatPercentDisplay;
                    item.HideQuantity = detail.HideQuantity;
                    item.HideUnit = detail.HideUnit;
                    item.HideUnitPrice = detail.HideUnitPrice;
                    item.UnitPrice = detail.UnitPrice;

                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                }
            }

            //thêm mới
            foreach (var item in newDetails)
            {
                var newDetail = new TicketDetailEntity
                {
                    Amount = item.Amount,
                    Note = item.Note,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    Index = item.Index,
                    Quantity = item.Quantity,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    PaymentAmount = item.PaymentAmount,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = item.VatPercentDisplay,
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    TenantId = item.TenantId,
                    HideQuantity = item.HideQuantity,
                    HideUnit = item.HideUnit,
                    HideUnitPrice = item.HideUnitPrice,
                };

                await _repoTicketDetail.InsertAsync(newDetail);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            await _repoTicketDetail.DeleteManyAsync(removeDetails);
        }

        private async Task UpdateInvoiceTaxbreakdownAsync(List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, List<TicketTaxBreakdownModel> invoiceTaxBreakdowns)
        {
            var modelTaxbreakDowns = invoiceTaxBreakdowns.GroupBy(x => x.VatPercent).ToDictionary(x => x.Key, x => x.ToList());
            var lstAdd = new List<TicketTaxBreakdownEntity>();
            var lstModify = new List<TicketTaxBreakdownEntity>();
            var lstRemove = new List<TicketTaxBreakdownEntity>();

            //Sửa
            foreach (var item in entityInvoiceTaxBreakDowns)
            {
                if (modelTaxbreakDowns.ContainsKey(item.VatPercent))
                {
                    var taxBreakdownModel = modelTaxbreakDowns[item.VatPercent].First();
                    item.VatAmount = taxBreakdownModel.VatAmount;
                    item.VatAmountBackUp = taxBreakdownModel.VatAmountBackUp;
                    item.Name = taxBreakdownModel.Name;

                    lstModify.Add(item);
                }
                else //xóa
                    lstRemove.Add(item);
            }

            //thêm mới
            foreach (var item in modelTaxbreakDowns)
            {
                var newTaxBreakDown = item.Value.FirstOrDefault();
                if (!entityInvoiceTaxBreakDowns.Any(x => x.VatPercent == item.Key))
                {
                    lstAdd.Add(new TicketTaxBreakdownEntity
                    {
                        Name = newTaxBreakDown.Name,
                        VatPercent = newTaxBreakDown.VatPercent,
                        VatAmount = newTaxBreakDown.VatAmount,
                        VatAmountBackUp = newTaxBreakDown.VatAmountBackUp,
                        InvoiceHeaderId = newTaxBreakDown.InvoiceHeaderId,
                        VatPercentDisplay = newTaxBreakDown.VatPercentDisplay,
                        TenantId = newTaxBreakDown.TenantId
                    });
                }
            }

            if (lstAdd != null && lstAdd.Any())
                await _repoTicketTaxBreakdown.InsertManyAsync(lstAdd);

            if (lstModify != null && lstModify.Any())
                await _repoTicketTaxBreakdown.UpdateManyAsync(lstModify);

            if (lstRemove != null && lstRemove.Any())
                await _repoTicketTaxBreakdown.DeleteManyAsync(lstRemove);
        }

        private async Task<string> GenerateDrawGetListQuery(Guid tenantId, Guid userId, PagingTicketRequest input)
        {
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            if (input.IsNullInvoice)
                condition.Append($@"AND ""Number"" IS NULL ");
            else
                condition.Append($@"AND ""Number"" IS NOT NULL ");

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (
                                    LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""BuyerEmail"") LIKE LOWER('%{q}%') 
                                    OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{q}%') 
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""PaymentMethod"") LIKE LOWER(N'%{q}%') 
                                    OR ""BuyerPhoneNumber"" LIKE '%{q}%' 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%') 
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    OR ""BuyerCode"" LIKE N'%{q}%' 
                                    ) ");

                //$"OR TemplateNo LIKE '%{input.Keyword}%' 
                //$"OR InvoiceStatus LIKE '%{input.Keyword}%' 
                //$"OR SignStatus LIKE '%{input.Keyword}%' 

            }

            if (!string.IsNullOrEmpty(input.BuyerFullName))
            {
                condition.Append($@"AND LOWER(""BuyerFullName"") = N'{input.BuyerFullName.ToLower()}'");
            }

            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssuedTime.HasValue)
            {
                var issueFromDate = input.IssuedTime.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" = '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueFromDate.HasValue)
            {
                var issueFromDate = input.IssueFromDate.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" >= '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueToDate.HasValue)
            {
                var issueToDate = input.IssueToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" < '{issueToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CancelFromDate.HasValue)
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");

            if (input.CancelToDate.HasValue)
            {
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");
            }

            //if (input.IssuedAt.HasValue)
            //{
            //    var issuedAt = input.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt);
            //}

            if (!string.IsNullOrEmpty(input.Customers))
            {
                var customers = input.Customers?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (""BuyerCode"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customers}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customers}%' 
                                        OR ""BuyerFullName"" LIKE N'%{customers}%'
                                        OR ""BuyerPhoneNumber"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customers}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.Customer))
            {
                var customer = input.Customer?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (""BuyerCode"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customer}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customer}%' 
                                        OR ""BuyerFullName"" LIKE N'%{customer}%'
                                        OR ""BuyerPhoneNumber"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customer}%') 
                                    ) ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            else
            {
                //lấy danh sách mẫu dc xem
                condition.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {RegistrationInvoiceType.HDKhac.GetHashCode()} 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (input.TotalPaymentAmount.HasValue)
            {
                condition.Append($@"AND ""TotalPaymentAmount"" = {input.TotalPaymentAmount.Value} ");
            }

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.IsGetDataForInvoiceError.HasValue && input.IsGetDataForInvoiceError.Value)
            {
                condition.Append($@"AND ""InvoiceStatus"" NOT IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) ");
            }

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.VerificationCodeStatuses != null && input.VerificationCodeStatuses.Any())
            {
                if (input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (input.Source != null && input.Source.Any())
                condition.Append($@"AND ""Source"" In ({String.Join(",", input.Source)}) ");

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (!string.IsNullOrEmpty(input.TransactionId))
                condition.Append($@"AND ""TransactionId"" = '{input.TransactionId}' ");

            if (!string.IsNullOrEmpty(input.ErpId))
                condition.Append($@"AND ""ErpId"" = '{input.ErpId}' ");

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            if (input.PrintedTime.HasValue)
            {
                condition.Append($@"AND ""PrintedTime"" IS NOT NULL 
                                          AND ""PrintedTime"" >= '{input.PrintedTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                          AND ""PrintedTime"" < DATE '{input.PrintedTime.Value.ToString("yyyyy-MM-dd", CultureInfo.GetCultureInfo("en-US"))}' + 1");
            }

            if (!string.IsNullOrEmpty(input.UserNamePrinted))
                condition.Append($@"AND ""UserNamePrinted"" LIKE '%{input.UserNamePrinted}%' ");

            if (!string.IsNullOrEmpty(input.BuyerBankAccount))
                condition.Append($@"AND ""BuyerBankAccount"" LIKE '%{input.BuyerBankAccount}%' ");

            if (!string.IsNullOrEmpty(input.BuyerCode))
                condition.Append($@"AND ""BuyerCode"" LIKE '%{input.BuyerCode}%' ");

            if (input.IsDeclared != null && input.IsDeclared.Any())
            {
                condition.Append($@" AND ""IsDeclared"" IN ({String.Join(",", input.IsDeclared)} ) ");
            }


            var sql = new StringBuilder();

            //  "FileDocumentId",   
            sql.Append($@"SELECT *  FROM
                            (SELECT * FROM (
                                SELECT A.*, rownum rn                                                          
                                FROM                                                                           
                                    (                                                                          
                                        SELECT  ""Id"",                                                         
                                                ""ErpId"",                                                       
                                                ""TransactionId"",                                             
                                                ""TemplateNo"",                                                
                                                ""SerialNo"",                                                  
                                                ""InvoiceNo"",                                                 
                                                ""Number"",                                                    
                                                ""InvoiceStatus"",                                             
                                                ""SignStatus"",                                                
                                                ""ApproveStatus"",                                             
                                                ""ApproveCancelStatus"",                                             
                                                ""ApproveDeleteStatus"",                                             
                                                ""InvoiceDate"",                                               
                                                ""TotalAmount"",                                               
                                                ""TotalVatAmount"",                                            
                                                ""TotalPaymentAmount"",                                        
                                                ""BuyerFullName"",                                             
                                                ""BuyerEmail"",                                                
                                                ""BuyerCode"",                                                 
                                                ""PrintedTime"",                                               
                                                ""FullNameCreator"",                                           
                                                ""UserNameCreator"",                                           
                                                ""CreatorErp"",                                                
                                                ""Note"",                                                      
                                                ""Source"",                                                    
                                                ""IsViewed"",                                                  
                                                ""IsOpened"",                                                  
                                                ""IsDeclared"",                                                  
                                                ""VerificationCode"",                                                  
                                                ""ExtraProperties"",                                                  
                                                COUNT(*) OVER () TotalItems                                   
                                        FROM ""TicketHeader""                                               
                                        WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {condition}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A                                                                        
                                WHERE rownum <= {input.SkipCount + input.MaxResultCount} 
                                )
                            WHERE rn > {input.SkipCount}
                        ) 
                        InvoiceHeader
                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC ");

            return sql.ToString();
        }
        private string GeneratCaculatePaymentAmountQuery(Guid tenantId, Guid userId, PagingTicketRequest input)
        {
            #region condition tim kiem
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            if (input.IsNullInvoice)
                condition.Append($@"AND ""Number"" IS NULL ");
            else
                condition.Append($@"AND ""Number"" IS NOT NULL ");

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (
                                    LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""BuyerEmail"") LIKE LOWER('%{q}%') 
                                    OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{q}%') 
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""PaymentMethod"") LIKE LOWER(N'%{q}%') 
                                    OR ""BuyerPhoneNumber"" LIKE '%{q}%' 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%') 
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    OR ""BuyerCode"" LIKE N'%{q}%' 
                                    ) ");

                //$"OR TemplateNo LIKE '%{input.Keyword}%' 
                //$"OR InvoiceStatus LIKE '%{input.Keyword}%' 
                //$"OR SignStatus LIKE '%{input.Keyword}%' 

            }

            if (!string.IsNullOrEmpty(input.BuyerFullName))
            {
                condition.Append($@"AND LOWER(""BuyerFullName"") = N'{input.BuyerFullName.ToLower()}'");
            }

            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssuedTime.HasValue)
            {
                var issueFromDate = input.IssuedTime.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" = '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueFromDate.HasValue)
            {
                var issueFromDate = input.IssueFromDate.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" >= '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueToDate.HasValue)
            {
                var issueToDate = input.IssueToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" < '{issueToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CancelFromDate.HasValue)
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");

            if (input.CancelToDate.HasValue)
            {
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");
            }

            //if (input.IssuedAt.HasValue)
            //{
            //    var issuedAt = input.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt);
            //}

            if (!string.IsNullOrEmpty(input.Customers))
            {
                var customers = input.Customers?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (""BuyerCode"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customers}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customers}%' 
                                        OR ""BuyerFullName"" LIKE N'%{customers}%'
                                        OR ""BuyerPhoneNumber"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customers}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.Customer))
            {
                var customer = input.Customer?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (""BuyerCode"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customer}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customer}%' 
                                        OR ""BuyerFullName"" LIKE N'%{customer}%'
                                        OR ""BuyerPhoneNumber"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customer}%') 
                                    ) ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            else
            {
                //lấy danh sách mẫu dc xem
                condition.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {RegistrationInvoiceType.HDKhac.GetHashCode()} 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (input.TotalPaymentAmount.HasValue)
            {
                condition.Append($@"AND ""TotalPaymentAmount"" = {input.TotalPaymentAmount.Value} ");
            }

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.VerificationCodeStatuses != null && input.VerificationCodeStatuses.Any())
            {
                if (input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (input.Source != null && input.Source.Any())
                condition.Append($@"AND ""Source"" In ({String.Join(",", input.Source)}) ");

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (!string.IsNullOrEmpty(input.TransactionId))
                condition.Append($@"AND ""TransactionId"" = '{input.TransactionId}' ");

            if (!string.IsNullOrEmpty(input.ErpId))
                condition.Append($@"AND ""ErpId"" = '{input.ErpId}' ");

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            if (input.PrintedTime.HasValue)
            {
                condition.Append($@"AND ""PrintedTime"" IS NOT NULL 
                                          AND ""PrintedTime"" >= '{input.PrintedTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                          AND ""PrintedTime"" < DATE '{input.PrintedTime.Value.ToString("yyyyy-MM-dd", CultureInfo.GetCultureInfo("en-US"))}' + 1");
            }

            if (!string.IsNullOrEmpty(input.UserNamePrinted))
                condition.Append($@"AND ""UserNamePrinted"" LIKE '%{input.UserNamePrinted}%' ");

            if (!string.IsNullOrEmpty(input.BuyerBankAccount))
                condition.Append($@"AND ""BuyerBankAccount"" LIKE '%{input.BuyerBankAccount}%' ");

            if (!string.IsNullOrEmpty(input.BuyerCode))
                condition.Append($@"AND ""BuyerCode"" LIKE '%{input.BuyerCode}%' ");

            #endregion
            var sql = new StringBuilder();
            condition.Append($@"AND ""InvoiceStatus"" IN ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.ThayThe.GetHashCode()}, {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) ");
            sql.Append($@"SELECT  SUM(""TotalPaymentAmount""*""ExchangeRate"")                                  
                        FROM ""TicketHeader""                                               
                        WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {condition}");

            return sql.ToString();
        }
        private async Task<string> GenerateDrawGetByIdQuery(Guid tenantId, long id)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            StringBuilder sql = new StringBuilder();

            sql.Append($@"  With HeaderExtras AS(                                                                       
                                SELECT A.""Id"", A.""FieldValue"", B.""FieldName""                                      
                                FROM ""TicketHeaderExtra"" A                                                         
                                INNER JOIN ""TicketHeaderField"" B ON A.""InvoiceHeaderFieldId"" = B.""Id""          
                                WHERE A.""InvoiceHeaderId"" = {id}                                                      
                            ),                                                                                          
                            TaxBreakdowns AS(                                                                           
                                SELECT ""Id"", ""Name"", ""VatAmount"", ""VatPercent"", ""VatAmountBackUp""             
                                FROM ""TicketTaxBreakdown""                                                          
                                WHERE ""InvoiceHeaderId"" = {id}                                                        
                            ),                                                                                          
                            Refer AS(                                                                                   
                                SELECT ""InvoiceReferenceId"", ""TemplateNoReference"",                                 
                                       ""SerialNoReference"", ""InvoiceNoReference"",                                   
                                       ""InvoiceDateReference"", ""Note""                                               
                                FROM ""TicketReference""                                                             
                                WHERE ""InvoiceHeaderId"" = {id}                                                        
                            ),                                                                                          
                            Details AS(                                                                                 
                                SELECT ""Id"", ""Index"", ""DiscountAmountBeforeTax"", ""DiscountPercentBeforeTax"",    
                                        ""PaymentAmount"", ""ProductId"", ""ProductCode"", ""ProductName"",             
                                        ""UnitId"", ""UnitName"", ""UnitPrice"", ""RoundingUnit"",                      
                                        ""Quantity"", ""Amount"", ""VatPercent"", ""VatAmount"",                        
                                        ""Note"", ""HideUnitPrice"", ""HideUnit"", ""HideQuantity""                     
                                FROM ""TicketDetail""                                                                
                                WHERE ""InvoiceHeaderId"" = {id}                                                        
                            ),                                                                                          
                            DetailExtras AS(                                                                            
                                SELECT C.""Id"", C.""InvoiceDetailId"", C.""FieldValue"", E.""FieldName""               
                                FROM ""TicketDetailExtra"" C                                                         
                                INNER JOIN Details D ON C.""InvoiceDetailId"" = D.""Id""                                
                                INNER JOIN ""TicketDetailField"" E ON C.""InvoiceDetailFieldId"" = E.""Id""          
                            )                                                                                           
                            SELECT H.*,                                                                                 
                                    (                                                                                   
                                        SELECT (JSON_ARRAYAGG (JSON_OBJECT(* RETURNING CLOB) ) )                        
                                        FROM HeaderExtras                                                               
                                    ) ""InvoiceHeaderExtrasJson"",                                                      
                                    (                                                                                   
                                        SELECT ( JSON_ARRAYAGG (JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB) )         
                                        FROM TaxBreakdowns                                                              
                                    ) ""InvoiceTaxBreakdownsJson"",                                                     
                                    (                                                                                   
                                        SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                        FROM Refer                                                                      
                                    ) ""InvoiceReferenceJson"",                                                         
                                    (                                                                                   
                                        SELECT                                                                          
                                        (                                                                               
                                            JSON_ARRAYAGG(                                                              
                                                JSON_OBJECT(                                                            
                                                    D.*,                                                                
                                                    'InvoiceDetailExtrasJson' VALUE(                                    
                                                        SELECT (JSON_ARRAYAGG (JSON_OBJECT(E.* RETURNING CLOB) RETURNING CLOB) )                        
                                                        FROM DetailExtras E                                            
                                                        WHERE E.""InvoiceDetailId"" = D.""Id""                         
                                                    ) 
                                                    RETURNING CLOB                                                     
                                                )   
                                                RETURNING CLOB                                                         
                                            )                                                                          
                                        )                                                                              
                                        FROM Details D                                                                 
                                    ) ""InvoiceDetailsJson""                                                           
                            FROM ""TicketHeader"" H                                                                 
                            WHERE ""Id"" = {id} AND ""TenantId"" = '{rawTenantId}' ");

            return sql.ToString();
        }

        public async Task<string> GenerateDrawCreateInvoice(CreateTicketHeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);

            var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(input.InfosNeedCreateInvoice.TenantInfo);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_TicketDetail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName.Replace("'", "''"),
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion


            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"", 
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"" ,
                                            ""ExtraProperties"",
                                            ""StatusTvan"",
                                            ""InvoiceDeleteSource"",
                                            ""IsDeclared"",
                                            ""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber""
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)input.Resource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ input.ErpId?.Replace("'", "''")}',                                                                                                                                          
                                            '{ input.CreatorErp?.Replace("'", "''")}',                                                                                                                                    
                                            '{ _invoiceService.GetTransactionId(input.Resource, input.TransactionId) }',                                                                                
                                            { input.InfosNeedCreateInvoice.Template.Id },                                                                                                               
                                            { input.TemplateNo },                                                                                                                                       
                                            '{ input.SerialNo }',                                                                                                                                       
                                            N'{ input.Note?.Replace("'", "''")}',                                                                                                                                         
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',     
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                     
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails?.Replace("'", "''")}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                  
                                            { input.InfosNeedCreateInvoice.ToCurrency.Rounding },                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode }',                                                                                               
                                            { input.InfosNeedCreateInvoice.ToCurrency.Conversion },                                                                                                     
                                            '{ input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode }',                                                                                                 
                                            { input.ExchangeRate },                                                                                                                                     
                                            '{ input.PaymentMethod?.Trim().Replace("'", "''") }',                                                                                                                                   
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc) }',        
                                            N'{ await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc) }',        
                                            { input.TotalAmount },                                                                                                                                      
                                            { input.TotalPaymentAmount },                                                                                                                               
                                            '{ input.UserFullName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.UserName?.Replace("'", "''") }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ input.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ input.BuyerFullName?.Replace("'", "''")}',                                                                                                                                   
                                            '{ input.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ input.BuyerAddressLine?.Replace("'", "''")}',                                                                                                                             
                                            '{ input.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                                
                                            '{ input.BuyerCityName?.Replace("'", "''") }',                                                                                                                                    
                                            '{ input.BuyerCountryCode }',                                                                                                                               
                                            '{ input.BuyerPhoneNumber }',                                                                                                                               
                                            '{ input.BuyerFaxNumber }',                                                                                                                                 
                                            '{ input.BuyerEmail }',                                                                                                                                     
                                            '{ input.BuyerBankName?.Replace("'", "''") }',                                                                                                                                    
                                            '{ input.BuyerBankAccount }',                                                                                                                               
                                            { input.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { input.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { input.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { input.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}',
                                            0,
                                            0,
                                            0,
                                            '{input.StoreCode?.Replace("'", "''")}',
                                            '{input.StoreName?.Replace("'", "''")}',
                                            '{input.BudgetUnitCode?.Replace("'", "''")}',
                                            '{input.BuyerIDNumber?.Replace("'", "''")}',
                                            '{input.BuyerPassportNumber?.Replace("'", "''")}'
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();



            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName?.Replace("'", "''"),
                        FieldValue = x.FieldValue.Replace("'", "''")
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { item.Index },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmountBeforeTax },                                   
                                                        { item.DiscountPercentBeforeTax },                                  
                                                        N'{ item.Note?.Replace("'", "''")}',                                                    
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName.Replace("'", "''") }',                                            
                                                        { item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim().Replace("'", "''") }',                                      
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },   
                                                        { item.VatAmount },                                                 
                                                        { item.VatPercent },                                                
                                                        '{ input.InfosNeedCreateInvoice.Taxes[item.VatPercent].Item2?.Replace("'", "''") }',    
                                                        { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL

            foreach (var item in input.InvoiceTaxBreakdowns)
            {
                var tax = input.InfosNeedCreateInvoice.Taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT   
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ tax.Item1 }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmountBackUp },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ tax.Item2?.Replace("'", "''") }',                                               
                                                              { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            //if (sqlValueHeaderExtras.Length > 0)
            //{
            //    sqlValueHeaderExtras = sqlValueHeaderExtras.Remove(0, 9);

            //    rawSql.Append($@"INSERT INTO ""TicketHeaderExtra"" (      
            //                            ""Id"",
            //                            ""InvoiceHeaderId"",                            
            //                            ""InvoiceHeaderFieldId"",                       
            //                            ""FieldValue"",                                 
            //                            ""TenantId"",                                   
            //                            ""Partition"",
            //                            ""IsActive"",
            //                            ""CreationTime""
            //                    ) {sqlValueHeaderExtras} ; ");
            //}

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmountBeforeTax"",            
                                              ""DiscountPercentBeforeTax"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                      
                                              ""VatAmount"",                          
                                              ""VatPercent"",                         
                                              ""VatPercentDisplay"",                  
                                              ""Partition"",                          
                                              ""IsPromotion"",                        
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            //if (sqlValueDetailExtras.Length > 0)
            //{
            //    sqlValueDetailExtras = sqlValueDetailExtras.Remove(0, 9);

            //    rawSql.Append($@"  INSERT INTO ""TicketDetailExtra"" (    
            //                                        ""Id"",
            //                                        ""InvoiceDetailId"",                        
            //                                        ""InvoiceDetailFieldId"",                   
            //                                        ""FieldValue"",                             
            //                                        ""TenantId"",                               
            //                                        ""Partition""                               
            //                                    ) {sqlValueDetailExtras} ; ");
            //}

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (     
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawCreateByGroupCustomerInvoice(CreateByGroupCustomerTicketHeaderEventSendData input, List<long> headerIds)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);

            var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(input.InfosNeedCreateInvoice.TenantInfo);

            var lstDetailId = await GetSEQsNextVal(headerIds.Count * input.InvoiceDetails.Count, SEQ_Name.SEQ_TicketDetail);

            StringBuilder rawSql = new StringBuilder("   BEGIN");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            var i = 0;
            foreach (var customer in input.InfosNeedCreateInvoice.Customers)
            {
                var headerId = headerIds[i];

                rawSql.Append($@"     INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",    
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties"",
                                            ""StatusTvan"",
                                            ""InvoiceDeleteSource"",
                                            ""IsDeclared"",
											""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)input.Resource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ input.ErpId?.Replace("'", "''") }',                                                                                                                                         
                                            '{ input.CreatorErp?.Replace("'", "''") }',                                                                                                                                   
                                            '{ _invoiceService.GetTransactionId(input.Resource, input.TransactionId) }',                                                                                
                                            { input.InfosNeedCreateInvoice.Template.Id },                                                                                                               
                                            { input.TemplateNo },                                                                                                                                       
                                            '{ input.SerialNo }',                                                                                                                                       
                                            N'{ input.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',    
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                      
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                 
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                   
                                            { input.InfosNeedCreateInvoice.ToCurrency.Rounding },                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode }',                                                                                               
                                            { input.InfosNeedCreateInvoice.ToCurrency.Conversion },                                                                                                     
                                            '{ input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode }',                                                                                                 
                                            { input.ExchangeRate },                                                                                                                                     
                                            '{ input.PaymentMethod?.Trim()?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',         
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',         
                                            { input.TotalAmount },                                                                                                                                      
                                            { input.TotalPaymentAmount },                                                                                                                               
                                            '{ input.UserFullName }',                                                                                                                                   
                                            '{ input.UserName }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ customer.CustomerCode?.Trim() }',                                                                                                                              
                                            N'{ customer.FullName.Replace("'", "''") }',                                                                                                                                 
                                            N'{ customer.LegalName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ customer.TaxCode?.Trim() }',                                                                                                                           
                                            N'{ customer.Address.Replace("'", "''") }',                                                                                                                                
                                            N'{ customer.District?.Replace("'", "''") }',                                                                                                                               
                                            N'{ customer.City?.Replace("'", "''") }',                                                                                                                                 
                                            N'{ customer.Country?.Replace("'", "''") }',                                                                                                                                
                                            '{ customer.Phone }',                                                                                                                               
                                            '{ customer.Fax }',                                                                                                                                 
                                            '{ customer.Email }',                                                                                                                                     
                                            N'{ customer.BankName?.Replace("'", "''") }',                                                                                                                                   
                                            '{ customer.BankAccount }',                                                                                                                               
                                            { input.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { input.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { input.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { input.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}',
                                            0,
                                            0,
                                            0,
                                            '{input.StoreCode?.Replace("'", "''")}',
                                            '{input.StoreName?.Replace("'", "''")}',
                                            '{input.BudgetUnitCode?.Replace("'", "''")}',
                                            '{input.BuyerIDNumber?.Replace("'", "''")}',
                                            '{input.BuyerPassportNumber?.Replace("'", "''")}'
                                        ); ");

                i++;
            }

            #endregion

            StringBuilder sqlValueDetails = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();

            var j = 0;
            foreach (var headerId in headerIds)
            {
                #region DETAILS SQL

                foreach (var item in input.InvoiceDetails)
                {
                    var detailId = lstDetailId[j];

                    var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                            ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                            : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                    ProductTypeEntity productType = null;
                    if (product != null && product.ProductTypeId.HasValue)
                        productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                    ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                    : null;

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue?.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount},                                                    
                                                        {item.DiscountAmountBeforeTax},                                   
                                                        {item.DiscountPercentBeforeTax},                                  
                                                        N'{item.Note?.Replace("'", "''")}',                                                    
                                                        N'{item.PaymentAmount}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {item.ProductType},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                     
                                                        {item.Quantity},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{item.UnitName?.Trim().Replace("'", "''")}',                                      
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {((productType != null && productType.HideQuantity) ? 1 : 0)},    
                                                        {((productType != null && productType.HideUnit) ? 1 : 0)},        
                                                        {((productType != null && productType.HideUnitPrice) ? 1 : 0)},   
                                                        {item.VatAmount},                                                 
                                                        {item.VatPercent},                                                
                                                        '{input.InfosNeedCreateInvoice.Taxes[item.VatPercent].Item2?.Replace("'", "''")}',    
                                                        {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");



                    j++;
                }

                #endregion

                #region TAXBREAKDOWN SQL

                foreach (var item in input.InvoiceTaxBreakdowns)
                {
                    var tax = input.InfosNeedCreateInvoice.Taxes[item.VatPercent];

                    sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT     
                                                                  {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                                  { headerId },                                                      
                                                                  '{ rawTenantId }',                                             
                                                                  '{ tax.Item1 }',                                               
                                                                  { item.VatAmount },                                            
                                                                  { item.VatAmountBackUp },                                      
                                                                  { item.VatPercent },                                           
                                                                  N'{ tax.Item2?.Replace("'", "''") }',                                              
                                                                  { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                              FROM DUAL ");
                }

                #endregion
            }

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmountBeforeTax"",            
                                              ""DiscountPercentBeforeTax"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                      
                                              ""VatAmount"",                          
                                              ""VatPercent"",                         
                                              ""VatPercentDisplay"",                  
                                              ""Partition"",                          
                                              ""IsPromotion"",                         
                                              ""ExtraProperties""                         
                                        ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (    
                                                    ""Id"",
                                                    ""InvoiceHeaderId"",                           
                                                    ""TenantId"",                                  
                                                    ""Name"",                                      
                                                    ""VatAmount"",                                 
                                                    ""VatAmountBackUp"",                           
                                                    ""VatPercent"",                                
                                                    ""VatPercentDisplay"",                         
                                                    ""Partition""                                  
                                                ) {sqlValueTaxBreakdowns} ; ");
            }

            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawCreateReplaceInvoice(CreateReplaceTicketHeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(input.InfosNeedCreateInvoice.InvoiceReference.SellerId);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_TicketDetail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   

                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties"",
                                            ""StatusTvan"",
                                            ""InvoiceDeleteSource"",
                                            ""IsDeclared"",
											""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)input.Resource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ input.ErpId?.Replace("'", "''") }',                                                                                                                                             
                                            '{ input.CreatorErp?.Replace("'", "''") }',                                                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.InvoiceReference.TransactionId }',                                                                                
                                            { input.InfosNeedCreateInvoice.Template.Id },                                                                                                               
                                            { input.TemplateNo },                                                                                                                                       
                                            '{ input.SerialNo }',                                                                                                                                       
                                            N'{ input.Note?.Replace("'", "''") }',                                                                                                                                            
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.ThayThe.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawSellerId}',    
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''") }',                                                                                                   
                                            { input.InfosNeedCreateInvoice.ToCurrency.Rounding },                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode }',                                                                                               
                                            { input.InfosNeedCreateInvoice.ToCurrency.Conversion },                                                                                                     
                                            '{ input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode }',                                                                                                 
                                            { input.ExchangeRate },                                                                                                                                     
                                            '{ input.PaymentMethod?.Trim().Replace("'", "''") }',                                                                                                                                  
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',       
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',        
                                            { input.TotalAmount },                                                                                                                                      
                                            { input.TotalPaymentAmount },                                                                                                                               
                                            '{ input.UserFullName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ input.UserName }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ input.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ input.BuyerFullName}',                                                                                                                                   
                                            '{ input.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ input.BuyerAddressLine}',                                                                                                                             
                                            '{ input.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ input.BuyerCityName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ input.BuyerCountryCode?.Replace("'", "''") }',                                                                                                                                
                                            '{ input.BuyerPhoneNumber }',                                                                                                                               
                                            '{ input.BuyerFaxNumber }',                                                                                                                                 
                                            '{ input.BuyerEmail }',                                                                                                                                     
                                            '{ input.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.BuyerBankAccount }',                                                                                                                               
                                            { input.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { input.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { input.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { input.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}',                                                                                                   
                                            0,                                                                                                   
                                            0,                                                                                                   
                                            0,
                                            '{input.StoreCode?.Replace("'", "''")}',
                                            '{input.StoreName?.Replace("'", "''")}',
                                            '{input.BudgetUnitCode?.Replace("'", "''")}',
                                            '{input.BuyerIDNumber?.Replace("'", "''")}',
                                            '{input.BuyerPassportNumber?.Replace("'", "''")}'                                                                                                   
                                        ); ");

            #endregion

            StringBuilder sqlValueDetails = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();

            #region HEADER EXTRAS SQL

            //if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            //{
            //    foreach (var item in input.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!input.InfosNeedCreateInvoice.HeaderFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = input.InfosNeedCreateInvoice.HeaderFields[item.FieldName];

            //        sqlValueHeaderExtras.Append($@"UNION ALL SELECT 
            //                                                      {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketHeaderExtra}""'),
            //                                                      {headerId},                                                       
            //                                                      { field.Id },                                                   
            //                                                      '{ item.FieldValue }',                                          
            //                                                      '{ rawTenantId }',                                              
            //                                                      { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
            //                                                      1,
            //                                                      '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }'      
            //                                                FROM DUAL ");
            //    }
            //}

            #endregion

            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue.Replace("'", "''"),
                        FieldName = x.FieldName,
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion


                sqlValueDetails.Append($@"UNION ALL SELECT   
                                                        { detailId },                                              
                                                        { item.Index },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmountBeforeTax },                                   
                                                        { item.DiscountPercentBeforeTax },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                 
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName.Replace("'", "''") }',                                             
                                                        { item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                      
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },   
                                                        { item.VatAmount },                                                 
                                                        { item.VatPercent },                                                
                                                        '{ input.InfosNeedCreateInvoice.Taxes[item.VatPercent].Item2?.Replace("'", "''") }',   
                                                        { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                #region DETAI EXTRAS SQL

                //if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                //{
                //    foreach (var extra in item.InvoiceDetailExtras)
                //    {
                //        //Nếu ko có DetailField thì bỏ qua, ko insert
                //        if (!input.InfosNeedCreateInvoice.DetailFields.ContainsKey(extra.FieldName))
                //            continue;

                //        var field = input.InfosNeedCreateInvoice.DetailFields[extra.FieldName];

                //        sqlValueDetailExtras.Append($@"UNION ALL SELECT   
                //                                                      {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketDetailExtra}""'),
                //                                                      { detailId },                                             
                //                                                      { field.Id },                                                      
                //                                                      '{ extra.FieldValue }',                                            
                //                                                      '{ rawTenantId }',                                                 
                //                                                      { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }        
                //                                                  FROM DUAL ");
                //    }
                //}

                #endregion

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL

            foreach (var item in input.InvoiceTaxBreakdowns)
            {
                var tax = input.InfosNeedCreateInvoice.Taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT     
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ tax.Item1 }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmountBackUp },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ tax.Item2?.Replace("'", "''") }',                                             
                                                              { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (   
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""TicketReference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {input.Id},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Id},
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Number ?? 0},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.SerialNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.ThayThe.GetHashCode()},
                                                    N'{input.Content?.Replace("'", "''") }', 
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  INSERT INTO ""TicketDocumentInfo"" (             
                                                    ""TenantId"",
                                                    ""Partition"",
                                                    ""InvoiceHeaderId"",
                                                    ""FileId"",
                                                    ""DocumentNo"",
                                                    ""DocumentDate"",
                                                    ""DocumentReason"",
                                                    ""Type"",
                                                    ""IsUploadFile""
                                                )                                                  
                                                VALUES (
                                                    '{rawTenantId}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                                    {headerId},
                                                    {input.IdFileDocument.Value},
                                                    N'{input.DocumentNo?.Replace("'", "''")}',
                                                    '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    N'{input.DocumentReason?.Replace("'", "''")}',
                                                    {(short)DocumentTemplateType.Replace.GetHashCode()},
                                                    {(input.IsUploadFile ? 1 : 0)}
                                                ); ");
                #endregion


                #region INVOICE DOCUMENT SQL

                rawSql.Append($@"  UPDATE ""TicketDocument"" SET ""InvoiceHeaderId"" = {headerId} WHERE ""Id"" = {input.IdFileDocument.Value}; ");

                #endregion
            }


            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawCreateAdjustInvoice(CreateAdjustTicketHeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(input.InfosNeedCreateInvoice.InvoiceReference.SellerId);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_TicketDetail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   

                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties"",
                                            ""StatusTvan"",
                                            ""InvoiceDeleteSource"",
                                            ""IsDeclared"",
											""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',                                                                                                                                            
                                            {(short)input.Resource.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{input.ErpId?.Replace("'", "''")}',                                                                                                                                             
                                            '{input.CreatorErp?.Replace("'", "''")}',                                                                                                                                       
                                            '{input.InfosNeedCreateInvoice.InvoiceReference.TransactionId}',                                                                                
                                            {input.InfosNeedCreateInvoice.Template.Id},                                                                                                               
                                            {input.TemplateNo},                                                                                                                                       
                                            '{input.SerialNo}',                                                                                                                                       
                                            N'{input.Note?.Replace("'", "''")}',                                                                                                                                            
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.DieuChinh.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawSellerId}',    
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                   
                                            {input.InfosNeedCreateInvoice.ToCurrency.Rounding},                                                                                                       
                                            '{input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode}',                                                                                               
                                            {input.InfosNeedCreateInvoice.ToCurrency.Conversion},                                                                                                     
                                            '{input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode}',                                                                                                 
                                            {input.ExchangeRate},                                                                                                                                     
                                            '{input.PaymentMethod?.Trim().Replace("'", "''")}',                                                                                                                                  
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinh))?.Trim().Replace("'", "''")}',       
                                            N'{(await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinh))?.Trim().Replace("'", "''")}',        
                                            {input.TotalAmount},                                                                                                                                      
                                            {input.TotalPaymentAmount},                                                                                                                               
                                            '{input.UserFullName?.Replace("'", "''")}',                                                                                                                                 
                                            '{input.UserName}',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{input.BuyerCode?.Trim()}',                                                                                                                              
                                            '{input.BuyerFullName}',                                                                                                                                   
                                            '{input.BuyerLegalName?.Replace("'", "''")}',                                                                                                                                  
                                            '{input.BuyerTaxCode?.Trim()}',                                                                                                                           
                                            '{input.BuyerAddressLine}',                                                                                                                             
                                            '{input.BuyerDistrictName?.Replace("'", "''")}',                                                                                                                              
                                            '{input.BuyerCityName?.Replace("'", "''")}',                                                                                                                                 
                                            '{input.BuyerCountryCode?.Replace("'", "''")}',                                                                                                                                
                                            '{input.BuyerPhoneNumber}',                                                                                                                               
                                            '{input.BuyerFaxNumber}',                                                                                                                                 
                                            '{input.BuyerEmail}',                                                                                                                                     
                                            '{input.BuyerBankName?.Replace("'", "''")}',                                                                                                                                  
                                            '{input.BuyerBankAccount}',                                                                                                                               
                                            {input.TotalDiscountAmountBeforeTax},                                                                                                                     
                                            {input.TotalDiscountAmountAfterTax},                                                                                                                      
                                            {input.TotalDiscountPercentAfterTax},                                                                                                                     
                                            {input.TotalVatAmount},                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}',                                                                                                   
                                            0,                                                                                                   
                                            0,                                                                                                   
                                            0,
                                            '{input.StoreCode?.Replace("'", "''")}',
                                            '{input.StoreName?.Replace("'", "''")}',
                                            '{input.BudgetUnitCode?.Replace("'", "''")}',
                                            '{input.BuyerIDNumber?.Replace("'", "''")}',
                                            '{input.BuyerPassportNumber?.Replace("'", "''")}'                                                                                                   
                                        ); ");

            #endregion

            StringBuilder sqlValueDetails = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();

            #region HEADER EXTRAS SQL

            //if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            //{
            //    foreach (var item in input.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!input.InfosNeedCreateInvoice.HeaderFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = input.InfosNeedCreateInvoice.HeaderFields[item.FieldName];

            //        sqlValueHeaderExtras.Append($@"UNION ALL SELECT 
            //                                                      {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketHeaderExtra}""'),
            //                                                      {headerId},                                                       
            //                                                      { field.Id },                                                   
            //                                                      '{ item.FieldValue }',                                          
            //                                                      '{ rawTenantId }',                                              
            //                                                      { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
            //                                                      1,
            //                                                      '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }'      
            //                                                FROM DUAL ");
            //    }
            //}

            #endregion

            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue.Replace("'", "''"),
                        FieldName = x.FieldName,
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion


                sqlValueDetails.Append($@"UNION ALL SELECT   
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount},                                                    
                                                        {item.DiscountAmountBeforeTax},                                   
                                                        {item.DiscountPercentBeforeTax},                                  
                                                        N'{item.Note?.Replace("'", "''")}',                                                 
                                                        N'{item.PaymentAmount}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                             
                                                        {item.ProductType},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                      
                                                        {item.Quantity},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{item.UnitName?.Trim()?.Replace("'", "''")}',                                       
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {((productType != null && productType.HideQuantity) ? 1 : 0)},    
                                                        {((productType != null && productType.HideUnit) ? 1 : 0)},        
                                                        {((productType != null && productType.HideUnitPrice) ? 1 : 0)},   
                                                        {item.VatAmount},                                                 
                                                        {item.VatPercent},                                                
                                                        '{input.InfosNeedCreateInvoice.Taxes[item.VatPercent].Item2?.Replace("'", "''")}',   
                                                        {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},        
                                                        0,
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                #region DETAI EXTRAS SQL

                //if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                //{
                //    foreach (var extra in item.InvoiceDetailExtras)
                //    {
                //        //Nếu ko có DetailField thì bỏ qua, ko insert
                //        if (!input.InfosNeedCreateInvoice.DetailFields.ContainsKey(extra.FieldName))
                //            continue;

                //        var field = input.InfosNeedCreateInvoice.DetailFields[extra.FieldName];

                //        sqlValueDetailExtras.Append($@"UNION ALL SELECT   
                //                                                      {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketDetailExtra}""'),
                //                                                      { detailId },                                             
                //                                                      { field.Id },                                                      
                //                                                      '{ extra.FieldValue }',                                            
                //                                                      '{ rawTenantId }',                                                 
                //                                                      { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }        
                //                                                  FROM DUAL ");
                //    }
                //}

                #endregion

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL

            foreach (var item in input.InvoiceTaxBreakdowns)
            {
                var tax = input.InfosNeedCreateInvoice.Taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT     
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              {headerId},                                                      
                                                              '{rawTenantId}',                                             
                                                              '{tax.Item1}',                                               
                                                              {item.VatAmount},                                            
                                                              {item.VatAmountBackUp},                                      
                                                              {item.VatPercent},                                           
                                                              N'{tax.Item2?.Replace("'", "''")}',                                             
                                                              {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (   
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""TicketReference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {input.Id},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Id},
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Number ?? 0},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.SerialNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.DieuChinh.GetHashCode()},
                                                    N'{input.Content?.Replace("'", "''")}', 
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  INSERT INTO ""TicketDocumentInfo"" (             
                                                    ""TenantId"",
                                                    ""Partition"",
                                                    ""InvoiceHeaderId"",
                                                    ""FileId"",
                                                    ""DocumentNo"",
                                                    ""DocumentDate"",
                                                    ""DocumentReason"",
                                                    ""Type"",
                                                    ""IsUploadFile""
                                                )                                                  
                                                VALUES (
                                                    '{rawTenantId}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                                    {headerId},
                                                    {input.IdFileDocument.Value},
                                                    N'{input.DocumentNo?.Replace("'", "''")}',
                                                    '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    N'{input.DocumentReason?.Replace("'", "''")}',
                                                    {(short)DocumentTemplateType.Replace.GetHashCode()},
                                                    {(input.IsUploadFile ? 1 : 0)}
                                                ); ");
                #endregion


                #region INVOICE DOCUMENT SQL

                rawSql.Append($@"  UPDATE ""TicketDocument"" SET ""InvoiceHeaderId"" = {headerId} WHERE ""Id"" = {input.IdFileDocument.Value}; ");

                #endregion
            }


            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawUpdateInvoice(UpdateTicketHeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""PaymentMethod"" = '{input.PaymentMethod?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalVatAmount"" = {input.TotalVatAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""TotalDiscountAmountBeforeTax"" = {input.TotalDiscountAmountBeforeTax},
                                    ""TotalPerAfterTax"" = {input.TotalDiscountPercentAfterTax},
                                    ""TotalDiscountAmountAfterTax"" = {input.TotalDiscountAmountAfterTax},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""BuyerCode"" = '{input.BuyerCode}',
                                    ""BuyerEmail"" = '{input.BuyerEmail}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName}',
                                    ""BuyerLegalName"" = N'{input.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine}',
                                    ""BuyerDistrictName"" = N'{input.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{input.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{input.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{input.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{input.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{input.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}',
                                    ""StoreCode"" = N'{input.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{input.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{input.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{input.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{input.BuyerPassportNumber?.Replace("'", "''")}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = input.InfosNeedUpdateInvoice.ProductTypes != null && input.InfosNeedUpdateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value)
                                ? input.InfosNeedUpdateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    InvoiceHeaderId = input.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        FieldValue = y.FieldValue,
                        FieldName = y.FieldName,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                InvoiceHeaderId = input.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmountBackUp,
                VatPercent = x.VatPercent,
                VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item2,
                Name = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, input.InvoiceDate.Date);
            await GenerateUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.TaxBreakdowns, commandTaxBreakDowns, input.InvoiceDate.Date);

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        public async Task<string> GenerateDrawUpdateReplaceInvoice(UpdateReplaceTicketHeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""PaymentMethod"" = '{input.PaymentMethod?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalVatAmount"" = {input.TotalVatAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""TotalDiscountAmountBeforeTax"" = {input.TotalDiscountAmountBeforeTax},
                                    ""TotalPerAfterTax"" = {input.TotalDiscountPercentAfterTax},
                                    ""TotalDiscountAmountAfterTax"" = {input.TotalDiscountAmountAfterTax},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""BuyerCode"" = '{input.BuyerCode}',
                                    ""BuyerEmail"" = '{input.BuyerEmail}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName}',
                                    ""BuyerLegalName"" = N'{input.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine}',
                                    ""BuyerDistrictName"" = N'{input.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{input.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{input.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{input.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{input.BuyerBankAccount}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}',
                                    ""StoreCode"" = N'{input.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{input.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{input.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{input.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{input.BuyerPassportNumber?.Replace("'", "''")}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = input.InfosNeedUpdateInvoice.ProductTypes != null && input.InfosNeedUpdateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value)
                                ? input.InfosNeedUpdateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Index = item.Index,
                    InvoiceHeaderId = input.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue?.Replace("'", "''"),
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    }).ToList()
                });
            }

            //var commandHeaderExtras = input.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            //{
            //    FieldName = x.FieldName,
            //    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
            //    InvoiceHeaderId = input.Id,
            //    FieldValue = x.FieldValue,
            //    InvoiceHeaderFieldId = input.InfosNeedUpdateInvoice.HeaderFields[x.FieldName].Id,
            //})
            //.ToList();

            var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                InvoiceHeaderId = input.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmountBackUp,
                VatPercent = x.VatPercent,
                VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item2,
                Name = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, input.InvoiceDate.Date);
            await GenerateUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.TaxBreakdowns, commandTaxBreakDowns, input.InvoiceDate.Date);

            //update documentInfo
            //TODO: làm có thể vừa insert vừa update trường hợp mà chưa có dữ liệu trong bảng InvoiceDocumentInfo
            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  UPDATE  ""TicketDocumentInfo""
                               SET             
                                ""Partition"" = {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                ""FileId"" = {input.IdFileDocument.Value},
                                ""DocumentNo"" = N'{input.DocumentNo?.Replace("'", "''")}',
                                ""DocumentDate"" = '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                ""DocumentReason"" = N'{input.DocumentReason?.Replace("'", "''")}',
                                ""IsUploadFile"" = {(input.IsUploadFile ? 1 : 0)}
                               WHERE ""InvoiceHeaderId"" = {input.Id};   ");
                #endregion
            }

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        public async Task<string> GenerateDrawUpdateAdjustInvoice(UpdateAdjustTicketHeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""PaymentMethod"" = '{input.PaymentMethod?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalVatAmount"" = {input.TotalVatAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""TotalDiscountAmountBeforeTax"" = {input.TotalDiscountAmountBeforeTax},
                                    ""TotalPerAfterTax"" = {input.TotalDiscountPercentAfterTax},
                                    ""TotalDiscountAmountAfterTax"" = {input.TotalDiscountAmountAfterTax},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""BuyerCode"" = '{input.BuyerCode}',
                                    ""BuyerEmail"" = '{input.BuyerEmail}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName}',
                                    ""BuyerLegalName"" = N'{input.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine}',
                                    ""BuyerDistrictName"" = N'{input.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{input.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{input.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{input.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{input.BuyerBankAccount}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}',
                                    ""StoreCode"" = N'{input.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{input.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{input.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{input.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{input.BuyerPassportNumber?.Replace("'", "''")}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = input.InfosNeedUpdateInvoice.ProductTypes != null && input.InfosNeedUpdateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value)
                                ? input.InfosNeedUpdateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Index = item.Index,
                    InvoiceHeaderId = input.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue?.Replace("'", "''"),
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                InvoiceHeaderId = input.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmountBackUp,
                VatPercent = x.VatPercent,
                VatPercentDisplay = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item2,
                Name = input.InfosNeedUpdateInvoice.Taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);
            await GenerateUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.TaxBreakdowns, commandTaxBreakDowns, input.InvoiceDate.Date);

            //update documentInfo
            //TODO: làm có thể vừa insert vừa update trường hợp mà chưa có dữ liệu trong bảng InvoiceDocumentInfo
            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  UPDATE  ""TicketDocumentInfo""
                               SET             
                                ""Partition"" = {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                ""FileId"" = {input.IdFileDocument.Value},
                                ""DocumentNo"" = N'{input.DocumentNo?.Replace("'", "''")}',
                                ""DocumentDate"" = '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                ""DocumentReason"" = N'{input.DocumentReason?.Replace("'", "''")}',
                                ""IsUploadFile"" = {(input.IsUploadFile ? 1 : 0)}
                               WHERE ""InvoiceHeaderId"" = {input.Id};   ");
                #endregion
            }

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        public async Task<long> ImportAsync(TicketModel request, InvoiceSource resource, ContextModel context, SignStatus signStatus, ApproveStatus approveStatus, TenantInfo tenantInfo, Dictionary<TenantMetaData, MetadataModel> tenantInfoMetadata, Dictionary<decimal, Tuple<string, string>> taxes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);
            var headerId = await GetSEQsNextVal(1, SEQ_Name.SEQ_TicketHeader);
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SEQ_Name.SEQ_TicketDetail);

            #region Header ExtraProperties
            var headerExtraProperties = new Dictionary<string, string>();
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var headerExtras = new List<InvoiceHeaderExtraModel>();
                foreach (var item in request.InvoiceHeaderExtras)
                {
                    headerExtras.Add(new InvoiceHeaderExtraModel
                    {
                        FieldName = item.FieldName,
                        FieldValue = item.FieldValue,
                        InvoiceHeaderFieldId = item.InvoiceHeaderFieldId
                    });

                }
                headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));
            }


            #endregion

            //Header
            var header = new TicketHeaderEntity
            {
                Id = headerId.First(),
                TenantId = context.TenantId,
                CreationTime = DateTime.Now,
                CreatorId = context.UserId,

                Source = (short)resource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = _invoiceService.GetTransactionId(resource, request.TransactionId),
                InvoiceTemplateId = request.InvoiceTemplateId,
                TemplateNo = request.TemplateNo,
                SerialNo = request.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,

                InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = tenantInfo.Id,
                SellerTaxCode = tenantInfo.TaxCode,
                SellerAddressLine = tenantInfo.Address,
                SellerCountryCode = tenantInfo.Country,
                SellerDistrictName = tenantInfo.District,
                SellerCityName = tenantInfo.City,
                SellerPhoneNumber = tenantInfo.Phone,
                SellerFaxNumber = tenantInfo.Fax,
                SellerEmail = tenantInfo.Emails,
                SellerBankName = tenantInfo.BankName,
                SellerBankAccount = tenantInfo.BankAccount,
                SellerLegalName = tenantInfo.LegalName,
                SellerFullName = tenantInfo.FullNameVi,

                RoundingCurrency = request.RoundingCurrency,
                FromCurrency = request.FromCurrency,
                CurrencyConversion = request.CurrencyConversion,
                ToCurrency = request.ToCurrency,
                ExchangeRate = request.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = request.PaymentAmountWords,
                PaymentAmountWordsEn = request.PaymentAmountWordsEn,
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = context.Session.UserInfo.FullName,
                UserNameCreator = context.Session.UserName,

                BuyerId = null,
                BuyerCode = request.BuyerCode?.Trim(),
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail,
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,
                ExtraProperties = headerExtraProperties.Any() ? new ExtraPropertyDictionary(headerExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value)) : null,

                Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            };

            await _repoTicketHeader.InsertAsync(header, autoSave: true);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert

            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            //InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = context.TenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        await _repoTicketHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}

            //Details
            var details = new List<TicketDetailEntity>();
            var i = 0;
            foreach (var item in request.InvoiceDetails)
            {
                var detailId = lstDetailId[i];
                #region DETAI EXTRAS SQL
                var detailExtraProperties = new Dictionary<string, string>();

                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtras = item.InvoiceDetailExtras.Select(x => new InvoiceDetailExtraModel
                    {
                        InvoiceDetailId = detailId,
                        InvoiceDetailFieldId = x.InvoiceDetailFieldId,
                        FieldValue = x.FieldValue,
                        FieldName = x.FieldName,
                    }).ToList();

                    detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));
                }

                #endregion
                details.Add(new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = context.TenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = item.ProductId.Value,
                    ProductName = item.ProductName,
                    ProductCode = item.ProductCode?.Trim(),
                    Quantity = item.Quantity,
                    UnitId = item.UnitId.Value,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    RoundingUnit = item.RoundingUnit,
                    HideQuantity = item.HideQuantity,
                    HideUnit = item.HideUnit,
                    HideUnitPrice = item.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    ExtraProperties = detailExtraProperties.Any() ? new ExtraPropertyDictionary(detailExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value)) : null,
                });
                i++;
            }

            await _repoTicketDetail.InsertManyAsync(details);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Detail Extras
            //Sau khi SaveChanges, các Id của bảng InvoiceDetail sẽ có dữ liệu
            //Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
            //var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    var items = indexes[detail.Index];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        //Nếu ko có DetailField thì bỏ qua, ko insert

            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            TenantId = context.TenantId,
            //            InvoiceDetailId = detail.Id,
            //            InvoiceDetailFieldId = item.InvoiceDetailFieldId,
            //            Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    await _repoTicketDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = tax.Item1,
                    TenantId = context.TenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = item.VatAmountBackUp,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                await _repoTicketTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            await transaction.CompleteAsync();

            return header.Id;
        }


        private async Task GenerateUpdateInvoiceDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<TicketDetailEntity> entityDetails, Dictionary<string, TicketDetailFieldEntity> entityDetailFields, List<TicketDetailModel> modelDetails, DateTime invoiceDate)
        {
            var removeDetails = new List<TicketDetailEntity>();

            IEnumerable<TicketDetailModel> newDetails = null;
            IEnumerable<int> duplicate = null;
            //group để bỏ trường hợp Index thêm mới
            if (entityDetails != null)
            {
                duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));
            }
            if(duplicate == null)
            {
                newDetails = modelDetails;
            } else
            {
                newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));
            }

            // Check thêm đk chi tiết rỗng
            if(entityDetails != null)
            {
                foreach (var item in entityDetails)
                {
                    if (duplicate != null && duplicate.Contains(item.Index)) // SỬA
                    {
                        var detailExtraProperties = "";
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                        if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                        {
                            var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                            {
                                FieldValue = x.FieldValue.Replace("'", "''"),
                                FieldName = x.FieldName,
                            }).ToList(), entityDetailFields);
                            detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }
                        rawSql.Append($@"   UPDATE ""TicketDetail""
                                        SET
                                            ""Amount"" = {detail.Amount},
                                            ""DiscountAmountBeforeTax"" = {detail.DiscountAmountBeforeTax},
                                            ""DiscountPercentBeforeTax"" = {detail.DiscountPercentBeforeTax},
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = {detail.PaymentAmount},
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" = {detail.Quantity},
                                            ""UnitName"" = N'{detail.UnitName?.Trim()?.Replace("'", "''")}',
                                            ""VatAmount"" = {detail.VatAmount},
                                            ""VatPercent"" = {detail.VatPercent},
                                            ""VatPercentDisplay"" = N'{detail.VatPercentDisplay?.Replace("'", "''")}',
                                            ""HideQuantity"" = {(detail.HideQuantity ? 1 : 0)},
                                            ""HideUnit"" = {(detail.HideUnit ? 1 : 0)},
                                            ""HideUnitPrice"" = {(detail.HideUnitPrice ? 1 : 0)},
                                            ""UnitPrice"" = {detail.UnitPrice},
                                            ""ExtraProperties"" = {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        WHERE ""Id"" = {item.Id};
                                    ");

                        //var entityDetailExtra = detailExtras.ContainsKey(item.Id) ? detailExtras[item.Id] : null;
                        //await UpdateInvoiceDetailExtrasAsync(detail.TenantId, item.Id, detail.InvoiceDetailExtras, entityDetailExtra);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                        //if (detailExtras.ContainsKey(item.Id))
                        //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                    }
                }
            }

            // THÊM MỚI
            if (newDetails != null && newDetails.Any())
            {
                var lstDetailId = await GetSEQsNextVal(newDetails.Count(), SEQ_Name.SEQ_TicketDetail);

                var i = 0;
                foreach (var item in newDetails)
                {
                    var detailId = lstDetailId[i];

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"  INSERT INTO ""TicketDetail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""DiscountAmountBeforeTax"",
                                            ""DiscountPercentBeforeTax"",
                                            ""PaymentAmount"",
                                            ""VatAmount"",
                                            ""VatPercent"",
                                            ""VatPercentDisplay"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""HideQuantity"",
                                            ""HideUnit"",
                                            ""HideUnitPrice"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""IsPromotion"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {detailId},
                                            {item.Amount},
                                            N'{item.Note?.Trim().Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()?.Replace("'", "''")}',
                                            N'{item.ProductName?.Trim()?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{item.UnitName?.Trim()?.Replace("'", "''")}',
                                            {item.UnitPrice},
                                            {item.Index},
                                            {item.Quantity},
                                            {item.DiscountAmountBeforeTax},
                                            {item.DiscountPercentBeforeTax},
                                            {item.PaymentAmount},
                                            {item.VatAmount},
                                            {item.VatPercent},
                                            N'{item.VatPercentDisplay?.Replace("'", "''")}',
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}',
                                            { (item.HideQuantity ? 1 : 0) },    
                                            { (item.HideUnit ? 1 : 0) },        
                                            { (item.HideUnitPrice ? 1 : 0) },   
                                            0,
                                            4,
                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
                                            0,
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        ); ");

                    //thêm các detail extra
                    //if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    //{
                    //    foreach (var detailExtra in item.InvoiceDetailExtras)
                    //    {
                    //        rawSql.Append($@"  INSERT INTO ""TicketDetailExtra"" 
                    //                            (
                    //                                ""FieldValue"",
                    //                                ""InvoiceDetailId"",
                    //                                ""InvoiceDetailFieldId"",
                    //                                ""TenantId"",
                    //                                ""Partition"" 
                    //                            ) VALUES (
                    //                                {detailExtra.FieldValue},
                    //                                {detailId},
                    //                                {detailExtra.InvoiceDetailFieldId},
                    //                                '{rawTenantId}',
                    //                                { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }
                    //                            ); ");
                    //    }
                    //}

                    i++;
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""TicketDetail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }

            //if (removeDetailExtras != null && removeDetailExtras.Any())
            //{
            //    rawSql.Append($@"   DELETE FROM ""TicketDetailExtra"" WHERE ""Id"" IN ( { String.Join(",", removeDetailExtras.Select(x => x.Id)) } ); ");
            //}
        }

        //private async Task GenerateUpdateInvoiceHeaderExtraAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<TicketHeaderExtraEntity> entityHeaderExtras, List<TicketHeaderExtraModel> modelHeaderExtras, DateTime invoiceDate)
        //{
        //    var lstAdd = new List<TicketHeaderExtraEntity>();
        //    var lstModify = new List<TicketHeaderExtraEntity>();
        //    var lstRemove = new List<TicketHeaderExtraEntity>();

        //    //trường hợp hóa đơn band dầu k có headerExtras
        //    if (entityHeaderExtras == null || !entityHeaderExtras.Any())
        //    {
        //        //nếu hóa đơn sửa k có header extra nào => như cũ
        //        if (modelHeaderExtras == null || !modelHeaderExtras.Any())
        //            return;

        //        //nếu hóa đơn sửa có => thêm mới tất cả
        //        foreach (var item in modelHeaderExtras)
        //        {
        //            lstAdd.Add(new TicketHeaderExtraEntity
        //            {
        //                FieldValue = item.FieldValue,
        //                InvoiceHeaderFieldId = item.InvoiceHeaderFieldId,
        //                InvoiceHeaderId = item.InvoiceHeaderId,
        //                TenantId = item.TenantId
        //            });
        //        }
        //    }
        //    else
        //    {
        //        //nếu hóa đơn sửa không có header extra nào  => xóa hết
        //        if (modelHeaderExtras == null || !modelHeaderExtras.Any())
        //        {
        //            foreach (var item in entityHeaderExtras)
        //            {
        //                lstRemove.Add(item);
        //            }
        //        }
        //        else
        //        {
        //            var index = modelHeaderExtras.GroupBy(x => x.InvoiceHeaderFieldId).ToDictionary(x => x.Key, x => x.FirstOrDefault());

        //            foreach (var item in entityHeaderExtras)
        //            {
        //                //sửa
        //                if (index.ContainsKey(item.InvoiceHeaderFieldId))
        //                {
        //                    item.FieldValue = index[item.InvoiceHeaderFieldId].FieldValue;
        //                    lstModify.Add(item);
        //                }
        //                else
        //                    lstRemove.Add(item);
        //            }

        //            //thêm
        //            var idNewHeaderExtras = index.Keys.Except(entityHeaderExtras.Select(x => x.InvoiceHeaderFieldId));
        //            var newHeaderExtras = index.Where(x => idNewHeaderExtras.Contains(x.Key));
        //            foreach (var item in newHeaderExtras)
        //            {
        //                lstAdd.Add(new TicketHeaderExtraEntity
        //                {
        //                    FieldValue = item.Value.FieldValue,
        //                    InvoiceHeaderFieldId = item.Key,
        //                    InvoiceHeaderId = item.Value.InvoiceHeaderId,
        //                    TenantId = item.Value.TenantId
        //                });
        //            }
        //        }
        //    }

        //    if (lstAdd != null && lstAdd.Any())
        //    {
        //        var rawHeaderExtra = new StringBuilder();
        //        foreach (var iAdd in lstAdd)
        //        {
        //            rawHeaderExtra.Append($@"UNION ALL SELECT
        //                                                {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketHeaderExtra}""'),
        //                                                N'{iAdd.FieldValue}',
        //                                                {iAdd.InvoiceHeaderId},
        //                                                {iAdd.InvoiceHeaderFieldId},
        //                                                '{rawTenantId}',
        //                                                { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
        //                                                1,
        //                                                '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }'
        //                                                FROM DUAL ");
        //        }

        //        if (rawHeaderExtra.Length > 0)
        //        {
        //            rawHeaderExtra = rawHeaderExtra.Remove(0, 9);

        //            rawSql.Append($@"  INSERT INTO ""TicketHeaderExtra"" (
        //                                    ""Id"",
        //                                    ""FieldValue"",
        //                                    ""InvoiceHeaderId"",
        //                                    ""InvoiceHeaderFieldId"",
        //                                    ""TenantId"",
        //                                    ""Partition"",
        //                                    ""IsActive"",
        //                                    ""CreationTime""
        //                                ) {rawHeaderExtra} ; ");
        //        }
        //    }

        //    if (lstModify != null && lstModify.Any())
        //    {
        //        foreach (var iModify in lstModify)
        //        {
        //            rawSql.Append($@"   UPDATE ""TicketHeaderExtra""
        //                                SET
        //                                    ""FieldValue"" = N'{iModify.FieldValue}',
        //                                    ""InvoiceHeaderId"" = {iModify.InvoiceHeaderId},
        //                                    ""InvoiceHeaderFieldId"" = {iModify.InvoiceHeaderFieldId},
        //                                    ""TenantId"" = '{rawTenantId}'
        //                                WHERE ""Id"" = {iModify.Id};
        //                            ");
        //        }
        //    }

        //    if (lstRemove != null && lstRemove.Any())
        //    {
        //        rawSql.Append($@"   DELETE FROM ""TicketHeaderExtra"" WHERE ""Id"" IN ( { String.Join(",", lstRemove.Select(x => x.Id)) } ); ");
        //    }
        //}

        private async Task GenerateUpdateInvoiceTaxbreakdownAsync(StringBuilder rawSql, string rawTenantId, List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, List<TicketTaxBreakdownModel> invoiceTaxBreakdowns, DateTime invoiceDate)
        {
            var modelTaxbreakDowns = invoiceTaxBreakdowns.GroupBy(x => x.VatPercent).ToDictionary(x => x.Key, x => x.ToList());
            var lstAdd = new List<TicketTaxBreakdownEntity>();
            var lstModify = new List<TicketTaxBreakdownEntity>();
            var lstRemove = new List<TicketTaxBreakdownEntity>();

            // SỬA
            if(entityInvoiceTaxBreakDowns != null)
            {
                foreach (var item in entityInvoiceTaxBreakDowns)
                {
                    if (modelTaxbreakDowns.ContainsKey(item.VatPercent))
                    {
                        var taxBreakdownModel = modelTaxbreakDowns[item.VatPercent].First();
                        item.VatAmount = taxBreakdownModel.VatAmount;
                        item.VatAmountBackUp = taxBreakdownModel.VatAmountBackUp;
                        item.Name = taxBreakdownModel.Name;

                        lstModify.Add(item);
                    }
                    else // XÓA
                        lstRemove.Add(item);
                }
            }

            // THÊM MỚI
            foreach (var item in modelTaxbreakDowns)
            {
                var newTaxBreakDown = item.Value.FirstOrDefault();
                if (entityInvoiceTaxBreakDowns == null || !entityInvoiceTaxBreakDowns.Any(x => x.VatPercent == item.Key))
                {
                    lstAdd.Add(new TicketTaxBreakdownEntity
                    {
                        Name = newTaxBreakDown.Name,
                        VatPercent = newTaxBreakDown.VatPercent,
                        VatAmount = newTaxBreakDown.VatAmount,
                        VatAmountBackUp = newTaxBreakDown.VatAmountBackUp,
                        InvoiceHeaderId = newTaxBreakDown.InvoiceHeaderId,
                        VatPercentDisplay = newTaxBreakDown.VatPercentDisplay,
                        TenantId = newTaxBreakDown.TenantId
                    });
                }
            }

            if (lstAdd != null && lstAdd.Any())
            {
                var rawTaxBreakdown = new StringBuilder();

                foreach (var iAdd in lstAdd)
                {
                    rawTaxBreakdown.Append($@"UNION ALL SELECT
                                                            {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                            N'{iAdd.Name}',
                                                            {iAdd.VatPercent},
                                                            {iAdd.VatAmount},
                                                            {iAdd.VatAmountBackUp},
                                                            {iAdd.InvoiceHeaderId},
                                                            N'{iAdd.VatPercentDisplay}',
                                                            '{rawTenantId}',
                                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }
                                                         FROM DUAL ");
                }

                if (rawTaxBreakdown.Length > 0)
                {
                    rawTaxBreakdown = rawTaxBreakdown.Remove(0, 9);

                    rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (
                                            ""Id"",
                                            ""Name"",
                                            ""VatPercent"",
                                            ""VatAmount"",
                                            ""VatAmountBackUp"",
                                            ""InvoiceHeaderId"",
                                            ""VatPercentDisplay"",
                                            ""TenantId"",
                                            ""Partition""
                                        ) {rawTaxBreakdown} ");
                }

                rawSql.Append("; ");
            }

            if (lstModify != null && lstModify.Any())
            {
                foreach (var iModify in lstModify)
                {
                    rawSql.Append($@"   UPDATE ""TicketTaxBreakdown""
                                        SET
                                            ""VatAmount"" = {iModify.VatAmount},
                                            ""VatAmountBackUp"" = {iModify.VatAmountBackUp},
                                            ""Name"" = N'{iModify.Name?.Replace("'", "''")}'
                                        WHERE ""Id"" = {iModify.Id};
                                    ");
                }
            }

            if (lstRemove != null && lstRemove.Any())
            {
                rawSql.Append($@"   DELETE FROM ""TicketTaxBreakdown"" WHERE ""Id"" IN ( { String.Join(",", lstRemove.Select(x => x.Id)) } ); ");
            }
        }

        private async Task Test(CreateTicketHeaderEventSendData input)
        {
            using (OracleConnection con = new OracleConnection("Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoicevcb;Password=Vnis@12A"))
            {
                // Open the connection
                con.Open();

                DataSet ds = new DataSet();

                // Set the connection on the command object
                OracleCommand cmd = new OracleCommand();
                cmd.Connection = con;
                cmd.CommandText = "TEST_GET_MULTIPLE_TABLES";
                cmd.CommandType = CommandType.StoredProcedure;

                OracleDataAdapter da = new OracleDataAdapter();
                da.SelectCommand = cmd;
                da.SelectCommand.Parameters.Add("p_Id", OracleDbType.Long, ParameterDirection.Input).Value = 1;
                da.SelectCommand.Parameters.Add("p_First", OracleDbType.RefCursor, ParameterDirection.Output);
                da.SelectCommand.Parameters.Add("p_Second", OracleDbType.RefCursor, ParameterDirection.Output);

                da.Fill(ds);

                DataTable dt1 = ds.Tables[0];
                DataTable dt2 = ds.Tables[1];

                con.Close();
            }

            ///// 
            try
            {
                var headerId = await GetSEQsNextVal(1, SEQ_Name.SEQ_TicketHeader);
                input.Id = headerId.First();
                var query = await GenerateDrawCreateInvoice(input);

                //var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<string>(query);

                OracleConnection con = new OracleConnection("Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoicevcb;Password=Vnis@12A");

                // Open a database connection
                con.Open();

                OracleCommand cmd = new OracleCommand();

                // INSERT statement with RETURNING clause to get the generated ID 
                cmd.CommandText = query.ToString();
                cmd.Connection = con;

                cmd.Parameters.Add(new OracleParameter
                {
                    ParameterName = ":resHeaderId",
                    OracleDbType = OracleDbType.Long,
                    Value = 0
                });

                // Execute INSERT statement
                var xxx = cmd.ExecuteNonQuery();

                var res = cmd.Parameters[":resHeaderId"].Value.ToString();
            }
            catch (Exception ex)
            {

            }
        }

        private Dictionary<string, string> GetHeaderExtraProperties(List<CommonHeaderExtraModel> invoiceHeaderExtras, Dictionary<string, TicketHeaderFieldEntity> entityHeaderFields)
        {
            var headerExtraProperties = new Dictionary<string, string>();

            var headerExtras = new List<InvoiceHeaderExtraModel>();
            foreach (var item in invoiceHeaderExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityHeaderFields.ContainsKey(item.FieldName))
                    continue;

                var field = entityHeaderFields[item.FieldName];
                headerExtras.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = item.FieldName,
                    FieldValue = item.FieldValue,
                    InvoiceHeaderFieldId = field.Id
                });

            }
            headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));

            return headerExtraProperties;
        }

        private Dictionary<string, string> GetDetailExtraProperties(long detailId, List<CommonDetailExtraModel> invoiceDetailExtras, Dictionary<string, TicketDetailFieldEntity> entityDetailFields)
        {
            var detailExtraProperties = new Dictionary<string, string>();

            var detailExtras = new List<InvoiceDetailExtraModel>();
            foreach (var extra in invoiceDetailExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityDetailFields.ContainsKey(extra.FieldName))
                    continue;

                var field = entityDetailFields[extra.FieldName];
                detailExtras.Add(new InvoiceDetailExtraModel
                {
                    InvoiceDetailId = detailId,
                    InvoiceDetailFieldId = field.Id,
                    FieldValue = extra.FieldValue,
                    FieldName = field.FieldName,
                });
            }
            detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));

            return detailExtraProperties;
        }
    }
}
