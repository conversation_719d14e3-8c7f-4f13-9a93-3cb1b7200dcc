using Core;
using Core.Application.Dtos;
using Core.Caching;
using Core.Dto.Shared.PrintNote;
using Core.Localization.Resources.AbpLocalization;
using Core.ObjectMapping;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Invoice.Models;
using Core.Shared.Invoices;
using Core.Shared.Models;
using Dapper;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Core.Oracle.Domain.Entities.PrintNote;
using VnisCore.Ticket.Application.Factories.Models;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Queries;
using VnisCore.Ticket.Application.Ticket.Models.Responses.Queries;
using VnisCore.Ticket.Infrastructure.IRepository;
using static VnisCore.Ticket.Application.Factories.Constants.CommonTicketConst;
using static VnisCore.Ticket.Application.Ticket.Models.ReadTicketModel;

namespace VnisCore.Ticket.Application.Factories.Caching
{
    public interface ICachedInvoiceBusiness
    {
        Task<InvoiceDateRangeModel> GetOrSetCachedInvoiceDateRangeAsync(Guid tenantId, short type, string invoiceNo, short templateNo, string serialNo, int actionType, List<int> types);

        Task<List<GetHeaderFieldModel>> GetOrSetCachedInvoiceHeaderFieldAsync(Guid tenantId);

        Task<List<GetDetailFieldModel>> GetOrSetCachedDetailFieldAsync(Guid tenantId, short type);

        Task<List<TypeData>> GetOrSetCachedInvoiceOrReferenceInvoiceAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types);

        Task<List<TypeData>> GetOrSetCachedInvoiceReferenceAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types);

        Task<List<ReadTemplateModel>> GetOrSetCachedReadTemplateAsync(Guid tenantId, Guid userId, short templateNo, VnisType vnisType);

        Task<List<RegisterAvailableModel>> GetOrSetCachedRegisterAvailableAsync(Guid tenantId, Guid userId, short templateNo);

        Task<List<GroupCustomerEntity>> GetOrSetCachedGroupCustomerAsync(Guid tenantId, short type);

        Task<PagedResultDto<ViewReferenceInvoiceModel>> ViewReferenceInvoices(PagingReferenceInvoiceRequest input, Guid tenantId);

        Task<ReadTicketModel> GetOrSetCachedInvoiceReferenceRefactorAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types);
    }

    public class CachedInvoiceBusiness : ICachedInvoiceBusiness
    {
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ITicketDetailRepository _ticketDetailRepository;
        private readonly ITicketHeaderRepository _ticketHeaderRepository;
        private readonly ITicketTaxBreakDownRepository _ticketTaxBreakDownRepository;
        private readonly ITicketReferenceRepository _ticketReferenceRepository;
        private readonly ITicketReferenceOldDecreeRepository _ticketReferenceOldDecreeRepository;
        private readonly ITicketDocumentInfoRepository _ticketDocumentInfoRepository;
        private readonly ITicketDocumentRepository _ticketDocumentRepository;
        private readonly IInvoicePrintNoteRepository _invoicePrintNoteRepository;

        public CachedInvoiceBusiness(
            IConfiguration configuration,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ITicketDetailRepository invoice01DetailRepository,
            ITicketHeaderRepository invoice01HeaderRepository,
            ITicketTaxBreakDownRepository invoice01TaxBreakdownRepository,
            ITicketReferenceRepository invoice01ReferenceRepository,
            ITicketDocumentInfoRepository invoice01DocumentInfoRepository,
            ITicketDocumentRepository invoice01DocumentRepository,
            IInvoicePrintNoteRepository invoicePrintNoteRepository,
            ITicketReferenceOldDecreeRepository invoice01ReferenceOldDecreeRepository)
        {
            _configuration = configuration;
            _appFactory = appFactory;
            _localizer = localizer;

            _ticketDetailRepository = invoice01DetailRepository;
            _ticketDocumentInfoRepository = invoice01DocumentInfoRepository;
            _ticketDocumentRepository = invoice01DocumentRepository;
            _ticketHeaderRepository = invoice01HeaderRepository;
            _ticketTaxBreakDownRepository = invoice01TaxBreakdownRepository;
            _ticketReferenceRepository = invoice01ReferenceRepository;
            _invoicePrintNoteRepository = invoicePrintNoteRepository;
            _ticketReferenceOldDecreeRepository = invoice01ReferenceOldDecreeRepository;
        }

        public async Task<InvoiceDateRangeModel> GetOrSetCachedInvoiceDateRangeAsync(Guid tenantId, short type, string invoiceNo, short templateNo, string serialNo, int actionType, List<int> types)
        {
            return await GetInvoiceDateRangeAsync(tenantId, invoiceNo, templateNo, serialNo, actionType, types);
        }

        public async Task<List<GetDetailFieldModel>> GetOrSetCachedDetailFieldAsync(Guid tenantId, short type)
        {
            var cacheKey = CacheKeyBuilder.BuildCacheKey(tenantId.ToString(), DatabaseExtension<TicketDetailFieldEntity>.GetTableName());
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<GetDetailFieldModel>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await GetInvoiceDetailFieldAsync(tenantId),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }

        public async Task<List<GetHeaderFieldModel>> GetOrSetCachedInvoiceHeaderFieldAsync(Guid tenantId)
        {
            var cacheKey = CacheKeyBuilder.BuildCacheKey(tenantId.ToString(), DatabaseExtension<TicketHeaderFieldEntity>.GetTableName());
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<GetHeaderFieldModel>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await GetInvoiceHeaderFieldAsync(tenantId),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }

        public async Task<List<TypeData>> GetOrSetCachedInvoiceOrReferenceInvoiceAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types)
        {
            return await GetInvoiceOrReferenceInvoiceAsync(invoiceId, referenceInvoiceId, tenantId, type, actionType, types);
        }

        public async Task<List<TypeData>> GetOrSetCachedInvoiceReferenceAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types)
        {
            return await GetReferenceInvoiceAsync(invoiceId, referenceInvoiceId, tenantId, type, actionType, types);
        }


        #region
        private async Task<InvoiceDateRangeModel> GetInvoiceDateRangeAsync(Guid tenantId, string invoiceNo, short templateNo, string serialNo, int actionType, List<int> types)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var result = new InvoiceDateRangeModel();

            // Lấy năm mẫu hóa đơn
            var templateYearStr = serialNo.Substring(1, 2);
            var checkYear = int.TryParse(templateYearStr, out int templateYear);

            // Cộng thêm 2000 năm nữa mới chuẩn
            templateYear = 2000 + templateYear;

            if (checkYear == false)
            {
                throw new UserFriendlyException("Mẫu hóa đơn không đúng định dạng");
            }

            if (templateYear > DateTime.Now.Year)
            {
                throw new UserFriendlyException($"Không thể thực hiện tạo/sửa với mẫu năm {templateYear}. Vui lòng kiểm tra lại mẫu");
            }
            DateTime minDayOfYear = new DateTime(templateYear, 1, 1);
            DateTime maxDayOfYear = new DateTime(templateYear, 12, 31);

            var resInvoiceDateRange = new MixInvoiceDateRangeModel();
            var sqlInvoiceTemplate = $@"SELECT ""Id"", ""Name"", ""TemplateNo"", ""SerialNo"" 
                                    FROM ""InvoiceTemplate""
                                    WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 AND ""TemplateNo"" = {templateNo} AND ""SerialNo"" = '{serialNo}'
                                    FETCH FIRST 1 ROW ONLY";

            var invoiceTemplate = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<InvoiceTemplateEntity>(sqlInvoiceTemplate.ToString());
            if (invoiceTemplate == null)
                return new InvoiceDateRangeModel();
            else
                resInvoiceDateRange.Id = invoiceTemplate.Id;

            var sqlMonitor = $@"SELECT ""Id"", ""CurrentNumber"", ""LastDocumentDate"", ""RegistrationHeaderId"", ""ActiveDate"", ""StartNumber"", ""EndNumber"" 
                        invoiceTemplate            FROM ""MonitorInvoiceTemplate"" WHERE ""Id"" = {invoiceTemplate?.Id} ";

            var monitorEntity = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<MonitorInvoiceTemplateEntity>(sqlMonitor.ToString());
            if (monitorEntity == null)
                return new InvoiceDateRangeModel();
            else
                resInvoiceDateRange.Monitor = monitorEntity;

            if (!string.IsNullOrEmpty(invoiceNo))
            {
                var no = int.Parse(invoiceNo);
                var sqlPrevInvoiceDate = $@" SELECT ""InvoiceDate"" 
                                    FROM ""TicketHeader""
                                    WHERE ""InvoiceTemplateId"" = {invoiceTemplate?.Id} AND ""Number"" < {no} AND ""InvoiceStatus"" != {InvoiceStatus.XoaBo.GetHashCode()} AND ""InvoiceStatus"" != {InvoiceStatus.XoaHuy.GetHashCode()}
                                    ORDER BY ""InvoiceNo"" DESC
                                    FETCH FIRST 1 ROW ONLY";

                var invoiceHeader1 = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<TicketHeaderEntity>(sqlPrevInvoiceDate.ToString());
                if (invoiceHeader1 != null)
                    resInvoiceDateRange.PrevInvoiceDate = invoiceHeader1.InvoiceDate;

                var sqlNextInvoiceDate = $@"SELECT ""InvoiceDate"" 
                                    FROM ""TicketHeader""
                                    WHERE ""InvoiceTemplateId"" = {invoiceTemplate?.Id} AND ""Number"" > {no} AND ""InvoiceStatus"" != {InvoiceStatus.XoaBo.GetHashCode()} AND ""InvoiceStatus"" != {InvoiceStatus.XoaHuy.GetHashCode()}
                                    ORDER BY ""InvoiceNo""
                                    FETCH FIRST 1 ROW ONLY";

                var invoiceHeader2 = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<TicketHeaderEntity>(sqlNextInvoiceDate.ToString());
                if (invoiceHeader2 != null)
                    resInvoiceDateRange.NextInvoiceDate = invoiceHeader2.InvoiceDate;
            }

            if (resInvoiceDateRange != null && resInvoiceDateRange.Id.HasValue)
            {
                var monitor = (resInvoiceDateRange != null && resInvoiceDateRange.Monitor != null) ? resInvoiceDateRange.Monitor : new MonitorInvoiceTemplateEntity();

                if (actionType == GetDataActionType.CreateInvoice
                    || actionType == GetDataActionType.CopyInvoice
                    || actionType == GetDataActionType.CreateInvoiceReplace
                    || actionType == GetDataActionType.CreateInvoiceManyAdjustment)
                {
                    result = await InvoiceDateRangeAsync(templateNo, serialNo, null, resInvoiceDateRange.Id, resInvoiceDateRange.PrevInvoiceDate, resInvoiceDateRange.NextInvoiceDate, monitor);
                }
                else
                {
                    result = await InvoiceDateRangeAsync(templateNo, serialNo, invoiceNo, resInvoiceDateRange.Id, resInvoiceDateRange.PrevInvoiceDate, resInvoiceDateRange.NextInvoiceDate, monitor);
                }
            }
            else
            {
                if (actionType != GetDataActionType.ViewInvoice &&
                    actionType != GetDataActionType.ViewInvoiceReplace)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.InvoiceDateRangeNotFound"]);
            }

            // TH Ki hieu khong thuoc nam hien tai
            if (result.Min < minDayOfYear)
            {
                result.Min = minDayOfYear;
            }
            if (result.Max > maxDayOfYear)
            {
                result.Max = maxDayOfYear;
            }

            return result;
        }

        private async Task<List<TypeData>> GetReferenceInvoiceAsync(long? invoiceId, long? referenceInvoiceId, Guid tenantId, short type, int actionType, List<int> types)
        {
            var result = new ReadTicketModel();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var sql = new StringBuilder();

            var lstIds = new List<long>();
            if (invoiceId.HasValue) lstIds.Add(invoiceId.Value);
            if (referenceInvoiceId.HasValue) lstIds.Add(referenceInvoiceId.Value);

            if (lstIds != null && lstIds.Any())
            {
                sql.Append($@" 
                            WITH InvoiceIds AS (
                                    SELECT ""InvoiceHeaderId"" AS Id FROM ""TicketReference"" WHERE ""InvoiceReferenceId"" = {invoiceId.Value} AND ""TenantId"" = '{rawTenantId}'
                                    UNION ALL
                                    SELECT {invoiceId.Value} AS Id FROM DUAL
                            ),
                            Headers AS (
                                    SELECT * FROM ""TicketHeader"" WHERE ""Id"" IN (SELECT * FROM InvoiceIds) AND ""TenantId"" = '{rawTenantId}'
                            ),                                                                              
                            TaxBreakdowns AS(                                                                           
                                SELECT A.""Id"", A.""Name"", A.""VatAmount"", A.""VatPercent"", A.""VatAmountBackUp"", A.""InvoiceHeaderId""              
                                FROM ""TicketTaxBreakdown"" A 
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                            ),                                                                                          
                            Refer AS(                                                                                   
                                SELECT A.""InvoiceReferenceId"", A.""TemplateNoReference"",                                 
                                       A.""SerialNoReference"", A.""InvoiceNoReference"",                                   
                                       A.""InvoiceDateReference"", A.""Note"", A.""InvoiceHeaderId""                                                
                                FROM ""TicketReference"" A   
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                            ),                                                                                          
                            Details AS(                                                                                 
                                SELECT A.""Id"", A.""Index"", A.""DiscountAmountBeforeTax"", A.""DiscountPercentBeforeTax"",    
                                        A.""PaymentAmount"", A.""ProductId"", A.""ProductCode"", A.""ProductName"", A.""ProductType"",             
                                        A.""UnitId"", A.""UnitName"", A.""UnitPrice"", A.""RoundingUnit"",                      
                                        A.""Quantity"", A.""Amount"", A.""VatPercent"", A.""VatAmount"",                        
                                        A.""Note"", A.""HideUnitPrice"", A.""HideUnit"", A.""HideQuantity"", A.""InvoiceHeaderId"", A.""ExtraProperties""                     
                                FROM ""TicketDetail"" A                   
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                                ORDER BY A.""Index""
                            ), 
                            DocumentInfos AS (
                                SELECT A.""Id"", A.""FileId"", A.""DocumentNo"", A.""DocumentDate"", A.""DocumentReason"", A.""Type"", A.""IsUploadFile""
                                FROM ""TicketDocumentInfo"" A
                                WHERE A.""InvoiceHeaderId"" = {invoiceId.Value}
                            ),
                            Documents AS (
                                SELECT A.""Id"", A.""PhysicalFileName"", A.""ContentType"", A.""FileName"", A.""Length""
                                FROM ""TicketDocument"" A
                                WHERE A.""InvoiceHeaderId"" = {invoiceId.Value} AND A.""IsDeleted"" = 0
                            )
                ");
            }

            if (types.Any(x => x == DataType.Invoice))
            {
                sql.Append($@"SELECT {DataType.Invoice} ""Type"", 
                                    (JSON_OBJECT(
                                        H.*,                                                
                                        'InvoiceReference' VALUE (                                                                                   
                                            SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                            FROM Refer 
                                            WHERE ""InvoiceHeaderId"" = {invoiceId}
                                        ),                                                         
                                        'InvoiceDetails' VALUE (                                                                                   
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        D.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Details D  
                                            WHERE D.""InvoiceHeaderId"" = {invoiceId}
                                        ),
                                        'DocumentInfos' VALUE (
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        I.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM DocumentInfos I  
                                        ),
                                        'InvoiceTaxBreakdowns' VALUE (                                                                                   
                                            SELECT ( JSON_ARRAYAGG (JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB) )         
                                            FROM TaxBreakdowns 
                                            WHERE ""InvoiceHeaderId"" = {invoiceId}
                                        ),

                                        'Documents' VALUE (
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        O.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Documents O  
                                        )
                                    RETURNING CLOB)) ""Data""
                            FROM Headers H                                                                 
                            WHERE H.""Id"" = {invoiceId}");
            }

            if (types.Any(x => x == DataType.ReferenceInvoice))
            {
                sql.Append(" UNION ALL ");
                sql.Append($@"SELECT {DataType.ReferenceInvoice} ""Type"", 
                                    (JSON_OBJECT(
                                        H.*,                                                     
                                        'InvoiceReference' VALUE (                                                                                   
                                            SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                            FROM Refer   
                                            WHERE ""InvoiceHeaderId"" = H.""Id""
                                        ),                                                         
                                        'InvoiceDetails' VALUE (                                                                                   
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        D.*                                                 
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Details D 
                                            WHERE D.""InvoiceHeaderId"" = H.""Id""
                                        )
                                    RETURNING CLOB)) ""Data""
                            FROM Headers H                                                                 
                            WHERE H.""Id"" != {invoiceId.Value}");
            }
            var abc = sql.ToString();

            try
            {
                var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());

                return data.ToList();

            }
            catch (Exception ex)
            {

                throw;
            }
        }

        private async Task<List<TypeData>> GetInvoiceOrReferenceInvoiceAsync(long? invoiceId, long? referenceInvoiceId, Guid tenantId, short type, int actionType, List<int> types)
        {
            var result = new ReadTicketModel();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var sql = new StringBuilder();

            var lstIds = new List<long>();
            if (invoiceId.HasValue) lstIds.Add(invoiceId.Value);
            if (referenceInvoiceId.HasValue) lstIds.Add(referenceInvoiceId.Value);

            if (lstIds != null && lstIds.Any())
            {
                sql.Append($@" WITH
                            InvoiceIds AS (
                                    SELECT ""InvoiceReferenceId"" AS Id FROM ""TicketReference"" WHERE ""InvoiceHeaderId"" = {invoiceId.Value} AND ""TenantId"" = '{rawTenantId}'
                                    UNION ALL
                                    SELECT {invoiceId.Value} AS Id FROM DUAL
                            ),
                            Headers AS (
                                    SELECT * FROM ""TicketHeader"" WHERE ""Id"" IN (SELECT * FROM InvoiceIds) AND ""TenantId"" = '{rawTenantId}'
                            ),                                                                              
                            TaxBreakdowns AS(                                                                           
                                SELECT A.""Id"", A.""Name"", A.""VatAmount"", A.""VatPercent"", A.""VatAmountBackUp"", A.""InvoiceHeaderId""              
                                FROM ""TicketTaxBreakdown"" A 
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                            ),                                                                                          
                            Refer AS(                                                                                   
                                SELECT A.""InvoiceReferenceId"", A.""TemplateNoReference"",                                 
                                       A.""SerialNoReference"", A.""InvoiceNoReference"",                                   
                                       A.""InvoiceDateReference"", A.""Note"", A.""InvoiceHeaderId""                                                
                                FROM ""TicketReference"" A   
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                            ),                                                                                          
                            Details AS(                                                                                 
                                SELECT A.""Id"", A.""Index"", A.""DiscountAmountBeforeTax"", A.""DiscountPercentBeforeTax"",    
                                        A.""PaymentAmount"", A.""ProductId"", A.""ProductCode"", A.""ProductName"", A.""ProductType"",             
                                        A.""UnitId"", A.""UnitName"", A.""UnitPrice"", A.""RoundingUnit"",                      
                                        A.""Quantity"", A.""Amount"", A.""VatPercent"", A.""VatAmount"",                        
                                        A.""Note"", A.""HideUnitPrice"", A.""HideUnit"", A.""HideQuantity"", A.""InvoiceHeaderId"", A.""ExtraProperties""                     
                                FROM ""TicketDetail"" A                   
                                INNER JOIN Headers B ON A.""InvoiceHeaderId"" = B.""Id""
                                ORDER BY A.""Index""
                            ), 
                            DocumentInfos AS (
                                SELECT A.""Id"", A.""FileId"", A.""DocumentNo"", A.""DocumentDate"", A.""DocumentReason"", A.""Type"", A.""IsUploadFile""
                                FROM ""TicketDocumentInfo"" A
                                WHERE A.""InvoiceHeaderId"" = {invoiceId.Value}
                            ),
                            Documents AS (
                                SELECT A.""Id"", A.""PhysicalFileName"", A.""ContentType"", A.""FileName"", A.""Length""
                                FROM ""TicketDocument"" A
                                WHERE A.""InvoiceHeaderId"" = {invoiceId.Value} AND A.""IsDeleted"" = 0
                            )
                ");

                if (types.Any(x => x == DataType.Invoice))
                {
                    sql.Append($@"SELECT {DataType.Invoice} ""Type"", 
                                    (JSON_OBJECT(
                                        H.*,                                                
                                        'InvoiceReference' VALUE (                                                                                   
                                            SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                            FROM Refer 
                                            WHERE ""InvoiceHeaderId"" = {invoiceId}
                                        ),                                                         
                                        'InvoiceDetails' VALUE (                                                                                   
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        D.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Details D  
                                            WHERE D.""InvoiceHeaderId"" = {invoiceId}
                                        ),
                                        'DocumentInfos' VALUE (
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        I.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM DocumentInfos I  
                                        ),
                                        'InvoiceTaxBreakdowns' VALUE (                                                                                   
                                            SELECT ( JSON_ARRAYAGG (JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB) )         
                                            FROM TaxBreakdowns 
                                            WHERE ""InvoiceHeaderId"" = {invoiceId}
                                        ),
                                        'Documents' VALUE (
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        O.*                                                     
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Documents O  
                                        )
                                    RETURNING CLOB)) ""Data""
                            FROM Headers H                                                                 
                            WHERE H.""Id"" = {invoiceId}");
                }

                if (types.Any(x => x == DataType.ReferenceInvoice))
                {
                    sql.Append(" UNION ALL ");
                    sql.Append($@"SELECT {DataType.ReferenceInvoice} ""Type"", 
                                    (JSON_OBJECT(
                                        H.*,    
                                        'InvoiceTaxBreakdowns' VALUE (                                                                                   
                                            SELECT ( JSON_ARRAYAGG (JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB) )         
                                            FROM TaxBreakdowns     
                                            WHERE ""InvoiceHeaderId"" = H.""Id""
                                        ), 
                                        'InvoiceReference' VALUE (                                                                                   
                                            SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                            FROM Refer   
                                            WHERE ""InvoiceHeaderId"" = H.""Id""
                                        ),                                                         
                                        'InvoiceDetails' VALUE (                                                                                   
                                            SELECT                                                                          
                                            (                                                                               
                                                JSON_ARRAYAGG(                                                              
                                                    JSON_OBJECT(                                                            
                                                        D.*                                                 
                                                    )   
                                                    RETURNING CLOB                                                         
                                                )                                                                          
                                            )                                                                              
                                            FROM Details D 
                                            WHERE D.""InvoiceHeaderId"" = H.""Id""
                                        )
                                    RETURNING CLOB)) ""Data""
                            FROM Headers H                                                                 
                            WHERE H.""Id"" != {invoiceId.Value}");
                }
            }
            var abc = sql.ToString();

            try
            {
                var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());

                return data.ToList();

            }
            catch (Exception ex)
            {

                throw;
            }
        }

        private async Task<List<GetHeaderFieldModel>> GetInvoiceHeaderFieldAsync(Guid tenantId)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var sqlHeaderFields = $@" SELECT  ""Id"",                                                         
                                        ""FieldName"",                                             
                                        ""DisplayName"",
                                        ""Metadata"",
                                        ""CreationTime""
                                FROM ""TicketHeaderField""                                               
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<GetHeaderFieldModel>(sqlHeaderFields.ToString())).ToList();
        }

        private async Task<List<GetDetailFieldModel>> GetInvoiceDetailFieldAsync(Guid tenantId)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var sqlHeaderFields = $@" SELECT  ""Id"",                                                         
                                        ""FieldName"",                                             
                                        ""DisplayName"",
                                        ""Metadata"",
                                        ""CreationTime""
                                FROM ""TicketDetailField""                                               
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<GetDetailFieldModel>(sqlHeaderFields.ToString())).ToList();
        }

        private async Task<InvoiceDateRangeModel> InvoiceDateRangeAsync(short templateNo, string serialNo, string invoiceNo, long? templateId, DateTime? previousDate, DateTime? nextDate, MonitorInvoiceTemplateEntity monitor)
        {
            var result = new InvoiceDateRangeModel();

            // Lấy năm mẫu hóa đơn
            var templateYearStr = serialNo.Substring(1, 2);
            var checkYear = int.TryParse(templateYearStr, out int templateYear);

            // Cộng thêm 2000 năm nữa mới chuẩn
            templateYear = 2000 + templateYear;

            if (checkYear == false)
            {
                throw new UserFriendlyException("Mẫu hóa đơn không đúng định dạng");
            }

            if (templateYear > DateTime.Now.Year)
            {
                throw new UserFriendlyException($"Không thể thực hiện tạo/sửa với mẫu năm {templateYear}. Vui lòng kiểm tra lại mẫu");
            }
            DateTime minDayOfYear = new DateTime(templateYear, 1, 1);
            DateTime maxDayOfYear = new DateTime(templateYear, 12, 31);

            if (!templateId.HasValue) // Không tìm thấy mẫu hóa đơn
                return result;

            result = new InvoiceDateRangeModel
            {
                TemplateId = templateId.Value,
                TemplateNo = templateNo,
                SerialNo = serialNo,
            };

            if (monitor == null) // Không tìm thấy mẫu hóa đơn
                return result;

            if (!monitor.RegistrationHeaderId.HasValue) // Mẫu hóa đơn chưa đăng ký phát hành
                return result;

            result.RegistrationHeaderId = monitor.RegistrationHeaderId.Value; //nếu báo lỗi chỗ này là chưa dkph

            //Nếu tạo hóa đơn mới, điều chỉnh, thay thế
            if (string.IsNullOrEmpty(invoiceNo))
            {
                result.Max = DateTime.SpecifyKind(DateTime.Now.Date, DateTimeKind.Local);
                if (monitor.LastDocumentDate.HasValue)
                {
                    //result.Min = DateTime.SpecifyKind(monitor.LastDocumentDate.Value.Date, DateTimeKind.Local);

                    //nếu đã hết số của dải hiện tại => lấy activeDate của dải tiếp theo
                    if (monitor.CurrentNumber == monitor.EndNumber && monitor.EndNumber == StaticData.MaxInvoiceNo)
                    {
                        return null;
                        ////lấy dkph tiếp theo của mẫu hiện tại
                        //var nextRegister = registrationDetails.FirstOrDefault(x => x.StartNumber == monitor.CurrentNumber.Value + 1);

                        ////nếu không có dkph tiếp theo => lấy min = LastInvoiceDate
                        //if (nextRegister == null)
                        //    result.Min = DateTime.SpecifyKind(monitor.LastDocumentDate.Value.Date, DateTimeKind.Local);
                        //else
                        //{
                        //    //nếu có dkph tiếp theo 
                        //    if (monitor.LastDocumentDate.Value > nextRegister.ActiveDate.Date)
                        //        result.Min = DateTime.SpecifyKind(monitor.LastDocumentDate.Value.Date, DateTimeKind.Local);
                        //    else
                        //        result.Min = DateTime.SpecifyKind(nextRegister.ActiveDate.Date, DateTimeKind.Local);
                        //}
                    }
                    //Nếu đang sinh số trong dải bình thường mà k phải số cuối cùng
                    else
                    {
                        //check với Active Date
                        // LastInvoiceDate <= ActiveDate khi mới vừa được chuyển dải số, đây là số đầu tiên được tạo của dải số đó
                        if (monitor.LastDocumentDate.Value <= monitor.ActiveDate)
                            result.Min = DateTime.SpecifyKind(monitor.ActiveDate.Value.Date, DateTimeKind.Local);
                        else // trường hợp số vẫn đddng được tạo trong 1 dải nào đó
                            result.Min = DateTime.SpecifyKind(monitor.LastDocumentDate.Value.Date, DateTimeKind.Local);
                    }

                }
                //1. Chưa sinh số nào (LastInvoiceDate = null && CurrentNumber = StartNumber - 1)
                //2. Đã sinh số nhưng lại xóa hủy/xóa bỏ các số đã được sinh ra (LastInvoiceDate = null && CurrentNumber >= StartNumber && CurrentNumber <= EndNumber)
                else
                {
                    //là số cuối cùng
                    if (monitor.CurrentNumber == monitor.EndNumber && monitor.EndNumber == StaticData.MaxInvoiceNo)
                    {
                        return null;
                    }
                    else
                    {
                        //đang tạo số bình thường trong 1 dải
                        result.Min = DateTime.SpecifyKind(monitor.ActiveDate.Value.Date, DateTimeKind.Local);
                    }
                }
            }
            else
            {
                //nếu là sửa
                var no = int.Parse(invoiceNo);

                //trường hợp là hóa đơn của dải đkph thứ hai thì ngày hóa đơn phải lớn hơn = ngày dkph2 và phải lớn hơn =  ngày hóa đơn cuối cùng của dải dkph 1
                if (!nextDate.HasValue) //nếu k có hóa đơn ở sau => max là ngày hiện tại
                    result.Max = DateTime.SpecifyKind(DateTime.Now.Date, DateTimeKind.Local);
                else //có hóa đơn ở sau thì ngày hóa đơn max = ngày hóa đơn sau
                    result.Max = DateTime.SpecifyKind(nextDate.Value.Date, DateTimeKind.Local);

                //k có hóa đơn trước thì min = ngày dkph
                if (!previousDate.HasValue)
                    result.Min = DateTime.SpecifyKind(monitor.ActiveDate.Value.Date, DateTimeKind.Local);
                else
                {
                    // trường hợp có hóa đơn previous, previous thuộc dải số dkph trước, số hiện tại đang sửa là dkph sau 
                    //=> check xem ngày hóa đơn previous có lớn hơn ngày dkph của dải sau không
                    if (previousDate.Value.Date > monitor.ActiveDate.Value.Date)
                        result.Min = DateTime.SpecifyKind(previousDate.Value.Date, DateTimeKind.Local);
                    else
                        result.Min = DateTime.SpecifyKind(monitor.ActiveDate.Value.Date, DateTimeKind.Local);
                }

            }

            // TH Ki hieu khong thuoc nam hien tai
            if (result.Min < minDayOfYear)
            {
                result.Min = minDayOfYear;
            }
            if (result.Max > maxDayOfYear)
            {
                result.Max = maxDayOfYear;
            }

            return result;
        }
        #endregion

        public async Task<List<ReadTemplateModel>> GetOrSetCachedReadTemplateAsync(Guid tenantId, Guid userId, short templateNo, VnisType vnisType)
        {
            var cacheKey = tenantId.ToString() + "-" + userId.ToString() + "-" + templateNo.ToString();
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<ReadTemplateModel>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await GetInvoiceTemplateAsync(tenantId, userId, vnisType),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }

        private async Task<List<ReadTemplateModel>> GetInvoiceTemplateAsync(Guid tenantId, Guid userId, VnisType invoiceType)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var sqlReadTemplates = $@"  SELECT a.""Id"", a.""Name"", a.""SerialNo"", a.""TemplateNo"" 
                                        FROM ""InvoiceTemplate"" a
                                        INNER JOIN ""UserReadTemplate"" b on a.""Id"" = b.""InvoiceTemplateId""
                                        WHERE a.""TenantId"" = '{rawTenantId}' 
                                            AND b.""UserId"" = '{rawUserId}' 
                                            AND a.""Type"" = {invoiceType.GetHashCode()} 
                                            AND a.""IsDeleted"" = 0
                                    ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<ReadTemplateModel>(sqlReadTemplates.ToString())).ToList();
        }

        public async Task<List<RegisterAvailableModel>> GetOrSetCachedRegisterAvailableAsync(Guid tenantId, Guid userId, short templateNo)
        {

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);
            var formatDate = DateTime.Now.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")); //thêm 1 ngày vì thời gian lưu của tct có giờ

            var sqlGetRegisterAvailabilities = $@"      
                        SELECT  T.""Id"", T.""Name"", T.""TemplateNo"", T.""SerialNo""
                        FROM ""InvoiceTemplate"" T
                        INNER JOIN(
                            SELECT ""TemplateId""
                            FROM ""AccountTokenTemplate""
                            WHERE ""UserId"" = '{rawUserId}' AND ""TemplateId"" IS NOT NULL
                        ) A ON T.""Id"" = A.""TemplateId""
                        INNER JOIN (
                            SELECT ""Id"", ""CurrentNumber"", ""EndNumber""
                            FROM ""MonitorInvoiceTemplate""
                            WHERE ""CurrentNumber"" < ""EndNumber"" AND ""TenantId"" = '{rawTenantId}'
                        ) M ON M.""Id"" = T.""Id""
                        INNER JOIN (
                            SELECT ""InvoiceTypes"", ""InvoiceHasCode"" FROM 
                                (
                                    SELECT ""InvoiceTypes"", ""InvoiceHasCode""
                                    FROM ""NewRegistrationHeader""
                                    WHERE
                                    ""NewRegistrationHeader"".""Status"" = {RegistrationTvanStatus.GDTAccepted.GetHashCode()} 
                                    AND ""InvoiceTypes"" IS NOT NULL AND LENGTH(TRIM(""InvoiceTypes"")) > 1
                                    AND ""NewRegistrationHeader"".""GDTResponseTime"" IS NOT NULL
                                    AND ""NewRegistrationHeader"".""GDTResponseTime"" < '{formatDate}' 
                                    AND ""NewRegistrationHeader"".""TenantId"" = '{rawTenantId}'
                                    AND ""IsDeleted"" = 0
                                    ORDER BY ""Id"" DESC
                                    FETCH FIRST 1 ROWS ONLY
                                )
                            WHERE ""InvoiceTypes"" LIKE '%1%' 
                        ) R ON 1 = 1
                        WHERE T.""TenantId"" = '{rawTenantId}' 
                            AND T.""IsDeleted"" = 0 
                            AND  ""FileId"" IS NOT NULL 
                            AND T.""TemplateNo"" = {templateNo}
                            -- AND EXTRACT(YEAR FROM T.""CreationTime"") = EXTRACT(YEAR FROM SYSDATE)
                            AND  (
                                (R.""InvoiceHasCode"" = 0 AND T.""SerialNo"" Like 'K%')
                                OR (R.""InvoiceHasCode"" = 1 AND T.""SerialNo"" Like 'C%')
                            )
                        ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<RegisterAvailableModel>(sqlGetRegisterAvailabilities.ToString())).ToList();
        }

        public async Task<List<GroupCustomerEntity>> GetOrSetCachedGroupCustomerAsync(Guid tenantId, short type)
        {
            var cacheKey = tenantId.ToString() + DatabaseExtension<GroupCustomerEntity>.GetTableName();
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<GroupCustomerEntity>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await GetGroupCustomerAsync(tenantId),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }

        private async Task<List<GroupCustomerEntity>> GetGroupCustomerAsync(Guid tenantId)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var sql = @$"SELECT * FROM ""GroupCustomer"" WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<GroupCustomerEntity>(sql)).ToList();
        }
        public async Task<PagedResultDto<ViewReferenceInvoiceModel>> ViewReferenceInvoices(PagingReferenceInvoiceRequest input, Guid tenantId)
        {
            // Lấy thông tin hóa đơn gốc (hóa đơn bị điều chỉnh)
            var rootInvoice = await _ticketHeaderRepository.GetAsync(input.InvoiceHeaderId);

            if (rootInvoice.TenantId != tenantId)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin hóa đơn");
            }
            // Áp dụng với các HD hiện tại
            if (rootInvoice.ReferenceInvoiceType == 0)
            {
                // Lấy thông tin hóa đơn liên quan tới hóa đơn gốc (hóa đơn điều chỉnh)
                var referenceInfos = await _ticketReferenceRepository.getInvoiceReferenceById(input.InvoiceHeaderId, tenantId);

                // Lấy thông tin hóa đơn liên quan
                List<TicketHeaderEntity> referenceInvoices = await _ticketHeaderRepository.getInvoiceByIds(referenceInfos.Select(x => x.InvoiceReferenceId).ToList(), tenantId);

                if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                    || rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                    || rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                {
                    // Trường hợp HD được chọn là Điều chỉnh
                    // Lấy đc HD liên quan là Gốc
                    // Từ HD gốc lấy HD liên quan
                    var gocInvoice = referenceInvoices.FirstOrDefault();
                    referenceInfos = await _ticketReferenceRepository.getInvoiceReferenceById(gocInvoice.Id, tenantId);
                    referenceInfos.Add(new TicketReferenceEntity() { InvoiceReferenceId = gocInvoice.Id });

                    // HD lien quan khong chua hoa don hien tai
                    if (referenceInfos.Where(x => x.InvoiceReferenceId == input.InvoiceHeaderId).Any())
                    {
                        var removeReference = referenceInfos.Where(x => x.InvoiceReferenceId == input.InvoiceHeaderId).ToList();
                        referenceInfos.RemoveAll(removeReference);
                    }

                    referenceInvoices = await _ticketHeaderRepository.getInvoiceByIds(referenceInfos.Select(x => x.InvoiceReferenceId).ToList(), tenantId);
                }
                referenceInvoices = referenceInvoices.Where(invoice => invoice.InvoiceStatus != (short)InvoiceStatus.XoaHuy).OrderByDescending(x => x.Id).ToList();

                var result = new PagedResultDto<ViewReferenceInvoiceModel>();
                result.TotalCount = referenceInvoices.Count;
                result.Items = referenceInvoices.Skip(input.SkipCount).Take(input.MaxResultCount).Select(x => new ViewReferenceInvoiceModel()
                {
                    Id = x.Id,
                    TemplateNo = x.TemplateNo.ToString(),
                    SerialNo = x.SerialNo,
                    InvoiceNo = x.InvoiceNo,
                    InvoiceStatus = x.InvoiceStatus,
                    SignStatus = x.SignStatus,
                    ApproveStatus = x.ApproveStatus,
                    ApproveCancelStatus = x.ApproveCancelStatus,
                    ApproveDeleteStatus = x.ApproveDeleteStatus,
                    InvoiceDate = x.InvoiceDate,
                    StatusTvan = x.StatusTvan,
                    VerificationCode = x.VerificationCode,
                    IsDeclared = x.IsDeclared,
                }).ToList();

                return result;
            }
            else
            {
                // Áp dụng với các HD lập Thay thế/Điều chỉnh khác
                // Lấy thông tin hóa đơn liên quan tới hóa đơn gốc (hóa đơn điều chỉnh)
                var referenceInfos = await _ticketReferenceOldDecreeRepository.getInvoiceReferenceById(input.InvoiceHeaderId, tenantId);

                var result = new PagedResultDto<ViewReferenceInvoiceModel>();
                result.TotalCount = referenceInfos.Count;
                result.Items = referenceInfos.Skip(input.SkipCount).Take(input.MaxResultCount).Select(x => new ViewReferenceInvoiceModel()
                {
                    Id = x.Id,
                    TemplateNo = x.TemplateNoReference,
                    SerialNo = x.SerialNoReference,
                    InvoiceNo = x.InvoiceNoReference,
                    InvoiceDate = x.InvoiceDateReference,
                }).ToList();

                return result;
            }

        }

        public async Task<ReadTicketModel> GetOrSetCachedInvoiceReferenceRefactorAsync(Guid tenantId, short type, long? invoiceId, long? referenceInvoiceId, int actionType, List<int> types)
        {
            try
            {
                ReadTicketModel result = new ReadTicketModel();

                var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
                var sql = new StringBuilder();

                var lstIds = new List<long>();
                if (invoiceId.HasValue) lstIds.Add(invoiceId.Value);
                if (referenceInvoiceId.HasValue) lstIds.Add(referenceInvoiceId.Value);

                if (lstIds != null && lstIds.Any())
                {
                    // Lấy thông tin hóa đơn gốc (hóa đơn bị điều chỉnh)
                    var rootInvoice = await _ticketHeaderRepository.GetAsync(invoiceId.Value);

                    // Lấy thông tin hóa đơn liên quan tới hóa đơn gốc (hóa đơn điều chỉnh)
                    var referenceInfos = await _ticketReferenceRepository.getInvoiceReferenceById(invoiceId.Value, tenantId);

                    // Lấy thông tin hóa đơn liên quan
                    List<TicketHeaderEntity> referenceInvoices = await _ticketHeaderRepository.getInvoiceByIds(referenceInfos.Select(x => x.InvoiceReferenceId).ToList(), tenantId);

                    if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                        || rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                        || rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                    {
                        // Trường hợp HD được chọn là Điều chỉnh
                        // Lấy đc HD liên quan là Gốc
                        // Từ HD gốc lấy HD liên quan
                        var gocInvoice = referenceInvoices.FirstOrDefault();
                        referenceInfos = await _ticketReferenceRepository.getInvoiceReferenceById(gocInvoice.Id, tenantId);
                        referenceInfos.Add(new TicketReferenceEntity() { InvoiceReferenceId = gocInvoice.Id });

                        // HD lien quan khong chua hoa don hien tai
                        if (referenceInfos.Where(x => x.InvoiceReferenceId == invoiceId.Value).Any())
                        {
                            var removeReference = referenceInfos.Where(x => x.InvoiceReferenceId == invoiceId.Value).ToList();
                            referenceInfos.RemoveAll(removeReference);
                        }

                        referenceInvoices = await _ticketHeaderRepository.getInvoiceByIds(referenceInfos.Select(x => x.InvoiceReferenceId).ToList(), tenantId);
                    }
                    referenceInvoices = referenceInvoices.Where(invoice => invoice.InvoiceStatus != (short)InvoiceStatus.XoaHuy).ToList();

                    // Lưu DS Id hóa đơn 
                    var ids = referenceInfos.Select(x => x.InvoiceReferenceId).ToList();
                    if (!ids.Contains(rootInvoice.Id))
                    {
                        ids.Add(rootInvoice.Id);
                    }

                    // Lấy thông tin % thuế suất của các hd điều chỉnh, bị điều chỉnh
                    var taxBreakdowns = await _ticketTaxBreakDownRepository.getTaxBreakDownByIds(ids, tenantId);

                    // Lấy thông tin chi tiết sản phẩm của các hd điều chỉnh, bị điều chỉnh
                    var detailInvoices = await _ticketDetailRepository.getInvoiceByIds(ids, tenantId);

                    // Lấy thông tin biên bản của các hd điều chỉnh, bị điều chỉnh
                    var documentInfos = await _ticketDocumentInfoRepository.getInvoiceByIds(ids, tenantId);

                    // Lay thong tin bien ban
                    var documents = await _ticketDocumentRepository.getInvoiceByIds(ids, tenantId);

                    // Mapping dữ liệu hóa đơn gốc
                    result = _appFactory.ObjectMapper.Map<TicketHeaderEntity, ReadTicketModel>(rootInvoice);

                    if (rootInvoice.ExtraProperties != null)
                    {
                        result.ExtraProperties = JsonConvert.SerializeObject(rootInvoice.ExtraProperties);

                        if (!string.IsNullOrEmpty(result.ExtraProperties))
                        {
                            var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(result.ExtraProperties);
                            var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                            if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                            {
                                headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                            }

                            if (headerExtraProperties.Any())
                            {
                                result.InvoiceHeaderExtras = headerExtraProperties.Select(x => new ReadTicketModel.GetTicketHeaderExtraResponseModel
                                {
                                    FieldValue = x.FieldValue,
                                    FieldName = x.FieldName
                                }).ToList();
                            }
                        }
                    }

                    result.InvoiceDetails = _appFactory.ObjectMapper.Map<List<TicketDetailEntity>, List<ReadTicketModel.GetTicketDetailResponseModel>>(detailInvoices.Where(x => x.InvoiceHeaderId == invoiceId.Value).ToList());

                    if (result.InvoiceDetails.Any())
                    {
                        foreach (var item in result.InvoiceDetails)
                        {
                            if (detailInvoices.Where(x => x.Id == item.Id).FirstOrDefault()?.ExtraProperties != null)
                            {
                                item.ExtraProperties = JsonConvert.SerializeObject(detailInvoices.Where(x => x.Id == item.Id).FirstOrDefault()?.ExtraProperties);
                                if (!string.IsNullOrEmpty(item.ExtraProperties))
                                {
                                    var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.ExtraProperties);
                                    var detailExtraProperties = new List<InvoiceDetailExtraModel>();
                                    if (extraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(extraProperties["invoiceDetailExtras"]))
                                    {
                                        detailExtraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(extraProperties["invoiceDetailExtras"]);
                                    }
                                    if (detailExtraProperties.Any())
                                    {
                                        item.InvoiceDetailExtras = detailExtraProperties.Select(x => new ReadTicketModel.GetTicketDetailExtraResponseModel
                                        {
                                            FieldName = x.FieldName,
                                            FieldValue = x.FieldValue
                                        }).ToList();
                                    }
                                }
                            }
                        }

                        result.InvoiceDetails = result.InvoiceDetails.OrderBy(x => x.Index).ToList();
                    }

                    result.InvoiceTaxBreakdowns = _appFactory.ObjectMapper.Map<List<TicketTaxBreakdownEntity>, List<ReadTicketModel.GetTicketTaxBreakdownResponseModel>>(taxBreakdowns.Where(x => x.InvoiceHeaderId == invoiceId.Value).ToList());

                    result.DocumentInfos = _appFactory.ObjectMapper.Map<List<TicketDocumentInfoEntity>, List<ReadTicketModel.GetTicketDocumentInfoResponseModel>>(documentInfos.ToList());

                    result.Documents = _appFactory.ObjectMapper.Map<List<TicketDocumentEntity>, List<ReadTicketModel.GetTicketDocumentResponseModel>>(documents.ToList());

                    // Mapping dữ liệu hóa đơn liên quan
                    List<ReferenceInvoiceResponseModel> referenceInvoiceReponses = new List<ReferenceInvoiceResponseModel>();
                    foreach (var referenceInvoice in referenceInvoices)
                    {

                        var infoReference = _appFactory.ObjectMapper.Map<TicketHeaderEntity, ReferenceInvoiceResponseModel>(referenceInvoice);

                        if (referenceInvoice.ExtraProperties != null)
                        {
                            infoReference.ExtraProperties = JsonConvert.SerializeObject(referenceInvoice.ExtraProperties);

                            if (!string.IsNullOrEmpty(infoReference.ExtraProperties))
                            {
                                var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(infoReference.ExtraProperties);
                                var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                                if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                                {
                                    headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                                }

                                if (headerExtraProperties.Any())
                                {
                                    infoReference.InvoiceHeaderExtras = headerExtraProperties.Select(x => new ReadTicketModel.GetTicketHeaderExtraResponseModel
                                    {
                                        FieldValue = x.FieldValue,
                                        FieldName = x.FieldName
                                    }).ToList();
                                }
                            }
                        }

                        if (referenceInvoice.InvoiceStatus == (short)InvoiceStatus.DieuChinhDinhDanh)
                        {
                            infoReference.InvoiceDetails = new List<GetTicketDetailResponseModel>();
                        }
                        else
                        {
                            infoReference.InvoiceDetails = _appFactory.ObjectMapper.Map<List<TicketDetailEntity>, List<ReadTicketModel.GetTicketDetailResponseModel>>(detailInvoices.Where(x => x.InvoiceHeaderId == referenceInvoice.Id).ToList());

                            foreach (var item in infoReference.InvoiceDetails)
                            {
                                if (detailInvoices.Where(x => x.Id == item.Id).FirstOrDefault()?.ExtraProperties.IsNullOrEmpty() == false)
                                {
                                    item.ExtraProperties = JsonConvert.SerializeObject(detailInvoices.Where(x => x.Id == item.Id).FirstOrDefault().ExtraProperties);
                                }
                                else
                                {
                                    item.ExtraProperties = null;
                                }
                                if (!string.IsNullOrEmpty(item.ExtraProperties))
                                {
                                    var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.ExtraProperties);
                                    var detailExtraProperties = new List<InvoiceDetailExtraModel>();
                                    if (extraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(extraProperties["invoiceDetailExtras"]))
                                    {
                                        detailExtraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(extraProperties["invoiceDetailExtras"]);
                                    }
                                    if (detailExtraProperties.Any())
                                    {
                                        item.InvoiceDetailExtras = detailExtraProperties.Select(x => new ReadTicketModel.GetTicketDetailExtraResponseModel
                                        {
                                            FieldName = x.FieldName,
                                            FieldValue = x.FieldValue
                                        }).ToList();
                                    }
                                }
                            }
                        }

                        infoReference.InvoiceTaxBreakdowns = _appFactory.ObjectMapper.Map<List<TicketTaxBreakdownEntity>, List<ReadTicketModel.GetTicketTaxBreakdownResponseModel>>(taxBreakdowns.Where(x => x.InvoiceHeaderId == referenceInvoice.Id).ToList());

                        referenceInvoiceReponses.Add(infoReference);
                    }
                    if (referenceInvoiceReponses.Count > 0)
                    {
                        result.ReferenceInvoices = referenceInvoiceReponses.OrderBy(x => x.Id).ToList();
                    }

                    // Lấy dữ liệu ghi chú
                    var printNotEntity = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(invoiceId.Value, (short)VnisType._05TVDT.GetHashCode(), tenantId);
                    if (printNotEntity != null)
                    {
                        result.InvoicePrintNote = _appFactory.ObjectMapper.Map<InvoicePrintNoteEntity, InvoicePrintNoteDto>(printNotEntity);
                    }
                }
                return result;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
    }
}
