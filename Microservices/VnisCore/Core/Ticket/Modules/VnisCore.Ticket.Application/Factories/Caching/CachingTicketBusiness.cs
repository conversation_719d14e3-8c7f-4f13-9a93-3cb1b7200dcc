using Core.Caching;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Factory;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Ticket.Application.Factories.Services;

namespace VnisCore.Ticket.Application.Factories.Caching
{
    public interface ICachingTicketBusiness
    {
        Task<List<InvoiceTemplateEntity>> GetOrSetCachedReadTemplatesAsync(short invoiceType);
    }

    public class CachingTicketBusiness : ICachingTicketBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;

        public CachingTicketBusiness(
            IAppFactory appFactory,
            IConfiguration configuration)
        {
            _appFactory = appFactory;
            _configuration = configuration;
        }

        public async Task<List<InvoiceTemplateEntity>> GetOrSetCachedReadTemplatesAsync(short invoiceType)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var invoiceService = _appFactory.GetServiceDependency<IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity>>();

            var cacheKey = CacheKeyBuilder.BuildCacheKey(tenantId.ToString(), userId.ToString(), invoiceType.ToString());
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<InvoiceTemplateEntity>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await invoiceService.GetReadInvoiceTemplates(tenantId, userId, VnisType._05TVDT),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }
    }
}
