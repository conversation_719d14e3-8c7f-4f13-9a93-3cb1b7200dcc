using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;

using Dapper;

using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using System;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.PrintNote;
using VnisCore.Ticket.Application.Factories.Services;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.AutoSignServer;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.TicketLog.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateReplaceTicket.MessageEventData;
using VnisCore.Ticket.Infrastructure.IRepository;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateReplaceTicket.SubscribeEventHandler
{
    public class UpdateReplaceTicketHeaderPublishEventHandler : IDistributedEventHandler<UpdateReplaceTicketHeaderEventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<UpdateReplaceTicketHeaderPublishEventHandler> _logger;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly Factories.Services.ITicketService _ticketService;
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IElasticService _elasticService;
        private readonly IInvoicePrintNoteRepository _invoicePrintNoteRepository;

        public UpdateReplaceTicketHeaderPublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<UpdateReplaceTicketHeaderPublishEventHandler> logger,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            Factories.Services.ITicketService ticketService,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IElasticService elasticService,
            IInvoicePrintNoteRepository invoicePrintNoteRepository)
        {
            _localizer = localizer;
            _logger = logger;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _ticketService = ticketService;
            _distributedEventBus = distributedEventBus;
            _elasticService = elasticService;
            _invoicePrintNoteRepository = invoicePrintNoteRepository;
        }

        public async Task HandleEventAsync(UpdateReplaceTicketHeaderEventSendData eventData)
        {
            try
            {
                var query = await _ticketService.GenerateDrawUpdateReplaceInvoice(eventData);

                var data = await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                //nếu có số hóa đơn thì mới update lại ngày hóa đơn cuối cùng
                var invoice = eventData.InfosNeedUpdateInvoice.Invoice;
                if (invoice.Number.HasValue)
                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

                #region Thêm nội dung ghi chú khi in hóa đơn
                if (eventData.InvoicePrintNote != null)
                {
                    var invoicePrintNote = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(eventData.Id, (short)VnisType._05TVDT.GetHashCode(), invoice.TenantId);
                    if (invoicePrintNote != null)
                    {
                        // Update 
                        invoicePrintNote.Note = eventData.InvoicePrintNote.Note;
                        invoicePrintNote.IsShowNote = eventData.InvoicePrintNote.IsShowNote;
                        await _invoicePrintNoteRepository.UpdateAsync(invoicePrintNote);
                    }
                    else
                    {
                        await _invoicePrintNoteRepository.InsertAsync(new InvoicePrintNoteEntity()
                        {
                            TenantId = invoice.TenantId,
                            Note = eventData.InvoicePrintNote.Note,
                            IsShowNote = eventData.InvoicePrintNote.IsShowNote,
                            InvoiceHeaderId = eventData.Id,
                            InvoiceType = (short)VnisType._05TVDT.GetHashCode(),
                            CreationTime = DateTime.Now,
                            CreatorId = eventData.UserId,
                        });
                    }
                }
                #endregion

                var rootInvoice = eventData.InfosNeedUpdateInvoice.InvoiceReference;

                // 
                await _distributedEventBus.PublishAsync(new UpdateReplaceTicketHeaderEventResultData(eventData));

                //Insert Log
                await _distributedEventBus.PublishAsync(new TicketLogEventSendData(new TicketLogModel
                {
                    InvoiceHeaderId = eventData.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(eventData.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Update.GetHashCode(),
                    Partition = long.Parse(eventData.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));

                //sync Customer
                await _distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._05TVDT,
                }));

                //sync Unit
                await _distributedEventBus.PublishAsync(new SyncUnitEventSendData(new SyncUnitRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._05TVDT,
                }));

                //// check có quy trình duyệt hay không
                //// nếu không có quy trình duyệt và có cấu hình ký tự động bằng signserver thì
                //// sau khi tạo hóa đơn xong thì ký luôn
                await _distributedEventBus.PublishAsync(new AutoSignTicketEventSendData
                {
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    Token = eventData.Token
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent UpdateReplace Error: {ex.Message}");
                _logger.LogError($"HandleEvent UpdateReplace Error: {ex.InnerException}");
            }
        }
    }
}