using Core.DependencyInjection;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.Messages;
using Core.Uow;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.Factories.Repositories;
using VnisCore.Ticket.Application.Factories.Services;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CancelTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.TicketLog.MessageEventData;


namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CancelTicket.SubscribeEventHandler
{
    public class CancelTicketHeaderPublishEventHandler : IDistributedEventHandler<CancelTicketHeaderEventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<CancelTicketHeaderPublishEventHandler> _logger;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly IInvoiceHeaderRepository<TicketHeaderEntity> _repoHeader;
        private readonly IRepository<TicketHeaderEntity, long> _repoTicketHeader;
        private readonly IAppFactory _appFactory;
        private readonly IElasticService _elasticService;
        public CancelTicketHeaderPublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<CancelTicketHeaderPublishEventHandler> logger,
            IInvoiceHeaderRepository<TicketHeaderEntity> repoHeader,
            IRepository<TicketHeaderEntity, long> repoTicketHeader,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IElasticService elasticService)
        {
            _localizer = localizer;
            _logger = logger;
            _invoiceService = invoiceService;
            _repoHeader = repoHeader;
            _repoTicketHeader = repoTicketHeader;
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
            _elasticService = elasticService;
        }

        public async Task HandleEventAsync(CancelTicketHeaderEventSendData eventData)
        {
            try
            {
                using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
                {
                    IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                    IsTransactional = true
                }, true);

                var invoices = await _repoHeader.GetByIdsAsync(eventData.Ids);
                var hasApproveCancel = await _invoiceService.HasApproveCancelAsync(eventData.TenantId);

                if (hasApproveCancel)
                    foreach (var item in invoices)
                    {
                        item.CancelTime = DateTime.Now;
                        item.CancelId = eventData.UserId;
                        item.ApproveCancelStatus = (short)ApproveStatus.ChoDuyet.GetHashCode();
                        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                        var result = new InvoiceCommandResponseModel
                        {
                            Id = item.Id,
                            TenantId = item.TenantId,
                            //Type = message.Type,
                            UserId = eventData.UserId,
                            UserFullName = eventData.UserFullName,
                            UserName = eventData.UserName,
                            //Method = HubMethod.InvoiceStatus,
                            //InvoiceStatus = InvoiceStatus.XoaHuy,
                            SignStatus = EnumExtension.ToEnum<SignStatus>(item.SignStatus),
                            InvoiceNo = item.InvoiceNo,
                            Number = item.Number,
                            TemplateNo = item.TemplateNo,
                            SerialNo = item.SerialNo,
                            Resource = eventData.Resource,
                            State = InvoiceActionState.Cancel,
                            ActionLogInvoice = ActionLogInvoice.Cancel,
                            Action = InvoiceAction.Cancel,
                            //ConnectionId = message.ConnectionId,
                            InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                            {
                                OldInvoiceDate = item.InvoiceDate,
                                OldTotalAmount = item.TotalAmount,
                                OldTotalPaymentAmount = item.TotalPaymentAmount,
                                OldTotalVatAmount = item.TotalVatAmount
                            }
                        };
                        // publish dashboard
                        await _distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                        //Insert Log
                        await _distributedEventBus.PublishAsync(new TicketLogEventSendData(new TicketLogModel
                        {
                            InvoiceHeaderId = item.Id,
                            TenantId = eventData.TenantId,
                            UserId = eventData.UserId,
                            UserName = eventData.UserName,
                            InvoiceType = EnumExtension.ToEnum<VnisType>(item.TemplateNo).GetHashCode(),
                            Action = ActionLogInvoice.Cancel.GetHashCode(),
                            Partition = long.Parse(item.InvoiceDate.ToString("yyyyMMddHHmm")),
                            ActionTime = DateTime.Now
                        }));
                    }


                //invoices.ForEach(x =>
                //{
                //    x.CancelTime = DateTime.Now;
                //    x.CancelId = eventData.UserId;
                //    x.ApproveDeleteStatus = (short)ApproveStatus.ChoDuyet.GetHashCode();
                //});
                else
                {
                    foreach (var item in invoices)
                    {
                        item.CancelTime = DateTime.Now;
                        item.CancelId = eventData.UserId;
                        item.InvoiceStatus = (short)InvoiceStatus.XoaHuy.GetHashCode();
                        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                        var result = new InvoiceCommandResponseModel
                        {
                            Id = item.Id,
                            TenantId = item.TenantId,
                            //Type = message.Type,
                            UserId = eventData.UserId,
                            UserFullName = eventData.UserFullName,
                            UserName = eventData.UserName,
                            //Method = HubMethod.InvoiceStatus,
                            //InvoiceStatus = InvoiceStatus.XoaHuy,
                            SignStatus = EnumExtension.ToEnum<SignStatus>(item.SignStatus),
                            InvoiceNo = item.InvoiceNo,
                            Number = item.Number,
                            TemplateNo = item.TemplateNo,
                            SerialNo = item.SerialNo,
                            Resource = eventData.Resource,
                            State = InvoiceActionState.Cancel,
                            ActionLogInvoice = ActionLogInvoice.Cancel,
                            Action = InvoiceAction.Cancel,
                            //ConnectionId = message.ConnectionId,
                            InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                            {
                                OldInvoiceDate = item.InvoiceDate,
                                OldTotalAmount = item.TotalAmount,
                                OldTotalPaymentAmount = item.TotalPaymentAmount,
                                OldTotalVatAmount = item.TotalVatAmount
                            }
                        };
                        // publish dashboard
                        await _distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                        //Insert Log
                        await _distributedEventBus.PublishAsync(new TicketLogEventSendData(new TicketLogModel
                        {
                            InvoiceHeaderId = item.Id,
                            TenantId = eventData.TenantId,
                            UserId = eventData.UserId,
                            UserName = eventData.UserName,
                            InvoiceType = EnumExtension.ToEnum<VnisType>(item.TemplateNo).GetHashCode(),
                            Action = ActionLogInvoice.Cancel.GetHashCode(),
                            Partition = long.Parse(item.InvoiceDate.ToString("yyyyMMddHHmm")),
                            ActionTime = DateTime.Now
                        }));
                    }
                }
                //invoices.ForEach(x =>
                //{
                //    x.CancelTime = DateTime.Now;
                //    x.CancelId = eventData.UserId;
                //    x.InvoiceStatus = (short)InvoiceStatus.XoaHuy.GetHashCode();
                //});

                //await _repoTicketHeader.UpdateManyAsync(invoices);

                if (!hasApproveCancel)
                {
                    var groupInvoiceTemplates = invoices.GroupBy(x => x.InvoiceTemplateId, y => y.Number);

                    // TODO: Update ngày hóa đơn cuối cùng cho multiple template
                    foreach (var item in groupInvoiceTemplates)
                    {
                        var maxNumber = item.Max();
                        //update lại ngày hóa đơn cuối cùng
                        await _invoiceService.UpdateLastInvoiceDateCancelDeleteAsync(eventData.TenantId, item.Key, maxNumber.Value);
                    }
                }

                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                await transaction.CompleteAsync();

                // logic insert
                await _distributedEventBus.PublishAsync(new CancelTicketHeaderEventResultData(eventData));
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent CancelAsync Error: {ex.Message}");
                _logger.LogError($"HandleEvent CancelAsync Error: {ex.InnerException}");
            }
        }
    }
}