using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Caching;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceExtra;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.TicketHeaderField.Dto;

namespace VnisCore.Ticket.Application.TicketHeaderField
{
    [Authorize(InvoiceExtraPermissions.InvoiceExtra.Default)]
    public class TicketHeaderFieldService : CrudAppService<TicketHeaderFieldEntity,
                                                            TicketHeaderFieldDto,
                                                            long,
                                                            TicketHeader<PERSON>ieldPagedRequestDto,
                                                            TicketHeaderFieldDto,
                                                            TicketHeaderFieldDto>, ITicketHeaderFieldService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<TicketHeaderFieldEntity, long> _repoHeaderField;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public TicketHeaderFieldService(IRepository<TicketHeaderFieldEntity, long> repoHeaderField,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier)
            : base(repoHeaderField)
        {
            _appFactory = appFactory;
            _repoHeaderField = repoHeaderField;
            _localizier = localizier;
        }

        [HttpGet(Utilities.ApiUrlBase + "GetById/{id:long}")]
        public override async Task<TicketHeaderFieldDto> GetAsync(long id)
        {
            if (!await _repoHeaderField.AnyAsync(x => x.Id == id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.HeaderField.NotFindHeaderField"]);
                //return new TicketHeaderFieldDto
                //{
                //    ErrorMessages = "Không thấy trường mở rộng"
                //};

            }

            return await base.GetAsync(id);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public override async Task<PagedResultDto<TicketHeaderFieldDto>> GetListAsync(TicketHeaderFieldPagedRequestDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;
            var query = _repoHeaderField.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.FieldName.ToLower(),  $"%{keyword.ToLower()}%")
                         || EF.Functions.Like(x.DisplayName.ToLower(),  $"%{keyword.ToLower()}%"))
                .WhereIf(input.TenantId.HasValue, x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var headerFields = await query.Select(x => ObjectMapper.Map<TicketHeaderFieldEntity, TicketHeaderFieldDto>(x)).PageBy(input.SkipCount, input.MaxResultCount).ToListAsync();

            return new PagedResultDto<TicketHeaderFieldDto>
            {
                TotalCount = await query.CountAsync(),
                Items = headerFields
            };
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Create)]
        [HttpPost(Utilities.ApiUrlBase + "Create")]
        public override async Task<TicketHeaderFieldDto> CreateAsync(TicketHeaderFieldDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id.Value;

            string[] fieldNames = new string[] { input.FieldName };

            var existed = await _repoHeaderField.Where(x => fieldNames.Contains(x.FieldName) && x.TenantId == _appFactory.CurrentTenant.Id.Value).ToListAsync();

            if (existed != null && existed.Any())
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.HeaderField.ExistHeaderField"]);
                //return new TicketHeaderFieldDto
                //{
                //    ErrorMessages = "Tên trường mở rộng đã tồn tại"
                //};
            }

            input.FieldName = input.FieldName?.Trim();
            input.CreationTime = DateTime.Now;
            input.CreatorId = _appFactory.CurrentUser.Id.Value;

            BeforeSavingEntityAsync(input);

            await RemoveCacheHeaderFieldAsync();

            return await base.CreateAsync(input);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Update)]
        [HttpPost(Utilities.ApiUrlBase + "Update/{id:long}")]
        public override async Task<TicketHeaderFieldDto> UpdateAsync(long id, TicketHeaderFieldDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id.Value;

            if (!await _repoHeaderField.AnyAsync(x => x.Id == id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.HeaderField.NotFindHeaderField"]);
                //return new TicketHeaderFieldDto
                //{
                //    ErrorMessages = "Không thấy trường mở rộng"
                //};
            }

            //TODO: NamLD xem lại
            //if (await _repoHeaderExtra.AnyAsync(x => x.InvoiceHeaderFieldId == id && x.TenantId == _appFactory.CurrentTenant.Id.Value))
            //{
            //    throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.HeaderField.UsedHeaderField"]);
            //    //return new TicketHeaderFieldDto
            //    //{
            //    //    ErrorMessages = "Đã có hóa đơn sử dụng trường mở rộng này"
            //    //};
            //}

            string[] fieldNames = new string[] { input.FieldName };

            var existed = await _repoHeaderField.Where(x => fieldNames.Contains(x.FieldName) && x.TenantId == _appFactory.CurrentTenant.Id.Value).ToListAsync();

            if (existed != null && existed.Any(x => x.Id != id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.HeaderField.ExistHeaderField"]);
                //return new TicketHeaderFieldDto
                //{
                //    ErrorMessages = "Tên trường mở rộng đã tồn tại"
                //};
            }

            input.FieldName = input.FieldName?.Trim();
            input.LastModificationTime = DateTime.Now;
            input.LastModifierId = _appFactory.CurrentUser.Id.Value;

            BeforeSavingEntityAsync(input);

            await RemoveCacheHeaderFieldAsync();

            return await base.UpdateAsync(id, input);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Delete)]
        [HttpPost(Utilities.ApiUrlBase + "Delete/{id:long}")]
        public override async Task DeleteAsync(long id)
        {
            var headerField = await _repoHeaderField.FirstOrDefaultAsync(x => x.Id == id);

            if (headerField == null)
            {
                // Tạm thời chưa trả gì
                return;
            }

            headerField.IsDeleted = true;
            headerField.DeletionTime = System.DateTime.Now;
            headerField.DeleterId = _appFactory.CurrentUser.Id;

            await RemoveCacheHeaderFieldAsync();

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        private TicketHeaderFieldDto BeforeSavingEntityAsync(TicketHeaderFieldDto input)
        {

            return input;
        }

        private async Task RemoveCacheHeaderFieldAsync()
        {
            var cacheKey = CacheKeyBuilder.BuildCacheKey(_appFactory.CurrentTenant.Id.Value.ToString(), DatabaseExtension<TicketHeaderFieldEntity>.GetTableName());
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<GetHeaderFieldModel>>>();
            await cache.RemoveAsync(cacheKey, null, true);
        }

    }

}