using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Invoice.Repositories.Settings;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using System;
using System.Collections.Generic;
using System.Reflection;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.Factories.Caching;
using VnisCore.Ticket.Application.Factories.Repositories;
using VnisCore.Ticket.Application.Factories.Services;
using VnisCore.Ticket.Application.Ticket.Business.Approve;
using VnisCore.Ticket.Application.Ticket.Dto;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Commands;
using VnisCore.Ticket.Application.Ticket.Services;
using VnisCore.Ticket.Application.Ticket.ValidationRule.Create;
using VnisCore.Ticket.Application.Ticket.ValidationRule.CreateByGroupCustomer;
using VnisCore.Ticket.Application.Ticket.ValidationRule.CreateReplace;
using VnisCore.Ticket.Application.Ticket.ValidationRule.Update;
using VnisCore.Ticket.Application.Ticket.ValidationRule.UpdateReplace;
using VnisCore.Ticket.Application.Ticket.ValidationRules.Approve;
using VnisCore.Ticket.Application.Ticket.ValidationRules.Cancel;
using VnisCore.Ticket.Application.Ticket.ValidationRules.CancelApprove;
using VnisCore.Ticket.Application.Ticket.ValidationRules.Create;
using VnisCore.Ticket.Application.Ticket.ValidationRules.Delete;
using VnisCore.Ticket.Application.Ticket.ValidationRules.Update;
using VnisCore.Ticket.Application.Ticket.ValidationRules.UpdateAdjust;
using VnisCore.Ticket.Application.Ticket.ValidationRules.UpdateReplace;
using VnisCore.Ticket.Application.TicketDocument.Interfaces;
using VnisCore.Ticket.Application.TicketDocument.Services;
using VnisCore.Ticket.Infrastructure;

namespace VnisCore.Ticket.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule),
        typeof(VnisCoreTicketInfrastructureModule)
    )]
    public class VnisCoreTicketApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<ITaxService, TaxService>();
            context.Services.AddScoped<ITicketService, TicketService>();
            context.Services.AddScoped<ICommonTicketService, CommonTicketService>();
            context.Services.AddScoped<ITicketTaxBreakdownRepository, TicketTaxBreakdownRepository>();
            context.Services.AddScoped<INumberService<TicketHeaderEntity>, NumberService<TicketHeaderEntity>>();
            context.Services.AddScoped<IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity>, InvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity>>();
            context.Services.AddScoped<IInvoiceHeaderRepository<TicketHeaderEntity>, InvoiceHeaderRepository<TicketHeaderEntity>>();
            context.Services.AddScoped<IInvoiceHeaderFieldRepository<TicketHeaderFieldEntity>, InvoiceHeaderFieldRepository<TicketHeaderFieldEntity>>();
            context.Services.AddScoped<IInvoiceDetailRepository<TicketDetailEntity>, InvoiceDetailRepository<TicketDetailEntity>>();
            context.Services.AddScoped<IInvoiceDetailFieldRepository<TicketDetailFieldEntity>, InvoiceDetailFieldRepository<TicketDetailFieldEntity>>();
            context.Services.AddScoped<IInvoiceReferenceRepository<TicketReferenceEntity>, InvoiceReferenceRepository<TicketReferenceEntity>>();
            context.Services.AddScoped<IInvoiceDocumentInfoRepository<TicketDocumentInfoEntity>, InvoiceDocumentInfoRepository<TicketDocumentInfoEntity>>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IInvoiceTemplateRepository, InvoiceTemplateRepository>();
            context.Services.AddScoped<IElasticService, ElasticService>();
            context.Services.AddScoped<ITicketHeaderRepository, TicketHeaderRepository>();
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<IRegistrationInvoiceService, RegistrationInvoiceService>();
            context.Services.AddScoped<ISendMailHttpClientService, SendMailHttpClientService>();
            context.Services.AddScoped<ILicenseSharedService, LicenseSharedService>();

            //pdf service
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();
            context.Services.AddScoped<IInvoiceDocumentFactory, InvoiceDocumentFactory>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            context.Services.AddScoped<VnisCore.Ticket.Application.TicketDocument.Interfaces.IInvoiceDocumentService, VnisCore.Ticket.Application.TicketDocument.Services.TicketDocumentService>();

            //Import Service
            context.Services.AddScoped<IImportTicketService, ImportTicketService>();
            context.Services.AddScoped<IImportTicketService, ImportTicketWithSettingService>();

            // caching
            context.Services.AddScoped<ICachingTicketBusiness, CachingTicketBusiness>();
            context.Services.AddScoped<ICachedInvoiceBusiness, CachedInvoiceBusiness>();
            context.Services.AddScoped<IApproveBusiness, ApproveBusiness>();

            // Setting
            context.Services.AddScoped<ISettingsRepository, SettingsRepository>();

            AddTicketValidators(context.Services);

            var configuration = context.Services.GetConfiguration();

            // elastic
            var url = configuration["Elasticsearch:Url"];
            var defaultIndex = configuration["Elasticsearch:Index"];

            var settings = new ConnectionSettings(new Uri(url))
                .DefaultIndex(defaultIndex)
                .EnableDebugMode();

            var client = new ElasticClient(settings);
            context.Services.AddSingleton<IElasticClient>(client);
            context.Services.AddAutoMapperObjectMapper<VnisCoreTicketApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreTicketApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreTicketApplicationModule).GetTypeInfo().Assembly);
        }

        public static void AddTicketValidators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateTicketHeaderDto>(new List<Type>
            {
                typeof(CreateInvoicePreProcess),
                //typeof(CreateInvoiceCheckCashierCodeRule),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(CreateInvoiceCheckTemplateCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckPaymentMethodRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckExistTaxRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<CreateTicketByGroupCustomerDto>(new List<Type>
            {
                typeof(CreateInvoiceByGroupCustomerPreProcess),
                //typeof(CreateInvoiceByGroupCustomerCheckCashierCodeRule),
                typeof(CreateInvoiceByGroupCustomerCheckCustomerRule),
                typeof(CreateInvoiceByGroupCustomerCheckLicenseRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTenantInfoRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTemplateRule),
                typeof(CreateInvoiceByGroupCustomerCheckTemplateCreateRule),
                typeof(CreateInvoiceByGroupCustomerCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistCurrencyRule),
                typeof(CreateInvoiceByGroupCustomerCheckExchangeRateRule),
                typeof(CreateInvoiceByGroupCustomerCheckPaymentMethodRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTaxRule),
                typeof(CreateInvoiceByGroupCustomerCheckHeaderExtraRule),
                typeof(CreateInvoiceByGroupCustomerCheckProductTypeRule),
                typeof(CreateInvoiceByGroupCustomerCheckDetailExtraRule),
            });

            services.AddConfigValidator<CreateReplaceTicketDto>(new List<Type>
            {
                typeof(CreateReplaceInvoicePreProcess),
                //typeof(CreateReplaceInvoiceCheckCashierCodeRule),
                typeof(CreateReplaceInvoiceCheckLicenseRule),
                typeof(CreateReplaceInvoiceCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceCheckDocumentDateRule),
                typeof(CreateReplaceInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(CreateReplaceInvoiceCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceCheckPaymentMethodRule),
                typeof(CreateReplaceInvoiceCheckExistTaxRule),
                typeof(CreateReplaceInvoiceCheckHeaderExtraRule),
                typeof(CreateReplaceInvoiceCheckProductTypeRule),
                typeof(CreateReplaceInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<CreateAdjustTicketHeaderDto>(new List<Type>
            {
                typeof(CreateAdjustInvoicePreProcess),
                typeof(CreateAdjustInvoiceCheckLicenseRule),
                typeof(CreateAdjustInvoiceCheckReferenceInvoiceRule),
                typeof(CreateAdjustInvoiceCheckDocumentDateRule),
                typeof(CreateAdjustInvoiceCheckExistTemplateRule),
                typeof(CreateAdjustInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(CreateAdjustInvoiceCheckTemplateCreateRule),
                typeof(CreateAdjustInvoiceCheckChangeSerialRule),
                typeof(CreateAdjustInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateAdjustInvoiceCheckExistCurrencyRule),
                typeof(CreateAdjustInvoiceCheckExchangeRateRule),
                typeof(CreateAdjustInvoiceCheckPaymentMethodRule),
                typeof(CreateAdjustInvoiceCheckExistTaxRule),
                typeof(CreateAdjustInvoiceCheckHeaderExtraRule),
                typeof(CreateAdjustInvoiceCheckProductTypeRule),
                typeof(CreateAdjustInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<UpdateTicketHeaderDto>(new List<Type>
            {
                typeof(UpdateInvoicePreProcess),
                typeof(UpdateInvoiceCheckInvoiceDateAfterUpdateRegistrationRule),
                //typeof(UpdateInvoiceCheckCashierCodeRule),
                typeof(UpdateInvoiceCheckStatusRule),
                typeof(UpdateInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceCheckExistCurrencyRule),
                typeof(UpdateInvoiceCheckExchangeRateRule),
                typeof(UpdateInvoiceCheckPaymentMethodRule),
                typeof(UpdateInvoiceCheckExistTaxRule),
                typeof(UpdateInvoiceCheckHeaderExtraRule),
                typeof(UpdateInvoiceCheckProductTypeRule),
                typeof(UpdateInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<UpdateReplaceTicketHeaderDto>(new List<Type>
            {
                typeof(UpdateReplaceInvoicePreProcess),
                typeof(UpdateReplaceInvoiceCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateReplaceInvoiceCheckStatusRule),
                typeof(UpdateReplaceInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceInvoiceCheckDocumentDateRule),
                typeof(UpdateReplaceInvoiceCheckExistCurrencyRule),
                typeof(UpdateReplaceInvoiceCheckExchangeRateRule),
                typeof(UpdateReplaceInvoiceCheckPaymentMethodRule),
                //typeof(UpdateReplaceInvoiceCheckDuplicateBuyerCodeRule),
                typeof(UpdateReplaceInvoiceCheckExistTaxRule),
                typeof(UpdateReplaceInvoiceCheckHeaderExtraRule),
                typeof(UpdateReplaceInvoiceCheckProductTypeRule),
                typeof(UpdateReplaceInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<UpdateAdjustTicketHeaderDto>(new List<Type>
            {
                typeof(UpdateAdjustInvoicePreProcess),
                typeof(UpdateAdjustInvoiceCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustCheckReferenceInvoiceRule),
                typeof(UpdateAdjustInvoiceCheckStatusRule),
                typeof(UpdateAdjustInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustInvoiceCheckDocumentDateRule),
                typeof(UpdateAdjustInvoiceCheckExistCurrencyRule),
                typeof(UpdateAdjustInvoiceCheckExchangeRateRule),
                typeof(UpdateAdjustInvoiceCheckPaymentMethodRule),
                typeof(UpdateAdjustInvoiceCheckExistTaxRule),
                typeof(UpdateAdjustInvoiceCheckHeaderExtraRule),
                typeof(UpdateAdjustInvoiceCheckProductTypeRule),
                typeof(UpdateAdjustInvoiceCheckDetailExtraRule),
            });

            services.AddConfigValidator<ApproveTicketRequestModel>(new List<Type>
            {
                typeof(ApproveTicketPreProcess),
                typeof(ApproveTicketCheckUserRule),
                typeof(ApproveTicketCheckStatusRule),
            });

            services.AddConfigValidator<CancelTicketHeaderDto>(new List<Type>
            {
                typeof(CancelInvoicePreProcess),
                typeof(CancelInvoiceCheckStatusRule),
                //typeof(CancelInvoiceCheckCashierCodeRule),
            });

            services.AddConfigValidator<DeleteTicketHeaderDto>(new List<Type>
            {
                typeof(DeleteInvoicePreProcess),
                typeof(DeleteInvoiceCheckStatusRule),
                //typeof(DeleteInvoiceCheckCashierCodeRule),
            });

            services.AddConfigValidator<CancelApproveTicketHeaderDto>(new List<Type>
            {
                typeof(CancelApproveTicketPreProcess),
                typeof(CancelApproveTicketCheckStatusRule),
            });
        }
    }


}
