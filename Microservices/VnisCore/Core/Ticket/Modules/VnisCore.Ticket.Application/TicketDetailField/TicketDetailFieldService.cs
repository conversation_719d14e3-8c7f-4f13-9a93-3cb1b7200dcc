using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Caching;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceExtra;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.TicketDetailField.Dto;

namespace VnisCore.Ticket.Application.TicketDetailField
{
    [Authorize(InvoiceExtraPermissions.InvoiceExtra.Default)]
    public class TicketDetailFieldService : CrudAppService<TicketDetailFieldEntity,
                                                            TicketDetailFieldDto,
                                                            long,
                                                            TicketDetailFieldPagedRequestDto,
                                                            TicketDetailFieldDto,
                                                            TicketDetailFieldDto>, ITicketDetailFieldService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<TicketDetailFieldEntity, long> _repoDetailField;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public TicketDetailFieldService(IRepository<TicketDetailFieldEntity, long> repoDetailField,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory)
            : base(repoDetailField)
        {
            _appFactory = appFactory;
            _repoDetailField = repoDetailField;
            _localizier = localizier;
        }

        [HttpGet(Utilities.ApiUrlBase + "GetById/{id:long}")]
        public override async Task<TicketDetailFieldDto> GetAsync(long id)
        {
            if (!await _repoDetailField.AnyAsync(x => x.Id == id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.TicketDetailField.NotFindDetailField"]);
            }

            return await base.GetAsync(id);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public override async Task<PagedResultDto<TicketDetailFieldDto>> GetListAsync(TicketDetailFieldPagedRequestDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;
            var query = _repoDetailField.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.FieldName.ToLower(),  $"%{keyword.ToLower()}%")
                         || EF.Functions.Like(x.DisplayName.ToLower(),  $"%{keyword.ToLower()}%"))
                .WhereIf(input.TenantId.HasValue, x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var detailFields = await query.Select(x => ObjectMapper.Map<TicketDetailFieldEntity, TicketDetailFieldDto>(x)).PageBy(input.SkipCount, input.MaxResultCount).ToListAsync();

            return new PagedResultDto<TicketDetailFieldDto>
            {
                TotalCount = await query.CountAsync(),
                Items = detailFields
            };
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Create)]
        [HttpPost(Utilities.ApiUrlBase + "Create")]
        public override async Task<TicketDetailFieldDto> CreateAsync(TicketDetailFieldDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id.Value;

            string[] fieldNames = new string[] { input.FieldName };

            var existed = await _repoDetailField.Where(x => fieldNames.Contains(x.FieldName) && x.TenantId == _appFactory.CurrentTenant.Id.Value).ToListAsync();

            if (existed != null && existed.Any())
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.TicketDetailField.ExistingDetailField"]);
            }

            input.FieldName = input.FieldName?.Trim();
            input.CreationTime = DateTime.Now;
            input.CreatorId = _appFactory.CurrentUser.Id.Value;

            BeforeSavingEntityAsync(input);

            await RemoveCacheDetailFieldAsync();

            return await base.CreateAsync(input);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Update)]
        [HttpPost(Utilities.ApiUrlBase + "Update/{id:long}")]
        public override async Task<TicketDetailFieldDto> UpdateAsync(long id, TicketDetailFieldDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id.Value;

            if (!await _repoDetailField.AnyAsync(x => x.Id == id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.TicketDetailField.NotFindDetailField"]);
            }

            string[] fieldNames = new string[] { input.FieldName };

            var existed = await _repoDetailField.Where(x => fieldNames.Contains(x.FieldName) && x.TenantId == _appFactory.CurrentTenant.Id.Value).ToListAsync();

            if (existed != null && existed.Any(x => x.Id != id))
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.TicketDetailField.ExistingDetailField"]);
            }

            input.FieldName = input.FieldName?.Trim();
            input.LastModificationTime = DateTime.Now;
            input.LastModifierId = _appFactory.CurrentUser.Id.Value;

            BeforeSavingEntityAsync(input);

            await RemoveCacheDetailFieldAsync();

            return await base.UpdateAsync(id, input);
        }

        [Authorize(InvoiceExtraPermissions.InvoiceExtra.Delete)]
        [HttpPost(Utilities.ApiUrlBase + "Delete/{id:long}")]
        public override async Task DeleteAsync(long id)
        {
            var detailField = await _repoDetailField.FirstOrDefaultAsync(x => x.Id == id);
          
            if (detailField == null)
            {
                // Tạm thời chưa trả gì
                return;
            }

            detailField.IsDeleted = true;
            detailField.DeletionTime = System.DateTime.Now;
            detailField.DeleterId = _appFactory.CurrentUser.Id;

            await RemoveCacheDetailFieldAsync();

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        private TicketDetailFieldDto BeforeSavingEntityAsync(TicketDetailFieldDto input)
        {
            return input;
        }
        private async Task RemoveCacheDetailFieldAsync()
        {
            var cacheKey = CacheKeyBuilder.BuildCacheKey(_appFactory.CurrentTenant.Id.Value.ToString(), DatabaseExtension<TicketDetailFieldEntity>.GetTableName());
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<GetDetailFieldModel>>>();
            await cache.RemoveAsync(cacheKey, null, true);
        }
    }

}