using Core;
using Core.Application.Dtos;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Identity;
using Core.Localization.Resources.AbpLocalization;
using Core.ObjectExtending;
using Core.Shared.AppConst;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.GenerateContentEmail;
using Core.Shared.Models;
using Core.Shared.Security;
using Core.Shared.Services;
using Core.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using IdentityUser = Core.Identity.IdentityUser;

namespace VnisCore.System.Application.User
{
    /// <summary>
    /// API tài kho<PERSON>n khách hàng
    /// </summary>
    [Authorize(IdentityPermissions.UsersPublic.Default)]
    public class UserPublicService : IdentityUserAppService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IRepository<IdentityUser, Guid> _repositoryIdentityUser;
        private readonly IRateLimiter _rateLimiter;
        private readonly IConfiguration _configuration;

        public UserPublicService(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IDistributedEventBus distributedEventBus,
            IOptions<IdentityOptions> identityOptions,
            IdentityUserManager userManager,
            IIdentityUserRepository userRepository,
            IIdentityRoleRepository roleRepository,
            IRepository<IdentityUser, Guid> repositoryIdentityUser,
            IRateLimiter rateLimiter,
            IConfiguration configuration)
            : base(userManager, userRepository, roleRepository, identityOptions)
        {
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
            _repositoryIdentityUser = repositoryIdentityUser;
            _localizer = localizer;
            _rateLimiter = rateLimiter;
            _configuration = configuration;
        }

        [Authorize(IdentityPermissions.UsersPublic.Default)]
        [HttpGet(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task<IdentityUserDto> GetAsync(Guid id)
        {
            ValidateCurrentTenantAnUser();

            IdentityUserDto user;

            if (id != Guid.Empty)
            {
                var tenantId = _appFactory.CurrentTenant.Id;
                var entity = await _repositoryIdentityUser.FirstOrDefaultAsync(x =>
                                x.Id == id
                                && x.TenantId == tenantId
                                && x.Type == (short)UserType.KhachHang.GetHashCode());

                if (entity == null)
                {
                    throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
                }
                user = ObjectMapper.Map<IdentityUser, IdentityUserDto>(entity);

            }
            else
                user = new IdentityUserDto();

            return user;
        }

        [Authorize(IdentityPermissions.UsersPublic.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.GetList)]
        public override async Task<PagedResultDto<IdentityUserDto>> GetListAsync(GetIdentityUsersInput input)
        {
            input.Type = (short) UserType.KhachHang.GetHashCode();
            return await base.GetListAsync(input);
        }

        /// <summary>
        /// Tạo tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.Create)]
        [HttpPost(Utilities.ApiUrlBase)]
        public override async Task<IdentityUserDto> CreateAsync(IdentityUserCreateDto input)
        {
            ValidateCurrentTenantAnUser();

            var currentTenantId = _appFactory.CurrentTenant.Id;

            await IdentityOptions.SetAsync();

            await ValidateUserExist(currentTenantId.Value, input.UserName);

            var user = new IdentityUser(
                GuidGenerator.Create(),
                input.UserName,
                input.Email,
                input.EmployeeCode,
                input.DepartmentCode,
                input.CashierCode,
                CurrentTenant.Id,
                input.Type
            );

            input.MapExtraPropertiesTo(user);

            input.Type = user.Type;
            input.EmployeeCode = user.EmployeeCode;
            input.DepartmentCode = user.DepartmentCode;
            input.CashierCode = user.CashierCode;
            input.LockoutEnabled = true;

            (await UserManager.CreateAsync(user, input.Password, false)).CheckErrors();
            await UpdateUserByInput(user, input);
            (await UserManager.UpdateAsync(user)).CheckErrors();

            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<IdentityUser, IdentityUserDto>(user);
        }

        /// <summary>
        /// Sửa tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.Update)]
        [HttpPut(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task<IdentityUserDto> UpdateAsync(Guid id, IdentityUserUpdateDto input)
        {
            ValidateCurrentTenantAnUser();

            await IdentityOptions.SetAsync();

            var user = await UserManager.GetByIdAsync(id);

            var currentTenantId = _appFactory.CurrentTenant.Id;
            if (user == null || user.TenantId != currentTenantId || user.Type != (short)UserType.KhachHang.GetHashCode())
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
            }

            user.ConcurrencyStamp = input.ConcurrencyStamp;
            user.Email = input.Email;
            (await UserManager.SetUserNameAsync(user, input.UserName)).CheckErrors();

            await UpdateUserByInput(user, input);
            input.MapExtraPropertiesTo(user);

            (await UserManager.UpdateAsync(user)).CheckErrors();

            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<IdentityUser, IdentityUserDto>(user);
        }

        /// <summary>
        /// Xóa tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.Delete)]
        [HttpDelete(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task DeleteAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x => 
                            x.Id == id 
                            && x.TenantId == tenantId 
                            && x.Type == (short)UserType.KhachHang.GetHashCode());

            if (user == null)
                throw new UserFriendlyException("Không tìm thấy tài khoản khách hàng. Vui lòng kiểm tra lại");

            await base.DeleteAsync(id);
        }

        /// <summary>
        /// Khóa tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.LockAccount)]
        [HttpPost(Utilities.ApiUrlBase + "lock-account/{id:guid}")]
        public async Task<bool> LockAccountAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.KhachHang.GetHashCode()); 

            if (user == null) 
                return false;

            user.LockoutEnabled = true;
            user.LockoutEnd = DateTime.MaxValue;

            await _repositoryIdentityUser.UpdateAsync(user);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// Mở khóa tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.LockAccount)]
        [HttpPost(Utilities.ApiUrlBase + "un-lock-account/{id:guid}")]
        public async Task<bool> UnLockAccountAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x => 
                            x.Id == id
                            && x.TenantId == tenantId 
                            && x.Type == (short) UserType.KhachHang.GetHashCode());

            if (user == null) 
                return false;

            user.LockoutEnabled = true;
            user.LockoutEnd = null;

            await _repositoryIdentityUser.UpdateAsync(user);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// Làm mới mật khẩu và gửi mail tài khoản khách hàng
        /// </summary>
        [Authorize(IdentityPermissions.UsersPublic.ResetPassword)]
        [HttpPost(Utilities.ApiUrlBase + "reset-password")]
        public async Task<bool> ResetPasswordAsync(Dto.ResetPasswordDto input)
        {
            var taxCode = _appFactory.CurrentTenant.TaxCode;

            var rateLimiter = _rateLimiter.AllowRequest(taxCode, UserType.KhachHang.ToString());
            if (!rateLimiter.AllowReq)
            {
                throw new UserFriendlyException($@"Giới hạn gửi yêu cầu quên mật khẩu tối đa {rateLimiter.LimitReq} lần trong vòng {rateLimiter.Expiry} phút. Vui lòng kiểm tra lại.");
            }

            var generator = new PasswordGenerator()
            {
                Length = IdentityUserConsts.MaxPasswordLength,
                MinLowercases = 1,
                MinUppercases = 1,
                MinDigits = 1,
                MinSpecials = 1
            };

            var password = generator.Generate();

            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == input.Id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.KhachHang.GetHashCode());
            if (user == null) 
                return false;

            if (!password.IsNullOrEmpty())
            {
                (await UserManager.RemovePasswordAsync(user)).CheckErrors();
                await UserManager.UpdatePasswordHash(user, password, false);
            }

            if (CurrentTenant.Id.HasValue)
            {
                await _distributedEventBus.PublishAsync(new GenerateContentEmailEventSendData(new GenerateContentMailMessageModel<BaseGenerateContentMaillModel>
                {
                    Action = EmailAction.ResetPassword.ToString(),
                    Data = new BaseGenerateContentMaillModel
                    {
                        TenantId = CurrentTenant.Id.Value,
                        Type = 0,
                        Emails = input.Email,
                        InvoiceHeaderId = 0,
                        FullName = user.Name,
                        UserId = user.Id,
                        UserName = user.UserName,
                        Password = password,
                    }
                }));
            }

            await CurrentUnitOfWork.SaveChangesAsync();

            return true;
        }

        private void ValidateCurrentTenantAnUser()
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
                throw new UserFriendlyException(_localizer["Vnis.BE.System.TenantInfoNotFound"]);

            if (!_appFactory.CurrentUser.Id.HasValue)
                throw new UserFriendlyException(_localizer["Vnis.BE.System.AccountInfoNotFound"]);
        }

        private async Task ValidateUserExist(Guid tenantId, string userName)
        {
            var user = await _repositoryIdentityUser.FirstOrDefaultAsync(x => x.UserName == userName && x.TenantId == tenantId && !x.IsDeleted);
            if (user != null)
                throw new UserFriendlyException("Tài khoản đã tồn tại");
        }

        private static string CreateCacheKey(ICurrentUser currentUser)
        {
            var userKey = currentUser.Id?.ToString("N") ?? "Anonymous";
            return $"ApplicationConfiguration_{userKey}_{CultureInfo.CurrentUICulture.Name}";
        }
    }
}
