using System.Threading.Tasks;
using Core;
using Core.AspNetCore.Mvc.ApplicationConfigurations;
using Core.Caching;
using Core.Identity;
using Core.TenantManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;

namespace VnisCore.System.Application.User
{
    [Authorize]
    public class ProfileService : ProfileAppService
    {
        public ProfileService(
            IdentityUserManager userManager,
            IOptions<IdentityOptions> identityOptions, 
            IDistributedCache<TenantCacheItem> tenantCache,
            IDistributedCache<ApplicationConfigurationDto> applicationConfigurationCache) : 
            base(userManager, identityOptions, tenantCache, applicationConfigurationCache)
        {
        }

        public override async Task<ProfileDto> GetAsync()
        {
            return await base.GetAsync();
        }

        public override async Task<ProfileDto> UpdateAsync(UpdateProfileDto input)
        {
            return await base.UpdateAsync(input);
        }

        public override async Task ChangePasswordAsync(ChangePasswordInput input)
        {
            if (input.RePassword != input.NewPassword)
            {
                throw new UserFriendlyException("Nhập lại mật khẩu mới không giống Mật khẩu mới đã đổi. Vui lòng kiểm tra lại");
            }

            await base.ChangePasswordAsync(input);
        }

        
    }
   
}