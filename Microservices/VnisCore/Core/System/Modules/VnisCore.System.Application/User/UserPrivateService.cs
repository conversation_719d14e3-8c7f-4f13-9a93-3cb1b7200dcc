using Core;
using Core.Application.Dtos;
using Core.Caching;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Identity;
using Core.Identity.Dto;
using Core.Localization.Resources.AbpLocalization;
using Core.ObjectExtending;
using Core.Shared.AppConst;
using Core.Shared.Attributes.Account;
using Core.Shared.Attributes.PITDeductionDocument;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Invoices;
using Core.Shared.Invoices.Invoice01;
using Core.Shared.MessageEventsData.GenerateContentEmail;
using Core.Shared.Models;
using Core.Shared.Security;
using Core.Shared.Services;
using Core.Users;
using Dapper;
using Dapper.Oracle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Model;
using OfficeOpenXml;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.System.Application.StoredProcedure.Procedures;
using VnisCore.System.Application.User.Dto;
using VnisCore.System.Application.User.Model;
using VnisCore.System.Application.User.Model.Excel;
using VnisCore.System.Application.User.Model.Request;
using VnisCore.System.Infrastructure.IRepository;
using IdentityRole = Core.Identity.IdentityRole;
using IdentityUser = Core.Identity.IdentityUser;

namespace VnisCore.System.Application.User
{
    /// <summary>
    /// API tài khoản nội bộ
    /// </summary>
    [Authorize(IdentityPermissions.UsersPrivate.Default)]
    public class UserPrivateService : IdentityUserAppService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<AccountTokenTemplateEntity, long> _repositoryAccountTokenTemplate;
        private readonly IRepository<UserReadTemplateEntity, long> _repositoryUserReadTemplate;
        private readonly IRepository<IdentityUser, Guid> _repositoryIdentityUser;
        private readonly IDistributedCache<CheckLockedUserModel> _lockedUserDistributedCache;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IConfiguration _configuration;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        protected IIdentityRoleRepository _identityRoleRepository { get; }
        private readonly IUserRepository _userRepository;
        private readonly IRateLimiter _rateLimiter;

        public UserPrivateService(
            IdentityUserManager userManager,
            IIdentityUserRepository userRepository,
            IIdentityRoleRepository roleRepository,
            IOptions<IdentityOptions> identityOptions,
            IAppFactory appFactory,
            IRepository<AccountTokenTemplateEntity, long> repositoryAccountTokenTemplate,
            IRepository<UserReadTemplateEntity, long> repositoryUserReadTemplate,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IConfiguration configuration,
            IRepository<IdentityUser, Guid> repositoryIdentityUser,
            IIdentityRoleRepository identityRoleRepository,
            IUserRepository customuserRepository,
            IDistributedCache<CheckLockedUserModel> lockedUserDistributedCache,
            IRateLimiter rateLimiter)
            : base(userManager, userRepository, roleRepository, identityOptions)
        {
            _appFactory = appFactory;
            _repositoryAccountTokenTemplate = repositoryAccountTokenTemplate;
            _repositoryUserReadTemplate = repositoryUserReadTemplate;
            _distributedEventBus = distributedEventBus;
            _configuration = configuration;
            _repositoryIdentityUser = repositoryIdentityUser;
            _localizer = localizer;
            _identityRoleRepository = identityRoleRepository;
            _userRepository = customuserRepository;
            _lockedUserDistributedCache = lockedUserDistributedCache;
            _rateLimiter = rateLimiter;
        }

        [Authorize(IdentityPermissions.UsersPrivate.Default)]
        [HttpGet(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task<IdentityUserDto> GetAsync(Guid id)
        {
            ValidateCurrentTenantAnUser();

            IdentityUserDto user;

            if (id != Guid.Empty)
            {
                var tenantId = _appFactory.CurrentTenant.Id;
                var entity = await _repositoryIdentityUser.FirstOrDefaultAsync(x =>
                                x.Id == id
                                && x.TenantId == tenantId
                                && x.Type == (short)UserType.NoiBo.GetHashCode());

                if (entity == null)
                {
                    throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
                }

                user = ObjectMapper.Map<IdentityUser, IdentityUserDto>(entity);

            }
            else
                user = new IdentityUserDto();

            #region select all tenant by currentUser.tenantId
            var sqlTenants = $@"WITH datatree(""Id"",""Level"") AS (
                                    SELECT ""Id"",1 AS ""Level"" FROM ""VnisTenants"" 
                                    WHERE ""IsDeleted"" = 0 AND ""Id"" = '{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.TenantId.Value)}'
                                    UNION ALL
                                    SELECT ""VnisTenants"".""Id"", (datatree.""Level"" + 1) AS ""Level""
                                    FROM ""VnisTenants""
                                        JOIN datatree ON ""VnisTenants"".""ParentId"" = datatree.""Id""
                                    WHERE ""VnisTenants"".""IsDeleted"" = 0
                                )
                                SELECT * FROM datatree";

            var dataTenants = await _appFactory.AuthDatabase.Connection.QueryAsync<TenantChildDto>(sqlTenants);

            var tenantChildDto = dataTenants as TenantChildDto[] ?? dataTenants.ToArray();
            var tenants = tenantChildDto.Any() ?
                string.Join(",", tenantChildDto.Select(x => $"'{OracleExtension.ConvertGuidToRaw(x.Id)}'"))
                : $"'{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.TenantId.Value)}'";
            #endregion

            #region sql raw 
            var query = $@"
                        select * from (
                            select distinct a.""Id"", a.""TenantId"", concat(concat(concat(a.""Name"",' - '), a.""TemplateNo""), a.""SerialNo"") as ""DisplayName"", 
                            CASE 
                               WHEN b.""TemplateId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandCreateTemplate"",
                            CASE 
                               WHEN c.""InvoiceTemplateId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandReadTemplate"",
                            0 AS ""IsGrandUsbToken"",
                            1 AS ""Type""
                            from ""InvoiceTemplate"" a 
                            left join (select ""TemplateId"" from ""AccountTokenTemplate"" where ""TenantId"" IN ({tenants}) 
                                        AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') b on a.""Id"" = b.""TemplateId""
                            left join (select ""InvoiceTemplateId"" from ""UserReadTemplate""  where ""TenantId"" IN ({tenants})  
                                        AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') c on a.""Id"" = c.""InvoiceTemplateId""
                            where a.""TenantId"" IN ({tenants})  AND ""IsDeleted"" = 0 
                            UNION ALL
                            select distinct a.""Id"", a.""TenantId"", a.""SubjectName"" as ""DisplayName"",
                            0 AS ""IsGrandCreateTemplate"",
                            0 AS ""IsGrandReadTemplate"",
                            CASE 
                               WHEN b.""UsbTokenId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandUsbToken"",
                            2 AS ""Type""
                            from ""UsbToken"" a 
                            left join (select ""UsbTokenId"" from ""AccountTokenTemplate""  where ""TenantId"" IN ({tenants})  
                                    AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') b on a.""Id"" = b.""UsbTokenId""
                            where a.""TenantId"" IN ({tenants})  AND ""IsDeleted"" = 0 
                        ) a ORDER BY ""DisplayName""
                    ";
            #endregion

            var dataUserGrandTemplate = await _appFactory.VnisCoreOracle.Connection.QueryAsync<UserIsGrandTokenAndTemplateDto>(query);

            var userIsGrandTokenAndTemplateDtos = dataUserGrandTemplate.ToList();
            if (userIsGrandTokenAndTemplateDtos.Any())
                user.UserIsGrandTokenAndTemplates = userIsGrandTokenAndTemplateDtos;

            return user;
        }

        [Authorize(IdentityPermissions.UsersPrivate.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.GetList)]
        public override async Task<PagedResultDto<IdentityUserDto>> GetListAsync(GetIdentityUsersInput input)
        {
            input.Type = (short)UserType.NoiBo.GetHashCode();

            var count = await _userRepository.GetCountAsync(CurrentTenant.Id.Value, input.Keyword, input.Type);
            var list = await _userRepository.GetListAsync(CurrentTenant.Id.Value, input.Sorting, input.MaxResultCount, input.SkipCount, input.Keyword, input.Type);

            return new PagedResultDto<IdentityUserDto>(
                count,
                ObjectMapper.Map<List<IdentityUser>, List<IdentityUserDto>>(list)
            );
        }

        /// <summary>
        /// Tạo tài khoản nội bộ
        /// </summary>
        [Authorize(IdentityPermissions.UsersPrivate.Create)]
        [HttpPost(Utilities.ApiUrlBase)]
        public override async Task<IdentityUserDto> CreateAsync(IdentityUserCreateDto input)
        {
            ValidateCreateUser(input);

            ValidateCurrentTenantAnUser();

            var currentTenantId = _appFactory.CurrentTenant.Id;

            await IdentityOptions.SetAsync();

            await ValidateUserExist(currentTenantId.Value, input.UserName);

            var user = new IdentityUser(
                GuidGenerator.Create(),
                input.UserName,
                input.Email,
                input.EmployeeCode,
                input.DepartmentCode,
                input.CashierCode,
                CurrentTenant.Id,
                input.Type
            );

            input.MapExtraPropertiesTo(user);

            input.Type = user.Type;
            input.EmployeeCode = user.EmployeeCode;
            input.DepartmentCode = user.DepartmentCode;
            input.CashierCode = user.CashierCode;
            input.LockoutEnabled = true;

            (await UserManager.CreateAsync(user, input.Password, false)).CheckErrors();
            await UpdateUserByInput(user, input);
            (await UserManager.UpdateAsync(user)).CheckErrors();

            #region insertRole

            if (input.Roles.Any())
            {
                var userRoles = input.Roles.Select(x =>
                    // ReSharper disable once PossibleInvalidOperationException
                    new IdentityUserRole(user.Id, x.Id, currentTenantId));
                user.Roles = userRoles.ToList();
            }

            #endregion

            if (user.Type == (short)AccountType.NoiBo)
            {
                await InsertTemplateAndUsbTokenUser(input, currentTenantId, user.Id);
            }

            await CurrentUnitOfWork.SaveChangesAsync();

            return ObjectMapper.Map<IdentityUser, IdentityUserDto>(user);
        }

        [Authorize(IdentityPermissions.UsersPrivate.Import)]
        [HttpPost(Utilities.ApiUrlBase + "import-private-user-excel")]
        public async Task<bool> ImportUserByExcel([FromForm] ImportUserRequestModel request)
        {
            if (request.File.Length == 0)
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.FileIsRequired"]);
            }

            int.TryParse(_configuration["Settings:ImportFileSize"], out var importFileSize);
            if (importFileSize == 0)
            {
                importFileSize = 5;
            }

            if (request.File.Length > importFileSize * 1024 * 1024)
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.FileLength"]);
            }

            var currentTenantId = request.currentTenantId;

            var UsbTokenRepo = _appFactory.Repository<UsbTokenEntity, long>();
            var usbTokens = await UsbTokenRepo.AsNoTracking()
                .Where(x => x.TenantId == currentTenantId)
                .ToListAsync();

            #region Get template 
            var sqlTenants = $@"WITH datatree(""Id"",""Level"") AS (
                                    SELECT ""Id"",1 AS ""Level"" FROM ""VnisTenants"" 
                                    WHERE ""IsDeleted"" = 0 AND ""Id"" = '{OracleExtension.ConvertGuidToRaw(currentTenantId)}'
                                    UNION ALL
                                    SELECT ""VnisTenants"".""Id"", (datatree.""Level"" + 1) AS ""Level""
                                    FROM ""VnisTenants""
                                        JOIN datatree ON ""VnisTenants"".""ParentId"" = datatree.""Id""
                                    WHERE ""VnisTenants"".""IsDeleted"" = 0
                                )
                                SELECT * FROM datatree";

            var dataTenants = await _appFactory.AuthDatabase.Connection.QueryAsync<TenantChildDto>(sqlTenants);

            var tenantChildDto = dataTenants as TenantChildDto[] ?? dataTenants.ToArray();
            var tenants = tenantChildDto.Any() ?
                string.Join(",", tenantChildDto.Select(x => $"'{OracleExtension.ConvertGuidToRaw(x.Id)}'"))
                : $"'{OracleExtension.ConvertGuidToRaw(currentTenantId)}'";
            var id = Guid.Empty;

            var usbSql = $@"select distinct a.""Id"", a.""TenantId"", a.""SubjectName"" as ""DisplayName"",
                            0 AS ""IsGrandCreateTemplate"",
                            0 AS ""IsGrandReadTemplate"",
                            CASE
                               WHEN b.""UsbTokenId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandUsbToken"",
                            2 AS ""Type""
                            from ""UsbToken"" a
                            left join(select ""UsbTokenId"" from ""AccountTokenTemplate""  where ""TenantId"" IN({tenants})  
                                    AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') b on a.""Id"" = b.""UsbTokenId""
                            where a.""TenantId"" IN({tenants})  AND ""IsDeleted"" = 0 ";

            var templateSql = $@"select distinct a.""Id"", a.""TenantId"", concat(a.""TemplateNo"", a.""SerialNo"") as ""DisplayName"", 
                            CASE 
                               WHEN b.""TemplateId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandCreateTemplate"",
                            CASE 
                               WHEN c.""InvoiceTemplateId"" IS NULL THEN 0
                               ELSE 1
                            END AS ""IsGrandReadTemplate"",
                            0 AS ""IsGrandUsbToken"",
                            1 AS ""Type""
                            from ""InvoiceTemplate"" a 
                            left join (select ""TemplateId"" from ""AccountTokenTemplate"" where ""TenantId"" IN ({tenants}) 
                                        AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') b on a.""Id"" = b.""TemplateId""
                            left join (select ""InvoiceTemplateId"" from ""UserReadTemplate""  where ""TenantId"" IN ({tenants})  
                                        AND ""UserId"" = '{OracleExtension.ConvertGuidToRaw(id)}') c on a.""Id"" = c.""InvoiceTemplateId""
                            where a.""TenantId"" IN ({tenants})  AND ""IsDeleted"" = 0 ";

            var usbToken = await _appFactory.VnisCoreOracle.Connection.QueryAsync<UserIsGrandTokenAndTemplateDto>(usbSql);

            var templates = await _appFactory.VnisCoreOracle.Connection.QueryAsync<UserIsGrandTokenAndTemplateDto>(templateSql);

            var Roles = await RoleRepository.GetListAsync(null, currentTenantId);
            #endregion

            byte[] bytes;
            await using (var stream = new MemoryStream())
            {
                await request.File.CopyToAsync(stream);
                bytes = stream.ToArray();
            }

            if (bytes.Length == 0)
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.FileDataIsNull"]);
            }

            await using var memoryStream = new MemoryStream(bytes);
            var package = new ExcelPackage(memoryStream);

            //chuyển dữ liệu về model excel
            var worksheet = package.Workbook.Worksheets[0];
            if (worksheet?.Dimension == null || worksheet?.Dimension?.End?.Row < 1)
            {
                //throw new UserFriendlyException(_localizer["Vnis.BE.Customer.Import.EmptyDataImport"]);
                throw new UserFriendlyException("Không có sheet dữ liệu hoặc định dạng sheet không đúng. Vui lòng kiểm tra lại file import đã đúng định dạng file mẫu chưa!");
            }

            if (worksheet?.Dimension?.End?.Row > 1000)
            {
                throw new UserFriendlyException("File vượt quá 1000 bản ghi. Vui lòng kiểm tra lại");
            }

            int rowStart = worksheet.Dimension.Start.Row;
            int rowEnd = worksheet.Dimension.End.Row;

            var columnCount = worksheet.Dimension.Columns;

            #region convert excel to model
            List<ImportUserExcelDto> importUserExcels = new List<ImportUserExcelDto>();
            for (var i = worksheet.Dimension.Start.Row + 1; i <= worksheet.Dimension.End.Row; i++)
            {
                var j = 1;
                // Neu userName = null and list != 0 => add info to user last
                // Neu userName != null => add user to list
                var userName = worksheet.Cells[i, IndexImportUserExcel.UserName].Value;
                if (userName == null && importUserExcels.Count < 1)
                {
                    // TH: Ko co user
                    continue;
                }
                else if (userName == null && importUserExcels.Count >= 1)
                {
                    // add template to last user
                    if (worksheet.Cells[i, IndexImportUserExcel.Template].Value == null)
                    {
                        // TH: row ko co gia tri template no
                        continue;
                    }
                    else
                    {
                        // Th co template
                        if (worksheet.Cells[i, IndexImportUserExcel.IsReadTemplate].Value == null && worksheet.Cells[i, IndexImportUserExcel.IsCreateTemplate].Value == null)
                        {
                            var templateName = worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString();
                            throw new UserFriendlyException("Mẫu " + templateName + " dòng thứ " + i + " không tích chọn xem hóa đơn hoặc tạo hóa đơn. Import thất bại");
                        }
                        var lastUser = importUserExcels.LastOrDefault();
                        lastUser.Template.Add(worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString());

                        if (worksheet.Cells[i, IndexImportUserExcel.IsReadTemplate].Value == null || worksheet.Cells[i, IndexImportUserExcel.IsReadTemplate].Value.ToString().ToLower() != "x")
                        {
                            lastUser.ReadTemplate.Add(false);
                        }
                        else
                        {
                            lastUser.ReadTemplate.Add(true);
                        }
                        if (worksheet.Cells[i, IndexImportUserExcel.IsCreateTemplate].Value == null || worksheet.Cells[i, IndexImportUserExcel.IsCreateTemplate].Value.ToString().ToLower() != "x")
                        {
                            lastUser.CreateTemplate.Add(false);
                        }
                        else
                        {
                            lastUser.CreateTemplate.Add(true);
                        }
                    }
                }
                else if (userName != null)
                {
                    var userNameStr = userName.ToString();
                    // check định dạng username
                    var isValidUserName = new UserNameAttribute().IsValid(userNameStr);
                    if (!isValidUserName)
                    {
                        throw new UserFriendlyException("Username \"" + userName.ToString() + "\" dòng số " + i + "  không hợp lệ. Yêu cầu chỉ gồm các ký tự chữ, số và ký tự đặc biệt chỉ gồm 5 ký tự -._@+");
                    }

                    //// Kiem tra UserName đã tồn tại trong csdl
                    var oracleUser = await base.FindByUsernameAsync(userName.ToString());
                    if (oracleUser != null)
                    {
                        throw new UserFriendlyException("Username \"" + userName.ToString() + "\" dòng số " + i + " đã tồn tại trên hệ thống. Import thất bại");
                    }

                    // Kieu tra userName đã có trùng trong file excel không
                    var duplicateUser = importUserExcels.Where(user => user.UserName == userName.ToString()).FirstOrDefault();
                    if (duplicateUser != null)
                    {
                        throw new UserFriendlyException("User name \"" + userName.ToString() + "\" đã bị trùng. Đã có ở dòng số " + duplicateUser.Index + " trong file excel. Import thất bại");
                    }

                    ImportUserExcelDto userExcelDto = new ImportUserExcelDto();
                    if (worksheet.Cells[i, IndexImportUserExcel.Name].Value == null)
                    {
                        throw new UserFriendlyException("Họ và tên dòng số " + i + " không được để trống");
                    }
                    else
                    {
                        var nameStr = worksheet.Cells[i, IndexImportUserExcel.Name].Value.ToString();
                        if (!string.IsNullOrEmpty(nameStr) && nameStr.Length > 100)
                        {
                            throw new UserFriendlyException("Họ và tên dòng số " + i + " không được quá 100 ký tự");
                        }

                        userExcelDto.Name = worksheet.Cells[i, IndexImportUserExcel.Name].Value.ToString();
                    }

                    userExcelDto.UserName = worksheet.Cells[i, IndexImportUserExcel.UserName].Value.ToString();

                    if (worksheet.Cells[i, IndexImportUserExcel.Password].Value == null)
                    {
                        throw new UserFriendlyException("Mật khẩu dòng số " + i + " không được để trống");
                    }
                    else
                    {
                        var passwdStr = worksheet.Cells[i, IndexImportUserExcel.Password].Value.ToString();
                        if (!passwdStr.IsPassword())
                        {
                            throw new UserFriendlyException("Mật khẩu dòng số " + i + " không hợp lệ.<br>Vui lòng nhập mật khẩu thỏa mãn các điều kiện sau:<br>Có ít nhất 8 ký tự,<br>Có tối đa 20 ký tự,<br>Có ít nhất 1 ký tự in hoa,<br>Có ít nhất 1 ký tự in thường,<br>Có ít nhất 1 ký tự số,<br>Có ít nhất 1 ký tự đặc biệt thuộc 19 ký tự sau: !@#$%^&*(),.?:{}|<>");
                        }

                        userExcelDto.Password = worksheet.Cells[i, IndexImportUserExcel.Password].Value.ToString();
                    }

                    var phoneNumber = worksheet.Cells[i, IndexImportUserExcel.PhoneNumber].Value;
                    if (phoneNumber != null)
                    {
                        var isValidPhoneNumber = new PhoneNumberAttribute().IsValid(phoneNumber.ToString());
                        if (!isValidPhoneNumber)
                        {
                            throw new UserFriendlyException("Giá trị Số điện thoại dòng số " + i + " không đúng định dạng. Vui lòng nhập 10 ký tự số và bắt đầu bằng số 0");
                        }
                        userExcelDto.PhoneNumber = phoneNumber.ToString();
                    }

                    var emailStr = worksheet.Cells[i, IndexImportUserExcel.Email].Value;
                    if (emailStr != null)
                    {
                        if (new EmailAddressAttribute().IsValid(emailStr.ToString()))
                        {
                            if (emailStr.ToString().Length > 50)
                            {
                                throw new UserFriendlyException("Email dòng số " + i + " không được quá 50 ký tự");
                            }

                            userExcelDto.Email = worksheet.Cells[i, IndexImportUserExcel.Email].Value.ToString();
                        }
                        else
                        {
                            throw new UserFriendlyException("Email dòng số " + i + " không đúng định dạng");
                        }
                    }

                    if (worksheet.Cells[i, IndexImportUserExcel.Role].Value == null)
                    {
                        throw new UserFriendlyException("Quyền dòng số " + i + " không được để trống");
                    }
                    else
                    {
                        var roleName = worksheet.Cells[i, IndexImportUserExcel.Role].Value.ToString();
                        if (Roles.Where(role => role.Name == roleName).ToList() == null)
                        {
                            throw new UserFriendlyException("Không tồn tại quyền " + roleName + " dòng số " + i + ". Import thất bại");
                        }
                        userExcelDto.Role = worksheet.Cells[i, IndexImportUserExcel.Role].Value.ToString();
                    }

                    if (worksheet.Cells[i, IndexImportUserExcel.Template].Value != null)
                    {
                        if (templates.Where(template => template.DisplayName == worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString()).FirstOrDefault() == null)
                        {
                            throw new UserFriendlyException("Không tồn tại mẫu " + worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString() + " dòng số " + i + ". Import thất bại");
                        }
                        // Khi đã điền Template thì phải có Create hoặc Read
                        if (worksheet.Cells[i, IndexImportUserExcel.IsReadTemplate].Value == null && worksheet.Cells[i, IndexImportUserExcel.IsCreateTemplate].Value == null)
                        {
                            var templateName = worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString();
                            throw new UserFriendlyException("Mẫu " + templateName + " dòng thứ " + i + " không tích chọn xem hóa đơn hoặc tạo hóa đơn. Import thất bại");
                        }
                        userExcelDto.Template.Add(worksheet.Cells[i, IndexImportUserExcel.Template].Value.ToString());

                        if (worksheet.Cells[i, IndexImportUserExcel.IsReadTemplate].Value == null)
                        {
                            userExcelDto.ReadTemplate.Add(false);
                        }
                        else
                        {
                            userExcelDto.ReadTemplate.Add(true);
                        }
                        if (worksheet.Cells[i, IndexImportUserExcel.IsCreateTemplate].Value == null)
                        {
                            userExcelDto.CreateTemplate.Add(false);
                        }
                        else
                        {
                            userExcelDto.CreateTemplate.Add(true);
                        }
                    }
                    if (worksheet.Cells[i, IndexImportUserExcel.UsbToken].Value != null)
                    {
                        userExcelDto.UsbToken = worksheet.Cells[i, IndexImportUserExcel.UsbToken].Value.ToString();
                    }
                    userExcelDto.Index = i;
                    importUserExcels.Add(userExcelDto);
                }
            }

            #endregion
            // Validate cell
            List<ImportUserModel> userList = new List<ImportUserModel>();

            var usbTokenIds = usbToken.Select(u => u.Id).ToList();
            #region Mapping data
            foreach (var user in importUserExcels)
            {
                ImportUserModel identity = new ImportUserModel();
                //identity.Id = GuidGenerator.Create();
                identity.UserName = user.UserName;
                identity.Name = user.Name;
                identity.Email = user.Email;
                identity.Password = user.Password;
                identity.PhoneNumber = user.PhoneNumber;
                //identity.Roles = Roles.Where(r => r.Name == user.Role).ToList().Select(r => new UserRoleDto() { Id = r.Id, Name = r.Name }).ToList();
                identity.Roles = Roles.Where(r => r.Name == user.Role).Select(r => new UserRoleDto() { Id = r.Id, Name = r.Name }).ToList();
                for (int i = 0; i < user.Template.Count; i++)
                {
                    var templateId = templates.FirstOrDefault(t => t.DisplayName == user.Template[i]).Id;
                    if (user.ReadTemplate[i] == true)
                    {
                        UserReadTemplateEntity readTemplate = new UserReadTemplateEntity();
                        readTemplate.InvoiceTemplateId = templateId;
                        //readTemplate.UserId = user.Id;
                        readTemplate.TenantId = currentTenantId;
                        identity.userReadTemplates.Add(readTemplate);
                    }

                    if (user.CreateTemplate[i] == true)
                    {
                        AccountTokenTemplateEntity createTemplate = new AccountTokenTemplateEntity();
                        //createTemplate.UserId = user.Id;
                        createTemplate.UsbTokenId = null;
                        createTemplate.TemplateId = templateId;
                        createTemplate.TenantId = currentTenantId;
                        identity.userCreateTemplates.Add(createTemplate);
                    }
                }

                if (!user.UsbToken.IsNullOrEmpty())
                {

                    foreach (var usbTokenItem in usbTokenIds)
                    {
                        AccountTokenTemplateEntity createTemplate = new AccountTokenTemplateEntity();
                        //createTemplate.UserId = user.Id;
                        createTemplate.UsbTokenId = usbTokenItem;
                        createTemplate.TemplateId = null;
                        createTemplate.TenantId = currentTenantId;
                        identity.usbTockensTemplate.Add(createTemplate);
                    }
                }
                userList.Add(identity);
            }

            #endregion

            #region create user
            foreach (var input in userList)
            {
                var user = new IdentityUser(
                    GuidGenerator.Create(),
                    input.UserName,
                    input.Email,
                    null,
                    null,
                    null,
                    CurrentTenant.Id,
                    (short)AccountType.NoiBo,
                    input.Name,
                    input.PhoneNumber
                );


                (await UserManager.CreateAsync(user, input.Password, false)).CheckErrors();
                (await UserManager.UpdateAsync(user)).CheckErrors();

                #region insertRole
                if (input.Roles.Any())
                {
                    var userRoles = input.Roles.Select(x =>
                        new IdentityUserRole(user.Id, x.Id, currentTenantId));
                    user.Roles = userRoles.ToList();
                }
                #endregion

                input.userReadTemplates.ForEach(u => u.UserId = user.Id);
                input.userCreateTemplates.ForEach(u => u.UserId = user.Id);
                input.usbTockensTemplate.ForEach(u => u.UserId = user.Id);


                #region user read template
                await _repositoryUserReadTemplate.InsertManyAsync(input.userReadTemplates);
                #endregion

                #region user create template
                await _repositoryAccountTokenTemplate.InsertManyAsync(input.userCreateTemplates);
                #endregion

                #region user usbToken
                await _repositoryAccountTokenTemplate.InsertManyAsync(input.usbTockensTemplate);
                #endregion
            }

            await CurrentUnitOfWork.SaveChangesAsync();
            #endregion
            return true;
        }

        /// <summary>
        /// Cập nhật tài khoản nội bộ
        /// </summary>
        [Authorize(IdentityPermissions.UsersPrivate.Update)]
        [HttpPut(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task<IdentityUserDto> UpdateAsync(Guid id, IdentityUserUpdateDto input)
        {
            ValidateUpdateUser(input);

            ValidateCurrentTenantAnUser();

            var currentTenantId = _appFactory.CurrentTenant.Id;

            await IdentityOptions.SetAsync();

            var user = await UserManager.GetByIdAsync(id);
            if (user == null || user.TenantId != currentTenantId || user.Type != (short)UserType.NoiBo.GetHashCode())
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
            }

            user.ConcurrencyStamp = input.ConcurrencyStamp;
            user.Email = input.Email;
            (await UserManager.SetUserNameAsync(user, input.UserName)).CheckErrors();

            await UpdateUserByInput(user, input);
            input.MapExtraPropertiesTo(user);

            (await UserManager.UpdateAsync(user)).CheckErrors();

            if (!input.Password.IsNullOrEmpty())
            {
                (await UserManager.RemovePasswordAsync(user)).CheckErrors();
                (await UserManager.AddPasswordAsync(user, input.Password)).CheckErrors();
            }

            if (input.Roles.Any())
            {
                var userRoles = input.Roles.Select(x =>
                    // ReSharper disable once PossibleInvalidOperationException
                    new IdentityUserRole(user.Id, x.Id, currentTenantId));
                user.Roles = userRoles.ToList();
            }

            if (user.Type == (short)AccountType.NoiBo)
            {
                await UpdateTemplateAndUsbTokenUser(input, currentTenantId, user.Id);
            }

            await CurrentUnitOfWork.SaveChangesAsync();

            //Xóa cache
            await RemoveCachedInvoiceTemplateAsync(user.TenantId.Value, user.Id, VnisType._01GTKT);
            await RemoveCachedInvoiceTemplateAsync(user.TenantId.Value, user.Id, VnisType._02GTTT);
            await RemoveCachedInvoiceTemplateAsync(user.TenantId.Value, user.Id, VnisType._03XKNB);
            await RemoveCachedInvoiceTemplateAsync(user.TenantId.Value, user.Id, VnisType._04HGDL);
            await RemoveCachedInvoiceTemplateAsync(user.TenantId.Value, user.Id, VnisType._05TVDT);

            return ObjectMapper.Map<IdentityUser, IdentityUserDto>(user);
        }

        /// <summary>
        /// Xóa tài khoản nội bộ
        /// </summary>
        [Authorize(IdentityPermissions.UsersPrivate.Delete)]
        [HttpDelete(Utilities.ApiUrlBase + "{id:guid}")]
        public override async Task DeleteAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.NoiBo.GetHashCode());

            if (user == null)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
            }
            await base.DeleteAsync(id);
        }

        [Authorize(IdentityPermissions.UsersPrivate.LockAccount)]
        [HttpPost(Utilities.ApiUrlBase + "lock-account/{id:guid}")]
        public async Task<bool> LockAccountAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.NoiBo.GetHashCode());

            if (user == null)
                return false;

            user.LockoutEnabled = true;
            user.LockoutEnd = DateTime.MaxValue;

            await _repositoryIdentityUser.UpdateAsync(user);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var cacheKey = "CheckLockedUser" + "_" + id.ToString();
            int timeCaching = 60;
            await _lockedUserDistributedCache.SetAsync(
                        cacheKey, // cacheKey
                        new CheckLockedUserModel()
                        {
                            Id = id,
                            UserName = user.UserName,
                            IsDeleted = user.IsDeleted,
                            LockoutEnabled = user.LockoutEnabled,
                            LockoutEnd = user.LockoutEnd,
                        },
                        options: new DistributedCacheEntryOptions()
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        }
                    );

            return true;
        }

        [Authorize(IdentityPermissions.UsersPrivate.LockAccount)]
        [HttpPost(Utilities.ApiUrlBase + "un-lock-account/{id:guid}")]
        public async Task<bool> UnLockAccountAsync(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.NoiBo.GetHashCode());

            if (user == null)
                return false;

            user.LockoutEnabled = true;
            user.LockoutEnd = null;

            await _repositoryIdentityUser.UpdateAsync(user);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var cacheKey = "CheckLockedUser" + "_" + id.ToString();
            int timeCaching = 60;
            await _lockedUserDistributedCache.SetAsync(
                        cacheKey, // cacheKey
                        new CheckLockedUserModel()
                        {
                            Id = id,
                            UserName = user.UserName,
                            IsDeleted = user.IsDeleted,
                            LockoutEnabled = user.LockoutEnabled,
                            LockoutEnd = user.LockoutEnd,
                        },
                        options: new DistributedCacheEntryOptions()
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        }
                    );

            return true;
        }

        [HttpPost(Utilities.ApiUrlBase + "reset-password")]
        public async Task<bool> ResetPasswordAsync(Dto.ResetPasswordDto input)
        {
            var taxCode = _appFactory.CurrentTenant.TaxCode;

            var rateLimiter = _rateLimiter.AllowRequest(taxCode, UserType.NoiBo.ToString());
            if (!rateLimiter.AllowReq)
            {
                throw new UserFriendlyException($@"Giới hạn gửi yêu cầu quên mật khẩu tối đa {rateLimiter.LimitReq} lần trong vòng {rateLimiter.Expiry} phút. Vui lòng kiểm tra lại.");
            }

            var generator = new PasswordGenerator()
            {
                Length = IdentityUserConsts.MaxPasswordLength,
                MinLowercases = 1,
                MinUppercases = 1,
                MinDigits = 1,
                MinSpecials = 1
            };

            var password = generator.Generate();

            var tenantId = _appFactory.CurrentTenant.Id;
            var user = _repositoryIdentityUser.FirstOrDefault(x =>
                            x.Id == input.Id
                            && x.TenantId == tenantId
                            && x.Type == (short)UserType.NoiBo.GetHashCode());

            if (user == null) return false;

            if (!password.IsNullOrEmpty())
            {
                (await UserManager.RemovePasswordAsync(user)).CheckErrors();
                await UserManager.UpdatePasswordHash(user, password, false);
            }

            if (CurrentTenant.Id.HasValue)
            {
                Log.Error("Start publish sendmail :{0}", user.JsonSerialize());
                await _distributedEventBus.PublishAsync(new GenerateContentEmailEventSendData(new GenerateContentMailMessageModel<BaseGenerateContentMaillModel>
                {
                    Action = EmailAction.ResetPassword.ToString(),
                    Data = new BaseGenerateContentMaillModel
                    {
                        TenantId = CurrentTenant.Id.Value,
                        Type = 0,
                        Emails = input.Email,
                        InvoiceHeaderId = 0,
                        FullName = user.Name,
                        UserId = user.Id,
                        UserName = user.UserName,
                        Password = password,
                    }
                }));
            }

            await CurrentUnitOfWork.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// Lấy DS quyền của User truyền vào
        /// id: UserId
        /// </summary>
        public override async Task<ListResultDto<IdentityRoleDto>> GetRolesAsync(Guid id)
        {
            return await base.GetRolesAsync(id);
        }

        /// <summary>
        /// Lấy DS quyền của Tenant hiện tại
        /// </summary>
        public override async Task<ListResultDto<IdentityRoleDto>> GetAssignableRolesAsync()
        {
            return await base.GetAssignableRolesAsync();
        }

        /// <summary>
        /// Cập nhật quyền của User
        /// </summary>
        public override async Task UpdateRolesAsync(Guid id, IdentityUserUpdateRolesDto input)
        {
            await base.UpdateRolesAsync(id, input);
        }

        public override async Task<IdentityUserDto> FindByUsernameAsync(string userName)
        {
            return await base.FindByUsernameAsync(userName);
        }

        public override async Task<IdentityUserDto> FindByEmailAsync(string email)
        {
            return await base.FindByEmailAsync(email);
        }

        private async Task<List<ImportUserDto>> ImportUserAsync(List<ImportUserDto> input, bool isValidateData)
        {
            var repoUser = _appFactory.Repository<IdentityUser, Guid>();
            var repoRole = _appFactory.Repository<IdentityRole, Guid>();

            var repoTenant = _appFactory.Repository<global::Core.TenantManagement.Tenant, Guid>();

            var tenantInput = input.Select(x => x.TenantCode).Distinct();
            var tenants = await repoTenant.GetListAsync(item => tenantInput.Any(x => x == item.TenantCode));
            var tenantCodes = tenants.Select(x => x.TenantCode);

            //kiểm tra danh sách các tenant code có trong hệ thống không, bỏ qua những tenant không có trên hệ thống
            //var dataImport = input.Where(x => tenantCodes.All(y => y == x.TenantCode));
            //var dataNotFoundTenantCode = input.Where(x => tenantCodes.All(y => y != x.TenantCode));
            var dataImport = input.Where(x => tenantCodes.Contains(x.TenantCode));
            var dataNotFoundTenantCode = input.Except(dataImport);

            // kiểm tra các tài khoản đã tồn tại trên hệ thống
            var param = new OracleDynamicParameters();
            param.Add("input_json", dataImport.JsonSerialize(), OracleMappingType.NClob, ParameterDirection.Input);
            param.Add(name: "output_data", value: DBNull.Value, dbType: OracleMappingType.RefCursor, direction: ParameterDirection.Output);
            var dataCheckExists = await _appFactory.AuthDatabase.Connection.QueryAsync<ImportUserDto>(
                SystemProcedureName.ImportUserCheckExists,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            var userInsert = new List<IdentityUser>();
            var userUpdate = new List<IdentityUser>();
            var userDelete = new List<IdentityUser>();
            var itemsError = new List<ImportUserDto>();
            foreach (var item in dataCheckExists)
            {
                var tenantCode = tenantCodes.FirstOrDefault(x => x == item.TenantCode);
                var tenantId = repoTenant.FirstOrDefault(x => x.TenantCode == tenantCode)?.Id;
                if (!tenantId.HasValue) continue;

                switch (item.Action)
                {
                    // thêm mới
                    case 1:
                        if (item.IsExists == false)
                        {
                            var user = new IdentityUser(GuidGenerator.Create(), item.UserLogin, item.Email, item.EmployeeCode,
                                item.DepartmentCode, item.CashierCode, tenantId, 1, item.FullName);
                            var role = repoRole.FirstOrDefault(x => x.Name == item.Role && x.TenantId == tenantId);
                            if (role != null)
                            {
                                user.AddRole(role.Id);
                                userInsert.Add(user);
                                item.IsError = false;
                                item.ActionText = item.Action + "";
                                item.ErrorMessage = "Thành công";
                                itemsError.Add(item);
                            }
                            else
                            {
                                item.IsError = true;
                                item.ActionText = item.Action + "";
                                item.ErrorMessage = "Tên nhóm quyền không tồn tại";
                                itemsError.Add(item);
                            }

                        }
                        else
                        {
                            item.IsError = true;
                            item.ActionText = item.Action + "";
                            item.ErrorMessage = "Tài khoản đã tồn tại";
                            itemsError.Add(item);
                        }
                        break;
                    //TODO: cần tối ưu lại cập nhật
                    case 2:
                        if (item.IsExists)
                        {
                            var user = repoUser.FirstOrDefault(x => x.UserName == item.UserLogin && x.TenantId == tenantId);
                            if (user != null)
                            {
                                user.Name = item.FullName;
                                user.Email = item.Email;
                                user.EmployeeCode = item.EmployeeCode;
                                user.DepartmentCode = item.DepartmentCode;
                                user.CashierCode = item.CashierCode;
                                userUpdate.Add(user);
                                item.IsError = false;
                                item.ActionText = item.Action + "";
                                item.ErrorMessage = "Thành công";
                                itemsError.Add(item);
                            }
                        }
                        else
                        {
                            item.IsError = true;
                            item.ActionText = item.Action + "";
                            item.ErrorMessage = "Tài khoản không tồn tại";
                            itemsError.Add(item);
                        }
                        break;
                    //TODO: cần tối ưu lại xóa
                    case 3:
                        if (item.IsExists)
                        {
                            var user = repoUser.FirstOrDefault(x => x.UserName == item.UserLogin && x.TenantId == tenantId);
                            if (user != null)
                                userDelete.Add(user);

                            item.IsError = false;
                            item.ActionText = item.Action + "";
                            item.ErrorMessage = "Thành công";
                            itemsError.Add(item);
                        }
                        else
                        {
                            item.IsError = true;
                            item.ActionText = item.Action + "";
                            item.ErrorMessage = "Tài khoản không tồn tại";
                            itemsError.Add(item);
                        }
                        break;
                }

            }

            if (!isValidateData)
            {
                if (userInsert.Any())
                {
                    await repoUser.InsertManyAsync(userInsert);
                    var password = _configuration.GetSection("DefaultValue:PasswordDefault")?.Value;
                    if (!password.IsNullOrEmpty())
                    {
                        foreach (var item in userInsert)
                        {
                            await UserManager.UpdatePasswordHash(item, password, false);
                        }
                    }
                }

                if (userUpdate.Any())
                {
                    await repoUser.UpdateManyAsync(userUpdate);
                }

                if (userDelete.Any())
                    await repoUser.DeleteManyAsync(userDelete);

                await CurrentUnitOfWork.SaveChangesAsync();
            }

            //TODO: hiển thị ra UI các dữ liệu lỗi
            foreach (var item in dataNotFoundTenantCode)
            {
                item.IsError = true;
                item.ActionText = item.Action + "";
                item.ErrorMessage = "Mã chi nhánh không tồn tại";
                itemsError.Add(item);
            }

            return itemsError;
        }

        private void ValidateCurrentTenantAnUser()
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
                //throw new UserFriendlyException("Không tìm thấy thông tin Chi nhánh");
                throw new UserFriendlyException(_localizer["Vnis.BE.System.TenantInfoNotFound"]);

            if (!_appFactory.CurrentUser.Id.HasValue)
                //throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");
                throw new UserFriendlyException(_localizer["Vnis.BE.System.AccountInfoNotFound"]);
        }

        private async Task ValidateUserExist(Guid tenantId, string userName)
        {
            var user = await _repositoryIdentityUser.FirstOrDefaultAsync(x => x.UserName == userName && x.TenantId == tenantId && !x.IsDeleted);
            if (user != null)
                throw new UserFriendlyException("Tài khoản đã tồn tại");
        }

        /// <summary>
        /// Thêm quyền Mẫu hóa đơn và CTS
        /// </summary>
        private async Task InsertTemplateAndUsbTokenUser(IdentityUserCreateOrUpdateDtoBase input, Guid? currentTenantId,
           Guid? userId)
        {
            #region user read template

            if (input.InvoiceTemplates.Any())
            {
                var readTemplates = input.InvoiceTemplates.Where(x => x.IsGrandReadTemplate).Select(x =>
                    new UserReadTemplateEntity
                    {
                        InvoiceTemplateId = x.Id,
                        // ReSharper disable once PossibleInvalidOperationException
                        TenantId = currentTenantId.Value,
                        // ReSharper disable once PossibleInvalidOperationException
                        UserId = userId.Value
                    });
                await _repositoryUserReadTemplate.InsertManyAsync(readTemplates);
            }

            #endregion

            #region user create template

            if (input.InvoiceTemplates.Any())
            {
                var createTemplates = input.InvoiceTemplates.Where(x => x.IsGrandCreateTemplate).Select(x =>
                    new AccountTokenTemplateEntity
                    {
                        UserId = userId,
                        UsbTokenId = null,
                        TemplateId = x.Id,
                        // ReSharper disable once PossibleInvalidOperationException
                        TenantId = currentTenantId.Value
                    });
                await _repositoryAccountTokenTemplate.InsertManyAsync(createTemplates);
            }

            #endregion

            #region user usbToken

            if (input.UsbTokens.Any())
            {
                var createTemplates = input.UsbTokens.Where(x => x.IsGrandUsbToken).Select(x => new AccountTokenTemplateEntity
                {
                    UserId = userId,
                    UsbTokenId = x.Id,
                    TemplateId = null,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = currentTenantId.Value
                });
                await _repositoryAccountTokenTemplate.InsertManyAsync(createTemplates);
            }

            #endregion
        }

        /// <summary>
        /// Cập nhật quyền Mẫu hóa đơn và CTS
        /// </summary>
        private async Task UpdateTemplateAndUsbTokenUser(IdentityUserCreateOrUpdateDtoBase input, Guid? currentTenantId,
           Guid? userId)
        {

            var createTemplateAndToken = await _repositoryAccountTokenTemplate.GetListAsync(x => x.UserId == userId);
            if (createTemplateAndToken.Any())
                await _repositoryAccountTokenTemplate.DeleteManyAsync(createTemplateAndToken.Select(x => x.Id));

            var readTemplate = await _repositoryUserReadTemplate.GetListAsync(x => x.UserId == userId);
            if (readTemplate.Any())
                await _repositoryUserReadTemplate.DeleteManyAsync(readTemplate.Select(x => x.Id));

            #region user read template

            if (input.InvoiceTemplates.Any())
            {
                var readTemplates = input.InvoiceTemplates.Where(x => x.IsGrandReadTemplate).Select(x =>
                    new UserReadTemplateEntity
                    {
                        InvoiceTemplateId = x.Id,
                        // ReSharper disable once PossibleInvalidOperationException
                        TenantId = currentTenantId.Value,
                        // ReSharper disable once PossibleInvalidOperationException
                        UserId = userId.Value
                    });
                await _repositoryUserReadTemplate.InsertManyAsync(readTemplates);
            }

            #endregion

            #region user create template

            if (input.InvoiceTemplates.Any())
            {
                var createTemplates = input.InvoiceTemplates.Where(x => x.IsGrandCreateTemplate).Select(x =>
                    new AccountTokenTemplateEntity
                    {
                        UserId = userId,
                        UsbTokenId = null,
                        TemplateId = x.Id,
                        // ReSharper disable once PossibleInvalidOperationException
                        TenantId = currentTenantId.Value
                    });
                await _repositoryAccountTokenTemplate.InsertManyAsync(createTemplates);
            }

            #endregion

            #region user usbToken

            if (input.UsbTokens.Any())
            {
                var createTemplates = input.UsbTokens.Where(x => x.IsGrandUsbToken).Select(x => new AccountTokenTemplateEntity
                {
                    UserId = userId,
                    UsbTokenId = x.Id,
                    TemplateId = null,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = currentTenantId.Value
                });
                await _repositoryAccountTokenTemplate.InsertManyAsync(createTemplates);
            }

            #endregion
        }

        private static string CreateCacheKey(ICurrentUser currentUser)
        {
            var userKey = currentUser.Id?.ToString("N") ?? "Anonymous";
            return $"ApplicationConfiguration_{userKey}_{CultureInfo.CurrentUICulture.Name}";
        }

        /// <summary>
        /// Validate thông tin User khi tạo
        /// </summary>
        private void ValidateCreateUser(IdentityUserCreateDto input)
        {
            var regexPhone = new Regex(@"^(\+|0){1}([0-9]{9,})\b$");
            if (input.Name.IsNullOrEmpty())
            {
                throw new UserFriendlyException("Họ và tên không được để trống");
            }
            if (input.Name.Length > 100)
            {
                throw new UserFriendlyException("Họ và tên tối đa 100 ký tự");
            }

            if (!input.PhoneNumber.IsNullOrEmpty())
            {
                if (input.PhoneNumber.Length > 10)
                {
                    throw new UserFriendlyException("Số điện thoại tối đa 10 ký tự");
                }
                if (!regexPhone.IsMatch(input.PhoneNumber))
                {
                    throw new UserFriendlyException("Số điện thoại không đúng định dạng");
                }
            }

            if (input.UserName.IsNullOrEmpty())
            {
                throw new UserFriendlyException("Username không được để trống");
            }
            if (input.UserName.Length > 50)
            {
                throw new UserFriendlyException("Username tối đa 50 ký tự");
            }

            if (input.Password.IsNullOrEmpty())
            {
                throw new UserFriendlyException("Mật khẩu không được để trống");
            }
            if (input.Password.Length < 6)
            {
                throw new UserFriendlyException("Mật khẩu tối thiểu 6 ký tự");
            }
            if (input.Password.Length > 50)
            {
                throw new UserFriendlyException("Mật khẩu tối đa 50 ký tự");
            }

            if (input.Type == 0)
            {
                // TK khach hang
                // MaxLength 500
                if (!input.Email.IsNullOrEmpty())
                {
                    if (input.Email.Length > 500)
                    {
                        throw new UserFriendlyException("Email tối đa 500 ký tự");
                    }
                    var splitMails = input.Email.Split(";");
                    if (splitMails.FirstOrDefault().Length > 50)
                    {
                        throw new UserFriendlyException("Email đầu tiên tối đa 50 ký tự");
                    }
                }
            }
            else
            {
                // TK 
                if (!input.Email.IsNullOrEmpty())
                {
                    if (input.Email.Length > 50)
                    {
                        throw new UserFriendlyException("Email tối đa 20 ký tự");
                    }
                }
            }
        }

        /// <summary>
        /// Validate thông tin User khi cập nhật
        /// </summary>
        private void ValidateUpdateUser(IdentityUserUpdateDto input)
        {
            var regexPhone = new Regex(@"^(\+|0){1}([0-9]{9,})\b$");
            if (input.Name.IsNullOrEmpty())
            {
                throw new UserFriendlyException("Họ và tên không được để trống");
            }
            if (input.Name.Length > 100)
            {
                throw new UserFriendlyException("Họ và tên tối đa 100 ký tự");
            }

            if (!input.PhoneNumber.IsNullOrEmpty())
            {
                if (input.PhoneNumber.Length > 10)
                {
                    throw new UserFriendlyException("Số điện thoại tối đa 10 ký tự");
                }
                if (!regexPhone.IsMatch(input.PhoneNumber))
                {
                    throw new UserFriendlyException("Số điện thoại không đúng định dạng");
                }
            }

            if (input.UserName.IsNullOrEmpty())
            {
                throw new UserFriendlyException("Username không được để trống");
            }
            if (input.UserName.Length > 50)
            {
                throw new UserFriendlyException("Username tối đa 50 ký tự");
            }

            if (input.Type == 0)
            {
                // TK khach hang
                // MaxLength 500
                if (!input.Email.IsNullOrEmpty())
                {
                    if (input.Email.Length > 500)
                    {
                        throw new UserFriendlyException("Email tối đa 500 ký tự");
                    }
                    var splitMails = input.Email.Split(";");
                    if (splitMails.FirstOrDefault().Length > 50)
                    {
                        throw new UserFriendlyException("Email đầu tiên tối đa 50 ký tự");
                    }
                }
            }
            else
            {
                // TK 
                if (!input.Email.IsNullOrEmpty())
                {
                    if (input.Email.Length > 50)
                    {
                        throw new UserFriendlyException("Email tối đa 20 ký tự");
                    }
                }

            }
        }

        private async Task RemoveCachedInvoiceTemplateAsync(Guid tenantId, Guid userId, VnisType type)
        {
            var cacheKey = tenantId.ToString() + "-" + userId.ToString() + "-" + type.GetHashCode().ToString();
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<ReadTemplateModel>>>();

            await cache.RemoveAsync(cacheKey, null);
        }
    }

}