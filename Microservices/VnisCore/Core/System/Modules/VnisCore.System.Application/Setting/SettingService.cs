using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.AspNetCore.Mvc.ApplicationConfigurations;
using Core.Caching;
using Core.Domain.Repositories;
using Core.MultiTenancy;
using Core.Shared.AppConst;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;
using Core.Users;
using Dapper;
using Dapper.Oracle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.AuthDatabase.Oracle.Application.Contracts.Permissions.System.Setting;
using VnisCore.System.Application.Setting.Dto;
using VnisCore.System.Application.StoredProcedure.Procedures;
using ISettingCoreService = Core.Shared.Services.ISettingService;
using SettingEntity = Core.SettingManagement.Setting;

namespace VnisCore.System.Application.Setting
{
    [Authorize(SettingTechnicalPermissions.Setting.Default)]
    public class SettingService : CrudAppService<global::Core.SettingManagement.Setting, SettingDto, Guid,
            SettingPagedRequestDto,
            SettingDto, SettingDto>,
        ISettingService
    {
        private readonly IAppFactory _appFactory;
        private readonly IDistributedCache<TenantCacheItem> _tenantCache;
        private readonly IDistributedCache<ApplicationConfigurationDto> _applicationConfigurationCache;
        private readonly IConfiguration _configuration;
        private readonly ISettingCoreService _settingCoreService;
        IRepository<global::Core.SettingManagement.Setting, Guid> _settingRepository;
        private readonly ITenantStore _tenantStore;

        public SettingService(
            IRepository<global::Core.SettingManagement.Setting, Guid> settingRepository, 
            IAppFactory appFactory,
            IDistributedCache<TenantCacheItem> tenantCache, 
            IDistributedCache<ApplicationConfigurationDto> applicationConfigurationCache, 
            IConfiguration configuration,
            ISettingCoreService settingCoreService,
            ITenantStore tenantStore) : base(settingRepository)
        {
            _appFactory = appFactory;
            _tenantCache = tenantCache;
            _applicationConfigurationCache = applicationConfigurationCache;
            _configuration = configuration;
            _settingCoreService = settingCoreService;
            _settingRepository = settingRepository;
            _tenantStore = tenantStore;
        }

        [Authorize(SettingTechnicalPermissions.Setting.Default)]
        [HttpGet(Utilities.ApiUrlBase + HttpActionConst.GetById + "{id:long}")]
        public override Task<SettingDto> GetAsync(Guid id)
        {
            return base.GetAsync(id);
        }

        [Authorize(SettingTechnicalPermissions.Setting.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.GetList)]
        public override Task<PagedResultDto<SettingDto>> GetListAsync(SettingPagedRequestDto input)
        {
            return base.GetListAsync(input);
        }

        [Authorize(SettingTechnicalPermissions.Setting.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.Create)]
        public override async Task<SettingDto> CreateAsync(SettingDto input)
        {
            return await base.CreateAsync(input);
        }

        [Authorize(SettingTechnicalPermissions.Setting.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.Update + "{id:long}")]
        public override async Task<SettingDto> UpdateAsync(Guid id, SettingDto input)
        {
            return await base.UpdateAsync(id, input);
        }

        [Authorize(SettingTechnicalPermissions.Setting.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.Delete + "{id:long}")]
        public override async Task DeleteAsync(Guid id)
        {
            await base.DeleteByIdAsync(id);
        }

        [Authorize(SettingTechnicalPermissions.Setting.UpdateBatch)]
        [HttpPost(Utilities.ApiUrlBase + "UpdateBatch")]
        public async Task<List<UpdateBatchSettingDto>> UpdateBatchAsync(List<UpdateBatchSettingDto> input)
        {

            short.TryParse(_appFactory.Configuration.GetSection("Settings:IsSharedSetting").Value, out var isSharedSetting);

            if (input.Any())
            {
                var tenantId = _appFactory.CurrentTenant.Id.Value;
                var childTenants = await _tenantStore.FindChildTenants(tenantId);
                foreach (var setting in input)
                {
                    var settingEntity = await base.GetAsync(setting.Id);
                    if (settingEntity.TenantId != Guid.Empty && settingEntity.TenantId != CurrentTenant.Id.Value)
                    {
                        // Khác tenant mặc định
                        // Kiểm tra cấu hình có thuộc tenant hiện tại không
                        if (!childTenants.Contains(setting.TenantId))
                        {
                            throw new UserFriendlyException($"Cấu hình '{setting.Name}' không thuộc chi nhánh hiện tại hoặc chi nhánh con. Không thể cập nhật");
                        }
                    }
                }
            }

            var param = new OracleDynamicParameters();
            param.Add("userid", _appFactory.CurrentUser.Id, OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add("tenantId", _appFactory.CurrentTenant.Id, OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add("input_json", input.JsonSerialize(), OracleMappingType.Clob, ParameterDirection.Input);

            await _appFactory.AuthDatabase.Connection.ExecuteAsync(
                isSharedSetting != 1 ? SystemProcedureName.UpdateBatchSetting : SystemProcedureName.UpdateBatchSettingShared,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            if (isSharedSetting == 1)
                await _tenantCache.RemoveManyAsync(new List<string>());
            else
            {
                await _tenantCache.RemoveAsync(TenantCacheItem.CalculateCacheKey(_appFactory.CurrentTenant.Id, null));
                await _tenantCache.RemoveAsync(TenantCacheItem.CalculateCacheKey(null, _appFactory.CurrentTenant.Name));
                await _applicationConfigurationCache.RemoveAsync(CreateCacheKey(CurrentUser));
            }
            
            return input;
        }

        [AllowAnonymous]
        [HttpPost(Utilities.ApiUrlBase +"Tool/ThemMailChoMailCanhBaoLicense")]
        public async Task<string> ThemMailChoMailCanhBaoLicense()
        {
            // Lay DS TenantId
            var sql = @$"SELECT ""Id"", ""TaxCode"", ""Emails""
                        FROM ""VnisTenants""
                        WHERE ""IsDeleted""=0";
            var tenants = _appFactory.AuthDatabase.Connection.Query<TenantDto>(sql);
            var taxcode = "";
            //tenants = tenants.ToList().Where(x => x.TaxCode == "**********-003").ToList();

            int dem = 0;
            try
            {

                // Duyet Kiem tra Cau hinh LicenceExpiredWarningEmails co gia tri chua
                // Neu chua Insert
                // Neu co => Bo qua
                var defaultSetting = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<SettingEntity>($@"
                                                    SELECT 
                                                        ""Id"", ""ParentId"", ""GroupCode"", ""Name"", ""Code"", ""Value"", ""ProviderName"", ""ProviderKey"", ""TenantId"", ""Options"", ""Type"", ""Description"", ""IsReadOnly"", ""ConcurrencyStamp"", ""CreationTime"", ""CreatorId"", ""LastModificationTime"", ""LastModifierId"", ""IsDeleted"", ""DeleterId"", ""DeletionTime""
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""Code"" = '{SettingKey.LicenceExpiredWarningEmails.ToString()}' 
                                                    AND ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(Guid.Empty)}' And ""IsDeleted"" = 0");

                foreach (var tenant in tenants)
                {
                    // Lay Setting theo tenant
                    var settingByTenant = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<SettingEntity>($@"
                                                    SELECT 
                                                        ""Id"", ""ParentId"", ""GroupCode"", ""Name"", ""Code"", ""Value"", ""ProviderName"", ""ProviderKey"", ""TenantId"", ""Options"", ""Type"", ""Description"", ""IsReadOnly"", ""ConcurrencyStamp"", ""CreationTime"", ""CreatorId"", ""LastModificationTime"", ""LastModifierId"", ""IsDeleted"", ""DeleterId"", ""DeletionTime""
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""Code"" = '{SettingKey.LicenceExpiredWarningEmails.ToString()}' 
                                                    AND ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenant.Id)}' And ""IsDeleted"" = 0");
                    taxcode = tenant.TaxCode;
                    if (settingByTenant != null)
                    {
                        if (settingByTenant.Value.IsNullOrEmpty() || settingByTenant.Value == "#")
                        {
                            // Update lai VnisSettings
                            settingByTenant.TenantId = tenant.Id;
                            settingByTenant.Value = tenant.Emails;
                            //await _settingRepository.UpdateAsync(settingByTenant);
                            var updateSql = @$"UPDATE ""VnisSettings""
                                                SET ""Value"" = '{tenant.Emails}'
                                                WHERE ""Code"" = '{SettingKey.LicenceExpiredWarningEmails.ToString()}' 
                                                    AND ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenant.Id)}' And ""IsDeleted"" = 0";
                            dem++;
                        }
                    }
                    else
                    {
                        // null
                        // Insert Setting
                        var insertSql = @$"INSERT INTO ""VnisSettings""
                                            (""Id"", ""ParentId"", ""GroupCode"", ""Name"", ""Code"", ""Value"", ""ProviderName"", ""ProviderKey"", ""TenantId"", ""Options"", ""Type"", ""Description"", ""IsReadOnly"", ""ExtraProperties"", ""ConcurrencyStamp"", ""CreationTime"", ""CreatorId"", ""LastModificationTime"", ""LastModifierId"", ""IsDeleted"", ""DeleterId"", ""DeletionTime"")
                                            VALUES
                                            ('{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}', 
                                            '{OracleExtension.ConvertGuidToRaw(defaultSetting.ParentId.Value)}', 
                                            '{defaultSetting.GroupCode}', 
                                            '{defaultSetting.Name}', 
                                            '{defaultSetting.Code}', 
                                            '{tenant.Emails}', 
                                            NULL, 
                                            NULL, 
                                            '{OracleExtension.ConvertGuidToRaw(tenant.Id)}', 
                                            NULL, 
                                            {defaultSetting.Type}, 
                                            '{defaultSetting.Description}',
                                            0, 
                                            NULL, 
                                            NULL, 
                                            TIMESTAMP '2021-09-16 16:57:59.000000', 
                                            '00000000000000000000000000000000', 
                                            NULL, 
                                            NULL, 
                                            0, 
                                            NULL, 
                                            NULL)
                                            ";
                        await _appFactory.AuthDatabase.Connection.ExecuteAsync(insertSql);
                        dem++;
                    }

                }
                return @$"Đã cập nhật Email cảnh báo hết hạn license cho {dem} chi nhánh";
            }
            catch (Exception e)
            {

                throw;
            }
            
        }

        public static string CreateCacheKey(ICurrentUser currentUser)
        {
            var userKey = currentUser.Id?.ToString("N") ?? "Anonymous";
            return $"ApplicationConfiguration_{userKey}_{CultureInfo.CurrentUICulture.Name}";
        }

        protected virtual async Task<SettingDto> BeforeUpdateEntityAsync(Guid id, SettingDto input)
        {
            return input;
        }
    }
}