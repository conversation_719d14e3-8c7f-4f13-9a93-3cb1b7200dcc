using Core.Account;
using Core.Application;
using Core.AutoMapper;
using Core.FeatureManagement;
using Core.Identity;
using Core.Modularity;
using Core.PermissionManagement;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.TenantManagement;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Reflection;

using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.Domain;
using VnisCore.System.Infrastructure;

namespace VnisCore.System.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(AbpAutoMapperModule),
        typeof(VnisCoreAuthDatabaseOracleDomainModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),
        typeof(AbpAccountApplicationModule),
        typeof(AbpIdentityApplicationModule),
        typeof(AbpPermissionManagementApplicationModule),
        typeof(AbpTenantManagementApplicationModule),
        typeof(AbpFeatureManagementApplicationModule),
        typeof(SharedModule),
        typeof(VnisCoreSystemInfrastructureModule)
    )]
    public class VnisCoreSystemApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            context.Services.AddAutoMapperObjectMapper<VnisCoreSystemApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreSystemApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreSystemApplicationModule).GetTypeInfo().Assembly);
        }
    }
}
