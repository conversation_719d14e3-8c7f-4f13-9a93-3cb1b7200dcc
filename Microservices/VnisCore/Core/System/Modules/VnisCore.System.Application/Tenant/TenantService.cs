using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Core;
using Core.Application.Dtos;
using Core.AspNetCore.Mvc.ApplicationConfigurations;
using Core.Caching;
using Core.Data;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.AppConst;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using VnisCore.System.Application.Tenant.Dto;
using VnisCore.System.Infrastructure.IRepository;

namespace VnisCore.System.Application.Tenant
{
    [Authorize(TenantManagementPermissions.Tenants.Default)]
    public class TenantService : TenantAppService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IDistributedCache<TenantCacheItem> _tenantCache;
        private readonly IEFTenantRepository _eftenantRepository;

        public TenantService(
            ITenantRepository tenantRepository,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            ITenantManager tenantManager,
            IDataSeeder dataSeeder,
            IDistributedEventBus distributedEventBus,
            IRepository<global::Core.TenantManagement.Tenant, Guid> repository,
            IDistributedCache<TenantCacheItem> tenantCache,
            IDistributedCache<ApplicationConfigurationDto> applicationConfigurationCache,
            IRepository<CurrencyEntity, long> repositoryCurrency,
            IRepository<TaxEntity, long> repositoryTax,
            IRepository<EmailTemplateEntity, long> repositoryEmailTemplate,
            IRepository<Invoice01HeaderFieldEntity, long> repositoryInvoice01HeaderField,
            IRepository<Invoice01DetailFieldEntity, long> repositoryInvoice01DetailField, 
            IConfiguration configuration,
            IRepository<NewRegistrationHeaderEntity, long> repositoryNewRegistrationHeader,
            IEFTenantRepository eftenantRepository) :
            base(
                tenantRepository,
                tenantManager,
                dataSeeder,
                distributedEventBus,
                repository,
                tenantCache,
                applicationConfigurationCache,
                repositoryCurrency,
                repositoryTax,
                repositoryEmailTemplate,
                repositoryInvoice01HeaderField,
                repositoryInvoice01DetailField,
                configuration,
                repositoryNewRegistrationHeader)
        {
            _tenantCache = tenantCache;
            _appFactory = appFactory;
            _localizer = localizer;
            _eftenantRepository = eftenantRepository;
        }

        public override async Task<TenantDto> GetAsync(Guid id)
        {
            var tenant = await _eftenantRepository.GetById(id);

            if (tenant == null || tenant.ParentId != _appFactory.CurrentTenant.Id.Value)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            return await base.GetAsync(id);
        }

        [AllowAnonymous]
        public async Task<Guid?> FindByTaxcodeAsync(string taxcode)
        {
            if (taxcode.IsNullOrEmpty()) return null;
            var tenant = await TenantRepository.FindByNameAsync(taxcode);
            if (tenant == null)
            {
                throw new UserFriendlyException(L["GivenTenantIsNotExist", taxcode]);
            }

            Guid? tenantId = tenant.Id;

            return tenantId;
        }


        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.GetList)]
        public override async Task<PagedResultDto<TenantDto>> GetListAsync(GetTenantsInput input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var tenantList = await _eftenantRepository.GetList(input, tenantId);
            var count = await _eftenantRepository.Count(input, tenantId);

            PagedResultDto<TenantDto> data = new PagedResultDto<TenantDto>();
            if (!tenantList.IsNullOrEmpty())
            {
                data.Items = tenantList;
            }
            data.TotalCount = count;
            
            if (data.TotalCount < 1)
                return data;

            var tenantIds = string.Join(",", data.Items.Select(x => $"'{OracleExtension.ConvertGuidToRaw(x.Id)}'"));
            var newRegistrationInvoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<IsEnableTenantDto>($@"
                select distinct ""TenantId"" from ""NewRegistrationHeader"" where ""TenantId"" IN ({tenantIds}) AND ""IsLocked"" = 1
            ");

            var isEnableTenantDtos = newRegistrationInvoices as IsEnableTenantDto[] ?? newRegistrationInvoices.ToArray();

            foreach (var item in data.Items)
            {
                var itemRegistration = isEnableTenantDtos.FirstOrDefault(x => x.TenantId == item.Id);
                // nếu chưa có đăng ký sử dụng nào được active => bỏ qua lấy trạng thái hiện tại trong isEnable
                if (itemRegistration is not { TenantId: { } })
                    continue;
                // nếu IsEnable == 1 và ngày khóa chi nhánh chưa đến thì set lại giá trị là active
                if (item.IsEnable == -1 && item.EffectiveDeactiveDate.HasValue && item.EffectiveDeactiveDate.Value > DateTime.Now)
                    item.IsEnable = 1;
            }

            return data;
        }

        [HttpGet(Utilities.ApiUrlBase + "tree-view")]
        public async Task<List<TenantTreeViewDto>> GetTenantTreeViewAsync()
        {
            var currentTenantId = _appFactory.CurrentUser.TenantId;

            //TODO: language
            if (!currentTenantId.HasValue)
                //throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                throw new UserFriendlyException(_localizer["Vnis.BE.System.CompanyInfoNotFound"]);

            #region sql treeview recursive

            //var sql = $@"
            //    WITH datatree(""Id"",""DisplayName"",""Code"", ""ParentId"", ""Level"",""TreeId"") AS (
            //        SELECT ""Id"", CONCAT(CONCAT(""FullNameVi"", ' - '), ""TaxCode"") AS ""DisplayName"", ""TenantCode"" AS ""Code"", ""ParentId"", 1 AS ""Level"",CAST(raw_to_guid(""Id"") AS VARCHAR ( 4000 )) AS ""TreeId"" FROM ""VnisTenants"" 
            //        WHERE ""IsDeleted"" = 0 AND ""Id"" = '{OracleExtension.ConvertGuidToRaw(currentTenantId.Value)}'
            //        UNION ALL
            //        SELECT ""VnisTenants"".""Id"", CONCAT(CONCAT(""VnisTenants"".""FullNameVi"", ' - '), ""VnisTenants"".""TaxCode"") AS ""DisplayName"", ""VnisTenants"".""TenantCode"" AS ""Code"", ""VnisTenants"".""ParentId"", (datatree.""Level"" + 1) AS ""Level"",
            //        CONCAT(CONCAT(datatree.""TreeId"",'|'),CAST(raw_to_guid(""VnisTenants"".""Id"") AS VARCHAR ( 4000 ))) AS ""TreeId""
            //        FROM ""VnisTenants""
            //            JOIN datatree ON ""VnisTenants"".""ParentId"" = datatree.""Id""
            //        WHERE ""VnisTenants"".""IsDeleted"" = 0
            //    )
            //    SELECT * FROM datatree
            //";

            var sql = $@"
                WITH datatree(""Id"",""DisplayName"",""Code"", ""ParentId"", ""Level"",""TreeId"") AS (
                    SELECT ""Id"",""FullNameVi"" AS ""DisplayName"", ""TenantCode"" AS ""Code"", ""ParentId"", 1 AS ""Level"",CAST(raw_to_guid(""Id"") AS VARCHAR ( 4000 )) AS ""TreeId"" FROM ""VnisTenants"" 
                    WHERE ""IsDeleted"" = 0 AND ""Id"" = '{OracleExtension.ConvertGuidToRaw(currentTenantId.Value)}'
                    UNION ALL
                    SELECT ""VnisTenants"".""Id"", ""VnisTenants"".""FullNameVi""  AS ""DisplayName"", ""VnisTenants"".""TenantCode"" AS ""Code"", ""VnisTenants"".""ParentId"", (datatree.""Level"" + 1) AS ""Level"",
                    CONCAT(CONCAT(datatree.""TreeId"",'|'),CAST(raw_to_guid(""VnisTenants"".""Id"") AS VARCHAR ( 4000 ))) AS ""TreeId""
                    FROM ""VnisTenants""
                        JOIN datatree ON ""VnisTenants"".""ParentId"" = datatree.""Id""
                    WHERE ""VnisTenants"".""IsDeleted"" = 0
                )
                SELECT * FROM datatree
            ";

            #endregion

            var response = await _appFactory.AuthDatabase.Connection.QueryAsync<TenantTreeViewDto>(sql);

            return response.ToList();
        }

        [HttpPost(Utilities.ApiUrlBase + "lock-tenant")]
        public async Task<bool> LockAccountAsync(LockTenantDto input)
        {
            var tenant = await _eftenantRepository.GetById(input.Id);

            if(tenant == null || tenant.ParentId != _appFactory.CurrentTenant.Id.Value)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }

            return await base.LockAccountAsync(input.Id, input.EffectiveDeactiveDate.ToLocalTime());
        }

        [HttpPost(Utilities.ApiUrlBase + "un-lock-tenant/{id:guid}")]
        public override async Task<bool> UnLockAccountAsync(Guid id)
        {
            var tenant = await _eftenantRepository.GetById(id);

            if (tenant == null || tenant.ParentId != _appFactory.CurrentTenant.Id.Value)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }

            return await base.UnLockAccountAsync(id);
        }

        public override async Task<TenantDto> CreateAsync(TenantCreateDto input)
        {
            ValidateTenant(input);


            var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
            var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            input.Token = token;

            return await base.CreateAsync(input);
        }

        [Authorize(TenantManagementPermissions.Tenants.Update)]
        public override async Task<TenantDto> UpdateAsync(Guid id, TenantUpdateDto input)
        {
            var tenant = await _eftenantRepository.GetById(input.Id);

            if (tenant == null || (tenant.ParentId != _appFactory.CurrentTenant.Id.Value && tenant.Id != _appFactory.CurrentTenant.Id.Value))
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh ");
            } 
            ValidateTenant(input);

            return await base.UpdateAsync(id, input);
        }

        public override async Task DeleteAsync(Guid id)
        {

            var tenant = await _eftenantRepository.GetById(id);

            if (tenant == null || tenant.ParentId != _appFactory.CurrentTenant.Id.Value)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }

            await base.DeleteAsync(id);
        }

        public override async Task<string> GetDefaultConnectionStringAsync(Guid id)
        {
            return await base.GetDefaultConnectionStringAsync(id);
        }

        public override async Task UpdateDefaultConnectionStringAsync(Guid id, string defaultConnectionString)
        {
            await base.UpdateDefaultConnectionStringAsync(id, defaultConnectionString);
        }

        public override async Task DeleteDefaultConnectionStringAsync(Guid id)
        {
            await base.DeleteDefaultConnectionStringAsync(id);
        }

        public void ValidateTenant(TenantCreateOrUpdateDtoBase tenant)
        {
            if (!tenant.TaxCode.IsNullOrEmpty())
            {
                if (!tenant.TaxCode.IsTaxCode())
                {
                    throw new UserFriendlyException("Mã số thuế không đúng định dạng", code: "400");
                }
            }

            if (!tenant.Emails.IsNullOrEmpty())
            {
                if (!tenant.Emails.IsPITEmail())
                {
                    throw new UserFriendlyException("Thư điện tử không đúng định dạng", code: "400");
                }
            }

            var regexPhone = new Regex(@"^(\+|0){1}([0-9]{9,})\b$");
            if (!tenant.Phones.IsNullOrEmpty())
            {
                if (!regexPhone.IsMatch(tenant.Phones))
                {
                    throw new UserFriendlyException("Điện thoại liên hệ không đúng định dạng", code: "400");
                }
            }

        }
        
    }
   
}