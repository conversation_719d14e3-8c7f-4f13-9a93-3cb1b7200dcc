using Core;
using Core.Application.Dtos;
using Core.AspNetCore.Mvc.ApplicationConfigurations;
using Core.Authorization.Permissions;
using Core.Caching;
using Core.Domain.Repositories;
using Core.Identity;
using Core.Localization.Resources.AbpLocalization;
using Core.PermissionManagement;
using Core.Shared.AppConst;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;
using Core.Users;
using Dapper;
using Dapper.Oracle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.System.Application.Role.Dto;
using VnisCore.System.Application.StoredProcedure.Procedures;
using VnisCore.System.Infrastructure.IRepository;

namespace VnisCore.System.Application.Role
{
    [Authorize]
    public class RoleService : IdentityRoleAppService
    {
        private readonly IAppFactory _appFactory;

        private readonly IDistributedCache<PermissionGrantCacheItem> _permissionGrantCacheItem;
        private readonly IDistributedCache<ApplicationConfigurationDto> _applicationConfigurationCache;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        private readonly IRoleRepository _roleRepository;

        private readonly IPermissionDefinitionManager _permissionDefinitionManager;

        public RoleService(IdentityRoleManager roleManager, 
            IIdentityRoleRepository roleRepository, 
            IAppFactory appFactory, 
            IDistributedCache<PermissionGrantCacheItem> permissionGrantCacheItem, 
            IDistributedCache<ApplicationConfigurationDto> applicationConfigurationCache, IStringLocalizer<CoreLocalizationResource> localizer,
            IRoleRepository customroleRepository,
            IPermissionDefinitionManager permissionDefinitionManager) : base(roleManager, roleRepository)
        {
            _appFactory = appFactory;
            _permissionGrantCacheItem = permissionGrantCacheItem;
            _applicationConfigurationCache = applicationConfigurationCache;
            _localizer = localizer;
            _roleRepository = customroleRepository;
            _permissionDefinitionManager = permissionDefinitionManager;
        }

        [Authorize(IdentityPermissions.Roles.Default)]
        [HttpGet(Utilities.ApiUrlBase + HttpActionConst.GetById + "{id:guid}")]
        public async Task<RoleDto> GetByIdAsync(Guid id)
        {
            var role = await _roleRepository.GetById(id, _appFactory.CurrentTenant.Id.Value);

            if (role == null)
            {
                throw new UserFriendlyException("Không tìm thấy quyền");
            }

            var roleDto = ObjectMapper.Map<IdentityRole, RoleDto>(role);

            var permission = await _appFactory.Repository<PermissionGrant, Guid>().GetListAsync(x =>
                x.ProviderName == "R" && x.ProviderKey == role.Name &&
                x.TenantId == _appFactory.CurrentTenant.Id);

            var permissionsGrant = ObjectMapper.Map<List<PermissionGrant>, List<PermissionsGrantDto>>(permission.ToList());
            roleDto.PermissionsGrant = permissionsGrant;

            return roleDto;
        }

        [Authorize(IdentityPermissions.Roles.Default)]
        [HttpGet(Utilities.ApiUrlBase + "getall")]
        public override async Task<ListResultDto<IdentityRoleDto>> GetAllListAsync()
        {
            return await base.GetAllListAsync();
        }

        [Authorize(IdentityPermissions.Roles.Default)]
        [HttpPost(Utilities.ApiUrlBase + HttpActionConst.GetList)]
        public override async Task<PagedResultDto<IdentityRoleDto>> GetListAsync(GetIdentityRolesInput input)
        {
            return await base.GetListAsync(input);
        }

        [Authorize(IdentityPermissions.Roles.Create)]
        public override async Task<IdentityRoleDto> CreateAsync(IdentityRoleCreateDto input)
        {
            var repo = _appFactory.Repository<IdentityRole, Guid>();

            var isExist = await repo.FirstOrDefaultAsync(x => x.Name == input.Name && x.TenantId == CurrentTenant.Id);
            if (isExist != null)
                //throw new UserFriendlyException("Mã nhóm đã tồn tại");
                throw new UserFriendlyException(_localizer["Vnis.BE.System.IdAlreadyExist"]);

            var role = await base.CreateAsync(input);

            if (!input.PermissionsGrant.Any()) return role;
            {
                var repoPermissionGrant = _appFactory.Repository<PermissionGrant, Guid>();
                var permissionsGrant = input.PermissionsGrant.Where(x => x.IsGranted).Select(item => new PermissionGrant(GuidGenerator.Create(), item.Name, "R", role.Name, _appFactory.CurrentTenant.Id)).ToList();
                await repoPermissionGrant.InsertManyAsync(permissionsGrant);
                await CurrentUnitOfWork.SaveChangesAsync();
            }

            return role;
        }

        [Authorize(IdentityPermissions.Roles.Update)]
        public override async Task<IdentityRoleDto> UpdateAsync(Guid id, IdentityRoleUpdateDto input)
        {
            if (!CurrentTenant.Id.HasValue)
                throw new UserFriendlyException("Không tìm thấy chi nhánh");

            var role = await _roleRepository.GetById(id, _appFactory.CurrentTenant.Id.Value);
            if (role == null)
            {
                throw new UserFriendlyException("Không tìm thấy quyền cần cần cập nhật");
            }

            var param = new OracleDynamicParameters();
            param.Add("id", id, OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add("displayName", input.DisplayName, OracleMappingType.NVarchar2, ParameterDirection.Input);
            param.Add("roleName", input.Name, OracleMappingType.NVarchar2, ParameterDirection.Input);
            param.Add("tenantId", _appFactory.CurrentTenant.Id, OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add("input_json", input.PermissionsGrant.JsonSerialize(), OracleMappingType.NClob, ParameterDirection.Input);

            await _appFactory.AuthDatabase.Connection.ExecuteAsync(
                SystemProcedureName.AuthUpdateRole,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            await CurrentUnitOfWork.SaveChangesAsync();

            // Refactor Remove Key permissionGrant
            var keyCaches = new List<string>();
            foreach (var permission in _permissionDefinitionManager.GetPermissions())
            {
                var key = CalculateCacheKey(permission.Name, "R", input.Name);
                keyCaches.Add(key);
            }
            await _permissionGrantCacheItem.RemoveManyAsync(keyCaches);

            await _applicationConfigurationCache.RemoveAsync(CreateCacheKey(CurrentUser));

            return new IdentityRoleDto();
        }

        [Authorize(IdentityPermissions.Roles.Delete)]
        public override async Task DeleteAsync(Guid id)
        {
            var role = await _roleRepository.GetById(id, _appFactory.CurrentTenant.Id.Value);
            if (role == null)
            {
                throw new UserFriendlyException("Không tìm thấy quyền cần xóa");
            }
            await base.DeleteAsync(id);
        }

        private static string CreateCacheKey(ICurrentUser currentUser)
        {
            var userKey = currentUser.Id?.ToString("N") ?? "Anonymous";
            return $"ApplicationConfiguration_{userKey}_{CultureInfo.CurrentUICulture.Name}";
        }

        protected virtual string CalculateCacheKey(string name, string providerName, string providerKey)
        {
            return PermissionGrantCacheItem.CalculateCacheKey(name, providerName, providerKey);
        }
    }
   
}