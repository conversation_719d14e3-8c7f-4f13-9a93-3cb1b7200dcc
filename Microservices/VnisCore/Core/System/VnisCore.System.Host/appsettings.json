{
  //"App": {
  //  "SelfUrl": "http://0.0.0.0:8054",
  //  "ClientUrl": "https://hoadon-test.mic.vn,https://tracuuhoadon.mic.vn",
  //  "CorsOrigins": "https://hoadon.mic.vn,https://gateway-test.mic.vn,https://gatewayapi-test.mic.vn,https://tracuuhoadon.mic.vn",
  //  "RedirectAllowedUrls": "https://hoadon-test.mic.vn,https://tracuuhoadon.mic.vn"
  //},
  //"ConnectionStrings": {
  //  "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=cdb1)))';User Id=einvoiceauth;Password=einvoice123",
  //  "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=cdb1)))';User Id=einvoicecore;Password=einvoice123",
  //  "VnisCoreMongoDbAuditLogging": "*******************************************************************************"
  //},
  //"IsSign123": true,
  //"Service": {
  //  "Name": "VnisCore.Sign.Host",
  //  "Title": "VnisCore.Sign.Host",
  //  "BaseUrl": "sign",
  //  "AuthApiName": "VnisCore.Sign.Host"
  //},
  //"TvanSupplier": {
  //  "VnPayInvoice": 1,
  //  "MInvoice": 2
  //},
  //"Redis": {
  //  "IsUsing": "true",
  //  "Configuration": "**********:6379,allowAdmin=true"
  //},
  //"AuthServer": {
  //  "Authority": "https://auth-test.mic.vn",
  //  "RequireHttpsMetadata": "false",
  //  "ApiName": "einvoice",
  //  "SwaggerClientId": "einvoice_vcb_swagger",
  //  "SwaggerClientSecret": "Vnis@12A"
  //},
  //"RabbitMQ": {
  //  "Connections": {
  //    "Default": {
  //      "HostName": "**********",
  //      "Port": 5672,
  //      "UserName": "einvoice",
  //      "Password": "einvoice@123",
  //      "VirtualHost": "/einvoice"
  //    }
  //  },
  //  "EventBus": {
  //    "ClientName": "einvoice.dashboard",
  //    "ExchangeName": "einvoice"
  //  }
  //},
  //"Minio": {
  //  "Endpoint": "**********:9000",
  //  "AccessKey": "einvoice",
  //  "SecretKey": "einvoice@123",
  //  "Region": null,
  //  "SessionToken": null,
  //  "BucketName": "einvoice"
  //},
  //"ElasticSearch": {
  //  "Invoice01": {
  //    "Index": "staging.core50.invoice01"
  //  },
  //  "Url": "http://**********:9200"
  //},
  //"TenantSign": {
  //  "Invoice01s": [
  //    {
  //      "TaxCodes": [],
  //      "FromHour": 0,
  //      "ToHour": 24,
  //      "InvoiceSources": [ 1, 2, 3 ]
  //    }
  //  ]
  //},
  //"Settings": {
  //  "TimePeriod": 10, //second
  //  "InvoiceTakeToSign": 100,
  //  "IsEnableSignInvoice01Schedule": 1
  //},
  //"Microservices": {
  //  "SendMail": {
  //    "Endpoint": "http://**********:8013/",
  //    "Timeout": 5
  //  }
  //},
  "Vault": {
    "Address": "http://vault.k8s-gds.vnpaytest.local",
    "Secret": "hvs.CAESIB_QgNipVWee9Q61_2_YzHS9Y731_RXNXrTSUgrYlNWYGh4KHGh2cy5Lb2NERVRHdDl3RWFkUjRZa2RKMjRzVWI",
    "Role": "",
    "MountPathCommon": "invoice-v5/common",
    "MountPathMicroService": "invoice-v5/system",
    "SecretType": "secrets",
    "MountPoint": "EFIN"
  }
}