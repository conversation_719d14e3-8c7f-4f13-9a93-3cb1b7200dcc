<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Version>5.31.3</Version>
  </PropertyGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\..\..\..\Framework\Core\Core.EventBus.RabbitMQ\Core.EventBus.RabbitMQ.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Core\Core.Swashbuckle\Core.Swashbuckle.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
    <ProjectReference Include="..\Modules\VnisCore.System.Application\VnisCore.System.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\template\excel\user\template-import-user-private.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
