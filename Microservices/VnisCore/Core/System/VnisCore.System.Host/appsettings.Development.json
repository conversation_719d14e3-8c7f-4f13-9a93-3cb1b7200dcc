{
  "App": {
    "SelfUrl": "http://localhost:6015",
    "CorsOrigins": "http://localhost:6789",
    "RedirectAllowedUrls": "http://localhost:4200"
  },
  "AppSelfUrl": "http://localhost:6015/",
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "Service": {
    "Name": "VnisCore.System.Host",
    "Title": "VnisCore.System.Host",
    "BaseUrl": "system",
    "AuthApiName": "VnisCore.System.Host"
  },
  "Redis": {
    "IsUsing": "true",
    //"Configuration": "**************,allowAdmin=true"
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14"
  },
  "AuthServer": {
    "Authority": "https://invoice-mass-auth.vnpaytest.vn",
    //"Authority": "http://*************:7020",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "************",
        "Port": 5672,
        "UserName": "admin",
        "Password": "admin@123",
        "VirtualHost": "/massv5-invoice"
      }
    },
    "EventBus": {
      "ClientName": "einvoice.system",
      "ExchangeName": "einvoice"
    }
  },
  "Settings": {
    "IsSharedSetting": 1,
    "IsSharedRolesTenants": 1,
    "DeactivationDays": 5,
    "TenantCodeHO": ""
  },
  "DefaultValue": {
    "AdminDefault": "admin",
    "PasswordDefault": "admin"
  },
  "PasswordGenerator": {
    "Length": 20,
    "MinLowercases": 1,
    "MinUppercases": 1,
    "MinDigits": 1,
    "MinSpecials": 1
  }
}