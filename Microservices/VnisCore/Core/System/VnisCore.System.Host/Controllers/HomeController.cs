using Core.Application.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace VnisCore.System.Host.Controllers
{
    public class HomeController : ApplicationService
    {
        private readonly IConfiguration _configuration;
        public HomeController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        // GET: api/Projects
        [HttpGet]
        public async Task Demo()
        {
            var mySecretValue = _configuration["Service:BaseUrl"];
        }

    }
}