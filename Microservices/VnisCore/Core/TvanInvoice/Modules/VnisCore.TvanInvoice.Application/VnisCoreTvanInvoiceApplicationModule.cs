using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Tvan;
using Core.Tvan.Interfaces;
using Core.Tvan.MInvoice;
using Core.Tvan.Services;
using Core.Tvan.Vnpay;
using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;

using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.Domain;
using VnisCore.Core.MongoDB;
using VnisCore.TvanInvoice.Application.ResendTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories.Invoice01;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories.Invoice02;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories.Invoice03;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories.Invoice04;
using VnisCore.TvanInvoice.Application.ResendTvan.Services;
using VnisCore.TvanInvoice.Application.ResendTvan.Services.Invoice01;
using VnisCore.TvanInvoice.Application.ResendTvan.Services.Invoice02;
using VnisCore.TvanInvoice.Application.ResendTvan.Services.Invoice03;
using VnisCore.TvanInvoice.Application.ResendTvan.Services.Invoice04;
using VnisCore.TvanInvoice.Application.ResponseTvan.Factories;
using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.InvoiceErrors;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.NotificationMS01TBKTDL.Invoice01;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.PITDeductionDocument;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.ResponseUndefinedTCT;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.TvanCheckInvoice;

namespace VnisCore.TvanInvoice.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(AbpAutoMapperModule),
        typeof(VnisCoreAuthDatabaseOracleDomainModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),
        typeof(VnisCoreMongoDbModule),

        typeof(SharedModule),
        typeof(TvanMInvoiceModule),
        typeof(TvanModule),
        typeof(TvanVnpayModule)
    )]
    public class VnisCoreTvanInvoiceApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            context.Services.AddAutoMapperObjectMapper<VnisCoreTvanInvoiceApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreTvanInvoiceApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreTvanInvoiceApplicationModule).GetTypeInfo().Assembly);
            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            context.Services.AddScoped<IDigitalSignatureService, DigitalSignatureService>();
            context.Services.AddScoped<ITenantRepository, TenantRepository>();

            context.Services.AddScoped<ISendMailHttpClientService, SendMailHttpClientService>();

            // repository 
            context.Services.AddScoped<IInvoice01HeaderRepository, Invoice01HeaderRepository>();
            context.Services.AddScoped<IInvoice01XmlRepository, Invoice01XmlRepository>();

            context.Services.AddScoped<IInvoice02HeaderRepository, Invoice02HeaderRepository>();
            context.Services.AddScoped<IInvoice02XmlRepository, Invoice02XmlRepository>();

            context.Services.AddScoped<IInvoice03HeaderRepository, Invoice03HeaderRepository>();
            context.Services.AddScoped<IInvoice03XmlRepository, Invoice03XmlRepository>();

            context.Services.AddScoped<IInvoice04HeaderRepository, Invoice04HeaderRepository>();
            context.Services.AddScoped<IInvoice04XmlRepository, Invoice04XmlRepository>();

            // service resend to TVAN
            context.Services.AddScoped<IDownloadFileInvoice01Service, DownloadFileInvoice01Service>();
            context.Services.AddScoped<IDownloadFileInvoice02Service, DownloadFileInvoice02Service>();
            context.Services.AddScoped<IDownloadFileInvoice03Service, DownloadFileInvoice03Service>();
            context.Services.AddScoped<IDownloadFileInvoice04Service, DownloadFileInvoice04Service>();

            context.Services.AddScoped<IResendToTvanService, ResendInvoice01HasCodeService>();
            //context.Services.AddScoped<IResendToTvanService, ResendInvoice01WithoutCodeService>();

            context.Services.AddScoped<IResendToTvanService, ResendInvoice02HasCodeService>();
            //context.Services.AddScoped<IResendToTvanService, ResendInvoice02WithoutCodeService>();

            context.Services.AddScoped<IResendToTvanService, ResendInvoice03HasCodeService>();
            //context.Services.AddScoped<IResendToTvanService, ResendInvoice03WithoutCodeService>();

            context.Services.AddScoped<IResendToTvanService, ResendInvoice04HasCodeService>();
            //context.Services.AddScoped<IResendToTvanService, ResendInvoice04WithoutCodeService>();

            //context.Services.AddScoped<IResendToTvanService, ResendTax01ReportService>();
            //context.Services.AddScoped<IResendToTvanService, ResendTax03ReportService>();

            // services receive Response from TVAN
            context.Services.AddScoped<IReceiveXmlFromTvanService, PITDeductionDocumentDeclarationReceivedResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, PITDeductionDocumentDeclarationAcceptedResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, RegistrationReceivedResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, RegistrationAcceptedResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, Invoice01HasCodeResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, NotificationTemplate01TBKTDLService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, InvoiceErrorResponseService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, TvanCheckInvoiceService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, ResponseUndefinedTCTService>();
            context.Services.AddScoped<IReceiveXmlFromTvanService, PITDeductionDocumentReceivedResponseService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice01ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice02ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice03ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, Invoice04ErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, TicketErrorService>();
            context.Services.AddScoped<IInvoiceErrorService, TbssHoaDonNgoaiHeThongService>();
            context.Services.AddScoped<IInvoiceErrorFactory, InvoiceErrorFactory>();
            context.Services.AddScoped<ITvanCheckInvoiceService, TvanCheckInvoice01Service>();
            context.Services.AddScoped<ITvanCheckInvoiceService, TvanCheckInvoice02Service>();
            context.Services.AddScoped<ITvanCheckInvoiceService, TvanCheckInvoice03Service>();
            context.Services.AddScoped<ITvanCheckInvoiceService, TvanCheckInvoice04Service>();
            context.Services.AddScoped<ITvanCheckInvoiceService, TvanCheckTicketService>();
            context.Services.AddScoped<IResendInvoiceToMInvoiceTvanService, ResendTvanMInvoiceInvoice01HasCodeService>();
            context.Services.AddScoped<IResendInvoiceToMInvoiceTvanService, ResendTvanMInvoiceInvoice02HasCodeService>();
            context.Services.AddScoped<IResendInvoiceToMInvoiceTvanService, ResendTvanMInvoiceInvoice03HasCodeService>();
            context.Services.AddScoped<IResendInvoiceToMInvoiceTvanService, ResendTvanMInvoiceInvoice04HasCodeService>();
            context.Services.AddScoped<IResendInvoiceToMInvoiceTvanFactory, ResendInvoiceToMInvoiceTvanFactory>();


            // service Thông báo kiểm tra dữ liệu MS01-TB/KTDL
            context.Services.AddScoped<INotificationTypeInvoice01Service, NotificationInvocie01WithoutCodeService>();
            context.Services.AddScoped<INotificationTypeInvoice01Service, NotificationInvoice01HasCodeService>();
        }
    }
}
