using Core;
using Core.Shared.Constants;
using System.Collections.Generic;
using System.Linq;
using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.InvoiceErrors;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Factories
{
    public interface IInvoiceErrorFactory
    {
        IInvoiceErrorService GetService(VnisType invoiceType);
    }

    public class InvoiceErrorFactory : IInvoiceErrorFactory
    {
        private readonly IEnumerable<IInvoiceErrorService> _services;
        
        public InvoiceErrorFactory(IEnumerable<IInvoiceErrorService> services)
        {
            _services = services;
        }

        public IInvoiceErrorService GetService(VnisType invoiceType)
        {
            var name = $"Invoice{invoiceType.GetHashCode():00}ErrorService";

            if (invoiceType == VnisType._6TNCN)
                name = nameof(TicketErrorService);
            
            if (invoiceType == VnisType._05TVDT)
                name = nameof(TicketErrorService);

            if (invoiceType == VnisType._11HĐNHT)
                name = nameof(TbssHoaDonNgoaiHeThongService);

            var implement = _services.FirstOrDefault(x => x.GetType().Name == name);
            if (implement == null)
                throw new UserFriendlyException($"Chưa có service {name}");

            return implement;
        }
    }
}
