using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.TenantManagement;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceError;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceHasCode;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCode;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCodeByReport;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCodeLoai2;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;
using Core.Tvan.Services.MS01TBKTDL.InvoiceWithoutCode;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Services
{
    /// <summary>
    /// Xử lý luồng 204
    /// </summary>
    public class NotificationTemplate01TBKTDLService : IReceiveXmlFromTvanService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ILogger<NotificationTemplate01TBKTDLService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IMS01TBKTDLInvoiceWithoutCodeFactory _mS01TBKTDLInvoiceWithoutCodeFactory;
        private readonly IMS01TBKTDLInvoiceHasCodeFactory _mS01TBKTDLInvoiceHasCodeFactory;
        private readonly IMS01TBKTDLInvoiceErrorFactory _mS01TBKTDLInvoiceErrorFactory;
        private readonly IMS01TBKTDLInvoiceWithoutCodeLoai2Factory _mS01TBKTDLInvoiceWithoutCodeLoai2Factory;
        private readonly IEnumerable<IMS01TBKTDLInvoiceWithoutCodeService> _mS01TBKTDLInvoiceWithoutCodeServices;
        private readonly IMS01TBKTDLInvoiceWithoutCodeByReportService _mS01TBKTDLInvoiceWithoutCodeByReportService;

        public NotificationTemplate01TBKTDLService(IAppFactory appFactory,
                                                   IStringLocalizer<CoreLocalizationResource> localizier,
                                                   ILogger<NotificationTemplate01TBKTDLService> logger,
                                                   IServiceProvider serviceProvider,
                                                   IMS01TBKTDLInvoiceWithoutCodeFactory mS01TBKTDLInvoiceWithoutCodeFactory,
                                                   IMS01TBKTDLInvoiceHasCodeFactory mS01TBKTDLInvoiceHasCodeFactory,
                                                   IMS01TBKTDLInvoiceErrorFactory mS01TBKTDLInvoiceErrorFactory,
                                                   IEnumerable<IMS01TBKTDLInvoiceWithoutCodeService> mS01TBKTDLInvoiceWithoutCodeServices,
                                                   IMS01TBKTDLInvoiceWithoutCodeLoai2Factory mS01TBKTDLInvoiceWithoutCodeLoai2Factory,
                                                   IMS01TBKTDLInvoiceWithoutCodeByReportService mS01TBKTDLInvoiceWithoutCodeByReportService)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _logger = logger;
            _serviceProvider = serviceProvider;
            _mS01TBKTDLInvoiceWithoutCodeFactory = mS01TBKTDLInvoiceWithoutCodeFactory;
            _mS01TBKTDLInvoiceHasCodeFactory = mS01TBKTDLInvoiceHasCodeFactory;
            _mS01TBKTDLInvoiceWithoutCodeServices = mS01TBKTDLInvoiceWithoutCodeServices;
            _mS01TBKTDLInvoiceErrorFactory = mS01TBKTDLInvoiceErrorFactory;
            _mS01TBKTDLInvoiceWithoutCodeLoai2Factory = mS01TBKTDLInvoiceWithoutCodeLoai2Factory;
            _mS01TBKTDLInvoiceWithoutCodeByReportService = mS01TBKTDLInvoiceWithoutCodeByReportService;
        }

        public async Task ReceiveXmlFromTvanAsync(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            using (var stream = new MemoryStream())
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);

                XmlSerializer serializer = new XmlSerializer(typeof(TDiepModel<TTChungModel, DLieuMS01TBaoModel>));
                StringReader strReader = new StringReader(xml);
                var responseTvan = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)serializer.Deserialize(strReader);

                var repoTenant = _appFactory.Repository<Tenant, Guid>();
                var tenant = await repoTenant.Where(x => x.TaxCode == responseTvan.DLieu.TBao.DLTBao.MST).FirstOrDefaultAsync();
                if (tenant == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.Invoice01TvanHasCode.TaxcodeNotFound"]);

                using var scope = _serviceProvider.CreateScope();
                var notificationServices = scope.ServiceProvider.GetServices<INotificationTypeInvoice01Service>();

                var loaiThongBao = responseTvan.DLieu.TBao.DLTBao.LTBao;
                switch (loaiThongBao)
                {
                    case (int)LTBao.Loai1:
                        // 204 cho MLTĐ 200: Hóa đơn có mã (TH lỗi)
                        var invoiceHasCodeService = await _mS01TBKTDLInvoiceHasCodeFactory.GetService(xml);
                        await invoiceHasCodeService.HandleResponseTvan(xml, transmissionPartner);
                        break;
                    case (int)LTBao.Loai2:
                        // 204 cho MLTĐ 300: thông báo sai sót
                        var invoiceErrorService = await _mS01TBKTDLInvoiceErrorFactory.GetService(xml);
                        if (invoiceErrorService != null)
                        {
                            await invoiceErrorService.HandleResponseTvan(xml);
                            break;
                        }

                        // 204 cho MLTĐ 203: hoá đơn không mã gửi từng lần
                        var invoiceWithoutCodeService = await _mS01TBKTDLInvoiceWithoutCodeLoai2Factory.GetService(xml);
                        if (invoiceWithoutCodeService != null)
                        {
                            await invoiceWithoutCodeService.HandleResponseTvan(xml, transmissionPartner);
                            break;
                        }

                        // 204 cho MLTĐ 400: hoá đơn không mã gửi theo bảng tổng hợp
                        await _mS01TBKTDLInvoiceWithoutCodeByReportService.HandleResponseTvan(xml: xml, ltbao: loaiThongBao, transmissionPartner: transmissionPartner);

                        break;
                    case (int)LTBao.Loai3:
                        var dsHDon = responseTvan.DLieu.TBao.DLTBao.LHDKMa.DSHDon.HDon;
                        if (!dsHDon.Any())
                            break;

                        var dsHDon01 = dsHDon.Where(x => x.KHMSHDon == RegistrationInvoiceType.HDGTGT.GetHashCode().ToString());
                        if (dsHDon01.Any())
                        {
                            var dsHDon01Group = dsHDon01.GroupBy(x => x.KHMSHDon, x => x);
                            var hdons = dsHDon01Group.SelectMany(hdon => hdon);
                            var service =  _mS01TBKTDLInvoiceWithoutCodeServices.FirstOrDefault(x => x.GetType().Name == nameof(MS01TBKTDLInvoice01WithoutCodeService));
                            await service.HandleResponseTvan(xml, hdons, responseTvan.TTChung);
                        }

                        var dsHDon02 = dsHDon.Where(x => x.KHMSHDon == RegistrationInvoiceType.HDBHang.GetHashCode().ToString());
                        if (dsHDon02.Any())
                        {
                            var dsHDon02Group = dsHDon02.GroupBy(x => x.KHMSHDon, x => x);
                            var hdons = dsHDon02Group.SelectMany(hdon => hdon);
                            var service = _mS01TBKTDLInvoiceWithoutCodeServices.FirstOrDefault(x => x.GetType().Name == nameof(MS01TBKTDLInvoice02WithoutCodeService));
                            await service.HandleResponseTvan(xml, hdons, responseTvan.TTChung);
                        }
                        
                        //TODO: check lại khi làm thêm tem hoặc phiếu thu điện tử
                        var tickets = dsHDon.Where(x => x.KHMSHDon == RegistrationInvoiceType.HDKhac.GetHashCode().ToString());
                        if (tickets.Any())
                        {
                            var ticketGroup = tickets.GroupBy(x => x.KHMSHDon, x => x);
                            var hdons = ticketGroup.SelectMany(hdon => hdon);
                            var service = _mS01TBKTDLInvoiceWithoutCodeServices.FirstOrDefault(x => x.GetType().Name == nameof(MS01TBKTDLTicketWithoutCodeService));
                            await service.HandleResponseTvan(xml, hdons, responseTvan.TTChung);
                        }

                        var dsHDon0304 = dsHDon.Where(x => x.KHMSHDon == RegistrationInvoiceType.CTu.GetHashCode().ToString());
                        if (dsHDon0304.Any())
                        {
                            var dsHDon0304Group = dsHDon0304.GroupBy(x => x.KHMSHDon, x => x);
                            var groups = dsHDon0304Group.SelectMany(group => group);
                            var dsHDon03 = groups.Where(x => x.KHHDon[3].ToString() == SerialNoInvoiceType.N.ToString());
                            var dsHDon04 = groups.Where(x => x.KHHDon[3].ToString() == SerialNoInvoiceType.B.ToString());

                            if (dsHDon03.Any())
                            {
                                var service = _mS01TBKTDLInvoiceWithoutCodeServices.FirstOrDefault(x => x.GetType().Name == nameof(MS01TBKTDLInvoice03WithoutCodeService));
                                await service.HandleResponseTvan(xml, dsHDon03, responseTvan.TTChung);
                            }

                            if (dsHDon04.Any())
                            {
                                var service = _mS01TBKTDLInvoiceWithoutCodeServices.FirstOrDefault(x => x.GetType().Name == nameof(MS01TBKTDLInvoice04WithoutCodeService));
                                await service.HandleResponseTvan(xml, dsHDon04, responseTvan.TTChung);
                            }
                        }
                        break;
                    case (int)LTBao.Loai4:
                        // 204 cho MLTĐ 400: hoá đơn không mã gửi theo bảng tổng hợp
                        await _mS01TBKTDLInvoiceWithoutCodeByReportService.HandleResponseTvan(xml: xml, ltbao: loaiThongBao, transmissionPartner: transmissionPartner);
                        break;
                    case (int)LTBao.Loai5:
                        break;
                    case (int)LTBao.Loai6:
                        break;
                    case (int)LTBao.Loai9:
                        var invoiceErrService = await _mS01TBKTDLInvoiceErrorFactory.GetService(xml);
                        if (invoiceErrService != null)
                        {
                            await invoiceErrService.HandleResponseTvan(xml);
                        }

                        // 204 cho MLTĐ 400: hoá đơn không mã gửi theo bảng tổng hợp
                        await _mS01TBKTDLInvoiceWithoutCodeByReportService.HandleResponseTvan(xml: xml, ltbao: loaiThongBao, transmissionPartner: transmissionPartner);
                        break;
                    default:
                        break;
                }
            }
        }


    }
}
