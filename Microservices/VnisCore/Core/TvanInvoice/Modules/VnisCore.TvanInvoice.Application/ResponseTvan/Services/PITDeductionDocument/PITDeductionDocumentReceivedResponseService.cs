using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.Response;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.PITDeductionDocument;
using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Services.PITDeductionDocument
{
    public class PITDeductionDocumentReceivedResponseService : IReceiveXmlFromTvanService
    {
        private readonly IFileService _fileService;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public PITDeductionDocumentReceivedResponseService(IFileService fileService,
                                           IAppFactory appFactory,
                                           IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _fileService = fileService;
            _appFactory = appFactory;
            _localizier = localizier;
        }

        public async Task ReceiveXmlFromTvanAsync(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            Log.Information($"PITDeductionDocument ReceivedResponseService ReceiveXmlFromTvanAsync");
            // convert xml to model
            using (var stream = new MemoryStream())
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);
                XmlSerializer serializer = new XmlSerializer(typeof(TDiepModel<TTChungModel, DLieuPITDeductionDocumentResponseModel>));
                StringReader strReader = new StringReader(xml);
                var pITDeductionDocumentTvanResponse = (TDiepModel<TTChungModel, DLieuPITDeductionDocumentResponseModel>)serializer.Deserialize(strReader);

                var repoPITDeductionDocument = _appFactory.Repository<PITDeductionDocumentEntity, long>();

                //lấy thông tin công ty
                var repoTenant = _appFactory.Repository<Tenant, Guid>();
                var tenant = await repoTenant.Where(x => x.TaxCode == pITDeductionDocumentTvanResponse.DLieu.TBao.DLTBao.MST).FirstOrDefaultAsync();

                if (tenant == null)
                    throw new UserFriendlyException("Chi nhánh không tồn tại");

                var pITDeductionDocument = await repoPITDeductionDocument.FirstOrDefaultAsync(x => x.TvanMessageCode == pITDeductionDocumentTvanResponse.TTChung.MTDTChieu);

                if (pITDeductionDocument == null)
                    throw new UserFriendlyException("Chứng từ không tồn tại");

                var ltbao = pITDeductionDocumentTvanResponse.DLieu.TBao.DLTBao.LTBao;
                int loaiThongBao = int.Parse(ltbao);

                if (loaiThongBao == LTBao.Loai2.GetHashCode())
                {
                    pITDeductionDocument.TvanResponseTime = DateTime.Now;
                    pITDeductionDocument.TvanStatus = (short)TvanStatus.TCTAccept;
                }
                else
                {
                    pITDeductionDocument.TvanResponseTime = DateTime.Now;
                    pITDeductionDocument.TvanStatus = (short)TvanStatus.TCTReject;
                }

                await repoPITDeductionDocument.UpdateAsync(pITDeductionDocument);
                var bytes = Encoding.UTF8.GetBytes(xml);
                await SaveTvanResponseAsync(bytes, pITDeductionDocument, tenant.Id, pITDeductionDocumentTvanResponse, transmissionPartner);
            }
        }

        private async Task SaveTvanResponseAsync(byte[] bytes, PITDeductionDocumentEntity headerEntity, Guid tenantId, TDiepModel<TTChungModel, DLieuPITDeductionDocumentResponseModel> pITDeductionDocumentTvanResponse, TransmissionPartnerEnum transmissionPartner)
        {
            //lưu xml
            //up lên minio trước
            //lưu file vào minio trước rồi mới lưu vào db

            var tvanXmlEntity = new TvanPITDeductionDocumentXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = $"{headerEntity.OrganizationTaxCode}_{pITDeductionDocumentTvanResponse.TTChung.MLTDiep}.xml".Replace("/", "_"),
                CreationTime = DateTime.Now,
                PhysicalFileName = $"{headerEntity.OrganizationTaxCode}_{pITDeductionDocumentTvanResponse.TTChung.MLTDiep}_{DateTime.Now.Ticks}.xml".Replace("/", "_"),
                TenantId = tenantId,
                Length = bytes.Length
            };

            var pathFileMinio = $"{MediaFileType.TvanPITDeductionDocumentXml}/{tenantId}/{tvanXmlEntity.CreationTime.Year}/{tvanXmlEntity.CreationTime.Month:00}/{tvanXmlEntity.CreationTime.Day:00}/{tvanXmlEntity.CreationTime.Hour:00}/{tvanXmlEntity.PhysicalFileName}";

            await _fileService.UploadAsync(pathFileMinio, bytes);
            var repoXml = _appFactory.Repository<TvanPITDeductionDocumentXmlEntity, long>();
            await repoXml.InsertAsync(tvanXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            // lưu thông tin phản hồi của TCT vào bảng PITDeductionDocumentTvanInfoEntity
            var reposTvanInfo = _appFactory.Repository<TvanInfoPITDeductionDocumentEntity, long>();
            var tvanInfoEntity = new TvanInfoPITDeductionDocumentEntity
            {
                MessageTypeCode = pITDeductionDocumentTvanResponse.TTChung.MLTDiep.ToString(),
                MessageCode = pITDeductionDocumentTvanResponse.TTChung.MTDiep,
                MessageCodeReference = pITDeductionDocumentTvanResponse.TTChung.MTDTChieu,
                FileId = tvanXmlEntity.Id,
                TenantId = tenantId,
                PITDeductionDocumentId = headerEntity.Id,
                Reason = pITDeductionDocumentTvanResponse.DLieu.TBao.DLTBao.LCTu?.DSCTu == null ? null : JsonConvert.SerializeObject(pITDeductionDocumentTvanResponse.DLieu.TBao.DLTBao.LCTu.DSCTu),
                Title = MLTDiep._213.ToDisplayName(),
                TransmissionPartner = (short)transmissionPartner
            };
            await reposTvanInfo.InsertAsync(tvanInfoEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

    }
}
