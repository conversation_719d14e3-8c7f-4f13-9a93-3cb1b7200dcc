using Core;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Dapper;
using Dapper.Oracle;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;
using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResponseTvan.Models;
using VnisCore.TvanInvoice.Application.StoredProcedure.Procedures;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Services.InvoiceErrors
{
    public class Invoice01ErrorService : IInvoiceErrorService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly IVnisCoreMongoInvoice01ReSyncRepository _mongoInvoice01ReSyncRepository;
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;

        public Invoice01ErrorService(IAppFactory appFactory,
                                     IFileService fileService,
                                     IVnisCoreMongoInvoice01ReSyncRepository mongoInvoice01ReSyncRepository,
                                    ElasticSearch elasticSearch,
                                    IConfiguration configuration)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _mongoInvoice01ReSyncRepository = mongoInvoice01ReSyncRepository;
            _elasticSearch = elasticSearch;
            _configuration = configuration;
        }

        public async Task SaveTvanInfoAsync(Guid tenantId, long fileId, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel, TransmissionPartnerEnum transmissionPartner, decimal tenantGroup)
        {
            var invoiceHeaders = await GetInvoiceAsyncV2(tenantId, responseTvanModel.TTChung.MTDTChieu);
            if (!invoiceHeaders.Any())
                throw new UserFriendlyException("Không tìm thấy thông tin hóa đơn sai sót");

            var invoiceErrors = new List<Invoice01ErrorEntity>();

            // ===> dữ liệu hóa đơn
            var hdons = responseTvanModel.DLieu.TBao.DLTBao?.DSHDon?.HDon;

            // ===> số thông báo
            var stbao = responseTvanModel.DLieu.TBao.STBao;

            // danh sách lý do không tiếp nhận
            // TĐ 301: có thẻ "TBao\DLTBao\DSLDKTNhan" => Tất cả HĐ trong TB đều KHÔNG được tiếp nhận
            var dsldktnhan = responseTvanModel.DLieu.TBao.DLTBao?.DSLDKTNhan?.LDo;

            var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice01ErrorEntity, long>();

            foreach (var item in invoiceHeaders)
            {
                short status301;
                string reason = null;

                if (dsldktnhan == null)
                {
                    status301 = (short)Tbss301Status.TiepNhan;

                    if (hdons != null && hdons.Any())
                    {
                        var invoice = hdons.FirstOrDefault(x =>
                            x.KHMSHDon == item.TemplateNo.ToString() &&
                            x.KHHDon == item.SerialNo &&
                            int.Parse(x.SHDon) == item.Number);

                        if (invoice != null)
                        {
                            status301 = (short)Tbss301Status.KhongTiepNhan;
                            reason = invoice.DSLDKTNhan?.LDo == null ? null : JsonConvert.SerializeObject(invoice.DSLDKTNhan.LDo);
                        }
                    }
                }
                else
                {
                    status301 = (short)Tbss301Status.KhongTiepNhan;
                    if (dsldktnhan.Any())
                    {
                        reason = JsonConvert.SerializeObject(dsldktnhan);
                    }
                }

                var tvanInfoEntity = new TvanInfoInvoice01ErrorEntity
                {
                    InvoiceHeaderId = item.Id,
                    MessageTypeCode = responseTvanModel.TTChung.MLTDiep.ToString(),
                    MessageCode = responseTvanModel.TTChung.MTDiep,
                    MessageCodeReference = responseTvanModel.TTChung.MTDTChieu,
                    FileId = fileId,
                    IsActive = true,
                    TenantId = tenantId,
                    Status = status301,
                    Reason = reason,
                    Title = MLTDiep._301.ToDisplayName(),
                    TransmissionPartner = (short)transmissionPartner,
                    CreationTime = DateTime.Now
                };

                await repoTvanInfo.InsertAsync(tvanInfoEntity);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                short statusTvan = status301 == (short)Tbss301Status.TiepNhan
                    ? (short)TvanStatus.TvanAccept
                    : (short)TvanStatus.TvanReject;

                var query = $@"select ""Id"" from ""Invoice01Error""
                   where ""InvoiceHeaderId"" = {item.Id}
                   order by ""Id"" desc
                   fetch first 1 rows only";

                var invoiceError = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice01ErrorEntity>(query);
                if (invoiceError != null)
                {
                    var sql = $@"UPDATE ""Invoice01Error"" SET ""TvanStatus"" = {statusTvan}, ""NumberInvoiceError"" = '{stbao.So}', ""LastModificationTime"" = SYSDATE
                     where ""Id"" = {invoiceError.Id}";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);

                    invoiceErrors.Add(new Invoice01ErrorEntity
                    {
                        InvoiceHeaderId = invoiceError.InvoiceHeaderId,
                        TvanStatus = statusTvan
                    });
                }
            }

            await UpdateResyncAsync(invoiceErrors, tenantGroup);
        }

        public async Task<long> UploadAndSaveFileAsync(Guid tenantId, string xml, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel)
        {
            var bytes = Encoding.UTF8.GetBytes(xml);

            //lưu xml
            //up lên minio trước
            //lưu file vào minio trước rồi mới lưu vào db
            var fileName = $"HoaDonSaiSot_{responseTvanModel.TTChung.MST}_{responseTvanModel.TTChung.MLTDiep}_{responseTvanModel.TTChung.MTDiep}_{DateTime.Now.Ticks}.xml".Replace("/", "_");
            var now = DateTime.Now;
            var tvanInvoice01ErrorXml = new TvanInvoice01ErrorXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                TenantId = tenantId,
                CreationTime = now
            };

            var pathFileMinio = $"{MediaFileType.Invoice01ErrorTvanXml}/{tenantId}/{now.Year}/{now.Month:00}/{now.Day:00}/{now.Hour:00}/{fileName}";
            await _fileService.UploadAsync(pathFileMinio, bytes);

            var repoXml = _appFactory.Repository<TvanInvoice01ErrorXmlEntity, long>();
            await repoXml.InsertAsync(tvanInvoice01ErrorXml);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return tvanInvoice01ErrorXml.Id;
        }

        public async Task<List<BaseInvoiceHeader>> GetInvoiceAsync(Guid tenantId, List<QueryInvoiceInfoModel> invoiceInfo)
        {
            var param = new OracleDynamicParameters();
            param.Add("json_conditions", JsonConvert.SerializeObject(invoiceInfo), OracleMappingType.NClob, ParameterDirection.Input);
            param.Add("tenantRawId", OracleExtension.ConvertGuidToRaw(tenantId), OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add(name: "output_data", value: DBNull.Value, dbType: OracleMappingType.RefCursor, direction: ParameterDirection.Output);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<BaseInvoiceHeader>(
                TvanInvoiceProcedureName.TvanInvoice01GetListByInfo,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            return data.ToList();
        }

        private async Task UpdateResyncAsync(List<Invoice01ErrorEntity> invoice01ErrorEntities, decimal group)
        {
            var tenantGroup = _configuration["Settings:TenantGroupsPrivate"];
            var groups = !string.IsNullOrEmpty(tenantGroup) ? tenantGroup.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa cấu hình TenantGroupsPrivate");
                return;
            }

            var tenantGroupIndexEs = "group-x";
            if (groups.Contains(group.ToString()))
                tenantGroupIndexEs = $"group-{group}";

            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            try
            {
                foreach (var item in invoice01ErrorEntities)
                {
                    await client.UpdateAsync<object>(item.InvoiceHeaderId, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.InvoiceErrorTvanStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.InvoiceErrorTvanStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.InvoiceErrorTvanStatus).FirstCharToLowerCase()}", item.TvanStatus)
                                )
                            )
                        );
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            #region
            //var ids = invoiceHeaders.Select(x => x.Id).ToList();
            //if (ids.Any())
            //var tenantGroup = _configuration["Settings:TenantGroupsPrivate"];
            //var groups = !string.IsNullOrEmpty(tenantGroup) ? tenantGroup.Split(",").ToList() : new List<string>();
            //if (!groups.Any())

            //{
            //    var invoice01ReSyncs = await _mongoInvoice01ReSyncRepository.GetByIdsAsync(ids);

            //    var invoice01ReSyncInsert = new List<MongoInvoice01ReSyncEntity>();

            //    if (invoice01ReSyncs.Any())
            //    {
            //        invoice01ReSyncs.ForEach(item =>
            //        {
            //            item.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode();
            //        });

            //        foreach (var item in ids)
            //        {
            //            var invoice01ReSync = invoice01ReSyncs.FirstOrDefault(x => x.Id == item);
            //            if (invoice01ReSync == null)
            //            {
            //                invoice01ReSync = new MongoInvoice01ReSyncEntity
            //                {
            //                    Id = item,
            //                    IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode()
            //                };
            //                invoice01ReSyncInsert.Add(invoice01ReSync);
            //            }
            //        }

            //        await _mongoInvoice01ReSyncRepository.UpdateManyAsync(invoice01ReSyncs);

            //        if (invoice01ReSyncInsert.Any())
            //            await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
            //    }
            //    else
            //    {
            //        foreach (var item in ids)
            //        {
            //            var invoice01ReSync = new MongoInvoice01ReSyncEntity
            //            {
            //                Id = item,
            //                IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode()
            //            };
            //            invoice01ReSyncInsert.Add(invoice01ReSync);
            //        }

            //        if (invoice01ReSyncInsert.Any())
            //        {
            //            await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
            //        }
            //    }
            //}
            #endregion
        }

        public async Task<List<BaseInvoiceHeader>> GetInvoiceAsyncV2(Guid tenantId, string messageCode)
        {
            var repo = _appFactory.Repository<Invoice01ErrorEntity, long>();

            return await repo.Where(x => x.MessageCode == messageCode).OrderBy(x => x.Index)
                       .Select(x => new BaseInvoiceHeader
                       {
                           Id = x.InvoiceHeaderId,
                           TemplateNo = x.TemplateNo,
                           SerialNo = x.SerialNo,
                           Number = x.Number
                       }).ToListAsync();
        }

        public Task<TbssHeaderEntity> GetInvoiceNgoaiHeThongAsync(Guid tenantId, string MessageCode)
        {
            throw new NotImplementedException();
        }

        public Task<long> UploadAndSaveFileHĐNgoaiHeThongAsync(Guid tenantId, long tbssHeaderId, string xml, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel)
        {
            throw new NotImplementedException();
        }

        public Task SaveTvanInfoHĐNgoaiHeThongAsync(Guid tenantId, long fileId, TbssHeaderEntity tbssHeader, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel, TransmissionPartnerEnum transmissionPartner)
        {
            throw new NotImplementedException();
        }
    }
}
