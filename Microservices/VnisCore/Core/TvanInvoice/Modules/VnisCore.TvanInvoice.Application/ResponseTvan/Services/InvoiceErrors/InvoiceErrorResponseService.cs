using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;
using Core.Tvan.Enums;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.TvanInvoice.Application.ResponseTvan.Factories;
using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Services.InvoiceErrors
{
    public class InvoiceErrorResponseService : IReceiveXmlFromTvanService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public InvoiceErrorResponseService(IAppFactory appFactory,
                                           IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _localizier = localizier;
        }

        public async Task ReceiveXmlFromTvanAsync(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            var responseTvanModel = XmlExtension.XmlDeserialize<TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel>>(xml);

            var repoTenant = _appFactory.Repository<Tenant, Guid>();
            var tenant = await repoTenant.Where(x => x.TaxCode == responseTvanModel.TTChung.MST).FirstOrDefaultAsync();
            if (tenant == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.InvoiceErrorTvanResponse.TaxcodeNotFound"]);

            var invoiceType = GetInvoiceType(responseTvanModel.TTChung.MTDTChieu);

            // check nếu là hóa đơn ngoài hệ thống thì phải dùng 1 luồng khác để xử lý
            if (invoiceType == VnisType._11HĐNHT)
            {
                var invoiceErrorFactory = _appFactory.GetServiceDependency<IInvoiceErrorFactory>();
                var invoiceErrorService = invoiceErrorFactory.GetService(invoiceType);

                var tbssHeader = await invoiceErrorService.GetInvoiceNgoaiHeThongAsync(tenant.Id, responseTvanModel.TTChung.MTDTChieu);
                if (tbssHeader == null)
                    throw new UserFriendlyException(_localizier[$"Vnis.BE.TvanInvoice.InvoiceErrorTvanResponse.InvoiceErrorNotFound"]);

                var fileId = await invoiceErrorService.UploadAndSaveFileHĐNgoaiHeThongAsync(tenant.Id, tbssHeader.Id, xml, responseTvanModel);

                // Upload lên Minio
                await invoiceErrorService.SaveTvanInfoHĐNgoaiHeThongAsync(tenant.Id, fileId, tbssHeader, responseTvanModel, transmissionPartner);
            }
            else
            {
                var invoiceErrorFactory = _appFactory.GetServiceDependency<IInvoiceErrorFactory>();
                var invoiceErrorService = invoiceErrorFactory.GetService(invoiceType);

                var fileId = await invoiceErrorService.UploadAndSaveFileAsync(tenant.Id, xml, responseTvanModel);

                // Upload lên Minio
                await invoiceErrorService.SaveTvanInfoAsync(tenant.Id, fileId, responseTvanModel, transmissionPartner, tenant.Group);
            }
        }

        private VnisType GetInvoiceType(string messageCode)
        {
            var repoInvoice01 = _appFactory.Repository<Invoice01ErrorEntity, long>();
            var repoInvoice02 = _appFactory.Repository<Invoice02ErrorEntity, long>();
            var repoInvoice03 = _appFactory.Repository<Invoice03ErrorEntity, long>();
            var repoInvoice04 = _appFactory.Repository<Invoice04ErrorEntity, long>();
            var repoTicket = _appFactory.Repository<TicketErrorEntity, long>();
            var repoPITDeductionDocument = _appFactory.Repository<PITDeductionDocumentErrorEntity, long>();

            if (repoInvoice01.Any(x => x.MessageCode == messageCode))
                return VnisType._01GTKT;
            if (repoInvoice02.Any(x => x.MessageCode == messageCode))
                return VnisType._02GTTT;
            if (repoInvoice03.Any(x => x.MessageCode == messageCode))
                return VnisType._03XKNB;
            if (repoInvoice04.Any(x => x.MessageCode == messageCode))
                return VnisType._04HGDL;
            if (repoTicket.Any(x => x.MessageCode == messageCode))
                return VnisType._05TVDT;
            if (repoPITDeductionDocument.Any(x => x.MessageCode == messageCode))
                return VnisType._6TNCN;

            return VnisType._11HĐNHT;
        }
    }
}
