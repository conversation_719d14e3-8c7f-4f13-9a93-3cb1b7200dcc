// using Core.Host.Shared;
using Core.VaultSharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;

namespace VnisCore.TvanInvoice.ResendTvanWorker
{
    public class Program
    {
        public static int Main(string[] args)
        {
            // ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting VnisCore.TvanInvoice.ResendTvanWorker.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "VnisCore.TvanInvoice.ResendTvanWorker terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }

        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            try
            {
                return VaultConfigure.CreateHostBuilder(args)
                                    .ConfigureServices((hostContext, services) =>
                                    {
                                        ConfigureServices(services, hostContext.Configuration);
                                        services.AddHostedService<ReSendTvanWorkerHostedService>();
                                    })
                                    .UseAutofac()
                                    .UseSerilog();
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }

        private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddApplication<ReSendTvanWorkerModule>();

            return services.BuildServiceProvider();
        }

        #region
        //private static IConfiguration _configuration;

        //public static IConfiguration Configuration
        //{
        //    get
        //    {
        //        var builder = new ConfigurationBuilder()
        //            .SetBasePath(Directory.GetCurrentDirectory())
        //            .AddJsonFile("appsettings.json");
        //        _configuration = builder.Build();
        //        return _configuration;
        //    }
        //}

        //public static int Main(string[] args)
        //{
        //    var folder = string.IsNullOrEmpty(Configuration["Logging:RootFolder:Folder"]) ? $"/Logs/{Configuration["Service:BaseUrl"]}"
        //       : Configuration["Logging:RootFolder:Folder"] + $"/Logs/{Configuration["Service:BaseUrl"]}";

        //    Log.Logger = new LoggerConfiguration()
        //        .MinimumLevel.Debug()
        //        .MinimumLevel.Information()
        //        .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
        //        .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
        //        .Enrich.FromLogContext()
        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Debug)
        //            .WriteTo.Async(c => c.File($"{folder}/debug/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))

        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Information)
        //            .WriteTo.Async(c => c.File($"{folder}/information/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))

        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Warning)
        //            .WriteTo.Async(c => c.File($"{folder}/warning/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))

        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Error)
        //            .WriteTo.Async(c => c.File($"{folder}/error/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))

        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Fatal)
        //            .WriteTo.Async(c => c.File($"{folder}/fatal/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))

        //        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(x => x.Level == LogEventLevel.Verbose)
        //            .WriteTo.Async(c => c.File($"{folder}/verbose/{DateTime.Now:yyyyMMdd}/{DateTime.Now:yyyy-MM-dd-HH}-log.txt", flushToDiskInterval: TimeSpan.FromMilliseconds(1), shared: true, rollingInterval: RollingInterval.Hour, retainedFileCountLimit: 168)))
        //        .CreateLogger();

        //    try
        //    {
        //        Log.Information("Starting VnisCore.Invoice01.GenerateNumber.");
        //        CreateHostBuilder(args).ConfigureAppConfiguration((hostingContext, config) =>
        //        {
        //            config
        //                .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
        //                .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
        //                .AddEnvironmentVariables();
        //        }).Build().Run();

        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Fatal(ex, "VnisCore.Invoice01.GenerateNumber terminated unexpectedly!");
        //        return 1;
        //    }
        //    finally
        //    {
        //        Log.CloseAndFlush();
        //    }
        //}

        //public static IHostBuilder CreateHostBuilder(string[] args) =>
        //            Host.CreateDefaultBuilder(args)
        //                .ConfigureServices((_, services) =>
        //                {
        //                    ConfigureServices(services);
        //                    services.AddHostedService<ReSendTvanWorkerHostedService>();
        //                })
        //                .UseAutofac()
        //                .UseSerilog();

        //private static ServiceProvider ConfigureServices(IServiceCollection services)
        //{
        //    services.AddApplication<ReSendTvanWorkerModule>();

        //    return services.BuildServiceProvider();
        //}
        #endregion
    }

}
