using System;
using System.Collections.Generic;
using System.Reflection;
using Core;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Data;
using Core.DependencyInjection;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.EventBus.RabbitMq;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Threading;
using Core.VaultSharp;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.TvanInvoice.ResendTvanWorker.BackgroundWorkers;
using VnisCore.TvanInvoice.ResendTvanWorker.Business;
using VnisCore.TvanInvoice.ResendTvanWorker.Interface;

namespace VnisCore.TvanInvoice.ResendTvanWorker
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),

        typeof(AbpAutofacModule),
        typeof(AbpCachingModule),

        typeof(AbpEntityFrameworkCoreOracleModule),

        typeof(VnisCoreAuthDatabaseOracleEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),

        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),

        typeof(AbpCachingStackExchangeRedisModule),
        typeof(AbpEventBusRabbitMqModule),

        typeof(SharedModule),
        typeof(SharedDtoModule)
     )]
    public class ReSendTvanWorkerModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<ReSendTvanWorkerModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<ReSendTvanWorkerAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(ReSendTvanWorkerModule).GetTypeInfo().Assembly);

            //context.Services.AddScoped<IResendInvoiceHasCodeBusiness, ResendInvoice01HasCodeBusiness>();
            //context.Services.AddScoped<IResendInvoiceHasCodeBusiness, ResendInvoice02HasCodeBusiness>();
            //context.Services.AddScoped<IResendInvoiceHasCodeBusiness, ResendInvoice03HasCodeBusiness>();
            //context.Services.AddScoped<IResendInvoiceHasCodeBusiness, ResendInvoice04HasCodeBusiness>();
            context.Services.AddScoped<IResendInvoiceHasCodeBusiness, ResendTicketHasCodeBusiness>();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<ResendInvoiceHasCodeBackgroundWorker>();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();

                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreOracleDbContext>();
            });
        }
    }
}
