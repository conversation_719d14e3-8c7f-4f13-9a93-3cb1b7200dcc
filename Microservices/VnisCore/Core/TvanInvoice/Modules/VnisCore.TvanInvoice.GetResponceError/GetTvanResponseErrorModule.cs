using Core;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Data;
using Core.DependencyInjection;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Threading;
using Core.Tvan;
using Core.Tvan.Vnpay;
using Core.VaultSharp;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;
using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.TvanInvoice.GetResponceError.BackgroundWorkers;
using VnisCore.TvanInvoice.GetResponceError.Interfaces;
using VnisCore.TvanInvoice.GetResponceError.Services;

namespace VnisCore.TvanInvoice.GetResponceError
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),

        typeof(AbpAutofacModule),
        typeof(AbpCachingModule),

        typeof(AbpEntityFrameworkCoreOracleModule),

        typeof(VnisCoreAuthDatabaseOracleEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),

        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),

        typeof(AbpCachingStackExchangeRedisModule),
        typeof(VnisCoreMongoDbModule),

        typeof(SharedModule),
        typeof(SharedDtoModule),
        typeof(TvanModule),
        typeof(TvanVnpayModule)
     )]
    public class GetTvanResponseErrorModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<GetTvanResponseErrorModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<GetTvanResponseErrorAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(GetTvanResponseErrorModule).GetTypeInfo().Assembly);

            context.Services.AddScoped<IInvoiceHasCodeBussinessService, Invoice01HasCodeBussinessService>();
            context.Services.AddScoped<IInvoiceHasCodeBussinessService, Invoice02HasCodeBussinessService>();
            context.Services.AddScoped<IInvoiceHasCodeBussinessService, Invoice03HasCodeBussinessService>();
            context.Services.AddScoped<IInvoiceHasCodeBussinessService, Invoice04HasCodeBussinessService>();
            context.Services.AddScoped<IInvoiceHasCodeBussinessService, TicketHasCodeBussinessService>();

            var configuration = context.Services.GetConfiguration();

            //callback tvan vnpay invoice
            context.Services.AddHttpClient("callback-tvan", (serviceProvider, client) =>
            {
                client.BaseAddress = new Uri(configuration.GetSection("CallBackTvanVnpay:EndPoint").Value);
                client.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("CallBackTvanVnpay:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });
            context.Services.AddSingleton<ICallBackTvanClient, CallBackTvanClient>();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<GetResponseInvoice01HasCodeBackgroundWorker>();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();

                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreOracleDbContext>();
            });
        }
    }
}
