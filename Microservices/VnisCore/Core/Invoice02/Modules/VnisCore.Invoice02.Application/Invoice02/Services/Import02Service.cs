using Core;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Services;
using VnisCore.Invoice02.Application.Invoice02.Models;

namespace VnisCore.Invoice02.Application.Invoice02.Services
{
    public class Import02Service : BaseImport02Service, IImport02Service
    {
        private readonly ILogger<Import02Service> _logger;
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;

        public Import02Service(
            ILogger<Import02Service> logger,
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService,
            IVnisCoreMongoInvoice02BatchIdRepository mongoInvoice02BatchIdRepository,
            IConfiguration configuration,
            Factories.Services.IInvoice02Service invoice02Service
)
            : base(logger, appFactory, serviceProvider, distributedEventBus, invoiceService, localizer, mongoInvoice02BatchIdRepository, configuration, invoice02Service)
        {
            _invoiceService = invoiceService;
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _localizer = localizer;
        }

        public override async Task<List<ImportInvoice02Model>> ReadExcelAsync(Guid tenantId, ExcelPackage package)
        {
            try
            {
                var worksheet = package.Workbook.Worksheets[0];
                if (worksheet?.Dimension == null || worksheet.Dimension.End.Row < 4)
                    //throw new UserFriendlyException("Không có sheet dữ liệu hoặc định dạng file không đúng");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.EmptyDataImportOrDataFormatIncorrect"]);

                var paymentMethods = new List<string>();
                var repoSetting = _serviceProvider.GetService<ISettingService>();
                var setting = await repoSetting.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());
                if (setting == null || string.IsNullOrEmpty(setting.Value))
                    //throw new UserFriendlyException("Chưa có cấu hình phương thức thanh toán");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.PaymentMethodNotFound"]);

                paymentMethods = setting.Value.Split(";").ToList();

                var repoInvoice02 = _appFactory.Repository<Invoice02HeaderEntity, long>();
                var duplicateErpIds = new List<string>();

                var repoCustomer = _appFactory.Repository<CustomerEntity, long>();
                var customers = await repoCustomer.Where(x => x.TenantId == tenantId).ToListAsync();
                var exitedCustomers = customers.ToDictionary(x => x.CustomerCode, x => x.TaxCode);

                var invoices = new List<ImportInvoice02Model>();
                var idErps = new List<string>();
                var regexCode = new Regex("^[a-zA-Z0-9][a-zA-Z0-9-_/.@]{0,48}[a-zA-Z0-9]{1,49}$|^[a-zA-Z0-9]{1}$");
                var customerIdExcels = new Dictionary<string, string>();
                var fromCurrency = await _appFactory.Repository<CurrencyEntity, long>().FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault);

                _logger.LogDebug($"Số dòng excel: {worksheet.Dimension.End.Row}");
                for (var i = 4; i <= worksheet.Dimension.End.Row; i++)
                {
                    try
                    {
                        #region lấy các trường từ file excel

                        //header
                        var templateNo = worksheet.Cells[i, 1].GetValue<string>()?.Trim();
                        var serialNo = worksheet.Cells[i, 2].GetValue<string>()?.Trim();
                        var idErp = worksheet.Cells[i, 3].GetValue<string>()?.Trim();
                        var invoiceDate = worksheet.Cells[i, 4].GetValue<string>()?.Trim();
                        var paymentMethod = worksheet.Cells[i, 5].GetValue<string>()?.Trim();
                        var paymentDate = worksheet.Cells[i, 6].GetValue<string>()?.Trim();
                        var currency = worksheet.Cells[i, 7].GetValue<string>()?.Trim();
                        var exchangeRate = worksheet.Cells[i, 8].GetValue<string>()?.Trim();
                        var buyerCode = worksheet.Cells[i, 9].GetValue<string>()?.Trim();
                        var buyerFullName = worksheet.Cells[i, 10].GetValue<string>()?.Trim();
                        var buyerLegalName = worksheet.Cells[i, 11].GetValue<string>()?.Trim();
                        var buyerTaxCode = worksheet.Cells[i, 12].GetValue<string>()?.Trim();
                        var buyerAddressLine = worksheet.Cells[i, 13].GetValue<string>()?.Trim();
                        var buyerBankAccount = worksheet.Cells[i, 14].GetValue<string>()?.Trim();
                        var buyerBankName = worksheet.Cells[i, 15].GetValue<string>()?.Trim();
                        var buyerEmail = worksheet.Cells[i, 16].GetValue<string>()?.Trim();
                        var buyerPhone = worksheet.Cells[i, 17].GetValue<string>()?.Trim();
                        var buyerFax = worksheet.Cells[i, 18].GetValue<string>()?.Trim();
                        var budgetUnitCode = worksheet.Cells[i, 19].GetValue<string>()?.Trim();
                        var buyerIDNumber = worksheet.Cells[i, 20].GetValue<string>()?.Trim();
                        var buyerPassportNumber = worksheet.Cells[i, 21].GetValue<string>()?.Trim();
                        var note = worksheet.Cells[i, 22].GetValue<string>()?.Trim();
                        var totalAmount = worksheet.Cells[i, 23].GetValue<string>()?.Trim();

                        var totalDiscountPercent = worksheet.Cells[i, 24].GetValue<string>()?.Trim();
                        //var totalDiscountAmountForEachProduct = worksheet.Cells[i, 26].GetValue<string>()?.Trim();

                        var totalDiscountAmount = worksheet.Cells[i, 25].GetValue<string>()?.Trim(); //tổng chiết khấu (chiết khấu toàn hóa đơn)
                        var totalPaymentAmount = worksheet.Cells[i, 26].GetValue<string>()?.Trim();

                        //detail
                        var productCode = worksheet.Cells[i, 27].GetValue<string>()?.Trim();
                        var productName = worksheet.Cells[i, 28].GetValue<string>()?.Trim();
                        var detailNote = worksheet.Cells[i, 29].GetValue<string>()?.Trim();
                        var unitName = worksheet.Cells[i, 30].GetValue<string>()?.Trim();
                        var quantity = worksheet.Cells[i, 31].GetValue<string>()?.Trim();
                        var unitPrice = worksheet.Cells[i, 32].GetValue<string>()?.Trim();
                        var amount = worksheet.Cells[i, 33].GetValue<string>()?.Trim();
                        var discountPercent = worksheet.Cells[i, 34].GetValue<string>()?.Trim();
                        var discountAmount = worksheet.Cells[i, 35].GetValue<string>()?.Trim();
                        var paymentAmount = worksheet.Cells[i, 36].GetValue<string>()?.Trim();
                        var productType = worksheet.Cells[i, 37].GetValue<string>()?.Trim();

                        #endregion

                        //validate các trường
                        #region validate headers
                        if (string.IsNullOrEmpty(idErp) && string.IsNullOrEmpty(templateNo) && string.IsNullOrEmpty(serialNo)
                            && string.IsNullOrEmpty(buyerCode) && string.IsNullOrEmpty(productCode))
                            continue;

                        // mẫu số - not null
                        if (string.IsNullOrEmpty(templateNo))
                            //throw new UserFriendlyException($"Mẫu số tại A{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TemplateNoIsNull", new string[] { i.ToString() }]);

                        if (templateNo.Length > 5)
                            //throw new UserFriendlyException($"Mẫu số tại A{i} không được dài quá 5 ký tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TemplateNoMaxLength", new string[] { i.ToString() }]);

                        // ký hiệu - not null - match regex
                        if (string.IsNullOrEmpty(serialNo))
                        {
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.SerialIsNull", new string[] { i.ToString() }]);
                        }
                        if (!serialNo.IsSerialNo())
                        {
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.SerialFormatIncorrect", new string[] { i.ToString() }]);
                        }

                        // số chứng từ - not null
                        if (string.IsNullOrEmpty(idErp))
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ErpIdIsNull", new string[] { i.ToString() }]);

                        if (idErp.Length > 200)
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} không được dài quá 200 kí tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ErpIdMaxLength", new string[] { i.ToString() }]);

                        idErp = $"{templateNo}-{serialNo}-{idErp}";
                        if (await repoInvoice02.AnyAsync(x => x.ErpId == idErp))
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} đã tồn tại trong hệ thống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ErpIdIsExist", new string[] { i.ToString() }]);

                        //check xem số chứng từ có trùng không
                        //nếu trùng với dòng trước thì bỏ qua, nếu không trùng với dòng trước mà đã có trong list thì báo lỗi
                        if (invoices.Count() > 0 && invoices.Last().ErpId != idErp && idErps.Any(x => x == idErp))
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} đã bị trùng trong file excel");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ErpIdDuplicate", new string[] { i.ToString() }]);

                        idErps.Add(idErp);

                        // ngày hóa đơn - not null
                        if (string.IsNullOrEmpty(invoiceDate))
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.InvoiceDateIsNull", new string[] { i.ToString() }]);

                        var dateInvoice = _invoiceService.GetDateTimeExcel(invoiceDate);
                        if (!dateInvoice.HasValue)
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không đúng kiểu dữ liệu DateTime như định dạng ô D3");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.InvoiceDateFormatIncorrect", new string[] { i.ToString() }]);

                        if (dateInvoice.Value.Date > DateTime.Now.Date)
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không được lớn hơn ngày hiện tại");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.InvoiceDateIncorrect", new string[] { i.ToString() }]);

                        //hình thức thanh toán -not null
                        if (string.IsNullOrEmpty(paymentMethod))
                            //throw new UserFriendlyException($"Hình thức thanh toán tại E{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.PaymentMethodIsNull", new string[] { i.ToString() }]);

                        if (paymentMethod.Length > 50)
                            //throw new UserFriendlyException($"Hình thức thanh toán tại E{i} dài tối đa 50 ký tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.PaymentMethodMaxLength", new string[] { i.ToString() }]);

                        if (!paymentMethods.Contains(paymentMethod))
                            //throw new UserFriendlyException($"Chưa có cấu hình phương thức thanh toán tại E{i}");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.PaymentMethodNotFound", new string[] { i.ToString() }]);

                        DateTime? outPaymentDate = null;
                        // ngày thanh toán
                        if (!string.IsNullOrEmpty(paymentDate))
                        {
                            outPaymentDate = _invoiceService.GetDateTimeExcel(paymentDate);
                            if (outPaymentDate == null)
                                //throw new UserFriendlyException($"Ngày thanh toán tại F{i} không đúng định dạng ngày tháng như ô D3");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.PaymentDateFormatIncorrect", new string[] { i.ToString() }]);
                        }

                        // tiền tệ - not null
                        if (string.IsNullOrEmpty(currency))
                            //throw new UserFriendlyException($"Tiền tệ tại G{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.CurrencyIsNull", new string[] { i.ToString() }]);

                        if (currency.Length > 3)
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.CurrencyMaxLength", new string[] { i.ToString() }]);

                        // tỷ giá - not null
                        if (string.IsNullOrEmpty(exchangeRate))
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ExchangeRateIsNull", new string[] { i.ToString() }]);

                        if (!decimal.TryParse(exchangeRate, out decimal numberExchangeRate))
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ExchangeRateIsNull", new string[] { i.ToString() }]);

                        if (numberExchangeRate == 0)
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ExchangeRateHigherThanZero", new string[] { i.ToString() }]);

                        if (currency == fromCurrency.CurrencyCode && numberExchangeRate != 1)
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ExchangeRateMustBe1", new string[] { i.ToString() }]);

                        // mã người mua hàng
                        if (!string.IsNullOrEmpty(buyerCode) && buyerCode.Length > 50)
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerIdMaxLength", new string[] { i.ToString() }]);

                        if (!regexCode.IsMatch(buyerCode))
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerIdSymbol", new string[] { i.ToString() }]);

                        if (!string.IsNullOrEmpty(buyerFullName))
                        {
                            if (buyerFullName.Length > 400)
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerNameMaxLength", new string[] { i.ToString() }]);
                        }
                        
                        if (!string.IsNullOrEmpty(buyerLegalName))
                        {
                            if (buyerLegalName.Length > 100)
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerLegalNameMaxLength", new string[] { i.ToString() }]);
                        }

                        // mã số thuế người mua
                        if (!string.IsNullOrEmpty(buyerTaxCode))
                        {
                            if (buyerTaxCode.Length > 14)
                                //throw new UserFriendlyException($"Mã số thuế người mua tại L{i} dài tối đa 14 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerTaxCodeMaxLength", new string[] { i.ToString() }]);

                            //check có phải mst không
                            if (!buyerTaxCode.IsTaxCode())
                                //throw new UserFriendlyException($"Mã số thuế người mua tại L{i} không đúng định dạng mã số thuế");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerTaxCodeFormatIncorrect", new string[] { i.ToString() }]);

                            // Kiểm tra thêm thông tin Tên đơn vị và Địa chỉ
                            if (buyerFullName.IsNullOrEmpty())
                            {
                                throw new UserFriendlyException($"Tên đơn vị người mua tại K{i} không được để trống trường hợp nhập thông tin Mã số thuế người mua");
                            }

                            if (buyerAddressLine.IsNullOrEmpty())
                            {
                                throw new UserFriendlyException($"Địa chỉ người mua tại M{i} không được để trống trường hợp nhập thông tin Mã số thuế người mua");
                            }
                        }

                        if (!string.IsNullOrEmpty(buyerAddressLine) && buyerAddressLine.Length > 400)
                            //throw new UserFriendlyException($"Địa chỉ người mua tại M{i} dài tối đa 400 ký tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerAddressLineMaxLength", new string[] { i.ToString() }]);

                        //tài khoản ngân hàng người mua
                        if (!string.IsNullOrEmpty(buyerBankAccount))
                        {
                            if (buyerBankAccount.Length > 30)
                                //throw new UserFriendlyException($"Tài khoản ngân hàng người mua tại N{i} dài tối đa 30 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerBankAccountMaxLength", new string[] { i.ToString() }]);
                        }

                        // chi nhánh ngân hàng người mua
                        if (!string.IsNullOrEmpty(buyerBankName))
                        {
                            if (buyerBankName.Length > 400)
                                //throw new UserFriendlyException($"Tên khoản ngân hàng người mua tại O{i} dài tối đa 400 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerBankAccountUsernameMaxLength", new string[] { i.ToString() }]);
                        }

                        // email người mua
                        if (!string.IsNullOrEmpty(buyerEmail))
                        {
                            if (buyerEmail.Length > 500)
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerEmailMaxLength", new string[] { i.ToString() }]);

                            if (!buyerEmail.IsManyEmails(';'))
                            {
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerEmailNotValidate", new string[] { $"P{i}" }]);
                            }
                        }

                        // số điện thoại người mua
                        if (!string.IsNullOrEmpty(buyerPhone))
                        {
                            if (buyerPhone.Length > 50)
                                //throw new UserFriendlyException($"Số điện thoại người mua tại Q{i} dài tối đa 50 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerNumberPhoneMaxLength", new string[] { i.ToString() }]);
                        }

                        // fax người mua
                        if (!string.IsNullOrEmpty(buyerFax))
                        {
                            if (buyerFax.Length > 20)
                                //throw new UserFriendlyException($"Số Fax người mua tại R{i} dài tối đa 20 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.BuyerFaxMaxLength", new string[] { i.ToString() }]);
                        }

                        // mã đơn vị quan hệ ngân sách
                        if (!string.IsNullOrEmpty(budgetUnitCode))
                        {
                            if (budgetUnitCode.Length > 7)
                                throw new UserFriendlyException($"Mã đơn vị quan hệ ngân sách tại S{i} dài tối đa 7 ký tự");

                            if (!budgetUnitCode.IsBudgetUnitCode())
                                throw new UserFriendlyException($"Mã đơn vị quan hệ ngân sách tại S{i} phải có 7 chữ số và bắt đầu bằng 1,2,3,7,8,9");
                        }

                        // số định danh
                        if (!string.IsNullOrEmpty(buyerIDNumber))
                        {
                            if (buyerIDNumber.Length != 12)
                                throw new UserFriendlyException($"Số định danh tại T{i} phải dài 12 ký tự");
                            // if (!buyerIDNumber.IsIDNumber12Character())
                            //     throw new UserFriendlyException($"Số định danh tại T{i} không đúng định dạng");
                        }

                        // số hộ chiếu
                        if (!string.IsNullOrEmpty(buyerPassportNumber))
                        {
                            if (buyerPassportNumber.Length > 20)
                                throw new UserFriendlyException($"Số hộ chiếu tại U{i} dài tối đa 20 ký tự");
                        }

                        // nội dung hóa đơn
                        if (!string.IsNullOrEmpty(note))
                        {
                            if (note.Length > 500)
                                //throw new UserFriendlyException($"Nội dung hóa đơn tại V{i} dài tối đa 500 ký tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.OrderDescriptionMaxLength", new string[] { i.ToString() }]);
                        }

                        // tổng tiền thanh toán
                        if (string.IsNullOrEmpty(totalAmount))
                            //throw new UserFriendlyException($"Tổng thành tiền tại W{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalAmountIsNull", new string[] { i.ToString() }]);

                        if (!decimal.TryParse(totalAmount, out decimal numberTotalAmount))
                            //throw new UserFriendlyException($"Tổng thành tiền tại W{i} không đúng định dạng số thập phân Decimal");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalAmountFormatDecimalIncorrect", new string[] { i.ToString() }]);


                        // Tiền chiết khấu toàn hóa đơn
                        decimal numberTotalDiscountAmount = 0;
                        if (!string.IsNullOrEmpty(totalDiscountAmount))
                        {
                            if (!decimal.TryParse(totalDiscountAmount, out numberTotalDiscountAmount))
                                //throw new UserFriendlyException($"Tổng tiền chiết khấu sau thuế tại V{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalDiscountAmountAfterTaxFormatDecimalIncorrect", new string[] { i.ToString() }]);
                        }

                        // tổng tiền thanh toán - not null
                        if (string.IsNullOrEmpty(totalPaymentAmount))
                            //throw new UserFriendlyException($"Tổng tiền thanh toán tại Y{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalPaymentAmountIsNull", new string[] { $"Z{i}" }]);

                        if (!decimal.TryParse(totalPaymentAmount, out decimal numberTotalPaymentAmount))
                            //throw new UserFriendlyException($"Tổng tiền thanh toán tại Y{i} không đúng định dạng số thập phân Decimal");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalPaymentAmountFormatDecimalIncorrect", new string[] { $"Z{i}" }]);


                        // % chiết khấu toàn hóa đơn
                        //if (!decimal.TryParse(totalDiscountPercent, out decimal numbertotalDiscountPercent))
                        //    //throw new UserFriendlyException($" % chiết khấu hóa đơn tại Y{i} không đúng định dạng số thập phân Decimal");
                        //    throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalDiscountPercentFormatDecimalIncorrect", new string[] { $"U{i}" }]);

                        decimal numbertotalDiscountPercent = 0;
                        if (!string.IsNullOrEmpty(totalDiscountPercent))
                        {
                            if (!decimal.TryParse(totalDiscountPercent, out numbertotalDiscountPercent))
                                //throw new UserFriendlyException($"Tổng phần trăm chiết khấu sau thuế tại U{i} không đúng định dạng số thập phân Double");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalDiscountPercentAfterTaxFormatDoubleIncorrect", new string[] { $"Z{i}" }]);
                        }


                        //// tổng tiền chiết khấu cho từng mặt hàng
                        //if (!decimal.TryParse(totalDiscountAmountForEachProduct, out decimal numbertotalDiscountAmountForEachProduct))
                        //    //throw new UserFriendlyException($"Tổng tiền chiết khấu cho từng mặt hàng tại Y{i} không đúng định dạng số thập phân Decimal");
                        //    throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.TotalDiscountAmountForEachProductFormatDecimalIncorrect", new string[] { $"Z{i}" }]);



                        if (string.IsNullOrEmpty(productType))
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductTypeIsNull", new string[] { i.ToString() }]);

                        var productTypes = new List<short>() { 1, 2, 3, 4 };
                        if (!productTypes.Contains(short.Parse(productType)))
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductTypeIncorrect", new string[] { i.ToString() }]);

                        #endregion

                        #region validate details
                        // mã hàng - not null
                        if (string.IsNullOrEmpty(productCode))
                            //throw new UserFriendlyException($"Mã hàng hóa tại Z{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductCodeIsNull", new string[] { $"AA{i}" }]);

                        if (productCode.Length > 50)
                            //throw new UserFriendlyException($"Mã hàng hóa tại Z{i} dài tối đa 50 ký tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductCodeMaxLength", new string[] { $"AA{i}" }]);

                        // tên hàng - not null
                        if (string.IsNullOrEmpty(productName))
                            //throw new UserFriendlyException($"Tên hàng hóa tại AA{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductNameIsNull", new string[] { $"AB{i}" }]);

                        if (productName.Length > 500)
                            //throw new UserFriendlyException($"Tên hàng hóa tại AA{i} dài tối đa 500 kí tự");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.ProductNameMaxLength", new string[] { $"AB{i}" }]);

                        // nội dung hàng hóa 
                        if (!string.IsNullOrEmpty(detailNote))
                            if (detailNote.Length > 250)
                                //throw new UserFriendlyException($"Nội dung hàng hóa tại AB{i} không được dài quá 250 kí tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.DetailNoteMaxLength", new string[] { $"AC{i}" }]);

                        // đơn vị tính - not null
                        if (short.Parse(productType) != 4)
                        {
                            if (string.IsNullOrEmpty(unitName))
                                //throw new UserFriendlyException($"Đơn vị tính tại AC{i} không được để trống");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.UnitNameIsNull", new string[] { $"AD{i}" }]);
                            if (unitName.Length > 50)
                                //throw new UserFriendlyException($"Đơn vị tính tại AC{i} không được dài quá 50 kí tự");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.UnitNameMaxLength", new string[] { $"AD{i}" }]);
                        }

                        // số lượng
                        decimal quantityOut = 0;
                        if (!string.IsNullOrEmpty(quantity))
                        {
                            if (!decimal.TryParse(quantity, out quantityOut))
                                //throw new UserFriendlyException($"Số lượng tại AD{i} không đúng định dạng số");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.QuantityNumberFormatIncorrect", new string[] { $"AE{i}" }]);
                        }

                        // đơn giá
                        decimal unitPriceOut = 0;
                        if (!string.IsNullOrEmpty(unitPrice))
                        {
                            if (!decimal.TryParse(unitPrice, out unitPriceOut))
                                //throw new UserFriendlyException($"Đơn giá tại AE{i} không đúng định dạng số");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.UnitPriceNumberFormatIncorrect", new string[] { $"AF{i}" }]);
                        }

                        // thành tiền trước thuế - not null
                        if (string.IsNullOrEmpty(amount))
                            //throw new UserFriendlyException($"Thành tiền trước thuế tại AH{i} không được để trống");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.AmountBeforeTaxIsNull", new string[] { $"AG{i}" }]);

                        if (!decimal.TryParse(amount, out decimal amountOut))
                            //throw new UserFriendlyException($"Thành tiền trước thuế tại AH{i} không đúng định dạng số");
                            throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.AmountBeforeTaxNumberFormatIncorrect", new string[] { $"AG{i}" }]);

                        //% chiết khấu trước thuế chi tiết hóa đơn
                        decimal numberDiscountPercent = 0;
                        if (!string.IsNullOrEmpty(discountPercent))
                        {
                            if (!decimal.TryParse(discountPercent, out numberDiscountPercent))
                                //throw new UserFriendlyException($"Phần trăm chiết khấu tại AF{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.DiscountPercentFormatDecimalIncorrect", new string[] { $"AH{i}" }]);

                            if (numberDiscountPercent < 0 || numberDiscountPercent > 100)
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.ImportService.DiscountPercentInvalid", $"AH{i}", 0, 100] + $". Giá trị hiện tại là: {numberDiscountPercent}");
                        }

                        //tiền chiết khấu trước thuế chi tiết hóa đơn
                        decimal numberDiscountAmount = 0;
                        if (!string.IsNullOrEmpty(discountAmount))
                        {
                            if (!decimal.TryParse(discountAmount, out numberDiscountAmount))
                                //throw new UserFriendlyException($"Tiền chiết khấu tại AG{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.DiscountAmountFormatDecimalIncorrect", new string[] { $"AI{i}" }]);
                        }

                        //thành tiền từng mặt hàng 
                        decimal numberPaymentAmount = 0;
                        if (!string.IsNullOrEmpty(paymentAmount))
                        {
                            if (!decimal.TryParse(paymentAmount, out numberPaymentAmount))
                                //throw new UserFriendlyException($"Thành tiền sau thuế tại AK{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice02.ImportService.PaymentAmountFormatDecimalIncorrect", new string[] { $"AJ{i}" }]);
                        }

                        #endregion

                        //add vào excel model
                        var invoice = new ImportInvoice02Model
                        {
                            TemplateNo = short.Parse(templateNo),
                            SerialNo = serialNo,
                            ErpId = idErp,
                            InvoiceDate = dateInvoice.Value,
                            PaymentMethod = paymentMethod,
                            PaymentDate = outPaymentDate ?? dateInvoice.Value,
                            Currency = currency,
                            ExchangeRate = numberExchangeRate,
                            BuyerCode = buyerCode,
                            BuyerLegalName = buyerLegalName,
                            BuyerFullName = buyerFullName,
                            BuyerTaxCode = buyerTaxCode,
                            BuyerAddressLine = buyerAddressLine,
                            BuyerBankAccount = buyerBankAccount,
                            BuyerBankName = buyerBankName,
                            BuyerEmail = buyerEmail,
                            BuyerPhone = buyerPhone,
                            BuyerFax = buyerFax,
                            BudgetUnitCode = budgetUnitCode,
                            BuyerIDNumber = buyerIDNumber,
                            BuyerPassportNumber = buyerPassportNumber,
                            Note = note,
                            TotalAmount = numberTotalAmount,
                            TotalDiscountPercent = numbertotalDiscountPercent,
                            TotalDiscountAmount = numberTotalDiscountAmount,
                            //TotalDiscountAmountForEachProduct = numbertotalDiscountAmountForEachProduct,
                            TotalPaymentAmount = numberTotalPaymentAmount,

                            ProductCode = productCode,
                            ProductName = productName,
                            DetailNote = detailNote,
                            UnitName = unitName,
                            Quantity = quantityOut,
                            UnitPrice = unitPriceOut,
                            DiscountPercent = numberDiscountPercent,
                            DiscountAmount = numberDiscountAmount,
                            Amount = amountOut,
                            PaymentAmount = numberPaymentAmount,
                            ProductType = short.Parse(productType),
                        };
                        invoices.Add(invoice);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                        if (ex.Message == ExceptionMessageConst.ObjectMustImplementIConvertible)
                        {
                            throw new UserFriendlyException($@"Định dạng dữ liệu dòng thứ {i} trong file Excel không đúng. Vui lòng kiểm tra lại.");
                        }
                        else
                        {
                            throw new UserFriendlyException(ex.Message);
                        }
                    }
                }

                return invoices;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
                //throw new UserFriendlyException(ex.Message);
            }

        }
    }
}
