using Core.Shared.Attributes;
using MediatR;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;

namespace VnisCore.Invoice02.Application.Invoice02.Models.Requests.Commands
{
    public class UpdateAdjustmentDetailInvoice02Request : IInvoiceHeaderDecreeNo70
    {

        public long Id { get; set; }
        [JsonIgnore]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [JsonIgnore]
        [SerialNo(ErrorMessage = "Ký hiệu không đúng định dạng")]
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        [JsonIgnore]
        [InvoiceNo(ErrorMessage = "Số hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }

        [StringLength(250, ErrorMessage = "Tài khoản người tạo hóa đơn chỉ được nhập tối đa 250 ký tự")]
        public string CreatorErp { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        //[MaxLength(500, ErrorMessage = "Tối đa 500 ký tự")]
        //public string Note { get; set; }

        #region Thông tin biên bản
        /// <summary>
        /// Số biên bản
        /// </summary>
        [StringLength(50, ErrorMessage = "Số biên bản chỉ được nhập tối đa 50 ký tự")]
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        [LessThanDate("InvoiceDate", ErrorMessage = "Ngày biên bản phải nhỏ hơn ngày hóa đơn")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Lý do biên bản
        /// </summary>
        [StringLength(500, ErrorMessage = "Lý do lập biên bản chỉ được nhập tối đa 500 ký tự")]
        public string DocumentReason { get; set; }

        /// <summary>
        /// Id File đính kèm của biên bản 
        /// </summary>
        public long? IdFileDocument { get; set; }

        /// <summary>
        /// có phải file upload không
        /// </summary>
        public bool IsUploadFile { get; set; }
        #endregion

        #region Thông tin chung
        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Loại chiết khấu
        /// 0 : Không chiết khấu
        /// 1 : Chiết khấu hàng hóa
        /// 2 : Chiết khấu tổng
        /// </summary>
        public short DiscountType { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu 
        /// </summary>
        public decimal TotalDiscountAmount { get; set; }
        #endregion

        public string ErrorMessage { get; set; }
        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<UpdateAdjustmentDetailInvoice02DetailRequestModel> InvoiceDetails { get; set; }
        #region ND70

        #region Vùng thông tin hóa đơn
        /// <summary>
        /// Mã cửa hàng
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã cửa hàng dài tối đa 50 ký tự")]
        public string StoreCode { get; set; }

        /// <summary>
        /// Tên cửa hàng
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên cửa hàng dài tối đa 400 ký tự")]
        [FieldDependencyReference("StoreCode", ErrorMessage = "Tên cửa hàng (StoreName) không được để trống")]
        public string StoreName { get; set; }
        #endregion

        #region Vùng thông tin người mua
        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã số đơn vị có quan hệ với ngân sách dài tối đa 7 ký tự")]
        [BudgetUnitCode(ErrorMessage = "Mã quan hệ ngân sách phải có 7 chữ số và bắt đầu bằng một trong các số: 1,2,3,7,8,9")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        [MaxLength(12, ErrorMessage = "MSố CC/CCCD/Định danh dài tối đa 12 ký tự")]
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số hộ chiếu/giấy tờ xuất/nhập cảnh dài tối đa 20 ký tự")]
        public string BuyerPassportNumber { get; set; }
        #endregion

        #endregion

        public class UpdateAdjustmentDetailInvoice02DetailExtraRequestModel
        {
            /// <summary>
            /// Id bản ghi field mở rộng của Detail
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateAdjustmentDetailInvoice02SpecificProductExtraModel
        {
            public long SpecificProductFieldId { get; set; }

            [SpecificProductType(ErrorMessage = "Định dạng tính chất hàng hoá đặc trưng không đúng")]
            public int Type { get; set; }

            [Required(ErrorMessage = "Tên trường hàng hóa đặc trưng không được để trống")]
            public string FieldName { get; set; }

            [MaxLength(200, ErrorMessage = "Giá trị trường hàng hóa đặc trưng dài tối đa 200 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateAdjustmentDetailInvoice02DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Tiền chiết khấu
            /// </summary>
            public decimal DiscountAmount { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            public decimal DiscountPercent { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            public decimal PaymentAmount { get; set; }

            /// <summary>
            /// Tính chất hàng hóa (Không phải nhóm hàng hóa)
            /// 1 - Hàng hóa, dịch vụ
            /// 2 - Khuyến mại
            /// 3 - Chiết khấu thương mại (Trong trường hợp muốn thể hiện thông tin chiết khấu theo dòng)
            /// 4 - Ghi chú/diễn giải
            /// </summary>
            [Required(ErrorMessage = "Tính chất hàng hóa không được để trống")]
            public short ProductType { get; set; }

            ///// <summary>
            ///// Mã sản phẩm
            ///// </summary>
            //[MaxLength(50, ErrorMessage = "Mã vật tư/hàng hóa chỉ được nhập tối đa 50 ký tự")]
            //public string ProductId { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            public decimal Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            public decimal Amount { get; set; }

            public string Note { get; set; }

            public List<UpdateAdjustmentDetailInvoice02DetailExtraRequestModel> InvoiceDetailExtras { get; set; }
            public List<UpdateAdjustmentDetailInvoice02SpecificProductExtraModel> InvoiceSpecificProductExtras { get; set; }
        }
    }
}
