using Core.Data;
using Core.Shared.Attributes;
using Core.Shared.Extensions;

using MediatR;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;

namespace VnisCore.Invoice02.Application.Invoice02.Models.Requests.Commands
{
    public class CreateInvoice02AdjustmentDetailRequest : IInvoiceHeaderDecreeNo70
    {
        //[JsonIgnore]
        //public Guid CodeInvoiceReference { get; set; }

        public long Id { get; set; }

        /// <summary>
        /// Mẫu số của hóa đơn gốc
        /// </summary>
        [Required(ErrorMessage = "Mẫu số hóa đơn gốc không được để trống")]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn gốc không đúng")]
        public short TemplateNoReference { get; set; }

        /// <summary>
        /// Id của hóa đơn gốc
        /// </summary>
        public long IdInvoiceRoot { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn gốc
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu hóa đơn gốc không được để trống")]
        [SerialNo(ErrorMessage = "Ký hiệu hóa đơn gốc không bao gồm các ký tự O, J, Z, W và phần số gồm 2 chữ số")]
        public string SerialNoReference { get; set; }

        /// <summary>
        /// Số hóa đơn gốc
        /// </summary>
        [Required(ErrorMessage = "Số hóa đơn của hóa đơn gốc không được để trống")]
        [InvoiceNo(ErrorMessage = "Số hóa đơn của hóa đơn gốc không hợp lệ")]
        public string InvoiceNoReference { get; set; }

        public long Number { get; set; }

        public string InvoiceNo { get; set; }

        #region Thông tin chung
        [StringLength(50, ErrorMessage = "Id bản ghi hóa đơn chỉ được nhập tối đa 50 ký tự")]
        public string ErpId { get; set; }

        [StringLength(250, ErrorMessage = "Tài khoản người tạo hóa đơn chỉ được nhập tối đa 250 ký tự")]
        public string CreatorErp { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        [Required(ErrorMessage = "Phương thức thanh toán không được để trống")]
        [StringLength(50, ErrorMessage = "Phương thức thanh toán chỉ được nhập tối đa 50 ký tự")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        [JsonIgnore]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [JsonIgnore]
        //[Required(ErrorMessage = "Ký hiệu không được để trống")]
        [SerialNo(ErrorMessage = "Ký hiệu không đúng định dạng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        //[MaxLength(500, ErrorMessage = "Tối đa 500 ký tự")]
        //public string Note { get; set; }
        #endregion

        #region Thông tin biên bản
        /// <summary>
        /// Số biên bản
        /// </summary>
        [StringLength(50, ErrorMessage = "Số biên bản chỉ được nhập tối đa 50 ký tự")]
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        [LessThanDate("InvoiceDate", ErrorMessage = "Ngày biên bản phải nhỏ hơn ngày hóa đơn")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Lý do biên bản
        /// </summary>
        [StringLength(500, ErrorMessage = "Lý do lập biên bản chỉ được nhập tối đa 500 ký tự")]
        public string DocumentReason { get; set; }

        /// <summary>
        /// Id File đính kèm của biên bản 
        /// </summary>
        public long? IdFileDocument { get; set; }

        /// <summary>
        /// có phải file upload không
        /// </summary>
        public bool IsUploadFile { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Loại chiết khấu
        /// 0 : Không chiết khấu
        /// 1 : Chiết khấu hàng hóa
        /// 2 : Chiết khấu tổng
        /// </summary>
        public short DiscountType { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế điều chỉnh
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế, là lượng điều chỉnh nếu là hóa đơn tăng/giảm
        /// </summary>
        public decimal TotalDiscountAmount { get; set; }
        #endregion

        public string ErrorMessages { get; set; }
        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<CreateAdjustmentDetailInvoice02DetailRequestModel> InvoiceDetails { get; set; }
        #region ND70

        #region Vùng thông tin hóa đơn
        /// <summary>
        /// Mã cửa hàng
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã cửa hàng dài tối đa 50 ký tự")]
        public string StoreCode { get; set; }

        /// <summary>
        /// Tên cửa hàng
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên cửa hàng dài tối đa 400 ký tự")]
        [FieldDependencyReference("StoreCode", ErrorMessage = "Tên cửa hàng (StoreName) không được để trống")]
        public string StoreName { get; set; }
        #endregion

        #region Vùng thông tin người mua
        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã số đơn vị có quan hệ với ngân sách dài tối đa 7 ký tự")]
        [BudgetUnitCode(ErrorMessage = "Mã quan hệ ngân sách phải có 7 chữ số và bắt đầu bằng một trong các số: 1,2,3,7,8,9")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        [MaxLength(12, ErrorMessage = "MSố CC/CCCD/Định danh dài tối đa 12 ký tự")]
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số hộ chiếu/giấy tờ xuất/nhập cảnh dài tối đa 20 ký tự")]
        public string BuyerPassportNumber { get; set; }
        #endregion

        #endregion

        /// <summary>
        /// trưởng mở rộng header
        /// </summary>
        //public List<CreateAdjustmentDetailInvoice02HeaderExtraRequestModel> InvoiceHeaderExtras { get; set; }


        public class CreateAdjustmentDetailInvoice02DetailExtraRequestModel
        {
            /// <summary>
            /// Id bản ghi field mở rộng của Detail
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class CreateAdjustmentDetailInvoice02SpecificProductExtraModel
        {
            public long SpecificProductFieldId { get; set; }

            [SpecificProductType(ErrorMessage = "Định dạng tính chất hàng hoá đặc trưng không đúng")]
            public int Type { get; set; }

            [Required(ErrorMessage = "Tên trường hàng hóa đặc trưng không được để trống")]
            public string FieldName { get; set; }

            [MaxLength(200, ErrorMessage = "Giá trị trường hàng hóa đặc trưng dài tối đa 200 ký tự")]
            public string FieldValue { get; set; }
        }

        public class CreateAdjustmentDetailInvoice02DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Tính chất hàng hóa (Không phải nhóm hàng hóa)
            /// 1 - Hàng hóa, dịch vụ
            /// 2 - Khuyến mại
            /// 3 - Chiết khấu thương mại (Trong trường hợp muốn thể hiện thông tin chiết khấu theo dòng)
            /// 4 - Ghi chú/diễn giải
            /// </summary>
            [Required(ErrorMessage = "Tính chất hàng hóa không được để trống")]
            public short ProductType { get; set; }

            /// <summary>
            /// Tiền chiết khấu
            /// </summary>
            public decimal DiscountAmount { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            public decimal DiscountPercent { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            public decimal PaymentAmount { get; set; }

            ///// <summary>
            ///// Mã sản phẩm
            ///// </summary>
            //[MaxLength(50, ErrorMessage = "Mã vật tư/hàng hóa chỉ được nhập tối đa 50 ký tự")]
            //public string ProductId { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            public decimal Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0")]
            public decimal Amount { get; set; }

            public string Note { get; set; }

            //public Dictionary<string, string> ExtraProperties { get; set; }
            public List<CreateAdjustmentDetailInvoice02DetailExtraRequestModel> InvoiceDetailExtras { get; set; }

            public List<CreateAdjustmentDetailInvoice02SpecificProductExtraModel> InvoiceSpecificProductExtras { get; set; }

            public ExtraPropertyDictionary ExtraProperties
            {
                get => InvoiceDetailExtras != null && InvoiceDetailExtras.Any()
                    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    {
                        { "invoiceDetailExtras", InvoiceDetailExtras.JsonSerialize()},
                    }) : null;
                set { }
            }
        }

        //public class CreateAdjustmentDetailInvoice02HeaderExtraRequestModel
        //{
        //    /// <summary>
        //    /// Id bản ghi field mở rộng của Detail
        //    /// </summary>
        //    [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
        //    public string FieldName { get; set; }

        //    /// <summary>
        //    /// Giá trị của field mở rộng
        //    /// </summary>
        //    [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng dài tối đa 500 ký tự")]
        //    public string FieldValue { get; set; }
        //}
    }
}
