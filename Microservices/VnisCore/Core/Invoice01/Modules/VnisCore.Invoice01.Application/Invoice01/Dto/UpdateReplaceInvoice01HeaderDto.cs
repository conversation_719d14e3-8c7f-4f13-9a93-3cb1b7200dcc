using Core.Dto.Shared.PrintNote;
using Core.Shared.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;

namespace VnisCore.Invoice01.Application.Invoice01.Dto
{
    public class UpdateReplaceInvoice01HeaderDto : IInvoiceHeaderDecreeNo70
    {
        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long Id { get; set; }

        #region ND70

        #region Vùng thông tin hóa đơn
        /// <summary>
        /// Mã cửa hàng
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã cửa hàng dài tối đa 50 ký tự")]
        public string StoreCode { get; set; }

        /// <summary>
        /// Tên cửa hàng
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên cửa hàng dài tối đa 400 ký tự")]
        [FieldDependencyReference("StoreCode", ErrorMessage = "Tên cửa hàng (StoreName) không được để trống")]
        public string StoreName { get; set; }

        /// <summary>
        /// Là hóa đơn cho thuê tài chính - Bổ sung theo ND70
        /// </summary>
        public bool IsFinancialLeaseInvoice { get; set; } = false;
        #endregion

        #region Vùng thông tin người mua
        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã số đơn vị có quan hệ với ngân sách dài tối đa 7 ký tự")]
        [BudgetUnitCode(ErrorMessage = "Mã quan hệ ngân sách phải có 7 chữ số và bắt đầu bằng một trong các số: 1,2,3,7,8,9")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        [MaxLength(12, ErrorMessage = "MSố CC/CCCD/Định danh dài tối đa 12 ký tự")]
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số hộ chiếu/giấy tờ xuất/nhập cảnh dài tối đa 20 ký tự")]
        public string BuyerPassportNumber { get; set; }
        #endregion

        #endregion

        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [SerialNo(ErrorMessage = "Ký hiệu không đúng định dạng")]
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        [InvoiceNo(ErrorMessage = "Số hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }

        [StringLength(250, ErrorMessage = "Tài khoản người tạo hóa đơn chỉ được nhập tối đa 250 ký tự")]
        public string CreatorErp { get; set; }

        #region Thông tin biên bản
        /// <summary>
        /// Số biên bản
        /// </summary>
        [StringLength(50, ErrorMessage = "Số biên bản chỉ được nhập tối đa 50 ký tự")]
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        [LessThanDate("InvoiceDate", ErrorMessage = "Ngày biên bản phải nhỏ hơn ngày hóa đơn")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Lý do biên bản
        /// </summary>
        [StringLength(500, ErrorMessage = "Lý do lập biên bản chỉ được nhập tối đa 500 ký tự")]
        public string DocumentReason { get; set; }

        /// <summary>
        /// Id File đính kèm của biên bản 
        /// </summary>
        public long? IdFileDocument { get; set; }

        /// <summary>
        /// có phải file upload không
        /// </summary>
        public bool IsUploadFile { get; set; }
        #endregion

        #region Thông tin chung
        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        [MaxLength(500, ErrorMessage = "Tối đa 500 ký tự")]
        public string Note { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        [Required(ErrorMessage = "Phương thức thanh toán không được để trống")]
        [StringLength(50, ErrorMessage = "Phương thức thanh toán chỉ được nhập tối đa 50 ký tự")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Làm tròn phần thập phân
        /// </summary>
        public int RoundingCurrency { get; set; }

        /// <summary>
        /// Chuyến đến tiền tệ
        /// </summary>
        [Required(ErrorMessage = "Loại tiền tệ không được để trống")]
        [MaxLength(3, ErrorMessage = "Loại tiền tệ chỉ được nhập tối đa 3 ký tự")]
        public string ToCurrency { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        [Required(ErrorMessage = "Tỷ giá chuyển đổi không được để trống")]
        [MoreThanValueAttribute("0", typeof(decimal), ErrorMessage = "Tỷ giá chuyển đổi phải lớn hơn 0")]
        [RangeNumberAttribute(5, 2, typeof(decimal), ErrorMessage = "Tỷ giá phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 99999.99")]
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Loại chiết khấu
        /// 0 : Không chiết khấu
        /// 1 : Chiết khấu hàng hóa
        /// 2 : Chiết khấu tổng
        /// </summary>
        public short DiscountType { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tổng tiền hàng phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tổng tiền thuế phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
        public decimal TotalVatAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tổng tiền thanh toán phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
        public decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tổng tiền chiết khấu trước thuế phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Tổng % chiết khấu sau thuế
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng % chiết khấu phải lớn hơn hoặc bằng 0")]
        public double TotalDiscountPercentAfterTax { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu sau thuế
        ///</summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng tiền chiết khấu sau thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalDiscountAmountAfterTax { get; set; }
        #endregion

        #region Thông tin người mua
        /// <summary>
        /// Mã người mua
        /// </summary>
        [MaxLength(50, ErrorMessage = "Tối đa 50 ký tự")]
        [CustomerId(ErrorMessage = "Số CIF viết không dấu, không chứa các ký tự đặc biệt ngoại trừ các ký tự .-_/@")]
        public string BuyerCode { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        [StringLength(500, ErrorMessage = "Email người mua chỉ được nhập tối đa 500 ký tự")]
        [ManyEmail]
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Họ tên người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Họ tên người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        [StringLength(100, ErrorMessage = "Tên khách hàng chỉ được nhập tối đa 100 ký tự")]
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Mã số thuế người mua chỉ được nhập tối đa 50 ký tự")]
        [TaxCode(ErrorMessage = "Mã số thuế không đúng định dạng")]
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        [StringLength(500, ErrorMessage = "Địa chỉ người mua chỉ được nhập tối đa 500 ký tự")]
        public string BuyerAddressLine { get; set; }

        [StringLength(250, ErrorMessage = "Tên quận/huyện người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerDistrictName { get; set; }

        [StringLength(250, ErrorMessage = "Tên tỉnh/thành phố người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerCityName { get; set; }

        [StringLength(5, ErrorMessage = "Mã quốc gia người mua chỉ được nhập tối đa 5 ký tự")]
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Số điện thoại người mua chỉ được nhập tối đa 50 ký tự")]
        public string BuyerPhoneNumber { get; set; }

        [StringLength(50, ErrorMessage = "Số fax người mua chỉ được nhập tối đa 50 ký tự")]
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Số tài khoản người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Số tài khoản người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Tên ngân hàng người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerBankName { get; set; }
        #endregion

        // additinal field
        public string ErrorMessages { get; set; }

        /// <summary>
        /// Thông tin ghi chú in hóa đơn
        /// </summary>
        public InvoicePrintNoteDto InvoicePrintNote { get; set; }

        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<UpdateReplaceInvoice01DetailRequestModel> InvoiceDetails { get; set; }

        public List<UpdateReplaceInvoice01HeaderExtraRequestModel> InvoiceHeaderExtras { get; set; }

        /// <summary>
        /// chi tiết thuế
        /// </summary>
        [Required(ErrorMessage = "Chi tiết thuế của hóa đơn")]
        [MinLength(1, ErrorMessage = "Chi tiết thuế phải có ít nhất 1 chi tiết")]
        public List<UpdateReplaceInvoice01TaxBreakdownRequestModel> InvoiceTaxBreakdowns { get; set; }


        public class UpdateReplaceInvoice01HeaderExtraRequestModel
        {
            /// <summary>
            /// Id bản ghi field mở rộng của Header
            /// </summary>
            [Required(ErrorMessage = "Id bản ghi field mở rộng của Header không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị field mở rộng dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateReplaceInvoice01DetailExtraRequestModel
        {
            /// <summary>
            /// Id bản ghi field mở rộng của Detail
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateReplaceInvoice01SpecificProductExtraModel
        {
            public long SpecificProductFieldId { get; set; }

            public int Type { get; set; }

            [Required(ErrorMessage = "Tên trường hàng hóa đặc trưng không được để trống")]
            public string FieldName { get; set; }

            [MaxLength(200, ErrorMessage = "Giá trị trường hàng hóa đặc trưng dài tối đa 200 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateReplaceInvoice01DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Chiết khấu
            /// </summary>
            [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Chiết khấu phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal DiscountAmountBeforeTax { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            [RangeNumberAttribute(2, 4, typeof(decimal), ErrorMessage = "% Chiết khấu phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 99.9999")]
            public decimal DiscountPercentBeforeTax { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            //[RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tiền thanh toán phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal PaymentAmount { get; set; }

            /// <summary>
            /// Mã sản phẩm
            /// </summary>
            [MaxLength(50, ErrorMessage = "Mã vật tư/hàng hóa chỉ được nhập tối đa 50 ký tự")]
            public string ProductCode { get; set; }

            /// <summary>
            /// Tính chất hàng hóa (Không phải nhóm hàng hóa)
            /// 1 - Hàng hóa, dịch vụ
            /// 2 - Khuyến mại
            /// 3 - Chiết khấu thương mại (Trong trường hợp muốn thể hiện thông tin chiết khấu theo dòng)
            /// 4 - Ghi chú/diễn giải
            /// </summary>
            [Required(ErrorMessage = "Tính chất hàng hóa không được để trống")]
            public short ProductType { get; set; }

            /// <summary>
            /// Tên sản phẩm
            /// </summary>
            [Required(ErrorMessage = "Tên vật tư/hàng hóa không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên vật tư/hàng hóa chỉ được nhập tối đa 500 ký tự")]
            public string ProductName { get; set; }

            /// <summary>
            /// Tên đơn vị tính
            /// </summary>
            //[Required(ErrorMessage = "Tên đơn vị tính không được để trống")]
            [MaxLength(50, ErrorMessage = "Tên đơn vị tính chỉ được nhập tối đa 50 ký tự")]
            public string UnitName { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Đơn giá phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            [RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal Amount { get; set; }

            /// <summary>
            /// Phần trăm thuế (-2: Không kê khai, -1: Không chịu thuế, 0: 0%, 5: 5%, 10: 10%)
            /// </summary>
            [RangeNumberAttribute(2, 2, typeof(decimal), true, ErrorMessage = "% thuế phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 99.99")]
            public decimal VatPercent { get; set; }

            /// <summary>
            /// Tổng tiền thuế
            /// </summary>
            //[RangeNumberAttribute(15, 6, typeof(decimal), ErrorMessage = "Tiền thuế phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 999999999999999.999999")]
            public decimal VatAmount { get; set; }

            /// <summary>
            /// Ghi chú
            /// </summary>
            [MaxLength(2000, ErrorMessage = "Nội dung chỉ được nhập tối đa 2000 ký tự")]
            public string Note { get; set; }

            public List<UpdateReplaceInvoice01DetailExtraRequestModel> InvoiceDetailExtras { get; set; }

            public List<UpdateReplaceInvoice01SpecificProductExtraModel> InvoiceSpecificProductExtras { get; set; }
        }

        public class UpdateReplaceInvoice01TaxBreakdownRequestModel
        {
            /// <summary>
            /// tien thue nguoi dung tinh
            /// </summary>
            public decimal VatAmount { get; set; }

            /// <summary>
            /// Phầm trăm thuế
            /// </summary>
            [RangeNumberAttribute(2, 2, typeof(decimal), true, ErrorMessage = "% thuế taxBreakdown phải lớn hơn hoặc bằng 0, nhỏ hơn hoặc bằng 99.99")]
            public decimal VatPercent { get; set; }

            /// <summary>
            /// tieefn thue he thong tu tinh
            /// </summary>
            public decimal VatAmountBackUp { get; set; }

            /// <summary>
            /// Thành tiền tính thuế
            /// </summary>
            public decimal Amount { get; set; }
        }
    }
}
