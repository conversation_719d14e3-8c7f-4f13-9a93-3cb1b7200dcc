// using Core.Host.Shared;
using Core.VaultSharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

using Serilog;

using System;

namespace VnisCore.SyncCatalogInvoice02.Worker
{
    public class Program
    {
        public static int Main(string[] args)
        {
            // ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting VnisCore.SyncCatalog.Worker.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "VnisCore.SyncCatalog.Worker terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }

        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            try
            {
                return VaultConfigure.CreateHostBuilder(args)
                                    .ConfigureServices((hostContext, services) =>
                                    {
                                        ConfigureServices(services, hostContext.Configuration);
                                        services.AddHostedService<VnisCoreSyncCatalogHostedService>();
                                    })
                                    .UseAutofac()
                                    .UseSerilog();
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }

        private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddApplication<VnisCoreSyncCatalogModule>();

            return services.BuildServiceProvider();
        }

        #region
        //private static IConfiguration _configuration;

        //public static IConfiguration Configuration
        //{
        //    get
        //    {
        //        var builder = new ConfigurationBuilder()
        //            .SetBasePath(Directory.GetCurrentDirectory())
        //            .AddJsonFile("appsettings.json");
        //        _configuration = builder.Build();
        //        return _configuration;
        //    }
        //}

        //public static int Main(string[] args)
        //{
        //    // ConfigureLogging.Configure();
        //    try
        //    {
        //        Log.Information("Starting VnisCore.Invoice01.GenerateNumber.");
        //        CreateHostBuilder(args).ConfigureAppConfiguration((hostingContext, config) =>
        //        {
        //            config
        //                .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
        //                .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
        //                .AddEnvironmentVariables();
        //        }).Build().Run();

        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Fatal(ex, "VnisCore.Invoice01.GenerateNumber terminated unexpectedly!");
        //        return 1;
        //    }
        //    finally
        //    {
        //        Log.CloseAndFlush();
        //    }
        //}

        //public static IHostBuilder CreateHostBuilder(string[] args) =>
        //            Host.CreateDefaultBuilder(args)
        //                .ConfigureServices((_, services) =>
        //                {
        //                    ConfigureServices(services);
        //                    services.AddHostedService<VnisCoreSyncCatalogHostedService>();
        //                })
        //                .UseAutofac()
        //                .UseSerilog();

        //private static ServiceProvider ConfigureServices(IServiceCollection services)
        //{
        //    services.AddApplication<VnisCoreSyncCatalogModule>();

        //    return services.BuildServiceProvider();
        //}
        #endregion
    }
}
