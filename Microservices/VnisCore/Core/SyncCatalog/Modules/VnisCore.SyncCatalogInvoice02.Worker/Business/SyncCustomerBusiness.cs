using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;

using Dapper;

using Microsoft.Extensions.Configuration;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.SyncCatalogInvoice02.Worker.Interface;
using VnisCore.SyncCatalogInvoice02.Worker.Repository;

namespace VnisCore.SyncCatalogInvoice02.Worker.Business
{
    public class SyncCustomerBusiness : ISyncCustomerBusiness
    {
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly ICustomerRepository _customerRepository;
        private readonly ISyncAccountBusiness _syncAccountBusiness;

        public SyncCustomerBusiness(
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IAppFactory appFactory,
            IConfiguration configuration,
            ICustomerRepository customerRepository,
            ISyncAccountBusiness syncAccountBusiness)
        {
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _configuration = configuration;
            _appFactory = appFactory;
            _customerRepository = customerRepository;
            _syncAccountBusiness = syncAccountBusiness;
        }

        public async Task SyncCustomerAsync()
        {
            // lấy theo group được cấu hình
            // nếu không có cấu hình tenantGroup thì mặc đinh lấy theo group 5
            var tenantGroup = _configuration["Settings:TenantGroup"];
            var tenantGroups = !string.IsNullOrEmpty(tenantGroup) ? tenantGroup.Split(",").Select(decimal.Parse).ToList() : new List<decimal> { 5 };

            int.TryParse(_configuration["Settings:TotalQuantityTakeInvoice"], out var takeInvoice);
            if (takeInvoice == 0)
                takeInvoice = 100;

            var invoices = await _mongoInvoice02Repository.GetUnSyncCustomerAsync(tenantGroups, takeInvoice);
            if (!invoices.Any())
                return;

            await SyncCustomerAsync(invoices);
        }

        private async Task SyncCustomerAsync(List<MongoInvoice02Entity> invoiceUnSyncCustomers)
        {
            try
            {
                //var invoiceIsSyncCustomersToCore = new List<MongoInvoice02Entity>();

                var groupInvoicesByTenants = invoiceUnSyncCustomers.GroupBy(x => new { x.TenantId, x.CreatorId });
                foreach (var invoices in groupInvoicesByTenants)
                {
                    var tenantId = invoices.FirstOrDefault() == null ? Guid.Empty : invoices.FirstOrDefault().TenantId;

                    // lấy cấu hình setting
                    var settingService = _appFactory.GetServiceDependency<ISettingService>();
                    var settingCheckUpdateCustomer = await settingService.GetByCodeAsync(tenantId, SettingKey.InvoiceUpdateCustomer.ToString());
                    var settingCheckCreateCustomer = await settingService.GetByCodeAsync(tenantId, SettingKey.InvoiceCreateCustomer.ToString());

                    var isCreate = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                    var isUpdate = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";

                    foreach (var invoice in invoices)
                    {
                        if (!string.IsNullOrEmpty(invoice.BuyerEmail) && !invoice.BuyerEmail.IsEmails(';'))
                        {
                            Log.Error($@"*** Email không đúng định dạng: SellerTaxCode: {invoice.SellerTaxCode} - TemplateNo: {invoice.TemplateNo} - SerialNo: {invoice.SerialNo} - InvoiceNo: {invoice.InvoiceNo} - BuyerEmail: {invoice.BuyerEmail}");
                            invoice.IsSyncedCustomerToCore = (short)SyncCatalogStatus.IncorrectFormatCustomerEmail;
                            //invoiceIsSyncCustomersToCore.Add(invoice);
                            continue;
                        }

                        var syncCustomerStatus = SyncCatalogStatus.UnSyncToCore;

                        try
                        {
                            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
                            var customer = await _customerRepository.GetByCustomerCode(rawTenantId, invoice.BuyerCode);
                            var creator = invoice.CreatorId ?? tenantId;


                            if (customer == null)
                            {
                                if (isCreate)
                                {
                                    var customerId = (await GetSEQsNextVal(1, SequenceName.Customer)).First();

                                    //tạo 
                                    StringBuilder queryInsert = new StringBuilder();
                                    queryInsert.Append($@"       
                                        BEGIN
                                        INSERT INTO ""Customer"" (                                      
                                            ""Id"",                                                           
                                            ""CustomerCode"",                                                     
                                            ""Address"",                                                       
                                            ""BankAccount"",                                                      
                                            ""BankName"",                                                        
                                            ""City"",                                                   
                                            ""TenantId"",                                                
                                            ""FullName"",                                            
                                            ""LegalName"",                                                   
                                            ""Phone"",                                                   
                                            ""Email"",                                                   
                                            ""UserId"",                                                       
                                            ""TaxCode"",                                  
                                            ""CreationTime"",
                                            ""CreatorId"",
                                            ""IsActive""
                                        )                                                                       
                                        VALUES");


                                    queryInsert.Append($@"(                                                                 
                                                { customerId},                                                                                                                                                   
                                                '{ invoice.BuyerCode }',                                                                                                                                          
                                                N'{ invoice.BuyerAddressLine?.Replace("'", "''") }',                                                                                               
                                                '{ invoice.BuyerBankAccount }',                                                                                                                                            
                                                N'{  invoice.BuyerBankName?.Replace("'", "''") }',                                                                                                                    
                                                N'{ invoice.BuyerCityName }',                                                                                                     
                                                '{ OracleExtension.ConvertGuidToRaw(tenantId) }',                                                                                                                                          
                                                N'{ invoice.BuyerFullName?.Replace("'", "''") }',                                                                                                                                     
                                                N'{ invoice.BuyerLegalName?.Replace("'", "''")}',                                                                                
                                                '{ invoice.BuyerPhoneNumber }',                                                                                                               
                                                '{ invoice.BuyerEmail }',                                                                                                               
                                                '{ OracleExtension.ConvertGuidToRaw(Guid.Empty)  }',                                                                                                               
                                                '{ invoice.BuyerTaxCode }',                                                                                                   
                                                '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                                '{  OracleExtension.ConvertGuidToRaw(creator) }',                                                                                               
                                                0
                                        ) ");

                                    queryInsert.Append("; ");

                                    queryInsert.Append($"Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                                    $"Set \"BuyerCode\" = '{invoice.BuyerCode}'" +
                                    $"Where \"Id\" = {invoice.Id}; ");

                                    var queryResult = queryInsert.Append($" END; ").ToString();
                                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);

                                    // tạo tài khoản khách hàng
                                    await _syncAccountBusiness.SyncAccountAsync(new Dtos.SyncAccountDto
                                    {
                                        InvoiceId = invoice.Id,
                                        TenantId = tenantId,
                                        Type = VnisType._02GTTT,
                                    });
                                }
                            }
                            else
                            {
                                if (isUpdate)
                                {
                                    StringBuilder raw = new StringBuilder();
                                    //nếu là điều chỉnh định danh thì k sửa mst người mua
                                    if ((InvoiceStatus)invoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh)
                                    {
                                        raw.Append($"BEGIN Update \"{DatabaseExtension<CustomerEntity>.GetTableName()}\" " +
                                                         $"Set \"FullName\" = N'{invoice.BuyerFullName?.Replace("'", "''")}', " +
                                                         $"\"LegalName\" = N'{invoice.BuyerLegalName?.Replace("'", "''")}', " +
                                                         $"\"Phone\" = '{invoice.BuyerPhoneNumber}', " +
                                                         $"\"Email\" = '{ invoice.BuyerEmail }', " +
                                                         $"\"Address\" = N'{invoice.BuyerAddressLine?.Replace("'", "''")}', " +
                                                         $"\"BankAccount\" = '{invoice.BuyerBankAccount?.Replace("'", "''")}', " +
                                                         $"\"BankName\" = N'{invoice.BuyerBankName?.Replace("'", "''")}', " +
                                                         $"\"Fax\" = '{invoice.BuyerFaxNumber}' " +
                                                         $"Where \"Id\" = {customer.Id}");
                                    }
                                    else
                                    {
                                        raw.Append($"BEGIN Update \"{DatabaseExtension<CustomerEntity>.GetTableName()}\" " +
                                                            $"Set \"FullName\" = N'{invoice.BuyerFullName?.Replace("'", "''")}', " +
                                                            $"\"LegalName\" = N'{invoice.BuyerLegalName?.Replace("'", "''")}', " +
                                                            $"\"Phone\" = N'{invoice.BuyerPhoneNumber}', " +
                                                            $"\"Email\" = N'{invoice.BuyerEmail}', " +
                                                            $"\"Address\" = N'{invoice.BuyerAddressLine?.Replace("'", "''")}', " +
                                                            $"\"BankAccount\" = N'{invoice.BuyerBankAccount?.Replace("'", "''")}', " +
                                                            $"\"BankName\" = N'{invoice.BuyerBankName?.Replace("'", "''")}', " +
                                                            $"\"TaxCode\" = N'{invoice.BuyerTaxCode}', " +
                                                            $"\"Fax\" = N'{invoice.BuyerFaxNumber}' " +
                                                            $"Where \"Id\" = {customer.Id} ");
                                    }

                                    raw.Append("; ");

                                    var queryResult = raw.Append($" END; ").ToString();
                                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);

                                    // tạo tài khoản khách hàng
                                    await _syncAccountBusiness.SyncAccountAsync(new Dtos.SyncAccountDto
                                    {
                                        InvoiceId = invoice.Id,
                                        TenantId = tenantId,
                                        Type = VnisType._02GTTT,
                                    });
                                }
                            }

                            syncCustomerStatus = SyncCatalogStatus.IsSyncedToCore;
                        }
                        catch (Exception ex)
                        {
                            Log.Error($@"{ex.Message}");
                            syncCustomerStatus = SyncCatalogStatus.SyncToCoreError;
                        }
                        finally
                        {
                            invoice.IsSyncedCustomerToCore = (short)syncCustomerStatus;
                        }

                        await _mongoInvoice02Repository.UpdateManySyncCustomerStatusAsync(new List<long> { invoice.Id }, invoice.IsSyncedCustomerToCore);
                        //invoiceIsSyncCustomersToCore.Add(invoice);
                    }
                }

                //if (invoiceIsSyncCustomersToCore.Any())
                //    await _mongoInvoice02Repository.UpdateManyAsync(invoiceIsSyncCustomersToCore);
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }
    }
}
