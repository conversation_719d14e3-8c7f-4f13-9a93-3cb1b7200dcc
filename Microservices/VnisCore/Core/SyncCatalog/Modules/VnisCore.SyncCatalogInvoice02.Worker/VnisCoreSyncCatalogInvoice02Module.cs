using Core;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Data;
using Core.DependencyInjection;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.EventBus.RabbitMq;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Threading;
using Core.VaultSharp;
using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Reflection;

using VnisCore.AuthDatabase.Oracle.Application.Contracts;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.SyncCatalogInvoice02.Worker.BackgroundWorkers;
using VnisCore.SyncCatalogInvoice02.Worker.Business;
using VnisCore.SyncCatalogInvoice02.Worker.Interface;
using VnisCore.SyncCatalogInvoice02.Worker.Repository;

namespace VnisCore.SyncCatalogInvoice02.Worker
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),
        typeof(VnisCoreMongoDbModule),


        typeof(AbpAutofacModule),
        typeof(AbpCachingModule),

        typeof(AbpEntityFrameworkCoreOracleModule),

        typeof(VnisCoreAuthDatabaseOracleEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleApplicationContractsModule),

        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VnisCoreOracleModuleContractsModule),

        typeof(AbpCachingStackExchangeRedisModule),

        typeof(SharedModule),
        typeof(SharedDtoModule),
        typeof(AbpEventBusRabbitMqModule)
     )]
    public class VnisCoreSyncCatalogModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCoreSyncCatalogModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreSyncCatalogAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreSyncCatalogModule).GetTypeInfo().Assembly);

            context.Services.AddScoped<ISyncAccountBusiness, SyncAccountBusiness>();
            context.Services.AddScoped<ISyncCustomerBusiness, SyncCustomerBusiness>();
            context.Services.AddScoped<ISyncUnitAndProductBusiness, SyncUnitAndProductBusiness>();

            // repository
            context.Services.AddScoped<ICustomerRepository, CustomerRepository>();
            context.Services.AddScoped<IInvoice02HeaderRepository, Invoice02HeaderRepository>();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<SyncCustomerBackgroundWorker>();
            context.AddBackgroundWorker<SyncUnitAndProductBackgroundWorker>();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();

                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreOracleDbContext>();
            });
        }
    }
}
