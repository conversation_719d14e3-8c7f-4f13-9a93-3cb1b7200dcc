using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using OfficeOpenXml;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01Excel.Application.Factories.Services;
using VnisCore.Invoice01Excel.Application.Invoice01.Dto;
using VnisCore.Invoice01Excel.Application.Invoice01.Models;

using static VnisCore.Invoice01Excel.Application.Invoice01.Dto.CreateInvoice01HeaderDto;

namespace VnisCore.Invoice01Excel.Application.Invoice01.Services
{
    public class UpdateByExcelInvoice01Service : BaseImport01Service, IImport01Service
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IInvoice01Service _invoice01Service;
        public UpdateByExcelInvoice01Service(ILogger<BaseImport01Service> logger,
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            IVnisCoreMongoInvoice01BatchIdRepository mongoInvoice01BatchIdRepository,
            IConfiguration configuration,
            IInvoice01Service invoice01Service,
            IVnisCoreMongoInvoice01ErpIdRepository mongoInvoice01ErpIdRepository)
            : base(logger, appFactory, serviceProvider, distributedEventBus, localizer, invoiceService, mongoInvoice01BatchIdRepository, configuration, invoice01Service, mongoInvoice01ErpIdRepository)
        {
            _invoiceService = invoiceService;
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _localizer = localizer;
            _invoice01Service = invoice01Service;
        }

        public override async Task<List<ImportInvoice01Model>> ReadExcelAsync(Guid tenantId, ExcelPackage package, Dictionary<string, string> parameters)
        {
            try
            {
                var worksheet = package.Workbook.Worksheets[0];
                if (worksheet?.Dimension == null || worksheet.Dimension.End.Row < 4)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Customer.Import.EmptyDataImport"]);

                var paymentMethods = new List<string>();
                // Regex Cu
                //var regexCode = new Regex("^[a-zA-Z0-9][a-zA-Z0-9-_/.@]{0,48}[a-zA-Z0-9]{1,49}$|^[a-zA-Z0-9]{1}$");
                // Regex moi
                var regexCode = new Regex("^[a-zA-Z0-9-/_][a-zA-Z0-9-_/.@]{0,48}[a-zA-Z0-9-/_]{1,49}$|^[a-zA-Z0-9-/_]{1}$");

                var repoSetting = _serviceProvider.GetService<ISettingService>();
                var setting = await repoSetting.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());
                if (setting == null || string.IsNullOrEmpty(setting.Value))
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PaymentMethodNotFound"]);

                var invoices = new List<ImportInvoice01Model>();
                paymentMethods = setting.Value.Split(";").ToList();

                for (var i = 4; i <= worksheet.Dimension.End.Row; i++)
                {
                    try
                    {
                        // header
                        var templateNo = worksheet.Cells[i, 1].GetValue<string>()?.Trim();
                        var serialNo = worksheet.Cells[i, 2].GetValue<string>()?.Trim();
                        var idErp = worksheet.Cells[i, 3].GetValue<string>()?.Trim();
                        var invoiceDate = worksheet.Cells[i, 4].GetValue<string>()?.Trim();
                        var paymentMethod = worksheet.Cells[i, 5].GetValue<string>()?.Trim();
                        var buyerId = worksheet.Cells[i, 6].GetValue<string>()?.Trim();
                        var buyerLegalName = worksheet.Cells[i, 7].GetValue<string>()?.Trim();
                        var buyerFullName = worksheet.Cells[i, 8].GetValue<string>()?.Trim();
                        var buyerTaxCode = worksheet.Cells[i, 9].GetValue<string>()?.Trim();
                        var buyerAddressLine = worksheet.Cells[i, 10].GetValue<string>()?.Trim();
                        var buyerEmail = worksheet.Cells[i, 11].GetValue<string>()?.Trim();
                        var budgetUnitCode = worksheet.Cells[i, 12].GetValue<string>()?.Trim();
                        var buyerIDNumber = worksheet.Cells[i, 13].GetValue<string>()?.Trim();
                        var buyerPassportNumber = worksheet.Cells[i, 14].GetValue<string>()?.Trim();
                        var note = worksheet.Cells[i, 15].GetValue<string>()?.Trim();
                        var tongTienThuHo = worksheet.Cells[i, 16].GetValue<string>()?.Trim(); // header Extras
                        var totalAmount = worksheet.Cells[i, 17].GetValue<string>()?.Trim(); // tổng thành tiền
                        var totalVatAmount = worksheet.Cells[i, 18].GetValue<string>()?.Trim(); // tổng tiền thuế
                        var totalPaymentAmount = worksheet.Cells[i, 19].GetValue<string>()?.Trim(); // tổng tiền thanh toán

                        //detail
                        var productCode = worksheet.Cells[i, 20].GetValue<string>()?.Trim();
                        var productName = worksheet.Cells[i, 21].GetValue<string>()?.Trim();
                        var detailNote = worksheet.Cells[i, 22].GetValue<string>()?.Trim();
                        var hanhTrinh = worksheet.Cells[i, 23].GetValue<string>()?.Trim(); // detail Extras
                        var unitName = worksheet.Cells[i, 24].GetValue<string>()?.Trim();
                        var quantity = worksheet.Cells[i, 25].GetValue<string>()?.Trim();
                        var unitPrice = worksheet.Cells[i, 26].GetValue<string>()?.Trim();
                        var amount = worksheet.Cells[i, 27].GetValue<string>()?.Trim();
                        var vatPercent = worksheet.Cells[i, 28].GetValue<string>()?.Trim();
                        var vatAmount = worksheet.Cells[i, 29].GetValue<string>()?.Trim();
                        var thuHoNhaChucTrach = worksheet.Cells[i, 30].GetValue<string>()?.Trim(); // detail Extras
                        var paymentAmount = worksheet.Cells[i, 31].GetValue<string>()?.Trim();
                        var productType = worksheet.Cells[i, 32].GetValue<string>()?.Trim();

                        #region validate headers

                        // mẫu số - not null
                        if (string.IsNullOrEmpty(templateNo))
                            //throw new UserFriendlyException($"Mẫu số tại A{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TemplateNoIsNull", $"A{i}"]);

                        if (templateNo.Length > 20)
                            //throw new UserFriendlyException($"Mẫu số tại A{i} không được dài quá 20 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TemplateNoMaxLength", $"A{i}"]);

                        // ký hiệu - not null - match regex
                        if (string.IsNullOrEmpty(serialNo) || !serialNo.IsSerialNo())
                            //throw new UserFriendlyException($"Ký hiệu tại B{i} không được để trống và phải đúng định dạng");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.SerialIsNullOrFormatIncorrect", $"B{i}"]);

                        // số chứng từ - not null
                        if (string.IsNullOrEmpty(idErp))
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ErpIdIsNull", $"C{i}"]);

                        if (idErp.Length > 200)
                            //throw new UserFriendlyException($"Số chứng từ tại C{i} không được dài quá 200 kí tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ErpIdMaxLength", $"C{i}"]);

                        idErp = $"{templateNo}-{serialNo}-{idErp}";

                        // ngày hóa đơn - not null
                        if (string.IsNullOrEmpty(invoiceDate))
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.InvoiceDateIsNull", $"D{i}"]);

                        var dateInvoice = _invoiceService.GetDateTimeExcel(invoiceDate);
                        if (!dateInvoice.HasValue)
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không đúng kiểu dữ liệu DateTime như định dạng ô D3");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.InvoiceDateFormatIncorrect", $"D{i}"]);

                        if (dateInvoice.Value.Date > DateTime.Now.Date)
                            //throw new UserFriendlyException($"Ngày hóa đơn tại D{i} không được lớn hơn ngày hiện tại");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.InvoiceDateIncorrect", $"D{i}"]);

                        //hình thức thanh toán -not null
                        if (string.IsNullOrEmpty(paymentMethod))
                            //throw new UserFriendlyException($"Hình thức thanh toán tại E{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.PaymentMethodIsNull", $"E{i}"]);

                        if (paymentMethod.Length > 50)
                            //throw new UserFriendlyException($"Hình thức thanh toán tại E{i} dài tối đa 50 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.PaymentMethodMaxLength", $"E{i}"]);

                        if (!paymentMethods.Contains(paymentMethod))
                            //throw new UserFriendlyException($"Chưa có cấu hình phương thức thanh toán tại E{i}");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.PaymentMethodNotFound", $"E{i}"]);

                        DateTime? outPaymentDate = null;
                        // mã người mua hàng - not null
                        if (string.IsNullOrEmpty(buyerId))
                            //throw new UserFriendlyException($"Mã người mua tại I{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerIdIsNull", $"F{i}"]);

                        if (buyerId.Length > 50)
                            //throw new UserFriendlyException($"Mã người mua tại I{i} dài tối đa 50 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerIdMaxLength", $"F{i}"]);

                        if (!regexCode.IsMatch(buyerId))
                            //throw new UserFriendlyException($"Mã người mua tại I{i} không được bắt đầu hoặc kết thúc bằng khoảng trắng hoặc kí tự đặc biệt và chỉ bao gồm các kí tự a-z, A-Z, 0-9, ., -, _, /, @");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerIdSymbol", $"F{i}"]);

                        // tên người mua
                        if (!string.IsNullOrEmpty(buyerLegalName))
                        {
                            if (buyerLegalName.Length > 100)
                                //throw new UserFriendlyException($"Tên người mua tại J{i} dài tối đa 100 ký tự");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerLegalNameMaxLength", $"G{i}"]);
                        }

                        // tên đơn vị mua hàng - not null
                        if (string.IsNullOrEmpty(buyerFullName))
                            //throw new UserFriendlyException($"Tên đơn vị mua hàng tại K{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerNameIsNull", $"H{i}"]);

                        if (buyerFullName.Length > 250)
                            //throw new UserFriendlyException($"Tên đơn vị mua hàng tại K{i} dài tối đa 250 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerNameMaxLength", $"H{i}"]);

                        // mã số thuế người mua
                        if (!string.IsNullOrEmpty(buyerTaxCode))
                        {
                            if (buyerTaxCode.Length > 50)
                                //throw new UserFriendlyException($"Mã số thuế người mua tại L{i} dài tối đa 50 ký tự");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerTaxCodeMaxLength", $"I{i}"]);

                            //check có phải mst không
                            if (!buyerTaxCode.IsTaxCode())
                                //throw new UserFriendlyException($"Mã số thuế người mua tại L{i} không đúng định dạng mã số thuế");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerTaxCodeFormatIncorrect", $"I{i}"]);
                        }

                        // địa chỉ người mua - not null
                        if (string.IsNullOrEmpty(buyerAddressLine))
                            //throw new UserFriendlyException($"Địa chỉ người mua tại M{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerAddressLineIsNull", $"J{i}"]);

                        if (buyerAddressLine.Length > 500)
                            //throw new UserFriendlyException($"Địa chỉ người mua tại M{i} dài tối đa 500 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerAddressLineMaxLength", $"J{i}"]);

                        // email người mua
                        if (!string.IsNullOrEmpty(buyerEmail))
                        {
                            if (buyerEmail.Length > 500)
                                //throw new UserFriendlyException($"Email người mua tại K{i} dài tối đa 500 ký tự");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.BuyerEmailMaxLength", $"K{i}"]);

                            if (!buyerEmail.IsManyEmails(';'))
                            {
                                throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice01.ImportService.BuyerEmailNotValidate", new string[] { $"K{i}" }]);
                            }
                        }

                        // mã đơn vị quan hệ ngân sách
                        if (!string.IsNullOrEmpty(budgetUnitCode))
                        {
                            if (budgetUnitCode.Length > 7)
                                throw new UserFriendlyException($"Mã đơn vị quan hệ ngân sách tại L{i} dài tối đa 7 ký tự");

                            if (!budgetUnitCode.IsBudgetUnitCode())
                                throw new UserFriendlyException($"Mã đơn vị quan hệ ngân sách tại L{i} phải có 7 chữ số và bắt đầu bằng 1,2,3,7,8,9");
                        }

                        // số định danh
                        if (!string.IsNullOrEmpty(buyerIDNumber))
                        {
                            if (buyerIDNumber.Length != 12)
                                throw new UserFriendlyException($"Số định danh tại M{i} phải dài 12 ký tự");
                            // if (!buyerIDNumber.IsIDNumber12Character())
                            //     throw new UserFriendlyException($"Số định danh tại M{i} không đúng định dạng");
                        }

                        // số hộ chiếu
                        if (!string.IsNullOrEmpty(buyerPassportNumber))
                        {
                            if (buyerPassportNumber.Length > 20)
                                throw new UserFriendlyException($"Số hộ chiếu tại N{i} dài tối đa 20 ký tự");
                        }

                        // nội dung hóa đơn
                        if (!string.IsNullOrEmpty(note))
                        {
                            if (note.Length > 500)
                                //throw new UserFriendlyException($"Nội dung hóa đơn tại O{i} dài tối đa 500 ký tự");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.OrderDescriptionMaxLength", $"O{i}"]);
                        }

                        // tổng tiền thanh toán
                        if (string.IsNullOrEmpty(totalAmount))
                            //throw new UserFriendlyException($"Tổng thành tiền tại Q{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalAmountIsNull", $"Q{i}"]);

                        if (!decimal.TryParse(totalAmount, out decimal numberTotalAmount))
                            //throw new UserFriendlyException($"Tổng thành tiền tại Q{i} không đúng định dạng số thập phân Decimal");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalAmountFormatDecimalIncorrect", $"Q{i}"] + $". Giá trị hiện tại là: {numberTotalAmount}");

                        if (!numberTotalAmount.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalAmountInvalid", $"Q{i}", $"-{StaticData.MaxTotalAmount}", StaticData.MaxTotalAmount] + $". Giá trị hiện tại là: {numberTotalAmount}");

                        // % chiết khấu toàn hóa đơn
                        double numberTotalDiscountPercent = 0;
                        // tổng tiền thuế - not null
                        decimal numberTotalVatAmount = 0;
                        if (!string.IsNullOrEmpty(totalVatAmount))
                        {
                            if (!decimal.TryParse(totalVatAmount, out numberTotalVatAmount))
                                //throw new UserFriendlyException($"Tổng tiền thuế tại R{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalVatAmountFormatDecimalIncorrect", $"R{i}"] + $". Giá trị hiện tại là: {numberTotalVatAmount}");

                            if (!numberTotalVatAmount.IsInRange(15, 6, true))
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalVatAmountInvalid", $"R{i}", $"-{StaticData.MaxTotalVatAmount}", StaticData.MaxTotalVatAmount] + $". Giá trị hiện tại là: {numberTotalVatAmount}");
                        }

                        // tổng tiền thanh toán - not null
                        if (string.IsNullOrEmpty(totalPaymentAmount))
                            //throw new UserFriendlyException($"Tổng tiền thanh toán tại S{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalPaymentAmountIsNull", $"S{i}"]);

                        if (!decimal.TryParse(totalPaymentAmount, out decimal numberTotalPaymentAmount))
                            //throw new UserFriendlyException($"Tổng tiền thanh toán tại S{i} không đúng định dạng số thập phân Decimal");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalPaymentAmountFormatDecimalIncorrect", $"S{i}"] + $". Giá trị hiện tại là: {numberTotalPaymentAmount}");

                        if (!numberTotalPaymentAmount.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.TotalPaymentAmountInvalid", $"S{i}", $"-{StaticData.MaxTotalPaymentAmount}", StaticData.MaxTotalPaymentAmount] + $". Giá trị hiện tại là: {numberTotalPaymentAmount}");
                        #endregion

                        #region validate details
                        if (string.IsNullOrEmpty(productType))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductTypeIsNull", $"AF{i}"]);

                        var productTypes = new List<short> { 1, 2, 3, 4 };
                        if (!productTypes.Contains(short.Parse(productType)))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductTypeIncorrect", $"AF{i}"]);

                        // mã hàng - not null
                        if (string.IsNullOrEmpty(productCode))
                            //throw new UserFriendlyException($"Mã hàng hóa tại T{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductCodeIsNull", $"T{i}"]);

                        if (productCode.Length > 50)
                            //throw new UserFriendlyException($"Mã hàng hóa tại T{i} dài tối đa 50 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductCodeMaxLength", $"T{i}"]);

                        // tên hàng - not null
                        if (string.IsNullOrEmpty(productName))
                            //throw new UserFriendlyException($"Tên hàng hóa tại U{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductNameIsNull", $"U{i}"]);

                        if (productName.Length > 500)
                            //throw new UserFriendlyException($"Tên hàng hóa tại U{i} dài tối đa 500 kí tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.ProductNameMaxLength", $"U{i}"]);

                        // nội dung hàng hóa
                        if (!string.IsNullOrEmpty(detailNote))
                            if (detailNote.Length > 250)
                                //throw new UserFriendlyException($"Nội dung hàng hóa tại V{i} không được dài quá 250 kí tự");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.DetailNoteMaxLength", $"V{i}"]);

                        // đơn vị tính - not null
                        //if (short.Parse(productType) != (short)ProductType.GhiChuDienGiai && string.IsNullOrEmpty(unitName))
                        //    //throw new UserFriendlyException($"Đơn vị tính tại X{i} không được để trống");
                        //    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.UnitNameIsNull", $"X{i}"]);

                        if (!string.IsNullOrEmpty(unitName) && unitName.Length > 50)
                            //throw new UserFriendlyException($"Đơn vị tính tại X{i} không được dài quá 50 kí tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.UnitNameMaxLength", $"X{i}"]);

                        // số lượng
                        decimal quantityOut = 0;
                        if (!string.IsNullOrEmpty(quantity))
                        {
                            if (!decimal.TryParse(quantity, out quantityOut))
                                //throw new UserFriendlyException($"Số lượng tại Y{i} không đúng định dạng số");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.QuantityNumberFormatIncorrect", $"Y{i}"] + $". Giá trị hiện tại là: {quantityOut}");

                            if (!quantityOut.IsInRange(15, 6, true))
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.QuantityInvalid", $"V{i}", $"-{StaticData.MaxQuantity}", StaticData.MaxQuantity] + $". Giá trị hiện tại là: {quantityOut}");
                        }

                        // đơn giá
                        decimal unitPriceOut = 0;
                        if (!string.IsNullOrEmpty(unitPrice))
                        {
                            if (!decimal.TryParse(unitPrice, out unitPriceOut))
                                //throw new UserFriendlyException($"Đơn giá tại AE{i} không đúng định dạng số");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.UnitPriceNumberFormatIncorrect", $"W{i}"] + $". Giá trị hiện tại là: {unitPriceOut}");

                            if (!unitPriceOut.IsInRange(15, 6, true))
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.UnitPriceInvalid", $"W{i}", $"-{StaticData.MaxUnitPrice}", StaticData.MaxUnitPrice] + $". Giá trị hiện tại là: {unitPriceOut}");
                        }

                        // thành tiền trước thuế - not null
                        if (string.IsNullOrEmpty(amount))
                            //throw new UserFriendlyException($"Thành tiền trước thuế tại AH{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.AmountBeforeTaxIsNull", $"X{i}"]);

                        if (!decimal.TryParse(amount, out decimal amountOut))
                            //throw new UserFriendlyException($"Thành tiền trước thuế tại AH{i} không đúng định dạng số");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.AmountBeforeTaxNumberFormatIncorrect", $"X{i}"] + $". Giá trị hiện tại là: {amountOut}");

                        if (!amountOut.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.AmountInvalid", $"X{i}", $"-{StaticData.MaxAmount}", StaticData.MaxAmount] + $". Giá trị hiện tại là: {amountOut}");

                        //% thuế - not null
                        if (string.IsNullOrEmpty(vatPercent))
                            //throw new UserFriendlyException($"Thuế suất tại AI{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatPercentIsNull", $"Y{i}"]);

                        if (!decimal.TryParse(vatPercent, out decimal numberVatPercent))
                            //throw new UserFriendlyException($"Thuế suất tại AI{i} không đúng định dạng số nguyên Int");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatPercentFormatIntIncorrect", $"Y{i}"] + $". Giá trị hiện tại là: {numberVatPercent}");

                        if (!numberVatPercent.IsInRange(2, 2, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatPercentInvalid", $"Y{i}", $"{StaticData.MaxVatPercent}", StaticData.MaxVatPercent] + $". Giá trị hiện tại là: {numberVatPercent}");

                        //tiền thuế từng mặt hàng - not null
                        if (string.IsNullOrEmpty(vatAmount))
                            //throw new UserFriendlyException($"Tiền thuế tại AJ{i} không được để trống");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatAmountIsNull", $"Z{i}"]);

                        if (!decimal.TryParse(vatAmount, out decimal numberVatAmount))
                            //throw new UserFriendlyException($"Tiền thuế tại AJ{i} không đúng định dạng số thập phân Decimal");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatAmountFormatDecimalIncorrect", $"Z{i}"] + $". Giá trị hiện tại là: {numberVatAmount}");

                        if (!numberVatAmount.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.VatAmountInvalid", $"Z{i}", $"-{StaticData.MaxVatAmount}", StaticData.MaxVatAmount] + $". Giá trị hiện tại là: {numberVatAmount}");

                        //thành tiền từng mặt hàng 
                        decimal numberPaymentAmount = 0;
                        if (!string.IsNullOrEmpty(paymentAmount))
                        {
                            if (!decimal.TryParse(paymentAmount, out numberPaymentAmount))
                                //throw new UserFriendlyException($"Thành tiền sau thuế tại AK{i} không đúng định dạng số thập phân Decimal");
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.PaymentAmountFormatDecimalIncorrect", $"AB{i}"] + $". Giá trị hiện tại là: {numberPaymentAmount}");

                            if (!numberPaymentAmount.IsInRange(15, 6, true))
                                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportService.PaymentAmountInvalid", $"AB{i}", $"-{StaticData.MaxPaymentAmount}", StaticData.MaxPaymentAmount] + $". Giá trị hiện tại là: {numberPaymentAmount}");
                        }

                        #endregion

                        var metadataDetailExtra = new Dictionary<string, string>();
                        if (!string.IsNullOrEmpty(hanhTrinh))
                            metadataDetailExtra.Add("HanhTrinh", hanhTrinh);

                        if (!string.IsNullOrEmpty(thuHoNhaChucTrach))
                            metadataDetailExtra.Add("ThuHo", thuHoNhaChucTrach);

                        //add vào excel model
                        var invoice = new ImportInvoice01Model
                        {
                            TemplateNo = short.Parse(templateNo),
                            SerialNo = serialNo,
                            ErpId = idErp,
                            InvoiceDate = dateInvoice.Value,
                            PaymentMethod = paymentMethod,
                            PaymentDate = outPaymentDate ?? dateInvoice.Value,
                            Currency = "VND",
                            ExchangeRate = 1,
                            BuyerCode = buyerId,
                            BuyerLegalName = buyerLegalName,
                            BuyerFullName = buyerFullName,
                            BuyerTaxCode = buyerTaxCode,
                            BuyerAddressLine = buyerAddressLine,
                            BuyerEmail = buyerEmail,
                            BudgetUnitCode = budgetUnitCode,
                            BuyerIDNumber = buyerIDNumber,
                            BuyerPassportNumber = buyerPassportNumber,
                            Note = note,
                            TotalAmount = numberTotalAmount,
                            TotalDiscountPercent = numberTotalDiscountPercent,
                            TotalVatAmount = numberTotalVatAmount,
                            TotalPaymentAmount = numberTotalPaymentAmount,
                            ProductType = short.Parse(productType),
                            MetadataHeaderExtra = string.IsNullOrEmpty(tongTienThuHo) ? null : new Dictionary<string, string>
                                            {
                                                { "tongtienthuho", tongTienThuHo }
                                            },
                            ProductCode = productCode,
                            ProductName = productName,
                            DetailNote = detailNote,
                            UnitName = unitName,
                            Quantity = quantityOut,
                            UnitPrice = unitPriceOut,
                            Amount = amountOut,
                            VatPercent = numberVatPercent,
                            VatAmount = numberVatAmount,
                            PaymentAmount = numberPaymentAmount,
                            MetadataDetailExtra = metadataDetailExtra
                        };

                        invoices.Add(invoice);
                    }
                    catch (Exception ex)
                    {

                        Log.Error(ex, ex.Message);
                        if (ex.Message == ExceptionMessageConst.ObjectMustImplementIConvertible)
                        {
                            throw new UserFriendlyException($@"Định dạng dữ liệu dòng thứ {i} trong file Excel không đúng. Vui lòng kiểm tra lại.");
                        }
                        else
                        {
                            throw new UserFriendlyException(ex.Message);
                        }
                    }
                }

                return invoices;
            }
            catch (Exception ex)
            {

                throw new UserFriendlyException(ex.Message);
            }
        }

        public override async Task<List<CreateInvoice01HeaderDto>> ToEntitiesAsync(Guid tenantId, Guid createBy, string userFullName, string userName, string cashierCode, List<ImportInvoice01Model> excelModels)
        {
            //thuế
            var taxService = _serviceProvider.GetService<ITaxService>();
            var existedTaxes = await taxService.GetTaxesAsync(tenantId);
            var taxes = excelModels.GroupBy(x => x.VatPercent).Select(x => x.FirstOrDefault());
            foreach (var item in taxes)
            {
                if (!existedTaxes.ContainsKey(item.VatPercent))
                    throw new UserFriendlyException($"Không tồn tại thuế suất {item.VatPercent}");
            }

            //tim cac mau/loai hoa don trong csdl mà được tạo
            var commonInvoice01Service = _serviceProvider.GetService<ICommonInvoice01Service>();
            var invoiceTemplates = (await commonInvoice01Service.GetRegisterAvailabilities(tenantId, createBy, 1))
                                    .ToDictionary(x => $"{x.TemplateNo}|{x.SerialNo}", x => x);


            var invoiceHeaders = new List<CreateInvoice01HeaderDto>();
            var tempInvoiceHeaders = excelModels.GroupBy(x => x.ErpId);

            foreach (var item in tempInvoiceHeaders)
            {
                //loc theo tung mau hoa don
                var invoiceHeader = new CreateInvoice01HeaderDto();
                var groupInvoice = item.ToList();
                var firstKey = groupInvoice.FirstOrDefault();

                if (firstKey == null)
                    continue;

                if (!invoiceTemplates.ContainsKey($"{firstKey.TemplateNo}|{firstKey.SerialNo}"))
                    throw new UserFriendlyException("Không tìm thấy mẫu hóa đơn để TẠO. Vui lòng kiểm tra thông tin mẫu hóa đơn hoặc phân quyền tạo hóa đơn");


                var invoiceTemplate = invoiceTemplates[$"{firstKey.TemplateNo}|{firstKey.SerialNo}"];

                AddInvoiceHeader(tenantId, createBy, userFullName, userName, cashierCode, firstKey, invoiceHeader);

                //add header extra
                var tenantInfo = _appFactory.CurrentTenant;
                var invoiceHeaderExtras = new List<CreateInvoice01HeaderExtraModel>();
                if (firstKey.MetadataHeaderExtra != null)
                {
                    foreach (var field in firstKey.MetadataHeaderExtra)
                    {
                        invoiceHeaderExtras.Add(new CreateInvoice01HeaderExtraModel
                        {
                            FieldValue = field.Value,
                            FieldName = field.Key,
                        });
                    }
                }

                if (invoiceHeaderExtras.Any())
                    invoiceHeader.InvoiceHeaderExtras = invoiceHeaderExtras;

                var invoiceDetails = new List<CreateInvoice01DetailModel>();
                var invoiceTaxBreakDown = new List<CreateInvoice01TaxBreakdownModel>();

                //decimal totalAmount = 0;
                //decimal totalPaymentAmount = 0;
                //decimal totalVatAmount = 0;

                var index = 1;
                foreach (var detailExcel in groupInvoice)
                {
                    var createInvoice01DetailExtraModel = new List<CreateInvoice01DetailExtraModel>();
                    if (detailExcel.MetadataDetailExtra != null)
                    {
                        foreach (var field in detailExcel.MetadataDetailExtra)
                        {
                            createInvoice01DetailExtraModel.Add(new CreateInvoice01DetailExtraModel
                            {
                                FieldValue = field.Value,
                                FieldName = field.Key,
                            });
                        }
                    }   
                    
                    var detail = ConvertToInvoiceDetail(detailExcel);
                    detail.Index = index;

                    //tự tính tiền thuế, paymentAmount
                    if (detail.VatPercent > 0)
                        detail.VatAmount = detail.Amount * detail.VatPercent / 100;
                    detail.PaymentAmount = detail.Amount + detail.VatAmount;

                    if (createInvoice01DetailExtraModel.Any())
                        detail.InvoiceDetailExtras = createInvoice01DetailExtraModel;

                    invoiceDetails.Add(detail);
                    index++;
                    if (!invoiceTaxBreakDown.Any(x => x.VatPercent == detail.VatPercent))
                    {
                        invoiceTaxBreakDown.Add(new CreateInvoice01TaxBreakdownModel
                        {
                            VatAmount = detail.VatAmount,
                            VatPercent = detail.VatPercent,
                            VatAmountBackUp = 0,
                        });
                    }
                    else
                    {
                        var taxBreakDown = invoiceTaxBreakDown.First(x => x.VatPercent == detail.VatPercent);
                        taxBreakDown.VatAmount += detail.VatAmount;
                    }
                }

                invoiceHeader.InvoiceDetails = invoiceDetails;

                invoiceHeader.InvoiceTaxBreakdowns = GetTaxBreakdowns(invoiceHeader, groupInvoice);
                invoiceHeader.DiscountType = (short)GetAutoDiscountType(invoiceHeader).GetHashCode();

                invoiceHeaders.Add(invoiceHeader);
            }

            return invoiceHeaders;
        }
    }
}
