using Core.Shared.Attributes;
using Core.Shared.Constants;
using MediatR;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands
{
    public class UpdateReplaceInvoice02ApiRequestModel : IRequest<UpdateReplaceInvoice02ApiResponseModel>, IInvoiceHeaderDecreeNo70
    {
        #region ND70

        #region Vùng thông tin hóa đơn
        /// <summary>
        /// Mã cửa hàng
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã cửa hàng dài tối đa 50 ký tự")]
        public string StoreCode { get; set; }

        /// <summary>
        /// Tên cửa hàng
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên cửa hàng dài tối đa 400 ký tự")]
        [FieldDependencyReference("StoreCode", ErrorMessage = "Tên cửa hàng (StoreName) không được để trống")]
        public string StoreName { get; set; }
        #endregion

        #region Vùng thông tin người mua
        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã số đơn vị có quan hệ với ngân sách dài tối đa 7 ký tự")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        [MaxLength(12, ErrorMessage = "MSố CC/CCCD/Định danh dài tối đa 12 ký tự")]
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số hộ chiếu/giấy tờ xuất/nhập cảnh dài tối đa 20 ký tự")]
        public string BuyerPassportNumber { get; set; }
        #endregion

        #endregion

        [JsonIgnore]
        public long Id { get; set; }

        [JsonIgnore]
        public string ErpId { get; set; }

        /// <summary>
        /// mẫu số hóa đơn
        /// </summary>
        [JsonIgnore]
        public short TemplateNo { get; set; }

        /// <summary>
        /// ký hiệu hóa đơn
        /// </summary>
        [JsonIgnore]
        [SerialNo(ErrorMessage = "Ký hiệu hóa đơn không đúng định dạng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn
        /// </summary>
        [JsonIgnore]
        public string InvoiceNo { get; set; }

        #region Thông tin chung
        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Ngày hóa đơn phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        [MaxLength(500, ErrorMessage = "Ghi chú dài tối đa 500 ký tự")]
        public string Note { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Loại chiết khấu
        /// 0 : Không chiết khấu
        /// 1 : Chiết khấu hàng hóa
        /// 2 : Chiết khấu tổng
        /// </summary>
        public short DiscountType { get; set; }

        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        [Required(ErrorMessage = "Phương thức thanh toán không được để trống")]
        [StringLength(50, ErrorMessage = "Phương thức thanh toán chỉ được nhập tối đa 50 ký tự")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Chuyến đến tiền tệ
        /// </summary>
        [Required(ErrorMessage = "Loại tiền tệ không được để trống")]
        [MaxLength(3, ErrorMessage = "Loại tiền tệ chỉ được nhập tối đa 3 ký tự")]
        public string Currency { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        [Required(ErrorMessage = "Tỷ giá chuyển đổi không được để trống")]
        [MoreThanValueAttribute("0", typeof(decimal), ErrorMessage = "Tỷ giá chuyển đổi phải lớn hơn 0")]
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tổng tiền trước thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tiền thanh toán phải lớn hơn hoặc bằng 0")]
        public decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng tiền chiết khấu trước thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalDiscountAmount { get; set; }
        #endregion

        #region Thông tin người mua
        /// <summary>
        /// Mã người mua
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã khách hàng tối đa 50 ký tự")]
        [CustomerId(ErrorMessage = "Mã khách hàng viết không dấu, không chứa các ký tự đặc biệt ngoại trừ các ký tự .-_/@")]
        public string BuyerCode { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        [StringLength(500, ErrorMessage = "Email người mua chỉ được nhập tối đa 500 ký tự")]
        [Email(ErrorMessage = "Email người mua không đúng định dạng email")]
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Họ tên người mua
        /// </summary>
        [StringLength(400, ErrorMessage = "Tên đơn vị chỉ được nhập tối đa 400 ký tự")]
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        [StringLength(100, ErrorMessage = "Người mua hàng chỉ được nhập tối đa 100 ký tự")]
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        [StringLength(14, ErrorMessage = "Mã số thuế người mua chỉ được nhập tối đa 14 ký tự")]
        [TaxCode(ErrorMessage = "Mã số thuế người mua không đúng định dạng")]
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        [StringLength(400, ErrorMessage = "Địa chỉ người mua chỉ được nhập tối đa 400 ký tự")]
        public string BuyerAddressLine { get; set; }

        [StringLength(500, ErrorMessage = "Tên quận/huyện người mua chỉ được nhập tối đa 500 ký tự")]
        public string BuyerDistrictName { get; set; }

        [StringLength(500, ErrorMessage = "Tên tỉnh/thành phố người mua chỉ được nhập tối đa 500 ký tự")]
        public string BuyerCityName { get; set; }

        [StringLength(5, ErrorMessage = "Mã quốc gia người mua chỉ được nhập tối đa 5 ký tự")]
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        [StringLength(20, ErrorMessage = "Số điện thoại người mua chỉ được nhập tối đa 20 ký tự")]
        public string BuyerPhoneNumber { get; set; }

        [StringLength(20, ErrorMessage = "Số fax người mua chỉ được nhập tối đa 20 ký tự")]
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Số tài khoản người mua
        /// </summary>
        [StringLength(30, ErrorMessage = "Số tài khoản người mua chỉ được nhập tối đa 30 ký tự")]
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        [StringLength(400, ErrorMessage = "Tên ngân hàng người mua chỉ được nhập tối đa 400 ký tự")]
        public string BuyerBankName { get; set; }
        #endregion

        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<UpdateReplaceInvoice02DetailRequestModel> InvoiceDetails { get; set; }

        public List<UpdateReplaceInvoice02HeaderExtraRequestModel> InvoiceHeaderExtras { get; set; }


        public class UpdateReplaceInvoice02HeaderExtraRequestModel
        {
            /// <summary>
            /// Tên bản ghi field mở rộng của Header
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Header không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên trường mở rộng của Header dài tối đa 500 ký tự")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng của Header dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateReplaceInvoice02DetailExtraRequestModel
        {
            /// <summary>
            /// Tên bản ghi field mở rộng của Detail
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên trường mở rộng của Detail dài tối đa 500 ký tự")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng của Detail dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class UpdateReplaceInvoice02DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Chiết khấu
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "Chiết khấu phải lớn hơn hoặc bằng 0")]
            public decimal DiscountAmount { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "% Chiết khấu phải lớn hơn hoặc bằng 0")]
            [RangeNumberAttribute(2, 4, typeof(decimal), ErrorMessage = " % Chiết khấu phải lớn hơn hoặc bằng 0 và nhỏ hơn hoặc bằng 99.9999")]
            public decimal DiscountPercent { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền sau thuế phải lớn hơn hoặc bằng 0")]
            public decimal PaymentAmount { get; set; }

            /// <summary>
            /// Mã sản phẩm
            /// </summary>
            [Required(ErrorMessage = "Mã vật tư/hàng hóa không được để trống")]
            [MaxLength(50, ErrorMessage = "Mã vật tư/hàng hóa chỉ được nhập tối đa 50 ký tự")]
            public string ProductCode { get; set; }

            /// <summary>
            /// Tính chất hàng hoá
            /// </summary>
            [ProductType(ErrorMessage = "Tính chất hàng hóa không được để trống")]
            public short ProductType { get; set; }

            /// <summary>
            /// Tên sản phẩm
            /// </summary>
            [Required(ErrorMessage = "Tên vật tư/hàng hóa không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên vật tư/hàng hóa chỉ được nhập tối đa 500 ký tự")]
            public string ProductName { get; set; }

            /// <summary>
            /// Tên đơn vị tính
            /// </summary>
            //[Required(ErrorMessage = "Tên đơn vị tính không được để trống")]
            [MaxLength(50, ErrorMessage = "Tên đơn vị tính chỉ được nhập tối đa 50 ký tự")]
            public string UnitName { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "Đơn giá phải lớn hơn hoặc bằng 0")]
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0")]
            public decimal Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0")]
            public decimal Amount { get; set; }

            /// <summary>
            /// Ghi chú
            /// </summary>
            [MaxLength(2000, ErrorMessage = "Nội dung chi tiết hóa đơn chỉ được nhập tối đa 2000 ký tự")]
            public string Note { get; set; }

            public List<UpdateReplaceInvoice02DetailExtraRequestModel> InvoiceDetailExtras { get; set; }
        }
    }
}
