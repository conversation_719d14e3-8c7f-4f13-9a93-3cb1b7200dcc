using Core.Shared.Attributes;
using MediatR;
using Microsoft.AspNetCore.Http;
using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands
{
    public class UpdateDocumentInfoInvoice02ApiRequestModel : IRequest<UpdateDocumentInfoInvoice02ApiResponseModel>
    {
        [JsonIgnore]
        public string ErpId { get; set; }

        /// <summary>
        /// Số biên bản
        /// </summary>
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        [LessThanCurrentDate(ErrorMessage = "Ngày biên bản phải nhỏ hơn hoặc bằng ngày hiện tại")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Lý do biên bản
        /// </summary>
        [StringLength(500, ErrorMessage = "Lý do lập biên bản chỉ được nhập tối đa 500 ký tự")]
        public string DocumentReason { get; set; }

        /// <summary>
        /// File đính kèm của biên bản - id bảng media
        /// </summary>
        //public long? FileDocumentId { get; set; }

        public IFormFile DocumentFile { get; set; }

        /// <summary>
        /// có phải file upload không
        /// true: file upload
        /// false: file thiết kế
        /// </summary>
        public bool IsUploadFile { get; set; }
    }
}
