using Core.Shared.Attributes;
using Core.Tvan.Constants;

using MediatR;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands
{
    public class CreateInvoice02TvanErrorRequestModel : IRequest<CreateInvoice02TvanErrorResponseModel>
    {
        /// <summary>
        /// Mã cơ quan thuế quản lý
        /// </summary>
        [Required(ErrorMessage = "Mã cơ quan thuế quản lý không được để trống")]
        [MaxLength(5, ErrorMessage = "Mã cơ quan thuế quản lý dài tối đa 5 ký tự")]
        public string CodeTaxDepartment { get; set; }

        /// <summary>
        /// Tên cơ quan thuế quản lý
        /// </summary>
        [Required(ErrorMessage = "Tên cơ quan thuế quản lý không được để trống")]
        [MaxLength(400, ErrorMessage = "Tên cơ quan thuế quản lý dài tối đa 400 ký tự")]
        public string TaxDepartment { get; set; }

        /// <summary>
        /// Mã đơn vị quan hệ ngân sách(Mã số đơn vị có quan hệ với ngân sách của đơn vị bán tài sản công)
        /// chưa biết đặt tên ra sao cho hợp lý
        /// Bắt buộc (Đối với đơn vị bán tài sản công không có Mã số thuế ) 
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã đơn vị quan hệ ngân sách dài tối đa 7 ký tự")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// địa danh
        /// </summary>
        [Required(ErrorMessage = "Địa danh không được để trống")]
        [MaxLength(50, ErrorMessage = "Địa danh dài tối đa 50 ký tự")]
        public string Place { get; set; }

        /// <summary>
        /// thông tin các hóa đơn sai sót
        /// </summary>
        [Required(ErrorMessage = "Thông tin các hóa đơn sai sót không được để trống")]
        [MinLength(1, ErrorMessage = "Thông tin các hóa đơn sai sót phải có ít nhất 1 hóa đơn")]
        public List<Invoice02TvanErrorRequestDataModel> InvoiceErrors { get; set; }
    }

    public class Invoice02TvanErrorRequestDataModel
    {
        /// <summary>
        /// Id hóa đơn
        /// </summary>
        //public long Id { get; set; }

        /// <summary>
        /// Mẫu số
        /// </summary>
        [Required(ErrorMessage = "Mẫu số không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng mẫu số không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu không được để trống")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Số hóa đơn không được để trống")]
        [InvoiceNo(ErrorMessage = "Số hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// thứ tự
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// Lý do
        /// </summary>
        [MaxLength(255, ErrorMessage = "Lý do dài tối đa 255 ký tự")]
        public string Reason { get; set; }

        ///// <summary>
        ///// Tính chất thông báo
        ///// Hủy/thay thế/điều chỉnh/giải trình
        ///// </summary>
        ////[DataType(typeof(TThaiTBao))]
        //[Required(ErrorMessage = "Tính chất thông báo không được để trống")]
        //[Range(1, 4, ErrorMessage = "Tính chất thông báo phải có giá trị từ 1 tới 4")]
        //public TThaiTBao Action { get; set; }
    }
}
