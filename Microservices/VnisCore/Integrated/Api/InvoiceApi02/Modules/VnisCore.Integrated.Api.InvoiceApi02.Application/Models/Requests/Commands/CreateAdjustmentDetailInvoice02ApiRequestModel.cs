using Core.Shared.Attributes;
using MediatR;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands
{
    public class CreateAdjustmentDetailInvoice02ApiRequestModel : IRequest<CreateAdjustmentDetailInvoice02ApiResponseModel>, IInvoiceHeaderDecreeNo70
    {
        #region ND70

        #region Vùng thông tin hóa đơn
        /// <summary>
        /// Mã cửa hàng
        /// </summary>
        [MaxLength(50, ErrorMessage = "Mã cửa hàng dài tối đa 50 ký tự")]
        public string StoreCode { get; set; }

        /// <summary>
        /// Tên cửa hàng
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên cửa hàng dài tối đa 400 ký tự")]
        [FieldDependencyReference("StoreCode", ErrorMessage = "Tên cửa hàng (StoreName) không được để trống")]
        public string StoreName { get; set; }
        #endregion

        #region Vùng thông tin người mua
        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã số đơn vị có quan hệ với ngân sách dài tối đa 7 ký tự")]
        [BudgetUnitCode(ErrorMessage = "Mã quan hệ ngân sách phải có 7 chữ số và bắt đầu bằng một trong các số: 1,2,3,7,8,9")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        [MaxLength(12, ErrorMessage = "MSố CC/CCCD/Định danh dài tối đa 12 ký tự")]
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số hộ chiếu/giấy tờ xuất/nhập cảnh dài tối đa 20 ký tự")]
        public string BuyerPassportNumber { get; set; }
        #endregion

        #endregion

        public string ErpIdReference { get; set; }

        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Mẫu số hóa đơn không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [SerialNo(ErrorMessage = "Ký hiệu không đúng định dạng")]
        public string SerialNo { get; set; }


        /// <summary>
        /// Mẫu số của hóa đơn gốc
        /// </summary>
        //[Required(ErrorMessage = "Mẫu số hóa đơn gốc không được để trống")]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn gốc không đúng")]
        [JsonIgnore]
        public short TemplateNoReference { get; set; }

        ///// <summary>
        ///// Ký hiệu hóa đơn gốc
        ///// </summary>
        //[Required(ErrorMessage = "Ký hiệu hóa đơn gốc không được để trống")]
        //[SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn gốc không đúng")]
        [JsonIgnore]
        public string SerialNoReference { get; set; }

        ///// <summary>
        ///// Số hóa đơn gốc
        ///// </summary>
        //[Required(ErrorMessage = "Số hóa đơn của hóa đơn gốc không được để trống")]
        //[InvoiceNo(ErrorMessage = "Số hóa đơn của hóa đơn gốc không hợp lệ")]
        [JsonIgnore]
        public string InvoiceNoReference { get; set; }

        #region Thông tin chung
        [Required(ErrorMessage = "Id bản ghi hóa đơn không được để trống")]
        [StringLength(50, ErrorMessage = "Id bản ghi hóa đơn chỉ được nhập tối đa 50 ký tự")]
        public string ErpId { get; set; }

        [Required(ErrorMessage = "Tài khoản người tạo hóa đơn không được để trống")]
        [StringLength(250, ErrorMessage = "Tài khoản người tạo hóa đơn chỉ được nhập tối đa 250 ký tự")]
        public string CreatorErp { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Ngày hóa đơn phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Tổng tiền chưa thuế điều chỉnh
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu điều chỉnh
        /// </summary>
        public decimal TotalDiscountAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        #endregion

        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<CreateAdjustmentDetailInvoice02DetailRequestModel> InvoiceDetails { get; set; }

        public class CreateAdjustmentDetailInvoice02DetailExtraRequestModel
        {
            /// <summary>
            /// tên bản ghi field mở rộng của Header
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên trường mở rộng của Detail dài tối đa 500 ký tự")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(500, ErrorMessage = "Giá trị trường mở rộng của Detail dài tối đa 500 ký tự")]
            public string FieldValue { get; set; }
        }

        public class CreateAdjustmentDetailInvoice02DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Chiết khấu
            /// </summary>
            public decimal DiscountAmount { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            [RangeNumberAttribute(2, 4, typeof(decimal), ErrorMessage = " % Chiết khấu phải lớn hơn hoặc bằng 0 và nhỏ hơn hoặc bằng 99.9999")]
            public decimal DiscountPercent { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            public decimal PaymentAmount { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            public double Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0")]
            public decimal Amount { get; set; }

            public string Note { get; set; }

            public List<CreateAdjustmentDetailInvoice02DetailExtraRequestModel> InvoiceDetailExtras { get; set; }
        }
    }
}
