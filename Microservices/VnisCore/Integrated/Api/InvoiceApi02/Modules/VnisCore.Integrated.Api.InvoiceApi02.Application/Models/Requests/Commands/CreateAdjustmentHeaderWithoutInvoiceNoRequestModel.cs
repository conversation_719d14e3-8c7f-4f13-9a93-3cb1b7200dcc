using MediatR;
using System.Text.Json.Serialization;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands
{
    public class CreateAdjustmentHeaderWithoutInvoiceNoRequestModel : IRequest<CreateAdjustmentHeaderWithoutInvoiceNoResponseModel>
    {
        public string ErpId { get; set; }

        /// <summary>
        /// Mẫu số
        ///// </summary>
        [JsonIgnore]
        public short TemplateNo { get; set; }

        ///// <summary>
        ///// Ký hiệu
        ///// </summary>
        [JsonIgnore]
        public string SerialNo { get; set; }

        ///// <summary>
        ///// Số hóa đơn
        /// </summary>
        [JsonIgnore]
        public string InvoiceNo { get; set; }
    }
}
