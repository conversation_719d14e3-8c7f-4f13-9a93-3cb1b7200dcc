using Core;
using Core.Application.Dtos;
using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls.TCTResponse;

using Dapper;

using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Dto;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Queries;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;

using static Core.Tvan.Models.Xmls.TCTResponse.DLieuInvoiceErrorResponseModel;
using static VnisCore.Integrated.Api.InvoiceApi02.Application.Constants.StaticData;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Services
{
    public class Invoice02Service : IInvoice02Service
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public Invoice02Service(IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService)
        {
            _localizier = localizier;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _serviceProvider = serviceProvider;
        }

        public async Task<PagedResultDto<PagingInvoice02ResponseModel>> GetListAsync(Guid tenantId, Guid userId, PagingInvoice02RequestModel input)
        {
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (""UserNameCreator"" LIKE N'%{input.Keyword}%' 
                                    OR LOWER(""BuyerEmail"") LIKE LOWER('%{input.Keyword}%') 
                                    OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{input.Keyword}%') 
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR ""PaymentMethod"" LIKE N'%{input.Keyword}%' 
                                    OR ""BuyerPhoneNumber"" LIKE '%{input.Keyword}%' 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{input.Keyword}%')
                                    OR ""ErpId"" LIKE '%{input.Keyword}%' 
                                    OR ""TransactionId"" LIKE '%{input.Keyword}%' 
                                    OR ""CreatorErp"" LIKE N'%{input.Keyword}%' 
                                    OR ""BuyerCode"" LIKE N'%{input.Keyword}%' 
                                    ) ");

            }

            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CancelFromDate.HasValue)
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");

            if (input.CancelToDate.HasValue)
            {
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");
            }

            if (!string.IsNullOrEmpty(input.Customers))
            {
                condition.Append($@"AND (""BuyerCode"" LIKE '%{input.Customers}%' 
                                        OR LOWER(""BuyerLegalName"") LIKE LOWER(N'%{input.Customers}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{input.Customers}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{input.Customers}%')
                                    ) ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            else
            {
                //lấy danh sách mẫu dc xem
                condition.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {2} 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (input.TotalPaymentAmount.HasValue)
            {
                condition.Append($@"AND ""TotalPaymentAmount"" = {input.TotalPaymentAmount.Value} ");
            }

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (!string.IsNullOrEmpty(input.TransactionId))
                condition.Append($@"AND ""TransactionId"" = '{input.TransactionId}' ");

            if (!string.IsNullOrEmpty(input.ErpId))
                condition.Append($@"AND ""ErpId"" = '{input.ErpId}' ");

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            if (!string.IsNullOrEmpty(input.BuyerCode))
                condition.Append($@"AND ""BuyerCode"" LIKE '%{input.BuyerCode}%' ");

            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any())
            {
                condition.Append("AND (");

                int index = 0;
                int numberOfHeaderExtra = input.InvoiceHeaderExtras.Count;

                foreach (var item in input.InvoiceHeaderExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfHeaderExtra - index) == 1)
                        {
                            condition.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
                        }
                        else
                        {
                            condition.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                condition.Append(")");
            }

            // tìm kiếm theo detailExtra
            var joinDetailExtra = new StringBuilder();
            var conditionDetailExtra = new StringBuilder();
            if (input.InvoiceDetailExtras != null && input.InvoiceDetailExtras.Any())
            {
                conditionDetailExtra.Append("AND (");

                int index = 0;
                int numberOfDetailExtra = input.InvoiceDetailExtras.Count;

                foreach (var item in input.InvoiceDetailExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfDetailExtra - index) == 1)
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
                        }
                        else
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                conditionDetailExtra.Append(")");

                joinDetailExtra.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""Invoice02Detail"" WHERE ""TenantId"" = '{rawTenantId}' {conditionDetailExtra}) B ON B.""InvoiceHeaderId"" = A.""Id""");
            }


            var sql = new StringBuilder();

            sql.Append($@"SELECT *  FROM
                            (SELECT * FROM (
                                SELECT A.*, rownum rn                                                          
                                FROM                                                                           
                                    (                                                                          
                                        SELECT  ""Id"",                                                         
                                                ""ErpId"",                                                       
                                                ""TransactionId"",                                             
                                                ""TemplateNo"",                                                
                                                ""SerialNo"",                                                  
                                                ""InvoiceNo"",                                                 
                                                ""Number"",                                                    
                                                ""InvoiceStatus"",                                             
                                                ""SignStatus"",                                                
                                                ""ApproveStatus"",                                             
                                                ""ApproveCancelStatus"",                                             
                                                ""ApproveDeleteStatus"",                                             
                                                ""InvoiceDate"",                                               
                                                ""TotalAmount"",                                               
                                                ""TotalPaymentAmount"",                                        
                                                ""BuyerFullName"",                                             
                                                ""BuyerEmail"",                                                
                                                ""BuyerCode"",                                                 
                                                ""PrintedTime"",                                               
                                                ""FullNameCreator"",                                           
                                                ""UserNameCreator"",                                           
                                                ""CreatorErp"",                                                
                                                ""Note"",                                                      
                                                ""Source"",                                                    
                                                ""IsViewed"",                                                  
                                                ""IsOpened"",                                                  
                                                ""IsDeclared"",                                                  
                                                ""VerificationCode"",                                                  
                                                ""ExtraProperties"",                                                  
                                                COUNT(*) OVER () TotalItems                                   
                                        FROM ""Invoice02Header""                                               
                                        WHERE ""TenantId"" = '{rawTenantId}' {condition}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A                                                                        
                                WHERE rownum <= {input.SkipCount + input.MaxResultCount} 
                                )
                            WHERE rn > {input.SkipCount}
                        ) 
                        InvoiceHeader   
                        -- sai sót
                        LEFT JOIN 
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceErrorReason, ""InvoiceHeaderId"", ""Status"" as TvanInfoInvoiceErrorStatus, ""Action"" as InvoiceErrorType
                            FROM ""TvanInfoInvoice02Error""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._302.GetHashCode()}
                        ) TvanInfoInvoiceError
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceError.""InvoiceHeaderId""
                        --có mã(lỗi 02 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceHasCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoInvoice02HasCode""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoInvoiceHasCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceHasCode.""InvoiceHeaderId""
                        -- Rà soát
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoCheckInvoiceReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoCheckInvoice02""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._302.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoCheckInvoice
                        ON InvoiceHeader.""Id"" = TvanInfoCheckInvoice.""InvoiceHeaderId""
                        --Không mã(lỗi 02 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceWithoutCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoInvoice02WithoutCode""
                            WHERE ""TenantId"" = '{rawTenantId}'  AND ""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoInvoiceWithoutCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceWithoutCode.""InvoiceHeaderId""  ");

            var query = sql.ToString();

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingInvoice02ResponseModel>(query);

            var result = new PagedResultDto<PagingInvoice02ResponseModel>();
            if (data != null && data.Any())
            {
                var items = data.OrderByDescending(x => x.InvoiceNo).ThenByDescending(x => x.SerialNo).ToList();
                items.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingInvoice02ResponseModel.PagingInvoice02HeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }

                    x.GDTResponseStatus = 0;

                    if (x.SerialNo.StartsWith('C'))
                    {
                        x.GDTResponseStatus = x.TvanInfoInvoiceErrorStatus;
                    }
                    else if (!string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) || !string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason))
                    {
                        x.GDTResponseStatus = -1;
                    }

                    var errorsMess = new string[] { @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceErrorReason) ? "" : $"Lý do sai sót: {string.Join("; ", JsonConvert.DeserializeObject<List<LDoHDonModel>>(x.TvanInfoInvoiceErrorReason)?.Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn có mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceHasCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceWithoutCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn không mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceWithoutCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason) ? "" : $"Thông báo về hóa đơn cần rà soát: {x.TvanInfoCheckInvoiceReason}")}" };

                    x.InvoiceErrorMessage = string.Join("\n", errorsMess.Where(x => !string.IsNullOrEmpty(x)));
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = items;
            }

            return result;
        }

        public async Task<Invoice02HeaderDto> CreateRawAsync(CreateInvoice02RequestModel request, TenantInfoModel tenant, InvoiceSource invoiceSource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceStatus invoiceStatus, SignStatus signStatus, ApproveStatus approveStatus, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, Invoice02HeaderFieldEntity> headerFields, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.Invoice02Header)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.Invoice02Detail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice02Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)invoiceSource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ _invoiceService.GetTransactionId(invoiceSource, request.TransactionId) }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankName?.Replace("'", "''")}',                                  
                                            '{tenant.BankAccount}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName : null)}',                                
                                            N'{ tenant.FullNameVi?.Replace("'", "''") }',                                                                                             
                                            { toCurrency.Rounding },                                                                                                       
                                            '{ fromCurrency.CurrencyCode }',                                                                                               
                                            { toCurrency.Conversion },                                                                                                     
                                            '{ toCurrency.CurrencyCode }',                                                                                                 
                                            REPLACE('{ request.ExchangeRate }',',','.'),                                                                                                                                     
                                            '{ request.PaymentMethod }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            REPLACE('{ request.TotalAmount }',',','.'),                                                                                                                                      
                                            REPLACE('{ request.TotalPaymentAmount }',',','.'),                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine.Replace("'", "''") }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            REPLACE('{ request.TotalDiscountAmount }',',','.'),                                                                                                                     
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            StringBuilder sqlValueDetails = new StringBuilder();

            var i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        REPLACE('{ item.Amount }',',','.'),                                                    
                                                        REPLACE('{ item.DiscountAmount }',',','.'),                                   
                                                        REPLACE('{ item.DiscountPercent }',',','.'),                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        REPLACE('{ item.PaymentAmount }',',','.'),                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { (short)item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        REPLACE('{ item.Quantity }',',','.'),                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        REPLACE('{ item.UnitPrice }',',','.'),                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },   
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice02Detail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmount"",            
                                              ""DiscountPercent"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                      
                                              ""Partition"",                          
                                              ""IsPromotion"",                        
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new Invoice02HeaderDto
            {
                Id = headerId
            };
        }

        public async Task<Invoice02HeaderDto> CreateOldDecreeRawAsync(CreateInvoice02OldDecreeRequestModel request, TenantInfoModel tenant, InvoiceSource invoiceSource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, SignStatus signStatus, ApproveStatus approveStatus, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, Invoice02HeaderFieldEntity> headerFields, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.Invoice02Header)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.Invoice02Detail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice02Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                              
                                            ""TotalDiscountAmount"",                                          
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ReferenceInvoiceType"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)invoiceSource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ _invoiceService.GetTransactionId(invoiceSource, request.TransactionId) }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)request.InvoiceStatus.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankName?.Replace("'", "''")}',                                  
                                            '{tenant.BankAccount}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName.Replace("'", "''") : null)}',                                
                                            N'{ tenant.FullNameVi?.Replace("'", "''") }',                                                                                             
                                            { toCurrency.Rounding },                                                                                                       
                                            '{ fromCurrency.CurrencyCode }',                                                                                               
                                            { toCurrency.Conversion },                                                                                                     
                                            '{ toCurrency.CurrencyCode }',                                                                                                 
                                            { request.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            { request.TotalAmount },                                                                                                                                      
                                            { request.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine.Replace("'", "''") }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { request.TotalDiscountAmount },                                                                                                                                
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            {request.ReferenceInvoiceType.GetHashCode()},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            StringBuilder sqlValueDetails = new StringBuilder();

            var i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmount },                                   
                                                        { item.DiscountPercent },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { (short)item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },     
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion


            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice02Detail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmount"",            
                                              ""DiscountPercent"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                     
                                              ""Partition"",                          
                                              ""IsPromotion"",                        
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            rawSql.Append($@"  INSERT INTO ""Invoice02ReferenceOldDecree"" (              
                                ""CreationTime"",                           
                                ""InvoiceDateReference"",                                  
                                ""InvoiceHeaderId"",                                      
                                ""InvoiceNoReference"",                                 
                                ""NumberReference"",                                
                                ""SerialNoReference"",                         
                                ""TemplateNoReference"",
                                ""TenantId"",
                                ""InvoiceStatus"",
                                ""Note"",
                                ""Partition""
                            )                                                  
                            VALUES (
                                '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                '{request.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                {headerId},
                                '{request.InvoiceReference.InvoiceNo}',
                                {int.Parse(request.InvoiceReference.InvoiceNo)},
                                '{request.InvoiceReference.SerialNo}',
                                '{request.InvoiceReference.TemplateNo}',
                                '{rawTenantId}',
                                {(short)request.InvoiceStatus.GetHashCode()},
                                N'{request.InvoiceReference.Note?.Replace("'", "''")}',
                                {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                            ); ");

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new Invoice02HeaderDto
            {
                Id = headerId
            };
        }

        public async Task UpdateRawAsync(UpdateInvoice02ApiRequestModel request, Invoice02HeaderEntity rootInvoice, List<Invoice02DetailEntity> details, Guid tenantId, Guid userId,
            CurrencyEntity toCurrency, Dictionary<string, Invoice02HeaderFieldEntity> headerFields, Dictionary<string, Invoice02DetailFieldEntity> detailFields,
            Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice02Header""
                                SET 
                                    ""ToCurrency"" = '{toCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note}',
                                    ""PaymentMethod"" = '{request.PaymentMethod.Replace("'", "''")}',
                                    ""RoundingCurrency"" = {toCurrency.Rounding},
                                    ""CurrencyConversion"" = {toCurrency.Conversion},
                                    ""ExchangeRate"" = REPLACE('{request.ExchangeRate}',',','.'),
                                    ""TotalAmount"" = REPLACE('{request.TotalAmount}',',','.'),
                                    ""TotalPaymentAmount"" = REPLACE('{request.TotalPaymentAmount}',',','.'),
                                    ""TotalDiscountAmount"" = REPLACE('{request.TotalDiscountAmount}',',','.'),
                                    ""PaymentAmountWords"" = N'{await _invoiceService.ReadMoneyViAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus)))}',
                                    ""PaymentAmountWordsEn"" = N'{await _invoiceService.ReadMoneyEnAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus)))}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{request.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{rootInvoice.CreatorErp}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice02DetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value)
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new Invoice02DetailModel
                {
                    TenantId = tenantId,
                    InvoiceHeaderId = rootInvoice.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    DiscountAmount = item.DiscountAmount,
                    DiscountPercent = item.DiscountPercent,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = (short)item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice02DetailExtraModel
                    {
                        FieldValue = y.FieldValue,
                        FieldName = y.FieldName,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await RawUpdateInvoiceDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate.Date);

            var query = rawSql.Append($@" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task<string> GenerateQueryUpdateAsync(Invoice02HeaderEntity input, CurrencyEntity toCurrency, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> oldDetails, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> newDatails, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> removeDetails, bool isChangeInvoiceAfterSign, global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceOldDto referenceOldDto)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.ExtraProperties != null && input.ExtraProperties.Count > 0)
            {
                headerExtraProperties = JsonConvert.SerializeObject(input.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice02Header""
                                SET 
                                    ""StoreCode"" = N'{input.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{input.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{input.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{input.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{input.BuyerPassportNumber?.Replace("'", "''")}',
                                    ""ToCurrency"" = '{input.ToCurrency}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""PaymentMethod"" = N'{input.PaymentMethod?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.RoundingCurrency},
                                    ""DiscountType"" = {input.DiscountType},
                                    ""CurrencyConversion"" = {input.CurrencyConversion},
                                    ""ExchangeRate"" = REPLACE('{input.ExchangeRate}',',','.'),
                                    ""TotalDiscountAmount"" = REPLACE('{input.TotalDiscountAmount}',',','.'),
                                    ""TotalAmount"" = REPLACE('{input.TotalAmount}',',','.'),
                                    ""TotalPaymentAmount"" = REPLACE('{input.TotalPaymentAmount}',',','.'),
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.TenantId, toCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.TenantId, toCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""BuyerCode"" = '{input.BuyerCode}',
                                    ""BuyerEmail"" = '{input.BuyerEmail}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{input.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine?.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{input.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{input.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{input.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{input.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{input.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{input.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = N'{input.CreatorErp?.Replace("'", "''")}',
                                    ""InvoiceStatus"" = {input.InvoiceStatus.GetHashCode()},
                                    ""ReferenceInvoiceType"" = {input.ReferenceInvoiceType.GetHashCode()},
                                    ""ExtraProperties"" = N'{headerExtraProperties?.Replace("'", "''")}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailRefactorAsync(rawSql, rawTenantId, oldDetails, newDatails, removeDetails);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, input.InvoiceDate.Date);

            if (isChangeInvoiceAfterSign)
            {
                rawSql.Append($@" UPDATE ""Invoice02Reference""
                              SET
                                     ""InvoiceDateReference"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                             WHERE ""InvoiceReferenceId"" = {input.Id};");
            }

            if (referenceOldDto != null)
            {
                rawSql.Append($@" UPDATE ""Invoice02ReferenceOldDecree""
                              SET
                                     ""InvoiceDateReference"" = '{referenceOldDto.InvoiceDateReference.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                     ""InvoiceNoReference"" = '{referenceOldDto.InvoiceNoReference}',
                                     ""InvoiceStatus"" = '{referenceOldDto.InvoiceStatus.GetHashCode()}',
                                     ""Note"" = N'{referenceOldDto.Note?.Trim().Replace("'", "''")}',
                                     ""NumberReference"" = {referenceOldDto.NumberReference},
                                     ""SerialNoReference"" = '{referenceOldDto.SerialNoReference}',
                                     ""TemplateNoReference"" = '{referenceOldDto.TemplateNoReference}'
                             WHERE ""InvoiceHeaderId"" = {input.Id};");
            }

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        private async Task GenerateUpdateInvoiceDetailRefactorAsync(StringBuilder rawSql, string rawTenantId, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> oldDetails, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> newDatails, List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto> removeDetails)
        {
            if (oldDetails != null && oldDetails.Any())
            {
                foreach (var detail in oldDetails)
                {
                    var detailExtraProperties = "";
                    if (detail.ExtraProperties != null && detail.ExtraProperties.Count > 0)
                    {
                        detailExtraProperties = JsonConvert.SerializeObject(detail.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""Invoice02Detail""
                                        SET
                                            ""Amount"" = REPLACE('{detail.Amount}',',','.'),
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = REPLACE('{detail.PaymentAmount}',',','.'),
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" = REPLACE('{detail.Quantity}',',','.'),
                                            ""UnitName"" = N'{detail.UnitName?.Trim()?.Replace("'", "''")}',
                                            ""HideQuantity"" = {(detail.HideQuantity ? 1 : 0)},
                                            ""HideUnit"" = {(detail.HideUnit ? 1 : 0)},
                                            ""HideUnitPrice"" = {(detail.HideUnitPrice ? 1 : 0)},
                                            ""UnitPrice"" = REPLACE('{detail.UnitPrice}',',','.'),
                                            ""DiscountAmount"" = REPLACE('{detail.DiscountAmount}',',','.'),
                                            ""DiscountPercent"" = REPLACE('{detail.DiscountPercent}',',','.'),
                                            ""ExtraProperties"" = N'{detailExtraProperties?.Replace("'", "''")}'
                                        WHERE ""Id"" = {detail.Id};
                                    ");
                }
            }

            // THÊM MỚI
            if (newDatails != null && newDatails.Any())
            {
                foreach (var item in newDatails)
                {
                    var detailExtraProperties = "";
                    if (item.ExtraProperties != null && item.ExtraProperties.Count > 0)
                    {
                        detailExtraProperties = JsonConvert.SerializeObject(item.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"  INSERT INTO ""Invoice02Detail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""DiscountAmount"",
                                            ""DiscountPercent"",
                                            ""PaymentAmount"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""HideQuantity"",
                                            ""HideUnit"",
                                            ""HideUnitPrice"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""IsPromotion"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {item.Id},
                                            REPLACE('{item.Amount}',',','.'),
                                            N'{item.Note?.Trim().Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()?.Replace("'", "''")}',
                                            N'{item.ProductName?.Trim()?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{item.UnitName?.Trim()?.Replace("'", "''")}',
                                            REPLACE('{item.UnitPrice}',',','.'),
                                            {item.Index},
                                            REPLACE('{item.Quantity}',',','.'),
                                            REPLACE('{item.DiscountAmount}',',','.'),
                                            REPLACE('{item.DiscountPercent}',',','.'),
                                            REPLACE('{item.PaymentAmount}',',','.'),
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}',
                                            { (item.HideQuantity ? 1 : 0) },    
                                            { (item.HideUnit ? 1 : 0) },        
                                            { (item.HideUnitPrice ? 1 : 0) },   
                                            {item.UnitId},
                                            {item.RoundingUnit},
                                            { item.Partition },
                                            0,
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        ); ");
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""Invoice02Detail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }


        public async Task<Invoice02HeaderDto> CreateAdjustmentHeaderRawAsync(CreateAdjustmentHeaderInvoice02ApiRequestModel request, TenantInfoModel tenant, Invoice02HeaderEntity rootInvoice, List<Invoice02DetailEntity> details,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, Dictionary<string, Invoice02HeaderFieldEntity> headerFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(rootInvoice.SellerId);

            var generateHeaderId = await GetSEQsNextVal(1, SEQ_Name.SEQ_Invoice02Header);
            var headerId = generateHeaderId.First();
            var lstDetailId = await GetSEQsNextVal(details.Count, SEQ_Name.SEQ_Invoice02Detail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice02Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",       
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   

                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmount"",                                              
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)resource },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ rootInvoice.TransactionId }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},                                                                                                   
                                            {(short)approveCancelStatus.GetHashCode()},                                                                                                   
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                   
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}', 
                                            '{tenant.TenantCode}',
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{tenant.Phones}',                                         
                                            '{tenant.Fax}',                                             
                                            '{tenant.Email}',                                         
                                            N'{tenant.BankName?.Replace("'", "''")}',                                  
                                            N'{tenant.BankAccount}',                            
                                            N'{tenant.LegalName?.Replace("'", "''")}',                                
                                            N'{tenant.FullNameVi.Replace("'", "''") }',                                                                                                   
                                            { rootInvoice.RoundingCurrency },                                                                                                       
                                            '{ rootInvoice.FromCurrency }',                                                                                               
                                            { rootInvoice.CurrencyConversion },                                                                                                     
                                            '{ rootInvoice.ToCurrency }',                                                                                                 
                                            { rootInvoice.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ rootInvoice.PaymentAmountWords?.Replace("'", "''") }',        
                                            N'{ rootInvoice.PaymentAmountWordsEn?.Replace("'", "''") }',        
                                            { rootInvoice.TotalAmount },                                                                                                                                      
                                            { rootInvoice.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine.Replace("'", "''") }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { rootInvoice.TotalDiscountAmount },                                                                                                                            
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            StringBuilder sqlValueDetails = new StringBuilder();


            #region DETAILS SQL

            var i = 0;
            var dictMappingDetailId = new Dictionary<long, long>();

            foreach (var item in details.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];
                dictMappingDetailId.Add(item.Id, detailId);

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    detailExtraProperties = JsonConvert.SerializeObject(item.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                              
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmount },                                   
                                                        { item.DiscountPercent },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { item.ProductId },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { item.ProductType },                                            
                                                        '{ item.ProductCode }',                                     
                                                        { item.Quantity },                                                  
                                                        { item.UnitId },                                   
                                                        N'{ item.UnitName?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { item.RoundingUnit },                             
                                                        { (item.HideQuantity ? 1 : 0) },    
                                                        { (item.HideUnit ? 1 : 0) },        
                                                        { (item.HideUnitPrice ? 1 : 0) },      
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                                   
                                                        N'{detailExtraProperties?.Replace("'", "''")}'                                                                  
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice02Detail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmount"",            
                                        ""DiscountPercent"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                       
                                        ""Partition"",                          
                                        ""IsPromotion"",
                                        ""ExtraProperties""
                                ) {sqlValueDetails} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""Invoice02Reference"" (             
                                        ""CreationTime"",                           
                                        ""InvoiceDateReference"",                                  
                                        ""InvoiceHeaderId"",                                      
                                        ""InvoiceNoReference"",                                 
                                        ""InvoiceReferenceId"",                           
                                        ""NumberReference"",                                
                                        ""SerialNoReference"",                         
                                        ""TemplateNoReference"",
                                        ""TenantId"",
                                        ""InvoiceStatus"",
                                        ""Note"",
                                        ""Partition""
                                    )                                                  
                                    VALUES (
                                        '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        '{rootInvoice.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        {headerId},
                                        '{rootInvoice.InvoiceNo}',
                                        {rootInvoice.Id},
                                        {rootInvoice.Number ?? 0},
                                        '{rootInvoice.SerialNo}',
                                        {rootInvoice.TemplateNo},
                                        '{rawTenantId}',
                                        {(short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode()},
                                        N'{rootInvoice.Note?.Replace("'", "''")}',
                                        {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                    ); ");

            #endregion


            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new Invoice02HeaderDto
            {
                Id = headerId
            };
        }

        public async Task<Invoice02HeaderDto> ReplaceRawAsync(CreateReplaceInvoice02ApiRequestModel request, TenantInfoModel tenant, Invoice02HeaderEntity rootInvoice, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource resource,
            Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<string, ProductEntity> products,
            Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, Invoice02HeaderFieldEntity> headerFields, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.Invoice02Header)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.Invoice02Detail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice02Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)InvoiceSource.Api.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp?.Replace("'", "''") }',                                                                                                                                     
                                            '{ _invoiceService.GetTransactionId(InvoiceSource.Api, rootInvoice.TransactionId) }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.ThayThe.GetHashCode()},                                                                                                                   
                                            {(short)signStatus.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankAccount}',                                  
                                            '{tenant.BankName?.Replace("'", "''")}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName?.Replace("'", "''") : null)}',                                
                                            N'{ tenant.FullNameVi?.Replace("'", "''") }',                                                                                             
                                            { toCurrency.Rounding },                                                                                                       
                                            '{ fromCurrency.CurrencyCode }',                                                                                               
                                            { toCurrency.Conversion },                                                                                                     
                                            '{ toCurrency.CurrencyCode }',                                                                                                 
                                            { request.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            { request.TotalAmount },                                                                                                                                      
                                            { request.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine.Replace("'", "''") }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { request.TotalDiscountAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            if (!request.InvoiceDetails.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.InvoiceDetailCanNotEmpty"]);

            StringBuilder sqlInsertDetails = new StringBuilder();

            var i = 0;

            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var product = string.IsNullOrEmpty(item.ProductCode?.Trim())
                            ? null
                            : ((units != null && products.ContainsKey(item.ProductCode?.Trim())) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlInsertDetails.Append($@"UNION ALL SELECT   
                                                        { detailId },                                              
                                                        { item.Index },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmount },                                   
                                                        { item.DiscountPercent },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName.Replace("'", "''") }',                                            
                                                        { (short)item.ProductType },                                            
                                                        '{ item.ProductCode?.Trim() }',                                                                           
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },        
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },                
                                                        0,
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlInsertDetails.Length > 0)
            {
                sqlInsertDetails = sqlInsertDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice02Detail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmount"",            
                                        ""DiscountPercent"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlInsertDetails} ; ");
            }


            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""Invoice02Reference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{rootInvoice.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {headerId},
                                                    '{request.InvoiceNoReference}',
                                                    {rootInvoice.Id},
                                                    {rootInvoice.Number ?? 0},
                                                    '{rootInvoice.SerialNo}',
                                                    {rootInvoice.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.ThayThe.GetHashCode()},
                                                    N'{request.Note?.Replace("'", "''")}',
                                                    {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new Invoice02HeaderDto
            {
                Id = headerId
            };
        }

        public async Task UpdateReplaceRawAsync(UpdateReplaceInvoice02ApiRequestModel request, Invoice02HeaderEntity rootInvoice, List<Invoice02DetailEntity> details, SignStatus signStatus,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency,
            Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, Invoice02HeaderFieldEntity> headerFields, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice02Header""
                                SET 
                                    ""ToCurrency"" = '{request.Currency}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note}',
                                    ""PaymentMethod"" = '{request.PaymentMethod}',
                                    ""RoundingCurrency"" =  REPLACE('{toCurrency.Rounding}',',','.'),
                                    ""CurrencyConversion"" = {toCurrency.Conversion},
                                    ""ExchangeRate"" =  REPLACE('{request.ExchangeRate}',',','.'),
                                    ""TotalAmount"" =  REPLACE('{request.TotalAmount}',',','.'),
                                    ""TotalPaymentAmount"" =  REPLACE('{request.TotalPaymentAmount}',',','.'),
                                    ""TotalDiscountAmount"" =  REPLACE('{request.TotalDiscountAmount}',',','.'),
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = '{request.BuyerBankName?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice02DetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value)
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new Invoice02DetailModel
                {
                    Amount = item.Amount,
                    DiscountAmount = item.DiscountAmount,
                    DiscountPercent = item.DiscountPercent,
                    Index = item.Index,
                    InvoiceHeaderId = rootInvoice.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = (short)item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = tenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice02DetailExtraModel
                    {
                        FieldName = y.FieldName,
                        FieldValue = y.FieldValue,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await RawUpdateInvoiceDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate.Date);

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task<Invoice02HeaderDto> CreateAdjustmentDetailRawAsync(CreateAdjustmentDetailInvoice02ApiRequestModel request, TenantInfoModel tenant, Invoice02HeaderEntity rootInvoice, List<Invoice02DetailEntity> rootInvoiceDetails,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template,
            CurrencyEntity toCurrency, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.Invoice02Header)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.Invoice02Detail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any())
            {
                headerExtraProperties = JsonConvert.SerializeObject(rootInvoice.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"       
                                BEGIN                                                                     
                                        INSERT INTO ""Invoice02Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",    
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmount"",                                                
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""
                                        )                                                                       
                                        VALUES(                                                                 
                                                { headerId },                                                                                                                                                   
                                                '{ rawTenantId }',                                                                                                                                          
                                                '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                                '{ rawUserId }',                                                                                                                                            
                                                { (short)resource },                                                                                                                    
                                                '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                                '{ request.ErpId }',                                                                                                                                          
                                                '{ request.CreatorErp }',                                                                                                                                     
                                                '{ rootInvoice.TransactionId}',                                                                                
                                                { template.Id },                                                                                                               
                                                { template.TemplateNo },                                                                                                                                       
                                                '{ template.SerialNo }',                                                                                                                                       
                                                N'{ rootInvoice.Note }',                                                                                                                                          
                                                '{ request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                                {(short)InvoiceStatus.DieuChinhTangGiam.GetHashCode()},                                                                                                                   
                                                {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                                {(short)approveStatus.GetHashCode()},                                                                                                   
                                                {(short)approveCancelStatus.GetHashCode()},                                                                                                   
                                                {(short)approveDeleteStatus.GetHashCode()},                                                                                                   
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                '{rawTenantId}',  
                                                '{tenant.TenantCode}',
                                                '{tenant.TaxCode}',                                                                                                        
                                                N'{tenant.Address}',                                                                                                       
                                                N'{tenant.Country}',                                                                                                       
                                                N'{tenant.District}',                                                                                                      
                                                N'{tenant.City}',                                                                                                          
                                                '{tenant.Phones}',                                         
                                                '{tenant.Fax}',                                             
                                                '{tenant.Email}',                                         
                                                N'{tenant.BankName}',                                  
                                                N'{tenant.BankAccount}',                            
                                                N'{tenant.LegalName}',                                
                                                N'{tenant.FullNameVi }',                                                                                                   
                                                { rootInvoice.RoundingCurrency },                                                                                                       
                                                '{ rootInvoice.FromCurrency }',                                                                                               
                                                { rootInvoice.CurrencyConversion },                                                                                                     
                                                '{ rootInvoice.ToCurrency }',                                                                                                 
                                                { rootInvoice.ExchangeRate },                                                                                                                                     
                                                '{ rootInvoice.PaymentMethod }',                                                                                                                                  
                                                '{ request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                                N'{ await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam) }',        
                                                N'{ await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam) }',        
                                                { request.TotalAmount },                                                                                                                                      
                                                { request.TotalPaymentAmount },                                                                                                                               
                                                '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                                '{ userNameCreator }',                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                '{ rootInvoice.BuyerCode?.Trim() }',                                                                                                                              
                                                '{ rootInvoice.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                                '{ rootInvoice.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                                '{ rootInvoice.BuyerTaxCode?.Trim() }',                                                                                                                           
                                                '{ rootInvoice.BuyerAddressLine.Replace("'", "''") }',                                                                                                                               
                                                '{ rootInvoice.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                                '{ rootInvoice.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                                '{ rootInvoice.BuyerCountryCode }',                                                                                                                               
                                                '{ rootInvoice.BuyerPhoneNumber }',                                                                                                                               
                                                '{ rootInvoice.BuyerFaxNumber }',                                                                                                                                 
                                                '{ rootInvoice.BuyerEmail }',                                                                                                                                     
                                                '{ rootInvoice.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                                '{ rootInvoice.BuyerBankAccount }',                                                                                                                               
                                                { request.TotalDiscountAmount },                                                                                                                                    
                                                1,                                                                                                                                                          
                                                {request.InvoiceDate.Year},                                                                                                                                   
                                                {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                                {request.InvoiceDate.Month},                                                                                                                                  
                                                {request.InvoiceDate.GetWeek()},                                                                                                                              
                                                {request.InvoiceDate.Day},                                                                                                                                    
                                                0,                                                                                                                                                          
                                                0,                                                                                                                                                          
                                                {long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))},                                                                                                   
                                                N'{headerExtraProperties}'                                                                                                
                                        ); ");

            #endregion

            StringBuilder sqlValueDetails = new StringBuilder();

            #region DETAILS SQL

            var detailIds = new List<long>();
            var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);
            int i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var idDetail = lstDetailId[i];
                if (!indexes.ContainsKey(item.Index))
                    //throw new Exception("Thông tin điều chỉnh tăng giảm chi tiết hóa đơn không đúng");
                    throw new Exception(_localizier["Vnis.BE.Invoice02.IndexInvoiceDetailIncorrect"]);

                var rootDetail = indexes[item.Index];
                var hideQuantity = rootDetail.HideQuantity ? 1 : 0;
                var hideUnit = rootDetail.HideUnit ? 1 : 0;
                var hideUnitPrice = rootDetail.HideUnitPrice ? 1 : 0;
                detailIds.Add(rootDetail.Id);

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(idDetail, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue,
                        FieldName = x.FieldName,
                    }).ToList(), detailFields);

                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                               
                                                          { idDetail},                                              
                                                          { i + 1 },                                                     
                                                          '{ rawTenantId }',                                                  
                                                          { headerId },                                                           
                                                          { item.Amount },                                                    
                                                          { item.DiscountAmount },                                   
                                                          { item.DiscountPercent },                                  
                                                          N'{ item.Note }',                                                   
                                                          N'{ item.PaymentAmount }',                                          
                                                          { rootDetail.ProductId },                             
                                                          N'{ rootDetail.ProductName }',                                            
                                                          { rootDetail.ProductType },                                            
                                                          '{ rootDetail.ProductCode?.Trim() }',                                     
                                                          { item.Quantity },                                                  
                                                          { rootDetail.UnitId },                                   
                                                          N'{ rootDetail.UnitName?.Trim() }',                                       
                                                          { item.UnitPrice },                                                 
                                                          { rootDetail.RoundingUnit },                             
                                                          { hideQuantity},    
                                                          { hideUnit },        
                                                          { hideUnitPrice },   
                                                          { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                          0,                                                                   
                                                          N'{detailExtraProperties}'                                                                   
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice02Detail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmount"",            
                                        ""DiscountPercent"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }

            #region INVOICE REFERENCE SQL
            StringBuilder sqlInsertInvoice02Reference = new StringBuilder();

            sqlInsertInvoice02Reference.Append($@"  INSERT INTO ""Invoice02Reference"" (             
                                                    ""CreationTime"",                           
                                                    ""CreatorId"",                                  
                                                    ""TenantId"",                                      
                                                    ""InvoiceHeaderId"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""TemplateNoReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""InvoiceNoReference"",                                  
                                                    ""NumberReference"",                                  
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceStatus"",                                  
                                                    ""Partition""                                                                 
                                                )                                                  
                                                VALUES ");

            //Add values
            sqlInsertInvoice02Reference.Append($@"  (                                                               
                                                      '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                      '{ rawUserId }',                                             
                                                      '{ rawTenantId }',                                               
                                                      { headerId },                                            
                                                      { rootInvoice.Id},                                      
                                                      { rootInvoice.TemplateNo },                                           
                                                      '{ rootInvoice.SerialNo }',                                              
                                                      '{ rootInvoice.InvoiceNo}',    
                                                      { rootInvoice.Number},    
                                                      '{ rootInvoice.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                      { InvoiceStatus.DieuChinhTangGiam.GetHashCode()},    
                                                      { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}    
                                                  ) ");

            sqlInsertInvoice02Reference.Append("; ");

            if (sqlInsertInvoice02Reference.Length > 0)
                rawSql.Append(sqlInsertInvoice02Reference);
            #endregion


            var query = rawSql.Append($" END; ").ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new Invoice02HeaderDto
            {
                Id = headerId
            };
        }

        public async Task CreateAdjustmentHeaderWithoutInvoiceNoAsync(Invoice02HeaderEntity invoice)
        {
            invoice.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        public async Task UpdateAdjustmentHeaderRawAsync(UpdateAdjustmentHeaderInvoice02ApiRequestModel request, Invoice02HeaderEntity rootInvoice,
            Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, Dictionary<string, Invoice02HeaderFieldEntity> headerFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            };
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice02Header""
                                SET 
                                    ""StoreCode"" = N'{request.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{request.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{request.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{request.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{request.BuyerPassportNumber?.Replace("'", "''")}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note?.Replace("'", "''")}',
                                    ""PaymentMethod"" = '{request.PaymentMethod?.Replace("'", "''")}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail?.Trim()}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{request.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{rootInvoice.CreatorErp}',
                                    ""ExtraProperties"" = N'{headerExtraProperties?.Replace("'", "''")}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var query = rawSql.Append($@" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task UpdateInvoiceReferenceRawAsync(Guid userId, Invoice02HeaderEntity rootInvoice, long invoiceHeaderId, int templateNo, string serialNo, string invoiceNo, int? number, DateTime invoiceDate)
        {
            // update thông tin hóa đơn gốc
            short invoiceStt = 0;
            if (rootInvoice.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiThayThe;

            if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiDieuChinhDinhDanh;

            if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiDieuChinhTangGiam;

            var invoice02Reference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice02ReferenceEntity>>();
            var invoice02Ref = await invoice02Reference.GetInvoiceReferenceRawAsync(invoiceHeaderId);

            StringBuilder raw = new StringBuilder();
            if (invoice02Ref != null)
            {
                //var rootInvoiceHeader = await repoInvoice02Header.GetByIdRawAsync(message.TenantId, invoice02Ref.InvoiceReferenceId);
                raw.Append($"BEGIN Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                        $"Set \"InvoiceStatus\" = '{invoiceStt}'" +
                        $"Where \"Id\" = {invoice02Ref.InvoiceReferenceId} ;");

                StringBuilder sqlInsertInvoice02Reference = new StringBuilder();

                sqlInsertInvoice02Reference.Append($@"  INSERT INTO ""Invoice02Reference"" (             
                                                ""CreationTime"",                           
                                                ""CreatorId"",                                  
                                                ""TenantId"",                                      
                                                ""InvoiceHeaderId"",                                 
                                                ""InvoiceReferenceId"",                           
                                                ""TemplateNoReference"",                                
                                                ""SerialNoReference"",                         
                                                ""InvoiceNoReference"",                                  
                                                ""NumberReference"",                                  
                                                ""InvoiceDateReference"",                                  
                                                ""InvoiceStatus"",                                  
                                                ""Partition""                                                                 
                                            )                                                  
                                            VALUES ");

                //Add values
                sqlInsertInvoice02Reference.Append($@"  (                                                               
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                    '{ OracleExtension.ConvertGuidToRaw(userId) }',                                             
                                                    '{ OracleExtension.ConvertGuidToRaw(rootInvoice.TenantId) }',                                               
                                                    { rootInvoice.Id },                                            
                                                    { invoiceHeaderId},                                      
                                                    { templateNo },                                           
                                                    '{ serialNo }',                                              
                                                    '{ invoiceNo}',    
                                                    { number},    
                                                    '{ invoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                    { rootInvoice.InvoiceStatus},    
                                                    { long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))}    
                                                ) ");

                sqlInsertInvoice02Reference.Append("; ");

                if (sqlInsertInvoice02Reference.Length > 0)
                    raw.Append(sqlInsertInvoice02Reference);

                var queryResult = raw.Append($" END; ").ToString();
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);
            }
        }

        public async Task UpdateAdjustmentDetailRawAsync(UpdateAdjustmentDetailInvoice02ApiRequestModel request, Invoice02HeaderEntity rootInvoice, List<Invoice02DetailEntity> details, Guid tenantId, Guid userId, CurrencyEntity toCurrency, Dictionary<string, Invoice02DetailFieldEntity> detailFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            rawSql.Append($"Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                          $"Set \"InvoiceDate\" = '{request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'," +
                          $"\"TotalAmount\" =  REPLACE('{request.TotalAmount}',',','.')," +
                          $"\"LastModifierId\" = '{ rawUserId }', " +
                          $"\"TotalPaymentAmount\" =  REPLACE('{request.TotalPaymentAmount}',',','.')," +
                          $"\"TotalDiscountAmount\" =  REPLACE('{request.TotalDiscountAmount}',',','.')," +
                          $"\"PaymentAmountWords\" = N'{(await _invoiceService.ReadMoneyViAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Trim().Replace("'", "''")}'," +
                          $"\"PaymentAmountWordsEn\" = N'{(await _invoiceService.ReadMoneyEnAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Trim().Replace("'", "''")}'" +
                          $"Where \"Id\" = {rootInvoice.Id}; ");

            var commandInvoiceDetails = new List<Invoice02DetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                commandInvoiceDetails.Add(new Invoice02DetailModel
                {
                    Amount = item.Amount,
                    DiscountAmount = item.DiscountAmount,
                    DiscountPercent = item.DiscountPercent,
                    Index = item.Index,
                    InvoiceHeaderId = rootInvoice.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    Quantity = item.Quantity,
                    TenantId = tenantId,
                    UnitPrice = item.UnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice02DetailExtraModel
                    {
                        FieldValue = y.FieldValue?.Replace("'", "''"),
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await GenerateUpdateInvoiceDetailAdjustmentDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate);

            var query = rawSql.Append($" END; ").ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        private async Task GenerateUpdateInvoiceDetailAdjustmentDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<Invoice02DetailEntity> entityDetails, Dictionary<string, Invoice02DetailFieldEntity> entityDetailFields, List<Invoice02DetailModel> modelDetails, DateTime invoiceDate)
        {
            var removeDetails = new List<Invoice02DetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""Invoice02Detail""
                                        SET
                                            ""Amount"" = REPLACE('{detail.Amount}',',','.'),
                                            ""DiscountAmount"" = REPLACE('{detail.DiscountAmount}',',','.'),
                                            ""DiscountPercent"" = REPLACE('{detail.DiscountPercent}',',','.'),
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = REPLACE('{detail.PaymentAmount}',',','.'),
                                            ""Quantity"" = REPLACE('{detail.Quantity}',',','.'),
                                            ""UnitPrice"" = REPLACE('{detail.UnitPrice}',',','.')
                                        WHERE ""Id"" = {item.Id};
                                    ");
                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                    //if (detailExtras.ContainsKey(item.Id))
                    //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""Invoice02Detail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }

        private async Task RawUpdateInvoiceDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<Invoice02DetailEntity> entityDetails, Dictionary<string, Invoice02DetailFieldEntity> entityDetailFields, List<Invoice02DetailModel> modelDetails, DateTime invoiceDate)
        {
            //đánh lại index detail
            int k = 1;
            foreach (var item in modelDetails.OrderBy(x => x.Index))
            {
                item.Index = k;
                k++;
            }

            var removeDetails = new List<Invoice02DetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index)).OrderBy(x => x.Index);

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue?.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""Invoice02Detail""
                                        SET
                                            ""Amount"" =  REPLACE('{detail.Amount}',',','.'),
                                            ""DiscountAmount"" =  REPLACE('{detail.DiscountAmount}',',','.'),
                                            ""DiscountPercent"" =  REPLACE('{detail.DiscountPercent}',',','.'),
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" =  REPLACE('{detail.PaymentAmount}',',','.'),
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName?.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" =  REPLACE('{detail.Quantity}',',','.'),
                                            ""UnitName"" = N'{detail.UnitName?.Trim()?.Replace("'", "''")}',
                                            ""HideQuantity"" = {(detail.HideQuantity ? 1 : 0)},
                                            ""HideUnit"" = {(detail.HideUnit ? 1 : 0)},
                                            ""HideUnitPrice"" = {(detail.HideUnitPrice ? 1 : 0)},
                                            ""UnitPrice"" =  REPLACE('{detail.UnitPrice}',',','.'),
                                            ""ExtraProperties"" = N'{detailExtraProperties}'
                                        WHERE ""Id"" = {item.Id};
                                    ");

                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                }
            }

            // THÊM MỚI
            if (newDetails != null && newDetails.Any())
            {
                var lstDetailId = await GetSEQsNextVal(newDetails.Count(), SEQ_Name.SEQ_Invoice02Detail);

                var i = 0;
                foreach (var item in newDetails)
                {
                    var detailId = lstDetailId[i];

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue?.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"  INSERT INTO ""Invoice02Detail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""DiscountAmount"",
                                            ""DiscountPercent"",
                                            ""PaymentAmount"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""HideQuantity"",
                                            ""HideUnit"",
                                            ""HideUnitPrice"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""IsPromotion"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {detailId},
                                            REPLACE('{item.Amount}',',','.'),
                                            N'{item.Note?.Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()}',
                                            N'{item.ProductName?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{item.UnitName?.Trim()?.Replace("'", "''")}',
                                            REPLACE('{item.UnitPrice}',',','.'),
                                            {item.Index},
                                            REPLACE('{item.Quantity}',',','.'),
                                            REPLACE('{item.DiscountAmount}',',','.'),
                                            REPLACE('{item.DiscountPercent}',',','.'),
                                            REPLACE('{item.PaymentAmount}',',','.'),
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}',
                                            { (item.HideQuantity ? 1 : 0) },    
                                            { (item.HideUnit ? 1 : 0) },        
                                            { (item.HideUnitPrice ? 1 : 0) },   
                                            0,
                                            4,
                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
                                            0,
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        ); ");

                    i++;
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""Invoice02Detail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }

        private Dictionary<string, string> GetHeaderExtraProperties(List<CommonHeaderExtraModel> invoiceHeaderExtras, Dictionary<string, Invoice02HeaderFieldEntity> entityHeaderFields)
        {
            var headerExtraProperties = new Dictionary<string, string>();

            var headerExtras = new List<InvoiceHeaderExtraModel>();
            foreach (var item in invoiceHeaderExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityHeaderFields.ContainsKey(item.FieldName))
                    continue;

                var field = entityHeaderFields[item.FieldName];
                headerExtras.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = item.FieldName,
                    FieldValue = item.FieldValue,
                });

            }
            headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));

            return headerExtraProperties;
        }

        private Dictionary<string, string> GetDetailExtraProperties(long detailId, List<CommonDetailExtraModel> invoiceDetailExtras, Dictionary<string, Invoice02DetailFieldEntity> entityDetailFields)
        {
            var detailExtraProperties = new Dictionary<string, string>();

            var detailExtras = new List<InvoiceDetailExtraModel>();
            foreach (var extra in invoiceDetailExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityDetailFields.ContainsKey(extra.FieldName))
                    continue;

                var field = entityDetailFields[extra.FieldName];
                detailExtras.Add(new InvoiceDetailExtraModel
                {
                    FieldValue = extra.FieldValue,
                    FieldName = field.FieldName,
                });
            }
            detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));

            return detailExtraProperties;
        }

        public async Task<List<CreateInvoice02GetDataValidDto>> CreateInvoice02GetDataValid(Guid tenantId, List<KeyValuePair<short, string>> templateInfos, List<string> currencies, List<string> productCodes, List<string> unitNames)
        {
            string conditionProductCode = string.Empty;
            if (productCodes != null && productCodes.Any())
            {
                StringBuilder rawConditionProduct = new StringBuilder();
                var strProductCodes = productCodes.Select(x => $"'{x}'").ToList();

                for (int i = 0; i < strProductCodes.Count; i += 1000)
                {
                    if (i >= 1000)
                        rawConditionProduct.Append(" OR ");

                    rawConditionProduct.Append($@" a.""ProductCode"" IN ({string.Join(", ", strProductCodes.Skip(i).Take(1000))}) ");
                }

                conditionProductCode = $"AND ( {rawConditionProduct.ToString()} )";
            }

            var sql = $@"
                    -- invoicetemplate
                    select {ValidateDataType.Template.GetHashCode()} AS ""Type"", att.""UserId"", template.""Id"" AS ""TemplateId"", ""TemplateNo"" as ""TemplateNo"", ""SerialNo"" as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"", null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", null as ""FieldName"" 
                    FROM ""InvoiceTemplate"" template LEFT JOIN ""AccountTokenTemplate"" att 
                    ON template.""Id"" = att.""TemplateId""
                    where template.""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' and template.""IsDeleted"" = 0 AND ({string.Join(" OR ", templateInfos.Select(x => @$"(""TemplateNo"" = {x.Key} AND ""SerialNo"" = '{x.Value}')"))}) and att.""TemplateId"" is not null and att.""UsbTokenId"" is null
                    UNION ALL
                    -- FromCurrencyCode
                    select {ValidateDataType.FromCurrency.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", ""CurrencyCode"" AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", ""NameVi"" as ""NameVi"", ""MinimumNameVi"" as ""MinimumNameVi"", ""NameEn"" as ""NameEn"", ""MinimumNameEn"" as ""MinimumNameEn"",   null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", null as ""FieldName"" FROM ""Currency"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' and ""IsDefault"" = 1 and ""IsDeleted"" = 0
                    UNION All
                    -- ToCurrencyCode
                    select {ValidateDataType.ToCurrency.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", ""CurrencyCode"" AS ""ToCurrencyCode"", ""Rounding"" as ""ToCurrencyRounding"", ""Conversion"" as ""ToCurrencyConversion"", ""NameVi"" as ""NameVi"", ""MinimumNameVi"" as ""MinimumNameVi"", ""NameEn"" as ""NameEn"", ""MinimumNameEn"" as ""MinimumNameEn"",  null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", null as ""FieldName"" FROM ""Currency"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' and ""IsDeleted"" = 0 {(currencies == null || !currencies.Any() ? "" : @$" AND ""CurrencyCode"" IN :Currencies")}
                    UNION All
                    -- tax
                    select {ValidateDataType.Tax.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"", ""Value"" AS ""TaxValue"", ""Name"" AS ""TaxName"", ""Display"" AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", null as ""FieldName"" FROM ""Tax"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}'  and ""IsDeleted"" = 0
                    UNION All
                    -- product, productype
                    select {ValidateDataType.Product.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"", null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", a.""Id"" AS ""ProductId"", ""ProductCode"" AS ""ProductCode"", ""HideQuantity"" AS ""HideQuantity"", ""HideUnit"" AS ""HideUnit"", ""HideUnitPrice"" AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", null as ""FieldName"" FROM ""Product"" a
                    left join (select ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice"" FROM ""ProductType"") b on a.""ProductTypeId"" = b.""Id""  
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}'  and ""IsDeleted"" = 0 {conditionProductCode}
                    UNION All
                    -- unit
                    select {ValidateDataType.Unit.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"", null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", ""Rounding"" AS ""UnitRounding"", ""Name"" AS ""UnitName"", ""Id"" AS ""UnitId"", null as ""FieldName"" FROM ""Unit"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}'  and ""IsDeleted"" = 0  {(unitNames == null || !unitNames.Any() ? "" : @$" AND ""Name"" IN :UnitNames  ")}
                    UNION All
                    -- headerField
                    select {ValidateDataType.HeaderField.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"", null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", ""FieldName"" as ""FieldName"" FROM ""Invoice02HeaderField"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}'  and ""IsDeleted"" = 0
                    UNION All
                    -- detailField
                    select {ValidateDataType.DetailField.GetHashCode()} AS ""Type"", null as ""UserId"", null AS ""TemplateId"", null as ""TemplateNo"", null as ""SerialNo"", null AS ""FromCurrencyCode"", null AS ""ToCurrencyCode"", null as ""ToCurrencyRounding"", null as ""ToCurrencyConversion"", null as ""NameVi"", null as ""MinimumNameVi"", null as ""NameEn"", null as ""MinimumNameEn"",  null AS ""TaxValue"", null AS ""TaxName"", null AS ""TaxDisplay"", null AS ""ProductId"", null AS ""ProductCode"", null AS ""HideQuantity"", null AS ""HideUnit"", null AS ""HideUnitPrice"", null AS ""UnitRounding"", null AS ""UnitName"", null AS ""UnitId"", ""FieldName"" as ""FieldName"" FROM ""Invoice02DetailField"" 
                    where ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}'  and ""IsDeleted"" = 0
                    ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<CreateInvoice02GetDataValidDto>(sql, new
            {
                Currencies = currencies,
                UnitNames = unitNames
            });
            return result.ToList();
        }
    }
}
