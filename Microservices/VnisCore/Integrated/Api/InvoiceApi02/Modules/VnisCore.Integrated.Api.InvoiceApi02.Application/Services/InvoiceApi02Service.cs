using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Data;
using Core.Dto.Shared;
using Core.Dto.Shared.Invoices.Invoice02;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Helper;
using Core.Shared.Invoice.Services;
using Core.Shared.Messages;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Shared.Validations;
using Core.TenantManagement;

using Dapper;

using Elasticsearch.Net;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Business;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Queries;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitMqEventBus.CreateInvoice02.MessageEventData;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application
{
    [Route("api/02gttt")]
    [Authorize]
    public class InvoiceApi02Service : ApplicationService, IInvoiceApi02Service
    {
        private readonly IAppFactory _factory;
        private readonly IValidatorFactory _validatorFactory;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;
        private readonly IValidationContext _validationContext;
        private readonly IConfiguration _configuration;
        private readonly IVnisCoreMongoInvoice02BatchIdRepository _mongoInvoice02BatchIdRepository;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IVnisCoreMongoInvoice02ErpIdRepository _mongoInvoice02ErpIdRepository;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IInvoiceHeaderRepository<Invoice02HeaderEntity> _repoInvoice02Header;
        private readonly IInvoiceDetailRepository<Invoice02DetailEntity> _repoInvoice02Detail;
        private readonly IVnisCoreMongoInvoice02ReSyncRepository _mongoInvoice02ReSyncRepository;
        private readonly ICreateAdjustmentHeaderWithoutInvoiceNoBusiness _createAdjustmentHeaderWithoutInvoiceNoBusiness;
        private readonly IUpdateRootInvoice02ApiBusiness _updateRootInvoice02ApiBusiness;
        private readonly IUpdateAdjustHeaderInvoice02Business _updateAdjustHeaderInvoice02Business;
        private readonly IVnisCoreMongoInvoice02LogRepository _vnisCoreMongoInvoice02LogRepository;
        private readonly IUpdateAdjustDetailInvoice02Business _updateAdjustDetailInvoice02Business;
        private readonly IUpdateReplaceInvoice02Business _updateReplaceInvoice02Business;
        private readonly IInvoiceReferenceRepository<Invoice02ReferenceEntity> _repoInvoice02Reference;
        private readonly ElasticSearch _elasticSearch;
        private readonly IInvoice02Service _invoice02Service;
        private readonly ISettingService _settingService;
        private readonly IRedisCacheService _redisCacheService;
        private readonly ITaxReport01Repository _taxReport01Repository;

        public InvoiceApi02Service(IAppFactory factory,
            IValidatorFactory validatorFactory,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService,
            IValidationContext validationContext,
            IConfiguration configuration,
            IVnisCoreMongoInvoice02BatchIdRepository mongoInvoice02BatchIdRepository,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IVnisCoreMongoInvoice02ErpIdRepository mongoInvoice02ErpIdRepository,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceHeaderRepository<Invoice02HeaderEntity> repoInvoice02Header,
            IInvoiceDetailRepository<Invoice02DetailEntity> repoInvoice02Detail,
            IVnisCoreMongoInvoice02ReSyncRepository mongoInvoice02ReSyncRepository,
            ICreateAdjustmentHeaderWithoutInvoiceNoBusiness createAdjustmentHeaderWithoutInvoiceNoBusiness,
            IVnisCoreMongoInvoice02LogRepository vnisCoreMongoInvoice02LogRepository,
            IUpdateRootInvoice02ApiBusiness updateRootInvoice02ApiBusiness,
            IUpdateAdjustHeaderInvoice02Business updateAdjustHeaderInvoice02Business,
            IUpdateAdjustDetailInvoice02Business updateAdjustDetailInvoice02Business,
            IUpdateReplaceInvoice02Business updateReplaceInvoice02Business,
            IInvoiceReferenceRepository<Invoice02ReferenceEntity> repoInvoice02Reference,
            ITaxReport01Repository taxReport01Repository,
            ElasticSearch elasticSearch,
            IInvoice02Service invoice02Service,
            ISettingService settingService,
            IRedisCacheService redisCacheService)
        {
            _validatorFactory = validatorFactory;
            _invoiceService = invoiceService;
            _validationContext = validationContext;
            _configuration = configuration;
            _mongoInvoice02BatchIdRepository = mongoInvoice02BatchIdRepository;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _mongoInvoice02ErpIdRepository = mongoInvoice02ErpIdRepository;
            _distributedEventBus = distributedEventBus;
            _localizier = localizier;
            _repoInvoice02Detail = repoInvoice02Detail;
            _repoInvoice02Header = repoInvoice02Header;
            _mongoInvoice02ReSyncRepository = mongoInvoice02ReSyncRepository;
            _createAdjustmentHeaderWithoutInvoiceNoBusiness = createAdjustmentHeaderWithoutInvoiceNoBusiness;
            _vnisCoreMongoInvoice02LogRepository = vnisCoreMongoInvoice02LogRepository;
            _updateRootInvoice02ApiBusiness = updateRootInvoice02ApiBusiness;
            _updateAdjustHeaderInvoice02Business = updateAdjustHeaderInvoice02Business;
            _updateAdjustDetailInvoice02Business = updateAdjustDetailInvoice02Business;
            _updateReplaceInvoice02Business = updateReplaceInvoice02Business;
            _repoInvoice02Reference = repoInvoice02Reference;
            _invoice02Service = invoice02Service;
            _factory = factory;
            _settingService = settingService;
            _elasticSearch = elasticSearch;
            _redisCacheService = redisCacheService;
            _taxReport01Repository = taxReport01Repository;
        }

        /// <summary>
        /// Tra cứu hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PagedResultDto<PagingInvoice02ResponseModel>> Query([FromBody] PagingInvoice02RequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// Tra cứu hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("list")]
        public async Task<PagedResultDto<PagingInvoice02ResponseModel>> QueryPost([FromBody] PagingInvoice02RequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// lấy thông hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("info")]
        public async Task<ReadInvoice02ApiResponseModel> ReadAsync([FromQuery] ReadInvoice02ApiRequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create")]
        public async Task<object> Create([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] CreateInvoice02RequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);


                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateInvoice02RequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var indexHeader = 0;
                var indexDetail = 0;

                var group = CurrentTenant.Group;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int i = 0; i < item.InvoiceDetails.Count; i++)
                    {
                        var itemDetail = item.InvoiceDetails[indexDetail];

                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[i];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    Id = Guid.NewGuid(),
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1,
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        })
                                );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);

                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }


            //try
            //{
            //    var tenantId = _factory.CurrentTenant.Id.Value;
            //    request.TemplateNo = infoRequestModel.TemplateNo;
            //    request.SerialNo = infoRequestModel.SerialNo;

            //    var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
            //    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
            //    if (!hasApprove && setting != null && setting.Value == "1")
            //    {
            //        var invoice = new Invoice02HeaderEntity
            //        {
            //            Id = response.Id,
            //            ErpId = request.ErpId,
            //            TransactionId = request.TransactionId,
            //            TemplateNo = request.TemplateNo,
            //            SerialNo = request.SerialNo,
            //            InvoiceNo = response.InvoiceNo,
            //            InvoiceStatus = (short)response.InvoiceStatus,
            //            SignStatus = (short)response.SignStatus,
            //        };
            //        var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

            //        await signService.SignServer(invoice);

            //        response.SignStatus = SignStatus.DaKy;
            //    }

            //    var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(response));
            //    return result;
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    if (ex.Data.Count > 0)
            //    {
            //        return new ResultApiViewModel<object>
            //        {
            //            Code = 15009,
            //            Message = ex.Message,
            //            Succeeded = false,
            //            Data = ex.Data,
            //        }; //TODO: 
            //    }
            //    else
            //    {
            //        return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //    }
            //}
        }

        /// <summary>
        /// tạo hóa đơn 32
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create32")]
        public async Task<object> Create32([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] CreateInvoice02OldDecreeRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var validator = _validatorFactory.GetValidator<CreateInvoice02OldDecreeRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateInvoice02OldDecreeRequestModel, MongoInvoice02Entity>(request) };
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);
                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;

                var indexHeader = 0;
                var indexDetail = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)request.InvoiceStatus;
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ReferenceInvoiceType = (short)request.ReferenceInvoiceType;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus));
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus));

                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceReferenceOld = new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceOldDto
                    {
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? (request.InvoiceReference.InvoiceDate.Date == request.InvoiceReference.InvoiceDate.Date.ToLocalTime() ? request.InvoiceReference.InvoiceDate.Date.AddHours(7) : request.InvoiceReference.InvoiceDate.Date.ToLocalTime()) : request.InvoiceReference.InvoiceDate.Date,
                        InvoiceHeaderId = item.Id,
                        InvoiceNoReference = request.InvoiceReference.InvoiceNo,
                        InvoiceStatus = (short)request.InvoiceStatus.GetHashCode(),
                        Note = request.InvoiceReference.Note,
                        NumberReference = int.Parse(request.InvoiceReference.InvoiceNo),
                        Partition = partition,
                        SerialNoReference = request.InvoiceReference.SerialNo,
                        TemplateNoReference = request.InvoiceReference.TemplateNo,
                        TenantId = tenantId,
                    };

                    foreach (var itemDetail in item.InvoiceDetails)
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();
                var startIndex = 0;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus),
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                return result;



                //TODO: ký tự động
                //var response = await _factory.Mediator.Send(request);

                //var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
                //var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
                //if (!hasApprove && setting != null && setting.Value == "1")
                //{
                //    var invoice = new Invoice02HeaderEntity
                //    {
                //        Id = response.Id,
                //        ErpId = request.ErpId,
                //        TransactionId = request.TransactionId,
                //        TemplateNo = request.TemplateNo,
                //        SerialNo = request.SerialNo,
                //        InvoiceNo = response.InvoiceNo,
                //        InvoiceStatus = (short)response.InvoiceStatus,
                //        SignStatus = (short)response.SignStatus,
                //    };
                //    var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

                //    await signService.SignServer(invoice);

                //    response.SignStatus = SignStatus.DaKy;
                //}

                //var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(response));
                //return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }

            #region old
            //try
            //{
            //    var tenantId = _factory.CurrentTenant.Id.Value;
            //    request.TemplateNo = infoRequestModel.TemplateNo;
            //    request.SerialNo = infoRequestModel.SerialNo;

            //    var validateModel = ObjectMapper.Map<CreateInvoice02OldDecreeRequestModel, CreateInvoice02RequestModel>(request);
            //    var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>(); //TODO: Nam Lê check lại sai model, chưa có validate cho model CreateInvoice02OldDecreeRequestModel
            //    var validationResult = await validator.HandleAsync(validateModel);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
            //    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
            //    if (!hasApprove && setting != null && setting.Value == "1")
            //    {
            //        var invoice = new Invoice02HeaderEntity
            //        {
            //            Id = response.Id,
            //            ErpId = request.ErpId,
            //            TransactionId = request.TransactionId,
            //            TemplateNo = request.TemplateNo,
            //            SerialNo = request.SerialNo,
            //            InvoiceNo = response.InvoiceNo,
            //            InvoiceStatus = (short)response.InvoiceStatus,
            //            SignStatus = (short)response.SignStatus,
            //        };
            //        var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

            //        await signService.SignServer(invoice);

            //        response.SignStatus = SignStatus.DaKy;
            //    }

            //    var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(response));
            //    return result;
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    if (ex.Data.Count > 0)
            //    {
            //        return new ResultApiViewModel<object>
            //        {
            //            Code = 15009,
            //            Message = ex.Message,
            //            Succeeded = false,
            //            Data = ex.Data,
            //        }; //TODO: 
            //    }
            //    else
            //    {
            //        return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //    }
            //}
            #endregion
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-and-sign")]
        public async Task<object> CreateAndSign([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] CreateInvoice02RequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateInvoice02RequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                // Invoice 02 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);

                // Invoice 02 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;

                for (int i = 0; i < data.Count; i++)
                {
                    var item = data[i];
                    item.Id = idInvoiceHeaders[i];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.CreatorId = CurrentUser.Id.Value;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int j = 0; j < item.InvoiceDetails.Count; j++)
                    {
                        var itemDetail = item.InvoiceDetails[j];
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[j];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }
                }

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(L["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                //Ký hóa đơn
                var requestSign = new Invoice02ApiSignServerRequestModel
                {
                    ErpId = request.ErpId
                };
                var validatorSign = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

                var validationSignResult = await validatorSign.HandleAsync(requestSign);
                if (!validationSignResult.Success)
                    throw new UserFriendlyException(L[validationSignResult.Message]);

                var responseSign = await _factory.Mediator.Send(requestSign);
                result.Data.SignStatus = responseSign.SignStatus;
                result.Succeeded = result.Succeeded ? true : false;

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }

            #region old
            //var result = new ResultApiModel<List<object>>
            //{
            //    Data = new List<object>(),
            //};
            //try
            //{
            //    request.TemplateNo = infoRequestModel.TemplateNo;
            //    request.SerialNo = infoRequestModel.SerialNo;

            //    var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);
            //    result.Data.Add(response);

            //    var requestSign = new Invoice02ApiSignServerRequestModel
            //    {
            //        ErpId = response.ErpId
            //    };
            //    var validatorSign = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

            //    var validationSignResult = await validatorSign.HandleAsync(requestSign);
            //    if (!validationSignResult.Success)
            //        throw new UserFriendlyException(L[validationSignResult.Message]);
            //    var responseSign = await _factory.Mediator.Send(requestSign);
            //    result.Data[0] = responseSign;
            //    result.Succeeded = true;
            //    return new ResultApiViewModel<List<object>>(result);
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    if (ex.Data.Count > 0)
            //    {
            //        return new ResultApiViewModel<object>
            //        {
            //            Code = 15009,
            //            Message = ex.Message,
            //            Succeeded = false,
            //            Data = ex.Data,
            //        }; //TODO: 
            //    }
            //    else
            //    {
            //        if (result.Data.Count > 0)
            //        {
            //            return new ResultApiViewModel<object>
            //            {
            //                Code = -1,
            //                Message = ex.Message,
            //                Succeeded = false,
            //                Data = result.Data,
            //            };
            //        }
            //        return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //    }
            //}
            #endregion
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-and-unofficial")]
        public async Task<object> CreateAndUnOfficial([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] CreateInvoice02RequestModel request)
        {
            var isDeleteErp = true;
            var result = new ResultApiModel<List<object>>
            {
                Data = new List<object>(),
            };

            CreateInvoice02ResponseModel invoiceCreated = null;
            var response = new CreateAndUnOfficialInvoice02ResponseModel();
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var stopwatch = new Stopwatch();
                stopwatch.Start();

                var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                stopwatch.Stop();

                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME VALIDATE DATA {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                stopwatch.Start();
                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);
                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME GET APPROVE-CANCEL-DELETE STATUS: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                stopwatch.Start();
                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME GET DATA TO CREATE INVOICE: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                // check sync catalog
                stopwatch.Start();
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());
                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME GET CONFIG SYNC CATALOG: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateInvoice02RequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var indexHeader = 0;
                var indexDetail = 0;

                var group = CurrentTenant.Group;
                stopwatch.Start();
                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int i = 0; i < item.InvoiceDetails.Count; i++)
                    {
                        var itemDetail = item.InvoiceDetails[indexDetail];

                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[i];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME PREPARE DATA TO INSERT MONGO: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    Id = Guid.NewGuid(),
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1,
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                stopwatch.Start();
                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME GENERATE NUMBER: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var responseCreate = new CreateInvoice02ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                };

                response.Id = responseCreate.Id;
                response.ErpId = responseCreate.ErpId;
                response.TransactionId = responseCreate.TransactionId;
                response.TemplateNo = responseCreate.TemplateNo;
                response.SerialNo = responseCreate.SerialNo;
                response.InvoiceNo = responseCreate.InvoiceNo;
                response.InvoiceStatus = responseCreate.InvoiceStatus;
                response.SignStatus = responseCreate.SignStatus;
                result.Data.Add(response);
                invoiceCreated = responseCreate;

                //// check hóa đơn đã được đồng bộ về core hay chưa
                //while (DateTime.Now < maxWaitTime)
                //{
                //    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                //    if (enityMongo?.IsSyncedToCore == 1)
                //    {
                //        break;
                //    }

                //    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                //}

                var requestUnOfficial = new DownloadUnOfficialInvoice02RequestModel
                {
                    ErpId = responseCreate.ErpId,
                    Id = responseCreate.Id
                };

                stopwatch.Start();
                var responseUnOfficial = await _factory.Mediator.Send(requestUnOfficial);
                stopwatch.Stop();
                Log.Fatal($@"CREATE-AND-OFFICIAL: TIME EXPORT PDF: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();
                response.FileUnOfficial = responseUnOfficial.Data;
                result.Data[0] = response;
                result.Succeeded = true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                result.Message = ex.Message;
                result.Succeeded = false;

                if (ex.Data.Count > 0)
                {
                    result.Code = 15009;
                    result.Data.Add(ex.Data);
                }
                else
                {
                    if (result.Data.Count > 0)
                    {
                        result.Code = -1;
                        result.Data = result.Data;
                    }
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }

                try
                {

                    var setting = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.AutoSignServer.ToString());

                    if (setting?.Value == "0")
                    {
                        if (invoiceCreated != null)
                        {
                            var requestSign = new Invoice02ApiSignServerRequestModel
                            {
                                ErpId = invoiceCreated.ErpId
                            };
                            var validatorSign = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

                            var validationSignResult = await validatorSign.HandleAsync(requestSign);
                            if (!validationSignResult.Success)
                                throw new UserFriendlyException(L[validationSignResult.Message]);

                            var responseSign = await _factory.Mediator.Send(requestSign);
                            response.SignStatus = responseSign.SignStatus;
                            result.Data[0] = response;
                            result.Succeeded = result.Succeeded ? true : false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);

                    if (ex.Data.Count == 0)
                    {
                        result.Succeeded = false;
                        result.Code = -1;
                        result.Message = ex.Message;
                    }
                    //result.Message = $"Ký hóa đơn thứ {result.Data.Count + 1} Id = {request.ElementAt(result.Data.Count).ErpId} lỗi: {ex.Message}";
                }
            }
            return new ResultApiViewModel<List<object>>(result);

            #region old
            //var result = new ResultApiModel<List<object>>
            //{
            //    Data = new List<object>(),
            //    Succeeded = true
            //};

            //CreateInvoice02ResponseModel invoiceCreated = null;
            //var response = new CreateAndUnOfficialInvoice02ResponseModel();
            //try
            //{
            //    request.TemplateNo = infoRequestModel.TemplateNo;
            //    request.SerialNo = infoRequestModel.SerialNo;

            //    var validator = _validatorFactory.GetValidator<CreateInvoice02RequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var responseCreate = await _factory.Mediator.Send(request);
            //    response.Id = responseCreate.Id;
            //    response.ErpId = responseCreate.ErpId;
            //    response.TransactionId = responseCreate.TransactionId;
            //    response.TemplateNo = responseCreate.TemplateNo;
            //    response.SerialNo = responseCreate.SerialNo;
            //    response.InvoiceNo = responseCreate.InvoiceNo;
            //    response.InvoiceStatus = responseCreate.InvoiceStatus;
            //    response.SignStatus = responseCreate.SignStatus;
            //    result.Data.Add(response);
            //    invoiceCreated = responseCreate;

            //    var requestUnOfficial = new DownloadUnOfficialInvoice02RequestModel
            //    {
            //        ErpId = responseCreate.ErpId
            //    };

            //    var responseUnOfficial = await _factory.Mediator.Send(requestUnOfficial);
            //    response.FileUnOfficial = responseUnOfficial.Data;
            //    result.Data[0] = response;
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    result.Message = ex.Message;
            //    result.Succeeded = false;

            //    if (ex.Data.Count > 0)
            //    {
            //        result.Code = 15009;
            //        result.Data.Add(ex.Data);
            //    }
            //    else
            //    {
            //        if (result.Data.Count > 0)
            //        {
            //            result.Code = -1;
            //            result.Data = result.Data;
            //        }
            //    }
            //}
            //finally
            //{
            //    try
            //    {
            //        if (invoiceCreated != null)
            //        {
            //            var requestSign = new Invoice02ApiSignServerRequestModel
            //            {
            //                ErpId = invoiceCreated.ErpId
            //            };
            //            var validatorSign = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

            //            var validationSignResult = await validatorSign.HandleAsync(requestSign);
            //            if (!validationSignResult.Success)
            //                throw new UserFriendlyException(L[validationSignResult.Message]);

            //            var responseSign = await _factory.Mediator.Send(requestSign);

            //            response.SignStatus = responseSign.SignStatus;
            //            result.Data[0] = response;
            //            result.Succeeded = result.Succeeded ? true : false;
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error(ex, ex.Message);

            //        if (ex.Data.Count == 0)
            //        {
            //            result.Succeeded = false;
            //            result.Code = -1;
            //            result.Message = ex.Message;
            //        }
            //        //result.Message = $"Ký hóa đơn thứ {result.Data.Count + 1} Id = {request.ElementAt(result.Data.Count).ErpId} lỗi: {ex.Message}";
            //    }
            //}

            //return new ResultApiViewModel<List<object>>(result);
            #endregion
        }

        /// <summary>
        /// duyệt hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("approve/{erpId}")]
        public async Task<object> Approve([FromRoute] string erpId)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var request = new ApproveInvoice02ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<ApproveInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var userId = _validationContext.GetItem<Guid>("UserId");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

                // mongoDB
                var invoiceMongoDB = await _mongoInvoice02Repository.GetById(invoice.Id);
                if (invoiceMongoDB == null)
                    throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                var entityUpdateMongo = new MongoInvoice02Entity();
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                    }
                }

                entityUpdateMongo.ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode();
                entityUpdateMongo.ApprovedId = userId;
                entityUpdateMongo.ApprovedTime = DateTime.Now;
                entityUpdateMongo.FullNameApprover = userFullName;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncApprove.GetHashCode();
                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo, true);

                try
                {
                    // oracle DB
                    if (invoice == null)
                        throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                    var queryHeader = $"Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                                 $"Set \"ApproveStatus\" = :ApproveStatus, \"ApprovedId\" = :ApprovedId, \"ApprovedTime\" = :ApprovedTime, \"FullNameApprover\" = :FullNameApprover " +
                                 $"Where \"Id\" = :Id";

                    await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                    {
                        Id = invoice.Id,
                        ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode(),
                        ApprovedId = OracleExtension.ConvertGuidToRaw(userId),
                        ApprovedTime = DateTime.Now,
                        FullNameApprover = userFullName,
                    });

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Approve.GetHashCode(),
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongoDB);
                    Log.Error(ex.Message);
                }


                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region
            //try
            //{
            //    var request = new ApproveInvoice02ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };
            //    var validator = _validatorFactory.GetValidator<ApproveInvoice02ApiRequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    return new ResultApiViewModel<ApproveInvoice02ApiResponseModel>(new ResultApiModel<ApproveInvoice02ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }

        /// <summary>
        /// duyệt và ký hoá đơn
        /// </summary>
        /// <param name="idErp"></param>
        /// <returns></returns>
        [HttpPost("approve-and-sign/{erpId}")]
        public async Task<object> ApproveAndSign([FromRoute] string erpId)
        {
            try
            {
                var requestApproveAndSign = new ApproveAndSignInvoice02ApiRequestModel
                {
                    ErpId = erpId
                };

                var validatorApprove = _validatorFactory.GetValidator<ApproveAndSignInvoice02ApiRequestModel>();
                var validationResultApprove = await validatorApprove.HandleAsync(requestApproveAndSign);
                if (!validationResultApprove.Success)
                    throw new UserFriendlyException(L[validationResultApprove.Message]);

                var userId = _validationContext.GetItem<Guid>("UserId");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<MongoInvoice02Entity>("Invoice");

                // mongoDB
                var invoiceMongoDB = await _mongoInvoice02Repository.GetById(invoice.Id);
                if (invoiceMongoDB == null)
                    throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                var entityUpdateMongo = new MongoInvoice02Entity();
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                    }
                }

                entityUpdateMongo.ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode();
                entityUpdateMongo.ApprovedId = userId;
                entityUpdateMongo.ApprovedTime = DateTime.Now;
                entityUpdateMongo.FullNameApprover = userFullName;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncApprove.GetHashCode();
                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo, true);

                try
                {
                    // oracle DB
                    if (invoice == null)
                        throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                    var queryHeader = $"Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                                 $"Set \"ApproveStatus\" = :ApproveStatus, \"ApprovedId\" = :ApprovedId, \"ApprovedTime\" = :ApprovedTime, \"FullNameApprover\" = :FullNameApprover " +
                                 $"Where \"Id\" = :Id";

                    await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                    {
                        Id = invoice.Id,
                        ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode(),
                        ApprovedId = OracleExtension.ConvertGuidToRaw(userId),
                        ApprovedTime = DateTime.Now,
                        FullNameApprover = userFullName,
                    });

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Approve,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongoDB);
                    Log.Error(ex.Message);
                }

                // Ký
                var requestSign = new Invoice02ApiSignServerRequestModel
                {
                    ErpId = erpId
                };

                var responseSign = await _factory.Mediator.Send(requestSign);

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(responseSign.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }

            #region
            //try
            //{
            //    var requestApproveAndSign = new ApproveAndSignInvoice02ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };
            //    var validatorApprove = _validatorFactory.GetValidator<ApproveAndSignInvoice02ApiRequestModel>();
            //    var validationResultApprove = await validatorApprove.HandleAsync(requestApproveAndSign);
            //    if (validationResultApprove.Success)
            //    {
            //        //Duyêt
            //        var requestApprove = new ApproveInvoice02ApiRequestModel
            //        {
            //            ErpId = erpId
            //        };

            //        var responseApprove = await _factory.Mediator.Send(requestApprove);
            //    }
            //    //var repoInvoice02Header = _factory.GetServiceDependency<IInvoiceHeaderRepository<Invoice02HeaderEntity>>();
            //    //var invoice = await repoInvoice02Header.GetByErpIdAsync(_factory.CurrentTenant.Id.Value, erpId);

            //    //Ký
            //    var requestSign = new Invoice02ApiSignServerRequestModel
            //    {
            //        ErpId = erpId
            //    };

            //    var responseSign = await _factory.Mediator.Send(requestSign);
            //    return new ResultApiViewModel<Invoice02ApiSignServerResponseModel>(new ResultApiModel<Invoice02ApiSignServerResponseModel>(responseSign));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }


        /// <summary>
        /// tạo hóa đơn theo lô
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch")]
        public async Task<object> CreateBatchAsync([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] List<CreateInvoice02RequestModel> request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                foreach (var item in request)
                {
                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                }

                var requestBatch = new CreateBatchInvoice02RequestModel
                {
                    Datas = request.Select(x => new CreateBatchInvoice02InfoRequestModel
                    {
                        Invoice = x
                    }).ToList()
                };
                var validator = _validatorFactory.GetValidator<CreateBatchInvoice02RequestModel>();
                var validationResult = await validator.HandleAsync(requestBatch);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);


                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("Templates").FirstOrDefault();
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var toCurrencies = _validationContext.GetItem<List<CurrencyEntity>>("ToCurrencies").ToDictionary(x => x.CurrencyCode, x => x);

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = _factory.ObjectMapper.Map<List<CreateInvoice02RequestModel>, List<MongoInvoice02Entity>>(request);


                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                // Invoice 02 Header
                var idInvoiceHeaders = await GetSEQsNextVal(request.Count, SequenceName.Invoice02Header);

                // Invoice 02 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);


                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                var totalItems = request.Count;

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;

                foreach (var item in data)
                {
                    if (!string.IsNullOrEmpty(item.BuyerEmail))
                    {
                        if (!item.BuyerEmail.IsEmails(';') || item.BuyerEmail.Count(x => x == '@') > 1)
                        {
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                        }
                    }

                    var toCurrency = toCurrencies[item.ToCurrency];
                    var partition = long.Parse(item.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = totalItems;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = totalItems
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= totalItems)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;


                var timeEstimatesSeconds = data.Count / 5;
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1s
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberInBatch);
                if (timeDelayInvoiceGenerateNumberInBatch < 1)
                {
                    // nếu thời gian chờ nhỏ hơn 1s => set default = 1s, ngược lại nếu thời gian chờ > 1s thì set bằng timeEstimatesSeconds
                    if (timeEstimatesSeconds < 1)
                        timeDelayInvoiceGenerateNumberInBatch = 1;
                    else timeDelayInvoiceGenerateNumberInBatch = timeEstimatesSeconds;
                }

                // thời delay mỗi lần kiểm tra trạng thái dữ liệu đã sinh số chưa
                var timeDelayLoopInvoiceGenerateNumberMany = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberMany"], out timeDelayLoopInvoiceGenerateNumberMany);

                var timeEstimatesMinutes = data.Count / 10;
                // tổng số thời gian chờ sinh số : default = 1 phút
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberMany);
                if (timeDelayInvoiceGenerateNumberMany < 1)
                {
                    // nếu thời gian chờ nhỏ hơn 1 phút => set default = 1 phút, ngược lại nếu thời gian chờ > 1 phút thì set bằng timeEstimatesMinutes
                    if (timeEstimatesMinutes < 1)
                        timeDelayInvoiceGenerateNumberMany = 1;
                    else timeDelayInvoiceGenerateNumberMany = timeEstimatesMinutes;
                }

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumberMany);

                var enityMongo = new MongoInvoice02Entity();

                await Task.Delay(TimeSpan.FromSeconds(timeDelayInvoiceGenerateNumberInBatch));

                while (DateTime.Now < maxWaitTime)
                {
                    var isSuscess = await _mongoInvoice02BatchIdRepository.GetByBatchIdDoneGenerateNumberAsync(batchId);

                    if (isSuscess)
                        break;

                    await Task.Delay(TimeSpan.FromSeconds(timeDelayLoopInvoiceGenerateNumberMany));
                }

                var enityMongos = await _mongoInvoice02Repository.GetByIdsAsync(idInvoiceHeaders);

                var result = new List<CreateInvoice02ResponseModel>();

                foreach (var itemMongo in enityMongos)
                {
                    var item = new CreateInvoice02ResponseModel
                    {
                        Id = itemMongo.Id,
                        ErpId = itemMongo.ErpId,
                        InvoiceNo = itemMongo.InvoiceNo,
                        InvoiceStatus = InvoiceStatus.Goc,
                        SerialNo = itemMongo.SerialNo,
                        SignStatus = SignStatus.ChoKy,
                        TemplateNo = itemMongo.TemplateNo,
                        TransactionId = itemMongo.TransactionId
                    };
                    result.Add(item);
                }

                return new ResultApiViewModel<object>
                {
                    Code = 0,
                    Message = "Thành công",
                    Succeeded = true,
                    Data = result,
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongos = _validationContext.GetItem<List<MongoInvoice02ErpIdEntity>>("InvoiceErpIds");

                        if (erpIdMongos != null)
                            await _mongoInvoice02ErpIdRepository.DeleteManyAsync(erpIdMongos);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn theo lô và ký
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch-and-sign")]
        public async Task<object> CreateBatchAndSignAsync([FromQuery] CreateInvoice02InfoRequestModel infoRequestModel, [FromBody] List<CreateInvoice02RequestModel> request)
        {
            var isDeleteErp = true;
            var result = new ResultApiModel<List<CreateInvoice02ResponseModel>>
            {
                Data = new List<CreateInvoice02ResponseModel>(),
                Succeeded = true
            };

            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                foreach (var item in request)
                {
                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                }

                var requestBatch = new CreateBatchInvoice02RequestModel
                {
                    Datas = request.Select(x => new CreateBatchInvoice02InfoRequestModel
                    {
                        Invoice = x
                    }).ToList()
                };
                var validator = _validatorFactory.GetValidator<CreateBatchInvoice02RequestModel>();
                var validationResult = await validator.HandleAsync(requestBatch);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("Templates").FirstOrDefault();
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrencies = _validationContext.GetItem<List<CurrencyEntity>>("ToCurrencies").ToDictionary(x => x.CurrencyCode, x => x);
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = _factory.ObjectMapper.Map<List<CreateInvoice02RequestModel>, List<MongoInvoice02Entity>>(request);

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);


                // Invoice 02 Header
                var idInvoiceHeaders = await GetSEQsNextVal(request.Count, SequenceName.Invoice02Header);

                // Invoice 02 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var totalItems = request.Count;

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;

                foreach (var item in data)
                {
                    if (!string.IsNullOrEmpty(item.BuyerEmail))
                    {
                        if (!item.BuyerEmail.IsEmails(';') || item.BuyerEmail.Count(x => x == '@') > 1)
                        {
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                        }
                    }

                    var partition = long.Parse(item.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                    var toCurrency = toCurrencies[item.ToCurrency];

                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = fromCurrency.CurrencyCode;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = totalItems;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= totalItems)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = totalItems
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);



                // phải chờ sinh số xong mới call api ký đc
                while (true)
                {
                    var isSuscess = await _mongoInvoice02BatchIdRepository.GetByBatchIdDoneGenerateNumberAsync(batchId);
                    if (isSuscess)
                        break;

                    var isGenNumberErr = await _mongoInvoice02BatchIdRepository.GetByBatchIdGenerateNumberErrorAsync(batchId);
                    if (isGenNumberErr)
                        break;

                    // await Task.Delay(TimeSpan.FromSeconds(timeDelayLoopInvoiceGenerateNumberMany));
                }

                var enityMongos = await _mongoInvoice02Repository.GetByIdsAsync(idInvoiceHeaders);

                var indexMongos = enityMongos.ToDictionary(x => x.Id);
                foreach (var item in data)
                {
                    result.Data.Add(new CreateInvoice02ResponseModel
                    {
                        Id = item.Id,
                        ErpId = item.ErpId,
                        InvoiceNo = indexMongos.ContainsKey(item.Id) ? indexMongos[item.Id].InvoiceNo : null,
                        InvoiceStatus = InvoiceStatus.Goc,
                        SerialNo = item.SerialNo,
                        SignStatus = SignStatus.ChoKy,
                        TemplateNo = item.TemplateNo,
                        TransactionId = item.TransactionId
                    });
                }


                // TODO: refactor ký nhiều
                foreach (var item in result.Data)
                {
                    var requestSign = new Invoice02ApiSignServerRequestModel
                    {
                        ErpId = item.ErpId
                    };
                    var validatorSign = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

                    var validationSignResult = await validatorSign.HandleAsync(requestSign);
                    if (!validationSignResult.Success)
                    {
                        throw new UserFriendlyException(L[validationSignResult.Message]);
                    }
                    else
                    {
                        var response = await _factory.Mediator.Send(requestSign);

                        item.SignStatus = response.SignStatus;
                    }
                }

                result.Message = "Thành công";
                result.Succeeded = true;

                Log.Information($@"Result: {result.JsonSerialize()}");

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    };
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongos = _validationContext.GetItem<List<MongoInvoice02ErpIdEntity>>("InvoiceErpIds");

                        if (erpIdMongos != null)
                            await _mongoInvoice02ErpIdRepository.DeleteManyAsync(erpIdMongos);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// Tạo hóa đơn thay thế 
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("replace")]
        public async Task<object> CreateReplace([FromBody] CreateReplaceInvoice02ApiRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<CreateReplaceInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var rootInvoice = _validationContext.GetItem<Invoice02HeaderEntity>("InvoiceReference");
                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var rootInvoiceDetails = await _repoInvoice02Detail.QueryByIdInvoiceHeaderRawAsync(CurrentTenant.Id.Value, rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateReplaceInvoice02ApiRequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);


                // Invoice 02 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);

                // Invoice 02 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.ThayThe;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerCode = request.BuyerCode;
                    item.BuyerFullName = request.BuyerFullName;
                    item.BuyerLegalName = request.BuyerLegalName;
                    item.BuyerTaxCode = request.BuyerTaxCode;
                    item.BuyerAddressLine = request.BuyerAddressLine;
                    item.BuyerDistrictName = request.BuyerDistrictName;
                    item.BuyerCityName = request.BuyerCityName;
                    item.BuyerCountryCode = request.BuyerCountryCode;
                    item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                    item.BuyerFaxNumber = request.BuyerFaxNumber;
                    item.BuyerEmail = request.BuyerEmail?.Trim();
                    item.BuyerBankName = request.BuyerBankName;
                    item.BuyerBankAccount = request.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = request.Note;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    item.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiThayThe.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var timeDelayInvoiceGenerateNumber = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumber"], out timeDelayInvoiceGenerateNumber);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();

                var isError = true;
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                while (!string.IsNullOrEmpty(enityMongo?.InvoiceNo) || DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;

                    }

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var invoiceReferenceUpdateEs = new List<Invoice02ReferenceEntity>
                {
                    new Invoice02ReferenceEntity
                    {
                        Id = rootInvoice.Id,
                        InvoiceNoReference = enityMongo.InvoiceNo,
                        InvoiceDateReference = enityMongo.InvoiceDate,
                        SerialNoReference = enityMongo.SerialNo,
                        TemplateNoReference = enityMongo.TemplateNo,
                        NumberReference = enityMongo.Number ?? 0
                    }
                };

                await InvoiceReSyncToMongo(invoiceReferenceUpdateEs.Select(x => x.Id).ToList(), InvoiceStatus.BiThayThe);
                await UpdateESInvoiceReferenceAsync(invoiceReferenceUpdateEs, InvoiceStatus.BiThayThe);
                //await InvoiceReSyncToEs(new List<long> { rootInvoice.Id });

                // TODO: Ký hóa đơn

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.ThayThe,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo
                }));

                return result;

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn điều chỉnh tăng giảm
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("adjustment-detail")]
        public async Task<object> CreateAdjustmentDetail([FromBody] CreateAdjustmentDetailInvoice02ApiRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                var validator = _validatorFactory.GetValidator<CreateAdjustmentDetailInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var fromCurrency = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.FromCurrency.GetHashCode());
                var toCurrencyData = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
                var toCurrency = new CurrencyEntity
                {
                    CurrencyCode = toCurrencyData.ToCurrencyCode,
                    Conversion = toCurrencyData.ToCurrencyConversion,
                    Rounding = toCurrencyData.ToCurrencyRounding,
                    NameVi = toCurrencyData.NameVi,
                    MinimumNameVi = toCurrencyData.MinimumNameVi,
                    NameEn = toCurrencyData.NameEn,
                    MinimumNameEn = toCurrencyData.MinimumNameEn
                };

                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var rootInvoice = _validationContext.GetItem<Invoice02HeaderEntity>("InvoiceReference");

                var rootInvoiceDetails = await _repoInvoice02Detail.QueryByIdInvoiceHeaderRawAsync(tenantId, rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckCreateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateAdjustmentDetailInvoice02ApiRequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.StoreCode = rootInvoice.StoreCode;
                    item.StoreName = rootInvoice.StoreName;
                    item.BudgetUnitCode = rootInvoice.BudgetUnitCode;
                    item.BuyerIDNumber = rootInvoice.BuyerIDNumber;
                    item.BuyerPassportNumber = rootInvoice.BuyerPassportNumber;

                    item.BuyerCode = rootInvoice.BuyerCode;
                    item.BuyerFullName = rootInvoice.BuyerFullName;
                    item.BuyerLegalName = rootInvoice.BuyerLegalName;
                    item.BuyerTaxCode = rootInvoice.BuyerTaxCode;
                    item.BuyerAddressLine = rootInvoice.BuyerAddressLine;
                    item.BuyerDistrictName = rootInvoice.BuyerDistrictName;
                    item.BuyerCityName = rootInvoice.BuyerCityName;
                    item.BuyerCountryCode = rootInvoice.BuyerCountryCode;
                    item.BuyerPhoneNumber = rootInvoice.BuyerPhoneNumber;
                    item.BuyerFaxNumber = rootInvoice.BuyerFaxNumber;
                    item.BuyerEmail = rootInvoice.BuyerEmail?.Trim();
                    item.BuyerBankName = rootInvoice.BuyerBankName;
                    item.BuyerBankAccount = rootInvoice.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = rootInvoice.Note;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);

                    item.InvoiceStatus = (short)InvoiceStatus.DieuChinhTangGiam.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.RoundingCurrency = rootInvoice.RoundingCurrency;
                    item.FromCurrency = rootInvoice.FromCurrency;
                    item.CurrencyConversion = rootInvoice.CurrencyConversion;
                    item.ToCurrency = rootInvoice.ToCurrency;
                    item.ExchangeRate = rootInvoice.ExchangeRate;
                    item.PaymentMethod = rootInvoice.PaymentMethod;

                    item.FromCurrency = fromCurrency.FromCurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    item.ExtraProperties = rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any()
                                            ? rootInvoice.ExtraProperties : null;
                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhTangGiam.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = tenantId
                    };

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        if (!indexes.ContainsKey(itemDetail.Index))
                            //throw new Exception("Thông tin điều chỉnh tăng giảm chi tiết hóa đơn không đúng");
                            throw new Exception(_localizier["Vnis.BE.Invoice02.IndexInvoiceDetailIncorrect"]);

                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                        var rootDetail = indexes[itemDetail.Index];

                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.RoundingUnit = rootDetail.RoundingUnit;
                        itemDetail.UnitId = rootDetail.UnitId;
                        itemDetail.UnitName = rootDetail.UnitName;
                        itemDetail.ProductName = rootDetail.ProductName;
                        itemDetail.ProductType = rootDetail.ProductType;
                        itemDetail.ProductCode = rootDetail.ProductCode;
                        itemDetail.ProductId = rootDetail.ProductId;
                        itemDetail.HideQuantity = rootDetail.HideQuantity;
                        itemDetail.HideUnit = rootDetail.HideUnit;
                        itemDetail.HideUnitPrice = rootDetail.HideUnitPrice;
                        itemDetail.Partition = partition;
                        itemDetail.ExtraProperties = rootDetail.ExtraProperties;

                        indexDetail++;
                    });

                    item.DiscountType = rootInvoice.DiscountType;

                    indexDetail++;
                }
                //data.ForEach(async item =>
                //{

                //});

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var invoiceReferenceUpdateEs = new List<Invoice02ReferenceEntity>
                {
                    new Invoice02ReferenceEntity
                    {
                        Id = rootInvoice.Id,
                        InvoiceNoReference = enityMongo.InvoiceNo,
                        InvoiceDateReference = enityMongo.InvoiceDate,
                        SerialNoReference = enityMongo.SerialNo,
                        TemplateNoReference = enityMongo.TemplateNo,
                        NumberReference = enityMongo.Number ?? 0
                    }
                };

                await InvoiceReSyncToMongo(invoiceReferenceUpdateEs.Select(x => x.Id).ToList(), InvoiceStatus.BiDieuChinhTangGiam);
                await UpdateESInvoiceReferenceAsync(invoiceReferenceUpdateEs, InvoiceStatus.BiDieuChinhTangGiam);
                //await InvoiceReSyncToEs(new List<long> { rootInvoice.Id });

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhTangGiam,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo,
                    TransactionId = rootInvoice.TransactionId
                }));
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// Tạo hóa đơn điều chỉnh định danh 
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header")]
        public async Task<object> CreateAdjustmentHeader([FromBody] CreateAdjustmentHeaderInvoice02ApiRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var rootInvoice = _validationContext.GetItem<Invoice02HeaderEntity>("InvoiceReference");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");

                var rootInvoiceDetails = await _repoInvoice02Detail.QueryByIdInvoiceHeaderRawAsync(CurrentTenant.Id.Value, rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice02Entity> { _factory.ObjectMapper.Map<CreateAdjustmentHeaderInvoice02ApiRequestModel, MongoInvoice02Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                // Invoice 02 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice02Header);

                // Invoice 02 Detail
                var totalItemsDetail = rootInvoiceDetails.Count;
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);


                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;


                data.ForEach(item =>
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;
                    item.SerialNo = request.SerialNo;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerCode = request.BuyerCode;
                    item.BuyerFullName = request.BuyerFullName;
                    item.BuyerLegalName = request.BuyerLegalName;
                    item.BuyerTaxCode = request.BuyerTaxCode;
                    item.BuyerAddressLine = request.BuyerAddressLine;
                    item.BuyerDistrictName = request.BuyerDistrictName;
                    item.BuyerCityName = request.BuyerCityName;
                    item.BuyerCountryCode = request.BuyerCountryCode;
                    item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                    item.BuyerFaxNumber = request.BuyerFaxNumber;
                    item.BuyerEmail = request.BuyerEmail?.Trim();
                    item.BuyerBankName = request.BuyerBankName;
                    item.BuyerBankAccount = request.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = request.Note;

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    //item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    //item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.PaymentAmountWords = rootInvoice.PaymentAmountWords;
                    item.PaymentAmountWordsEn = rootInvoice.PaymentAmountWordsEn;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.RoundingCurrency = rootInvoice.RoundingCurrency;
                    item.FromCurrency = rootInvoice.FromCurrency;
                    item.CurrencyConversion = rootInvoice.CurrencyConversion;
                    item.ToCurrency = rootInvoice.ToCurrency;
                    item.ExchangeRate = rootInvoice.ExchangeRate;
                    item.PaymentMethod = request.PaymentMethod;
                    item.TotalAmount = rootInvoice.TotalAmount;
                    item.TotalPaymentAmount = rootInvoice.TotalPaymentAmount;


                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    //item.ExtraProperties = rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any()
                    //    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    //    {
                    //                                            { "invoiceHeaderExtras", rootInvoice.ExtraProperties.JsonSerialize()},
                    //    }) : null;

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    item.PaymentAmountWords = rootInvoice.PaymentAmountWords;
                    item.PaymentAmountWordsEn = rootInvoice.PaymentAmountWordsEn;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    item.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };

                    var invoiceDetails = new List<Invoice02DetailDto>();

                    rootInvoiceDetails.ForEach(itemDetail =>
                    {
                        if (!indexes.ContainsKey(itemDetail.Index))
                            throw new Exception("Thông tin chi tiết hóa đơn gốc không đúng");

                        var rootDetail = indexes[itemDetail.Index];
                        invoiceDetails.Add(new Invoice02DetailDto
                        {
                            Id = idInvoiceDetails[indexDetail],
                            Index = rootDetail.Index,
                            Note = rootDetail.Note,
                            InvoiceHeaderId = item.Id,
                            TenantId = CurrentTenant.Id.Value,
                            RoundingUnit = rootDetail.RoundingUnit,
                            UnitPrice = rootDetail.UnitPrice,
                            PaymentAmount = rootDetail.PaymentAmount,
                            Amount = rootDetail.Amount,
                            UnitId = rootDetail.UnitId,
                            UnitName = rootDetail.UnitName,
                            ProductName = rootDetail.ProductName,
                            ProductType = rootDetail.ProductType,
                            ProductCode = rootDetail.ProductCode,
                            ProductId = rootDetail.ProductId,
                            HideQuantity = rootDetail.HideQuantity,
                            HideUnit = rootDetail.HideUnit,
                            HideUnitPrice = rootDetail.HideUnitPrice,
                            Partition = partition,
                            Quantity = rootDetail.Quantity,
                            IsPromotion = rootDetail.IsPromotion,
                            ExtraProperties = rootDetail.ExtraProperties != null && rootDetail.ExtraProperties.Any()
                                        ? rootDetail.ExtraProperties : null
                        });

                        indexDetail++;

                        item.InvoiceDetails = invoiceDetails;
                    });

                    item.DiscountType = rootInvoice.DiscountType;

                    indexHeader++;
                });

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice02BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice02BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice02BatchIdRepository.InsertManyAsync(invoice02BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice02HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var timeDelayInvoiceGenerateNumber = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumber"], out timeDelayInvoiceGenerateNumber);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice02Entity();
                var isError = true;
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                while (!string.IsNullOrEmpty(enityMongo?.InvoiceNo) || DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice02Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;

                    }

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                }

                if (isError)
                {
                    invoice02BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice02BatchIdRepository.UpdateManyAsync(invoice02BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.GenerateNumberFail"]);
                }

                var invoiceReferenceUpdateEs = new List<Invoice02ReferenceEntity>
                {
                    new Invoice02ReferenceEntity
                    {
                        Id = rootInvoice.Id,
                        InvoiceNoReference = enityMongo.InvoiceNo,
                        InvoiceDateReference = enityMongo.InvoiceDate,
                        SerialNoReference = enityMongo.SerialNo,
                        TemplateNoReference = enityMongo.TemplateNo,
                        NumberReference = enityMongo.Number ?? 0
                    }
                };

                await InvoiceReSyncToMongo(invoiceReferenceUpdateEs.Select(x => x.Id).ToList(), InvoiceStatus.BiDieuChinhDinhDanh);
                await UpdateESInvoiceReferenceAsync(invoiceReferenceUpdateEs, InvoiceStatus.BiDieuChinhDinhDanh);
                //await InvoiceReSyncToEs(new List<long> { rootInvoice.Id });

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo
                }));

                return result;

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice02ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice02ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// Xóa hủy hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("cancel/{erpId}")]
        public async Task<object> CancelAsync([FromRoute] string erpId)
        {
            try
            {
                var request = new CancelInvoice02ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<CancelInvoice02ApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");


                var hasApproveDelete = await _invoiceService.HasApproveCancelAsync(tenantId);

                // MongoDB
                var invoiceMongoDB = await _mongoInvoice02Repository.GetById(invoice.Id);
                if (invoiceMongoDB != null)
                {
                    var entityUpdateMongo = new MongoInvoice02Entity();
                    var input = request;
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                        }
                    }

                    if (hasApproveDelete)
                        entityUpdateMongo.ApproveStatus = (short)ApproveStatus.ChoDuyet;
                    else
                        entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.XoaHuy;

                    entityUpdateMongo.CancelTime = DateTime.Now;
                    entityUpdateMongo.CancelId = userId;
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncCancel.GetHashCode();
                    await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);
                    //throw new UserFriendlyException($"Không tìm thấy hóa đơn");
                }



                var response = new InvoiceCommandResponseModel();

                try
                {
                    if (hasApproveDelete)
                    {
                        var query = $@"UPDATE ""Invoice02Header"" SET
                                  ""ApproveCancelStatus"" = {(short)ApproveStatus.ChoDuyet},
                                  ""CancelTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""CancelId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                        await InvoiceReSyncToEs(new List<long> { invoice.Id });
                    }
                    else
                    {
                        var query = $@"UPDATE ""Invoice02Header"" SET
                                  ""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy},
                                  ""CancelTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""CancelId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                        await InvoiceReSyncToEs(new List<long> { invoice.Id });
                    }

                    response = new InvoiceCommandResponseModel
                    {
                        Id = invoice.Id,
                        TenantId = tenantId,
                        Type = VnisType._02GTTT,
                        UserId = userId,
                        UserFullName = userFullName,
                        UserName = userName,
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        Resource = InvoiceSource.Api,
                        State = InvoiceActionState.Cancel,
                        ActionLogInvoice = ActionLogInvoice.Cancel,
                        Action = InvoiceAction.Cancel,
                        //ConnectionId = message.ConnectionId,
                        InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                        {
                            OldInvoiceDate = invoice.InvoiceDate,
                            OldTotalAmount = invoice.TotalAmount,
                            OldTotalPaymentAmount = invoice.TotalPaymentAmount
                        }
                    };

                    invoice.CancelId = userId;

                    //update lại ngày hóa đơn cuối cùng
                    await _invoiceService.UpdateLastInvoiceDateAfterCancelDeleteRawAsync(tenantId, invoice);

                    response.Method = HubMethod.InvoiceStatus;
                    response.InvoiceStatus = InvoiceStatus.XoaHuy;
                    response.ApproveStatus = ApproveStatus.KhongQuyTrinhDuyet;
                    response.ActionAt = DateTime.Now;
                    response.ActionAtUtc = DateTime.UtcNow;
                }
                catch (Exception ex)
                {
                    if (invoiceMongoDB != null)
                        await _mongoInvoice02Repository.UpdateAsync(invoiceMongoDB);

                    Log.Error(ex.Message);
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = response.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = response.InvoiceNo,
                    InvoiceStatus = response.InvoiceStatus,
                    SerialNo = response.SerialNo,
                    SignStatus = response.SignStatus,
                    TemplateNo = response.TemplateNo,
                    TransactionId = invoiceMongoDB.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }

            #region old
            //try
            //{
            //    var request = new CancelInvoice02ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };

            //    var validator = _validatorFactory.GetValidator<CancelInvoice02ApiRequestModel>();

            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    return new ResultApiViewModel<CancelInvoice02ApiResponseModel>(new ResultApiModel<CancelInvoice02ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }

        /// <summary>
        /// Xóa hóa đơn
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [HttpPost("delete/{erpId}")]
        public async Task<object> DeleteAsync([FromRoute] string erpId)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var request = new DeleteInvoice02ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<DeleteInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

                var hasApproveDelete = await _invoiceService.HasApproveDeleteAsync(CurrentTenant.Id.Value);
                // MongoDB
                var invoiceMongoDB = await _mongoInvoice02Repository.GetById(invoice.Id);
                if (invoiceMongoDB != null)
                {
                    var entityUpdateMongo = new MongoInvoice02Entity();
                    var input = request;
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                        }
                    }

                    if (hasApproveDelete)
                    {
                        entityUpdateMongo.ApproveStatus = (short)ApproveStatus.ChoDuyet;
                    }
                    else
                    {
                        entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.XoaBo;
                    }

                    entityUpdateMongo.DeleteTime = DateTime.Now;
                    entityUpdateMongo.DeleteId = userId;
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncDelete.GetHashCode();
                    await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);

                    //throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để xóa");
                }


                var invoiceStatus = (short)ApproveStatus.ChoDuyet;
                try
                {
                    if (hasApproveDelete)
                    {
                        var query = $@"UPDATE ""Invoice02Header"" SET
                                  ""ApproveDeleteStatus"" = {(short)ApproveStatus.ChoDuyet},
                                  ""DeleteTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""DeleteId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                        await InvoiceReSyncToEs(new List<long> { invoice.Id });
                        invoiceStatus = (short)ApproveStatus.ChoDuyet;
                    }
                    else
                    {
                        var query = $@"UPDATE ""Invoice02Header"" SET
                                  ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo},
                                  ""DeleteTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""DeleteId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                        await InvoiceReSyncToEs(new List<long> { invoice.Id });
                        invoiceStatus = (short)InvoiceStatus.XoaBo;

                        #region Xóa hóa đơn khỏi BTH theo ND70
                        // Xóa hóa đơn khỏi bảng tổng hợp
                        var detailMappingEntities = _validationContext.GetItem<List<TaxReport01DetailMappingEntity>>("TaxReport01DetailMappings");
                        if (detailMappingEntities != null && detailMappingEntities.Any())
                        {
                            foreach (var item in detailMappingEntities)
                            {
                                // Lấy danh sách id hóa đơn thuộc bảng tổng hợp
                                var invoiceIds = item.InvoiceIds?.Split(',').Select(long.Parse).ToList() ?? new List<long>();

                                // Nếu id hóa đơn thuộc bảng tổng hợp đã tồn tại trong danh sách hóa đơn đang xóa
                                if (invoiceIds.Any(x => x == invoice.Id))
                                {
                                    try
                                    {
                                        invoiceIds.Remove(invoice.Id);

                                        await _taxReport01Repository.UpdateInvoiceHeadersInDetailMapping(item.Id, invoiceIds, CurrentTenant.Id.Value);
                                    }
                                    catch (Exception)
                                    {
                                        // Không cần bắn ra exception vì khi 
                                        Log.Error($"Xóa hóa đơn khỏi bảng tổng hợp lỗi: {item.Id} - {invoice.Id}");
                                    }
                                }
                            }
                        }
                        #endregion
                    }

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Delete,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    if (invoiceMongoDB != null)
                        await _mongoInvoice02Repository.UpdateAsync(invoiceMongoDB);

                    Log.Error(ex.Message);
                    throw new UserFriendlyException($"{ex.Message}");
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region
            //try
            //{
            //    var request = new DeleteInvoice02ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };
            //    var validator = _validatorFactory.GetValidator<DeleteInvoice02ApiRequestModel>();

            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);
            //    return new ResultApiViewModel<DeleteInvoice02ApiResponseModel>(new ResultApiModel<DeleteInvoice02ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }

        /// <summary>
        /// tạo hóa đơn điều chỉnh định danh không sinh số
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header-without-invoiceNo")]
        public async Task<object> CreateAdjustmentHeaderWithoutInvoiceNo(CreateAdjustmentHeaderWithoutInvoiceNoRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                var partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var invoiceMongo = await _mongoInvoice02Repository.GetById(invoice.Id);
                if (invoiceMongo != null)
                {
                    var entityUpdateMongo = new MongoInvoice02Entity();
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                        }
                    }

                    entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;
                    entityUpdateMongo.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02ReferenceDto
                    {
                        InvoiceHeaderId = invoice.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? invoice.InvoiceDate.Date.ToLocalTime() : invoice.InvoiceDate.Date,
                        InvoiceNoReference = invoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode(),
                        InvoiceReferenceId = invoice.Id,
                        NumberReference = invoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = invoice.SerialNo,
                        TemplateNoReference = invoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                    // sync catalog
                    entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);
                }

                try
                {
                    var message = new InvoiceCommandRequestModel
                    {
                        Resource = InvoiceSource.Api,
                        InvoiceId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        TenantCode = CurrentTenant.TenantCode,
                        TaxCode = CurrentTenant.TaxCode,
                        Address = CurrentTenant.Address,
                        Country = CurrentTenant.Country,
                        District = CurrentTenant.District,
                        City = CurrentTenant.City,
                        Phones = CurrentTenant.Phones,
                        Fax = CurrentTenant.Fax,
                        Email = CurrentTenant.Emails,
                        LegalName = CurrentTenant.LegalName,
                        BankName = CurrentTenant.BankName,
                        BankAccount = CurrentTenant.BankAccount,
                        SellerFullName = CurrentTenant.FullNameVi,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        UserFullName = CurrentUser.FullName,
                        Date = invoice.InvoiceDate,
                        SerialNo = request.SerialNo,
                        TemplateNo = request.TemplateNo,
                        InvoiceNo = request.InvoiceNo,
                        Number = invoice.Number,
                        Action = InvoiceAction.CreateAdjustmentHeaderWithoutInvoiceNo,
                        Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                        Type = VnisType._02GTTT,
                        InvoiceRootId = invoice?.Id
                    };

                    await _createAdjustmentHeaderWithoutInvoiceNoBusiness.CreateAsync(message);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);

                    //nếu update oracle lỗi thì update lại trạng thái cũ ở mongo nếu có
                    if (invoiceMongo != null)
                    {
                        await _mongoInvoice02Repository.UpdateAsync(invoiceMongo);
                    }

                    return new ResultApiViewModel(false, -1, ex.Message);
                }

                var result = new ResultApiViewModel<CreateInvoice02ResponseModel>(new ResultApiModel<CreateInvoice02ResponseModel>(new CreateInvoice02ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh,
                    SerialNo = invoice.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));
                result.Code = 0;
                result.Message = "Thành công";

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region old
            //try
            //{
            //    var tenantId = _factory.CurrentTenant.Id.Value;
            //    var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");
            //    var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
            //    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
            //    if (!hasApprove && setting != null && setting.Value == "1")
            //    {
            //        var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

            //        await signService.SignServer(invoice);

            //        response.SignStatus = SignStatus.DaKy;
            //    }

            //    return new ResultApiViewModel<CreateAdjustmentHeaderWithoutInvoiceNoResponseModel>(new ResultApiModel<CreateAdjustmentHeaderWithoutInvoiceNoResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }

        /// <summary>
        /// sửa hóa đơn gốc
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("update/{erpId}")]
        //[Authorize(nameof(Invoice02Policy.Invoice02FormPolicy.Invoice02_Update))]
        public async Task<object> Update([FromRoute] string erpId, [FromBody] UpdateInvoice02ApiRequestModel request)
        {

            // Thực hiện insert ErpId vào Redis
            // Nếu đã tồn tại trên redis thì báo lỗi
            // Nếu không kiểm tra trên tiếp luồng cũ
            var key = $"Invoice02_{_factory.CurrentTenant.Id.Value}_{erpId}";

            // Thiết lập TTL key = 5 phút
            var cacheExpiration = new TimeSpan(0, 5, 0);
            var isExistErpId = _redisCacheService.SetWithWhen<bool>(key, true, cacheExpiration, StackExchange.Redis.When.NotExists);
            if (!isExistErpId)
            {
                throw new UserFriendlyException($"Hóa đơn ErpId {erpId} đang được thao tác, không thể sửa. Vui lòng thử lại sau ít phút");
            }

            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsEmails(';') || request.BuyerEmail.Count(x => x == '@') > 1)
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<UpdateInvoice02ApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoiceRoot = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckUpdateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var response = new UpdateInvoice02ApiResponseModel();

                var invoiceMongo = await _mongoInvoice02Repository.GetById(invoiceRoot.Id);
                if (invoiceMongo == null)
                    throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {erpId}");

                //await _updateRootInvoice02ApiBusiness.UpdateMongoAsync(request);

                // update oracle
                var entityUpdateMongo = new MongoInvoice02Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                entityUpdateMongo.StoreCode = input.StoreCode;
                entityUpdateMongo.StoreName = input.StoreName;
                entityUpdateMongo.BudgetUnitCode = input.BudgetUnitCode;
                entityUpdateMongo.BuyerIDNumber = input.BuyerIDNumber;
                entityUpdateMongo.BuyerPassportNumber = input.BuyerPassportNumber;

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;

                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;

                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;

                entityUpdateMongo.Note = input.Note;

                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;

                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;

                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;

                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    {
                                        { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                    }) : null;



                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        DiscountPercent = item.DiscountPercent,
                        DiscountAmount = item.DiscountAmount,
                        UnitPrice = item.UnitPrice,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var entityIndex = entityDetails.Select(x => x.Index);
                var duplicate = commandInvoiceDetails.Select(x => x.Index).Distinct()
                                            .Intersect(entityIndex);

                var newIndex = commandInvoiceDetails.Where(x => !entityIndex.Contains(x.Index)).ToList();

                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);
                    //thêm mới

                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex.ElementAt(i);
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);

                try
                {
                    var invoice = _factory.ObjectMapper.Map<MongoInvoice02Entity, Invoice02HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice02Service.GenerateQueryUpdateAsync(invoice, toCurrency, oldDetails, newDetails, removeDetails, false, null);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    response = new UpdateInvoice02ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    };

                    await _invoiceService.UpdateLastInvoiceDateAsync(entityUpdateMongo.TenantId, entityUpdateMongo.InvoiceTemplateId, entityUpdateMongo.Number.Value);
                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                        InvoiceType = (short)VnisType._02GTTT
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                _redisCacheService.Remove(key);

                return new ResultApiViewModel<UpdateInvoice02ApiResponseModel>(new ResultApiModel<UpdateInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        [HttpPost("update32/{erpId}")]
        public async Task<object> Update32([FromRoute] string erpId, [FromBody] UpdateInvoice02OldDecreeRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsEmails(';') || request.BuyerEmail.Count(x => x == '@') > 1)
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<UpdateInvoice02OldDecreeRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoiceRoot = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToUpdate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckUpdateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var response = new UpdateInvoice02OldDecreeResponseModel();

                var invoiceMongo = await _mongoInvoice02Repository.GetById(invoiceRoot.Id);
                if (invoiceMongo == null)
                    throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {erpId}");

                // update oracle
                var entityUpdateMongo = new MongoInvoice02Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;

                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;

                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;

                entityUpdateMongo.Note = input.Note;

                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;

                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;

                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;

                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;
                entityUpdateMongo.ReferenceInvoiceType = (short)input.ReferenceInvoiceType.GetHashCode();

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    {
                                        { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                    }) : null;



                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        DiscountPercent = item.DiscountPercent,
                        DiscountAmount = item.DiscountAmount,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var entityIndex = entityDetails.Select(x => x.Index);
                var duplicate = commandInvoiceDetails.Select(x => x.Index).Distinct()
                                            .Intersect(entityIndex);

                var newIndex = commandInvoiceDetails.Where(x => !entityIndex.Contains(x.Index)).ToList();

                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);
                    //thêm mới

                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex.ElementAt(i);
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);

                try
                {
                    var invoice = _factory.ObjectMapper.Map<MongoInvoice02Entity, Invoice02HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice02Service.GenerateQueryUpdateAsync(invoice, toCurrency, oldDetails, newDetails, removeDetails, false, entityUpdateMongo.InvoiceReferenceOld);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    await _invoiceService.UpdateLastInvoiceDateAsync(entityUpdateMongo.TenantId, entityUpdateMongo.InvoiceTemplateId, entityUpdateMongo.Number.Value);
                    response = new UpdateInvoice02OldDecreeResponseModel
                    {
                        Id = entityUpdateMongo.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = entityUpdateMongo.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    };

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateInvoice02OldDecreeResponseModel>(new ResultApiModel<UpdateInvoice02OldDecreeResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// sửa hóa đơn thay thế
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("replace/update/{erpId}")]
        //[Authorize(nameof(Invoice02Policy.Invoice02FormPolicy.Invoice02_Update))]
        public async Task<object> UpdateReplace([FromRoute] string erpId, [FromBody] UpdateReplaceInvoice02ApiRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.ErpId = erpId;
                var validator = _validatorFactory.GetValidator<UpdateReplaceInvoice02ApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var invoiceMongo = await _mongoInvoice02Repository.GetByErpId(erpId, CurrentTenant.Id.Value);
                if (invoiceMongo == null)
                    throw new UserFriendlyException("Không tìm thấy hóa đơn");

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckUpdateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var entityUpdateMongo = new MongoInvoice02Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                entityUpdateMongo.StoreCode = input.StoreCode;
                entityUpdateMongo.StoreName = input.StoreName;
                entityUpdateMongo.BudgetUnitCode = input.BudgetUnitCode;
                entityUpdateMongo.BuyerIDNumber = input.BuyerIDNumber;
                entityUpdateMongo.BuyerPassportNumber = input.BuyerPassportNumber;

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;
                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;
                entityUpdateMongo.Note = input.Note;
                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.ThayThe))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.ThayThe))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;
                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;
                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;
                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();
                //entityUpdateMongo.IsSyncedUnitToCore = 0;
                //entityUpdateMongo.IsSyncedProductToCore = 0;
                //entityUpdateMongo.IsSyncedCustomerToCore = 0;

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                {
                { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                }) : null;

                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        DiscountPercent = item.DiscountPercent,
                        DiscountAmount = item.DiscountAmount,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var duplicate = commandInvoiceDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));
                var newIndex = commandInvoiceDetails.Where(x => !duplicate.Contains(x.Index)).ToList();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);
                    await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice02Detail);

                    //thêm mới
                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex[i];
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);

                var rootInvoice = await _repoInvoice02Header.GetByIdRawAsync(invoice.TenantId, invoice.Id);
                if (rootInvoice == null)
                    return new ResultApiViewModel<UpdateReplaceInvoice02ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice02ApiResponseModel>(new UpdateReplaceInvoice02ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    }));

                try
                {
                    var data = _factory.ObjectMapper.Map<MongoInvoice02Entity, Invoice02HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice02Service.GenerateQueryUpdateAsync(data, toCurrency, oldDetails, newDetails, removeDetails, false, null);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);



                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateReplaceInvoice02ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice02ApiResponseModel>(new UpdateReplaceInvoice02ApiResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    TransactionId = invoice.TransactionId,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                }));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// sửa hóa đơn điều chỉnh định danh
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header/update/{erpId}")]
        public async Task<object> UpdateAdjustmentHeader([FromRoute] string erpId, [FromBody] UpdateAdjustmentHeaderInvoice02ApiRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<UpdateAdjustmentHeaderInvoice02ApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);


                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var tenant = _validationContext.GetItem<Tenant>("Tenant");

                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");
                if (invoice == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.InvoiceNotFound"]);

                if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
                {
                    var headerExtras = request.InvoiceHeaderExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    request.InvoiceHeaderExtras = headerExtras;
                }

                var response = new UpdateAdjustmentHeaderInvoice02ApiResponseModel();

                request.Id = invoice.Id;

                var invoicesMongoDB = await _mongoInvoice02Repository.GetByIdsAsync(new List<long> { request.Id });
                await _updateAdjustHeaderInvoice02Business.UpdateMongoAsync(request);

                try
                {
                    var responseInvoice = await _updateAdjustHeaderInvoice02Business.UpdateOracleAsync(
                       new InvoiceCommandRequestModel
                       {
                           Resource = InvoiceSource.Api,
                           InvoiceId = invoice.Id,
                           InvoiceNo = invoice.InvoiceNo,
                           Number = invoice.Number,
                           TenantId = tenantId,
                           UserId = userId,
                           UserName = userName,
                           UserFullName = userFullName,
                           Date = request.InvoiceDate,
                           SerialNo = invoice.SerialNo,
                           TemplateNo = invoice.TemplateNo,
                           Action = InvoiceAction.UpdateAdjustmentHeader,
                           Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                           Type = VnisType._02GTTT,
                       });

                    response = new UpdateAdjustmentHeaderInvoice02ApiResponseModel
                    {
                        Id = responseInvoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = responseInvoice.InvoiceNo,
                        InvoiceStatus = responseInvoice.InvoiceStatus,
                        SignStatus = responseInvoice.SignStatus
                    };

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Delete,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateManyAsync(invoicesMongoDB);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateAdjustmentHeaderInvoice02ApiResponseModel>(new ResultApiModel<UpdateAdjustmentHeaderInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// sửa hóa đơn điều chỉnh tăng giảm
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-detail/update/{erpId}")]
        public async Task<object> UpdateAdjustmentDetail([FromRoute] string erpId, [FromBody] UpdateAdjustmentDetailInvoice02ApiRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var validator = _validatorFactory.GetValidator<UpdateAdjustmentDetailInvoice02ApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);


                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");
                //var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
                var toCurrencyData = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
                var toCurrency = new CurrencyEntity
                {
                    CurrencyCode = toCurrencyData.ToCurrencyCode,
                    Conversion = toCurrencyData.ToCurrencyConversion,
                    Rounding = toCurrencyData.ToCurrencyRounding,
                    NameVi = toCurrencyData.NameVi,
                    MinimumNameVi = toCurrencyData.MinimumNameVi,
                    NameEn = toCurrencyData.NameEn,
                    MinimumNameEn = toCurrencyData.MinimumNameEn
                };

                // check sync catalog
                var settingService = _factory.GetServiceDependency<ISettingService>();
                var settingCheckUpdateCustomer = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";

                var invoiceMongo = await _mongoInvoice02Repository.GetById(invoice.Id);
                var input = request;
                var entityUpdateMongo = new MongoInvoice02Entity();

                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice02Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;
                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam))?.Trim().Replace("'", "''");
                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;
                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();
                //entityUpdateMongo.IsSyncedUnitToCore = 0;
                //entityUpdateMongo.IsSyncedProductToCore = 0;

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice02.Api.Intergration.Create.IndexIsDuplicated"]);

                    var entityOld = entityDetails.FirstOrDefault(x => x.Index == item.Index);
                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice02.Invoice02DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = entityOld.ProductId,
                        ProductCode = entityOld.ProductCode,
                        ProductName = entityOld.ProductName,
                        //ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = entityOld.UnitId,
                        UnitName = entityOld.UnitName,
                        HideQuantity = entityOld.HideQuantity,
                        HideUnit = entityOld.HideUnit,
                        HideUnitPrice = entityOld.HideUnitPrice,
                        RoundingUnit = entityOld.RoundingUnit,
                        UnitPrice = item.UnitPrice,
                        Partition = partition,
                        DiscountPercent = item.DiscountPercent,
                        DiscountAmount = item.DiscountAmount,
                        ExtraProperties = entityOld.ExtraProperties
                        //ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                        //                ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                        //                {
                        //                { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                        //                }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var duplicate = commandInvoiceDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;

                await _mongoInvoice02Repository.UpdateAsync(entityUpdateMongo);



                var response = new UpdateAdjustmentDetailInvoice02ApiResponseModel();

                try
                {
                    foreach (var item in request.InvoiceDetails)
                    {
                        if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                        {
                            var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                            item.InvoiceDetailExtras = detailExtras;
                        }
                    }

                    var responseInvoice = await _updateAdjustDetailInvoice02Business.UpdateOracleAsync(
                    new InvoiceCommandRequestModel
                    {
                        Resource = InvoiceSource.Api,
                        InvoiceId = invoice.Id,
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        TenantId = tenantId,
                        UserId = userId,
                        UserName = userName,
                        UserFullName = userFullName,
                        Date = request.InvoiceDate,
                        SerialNo = invoice.SerialNo,
                        TemplateNo = invoice.TemplateNo,
                        Action = InvoiceAction.UpdateAdjustmentDetail,
                        Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                        Type = VnisType._02GTTT,
                    });

                    response = new UpdateAdjustmentDetailInvoice02ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = responseInvoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                        TransactionId = invoice.TransactionId
                    };

                    // insert log
                    var invoice02LogEntity = new MongoInvoice02LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateAdjustmentDetailInvoice02ApiResponseModel>(new ResultApiModel<UpdateAdjustmentDetailInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
            #region old
            //try
            //{
            //    var validator = _validatorFactory.GetValidator<UpdateAdjustmentDetailInvoice02ApiRequestModel>();
            //    request.ErpId = erpId;

            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);
            //    return new ResultApiViewModel<UpdateAdjustmentDetailInvoice02ApiResponseModel>(new ResultApiModel<UpdateAdjustmentDetailInvoice02ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }


        /// <summary>
        /// API ký hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("sign-server/{erpId}")]
        public async Task<object> Sign([FromRoute] string erpId)
        {
            try
            {
                var request = new Invoice02ApiSignServerRequestModel
                {
                    ErpId = erpId
                };
                var validator = _validatorFactory.GetValidator<Invoice02ApiSignServerRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<Invoice02ApiSignServerResponseModel>(new ResultApiModel<Invoice02ApiSignServerResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// tạo biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("documents/{erpId}")]
        public async Task<object> CreateDocument([FromRoute] string erpId, [FromForm] CreateDocumentInfoInvoice02ApiRequestModel request)
        {
            try
            {
                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<CreateDocumentInfoInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<CreateDocumentInfoInvoice02ApiResponseModel>(new ResultApiModel<CreateDocumentInfoInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// update biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("documents/update/{erpId}")]
        public async Task<object> UpdateDocument([FromRoute] string erpId, [FromForm] UpdateDocumentInfoInvoice02ApiRequestModel request)
        {
            try
            {
                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<UpdateDocumentInfoInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<UpdateDocumentInfoInvoice02ApiResponseModel>(new ResultApiModel<UpdateDocumentInfoInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// xóa biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [HttpPost("documents/delete/{erpId}")]
        public async Task<object> DeleteDocument([FromRoute] string erpId)
        {
            try
            {
                var request = new DeleteDocumentInfoInvoice02ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<DeleteDocumentInfoInvoice02ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<DeleteDocumentInfoInvoice02ApiResponseModel>(new ResultApiModel<DeleteDocumentInfoInvoice02ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// In thể hiện
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("unofficial/{erpId}")]
        public async Task<object> DownloadUnOfficial([FromRoute] string erpId)
        {
            try
            {
                var request = new DownloadUnOfficialInvoice02RequestModel
                {
                    ErpId = erpId
                };

                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// In thể hiện
        /// </summary>
        /// <param name="erpIds"></param>
        /// <param name="printAction"></param>
        /// <returns></returns>
        [HttpPost("unofficial/invoice-or-notice/{printAction}")]
        public async Task<object> DownloadUnOfficial([FromRoute] string printAction, [FromBody] List<string> erpIds)
        {
            try
            {
                var result = await _factory.Mediator.Send(new DownloadUnOfficialInvoice02WithPrintActionRequestModel
                {
                    InvoiceTypeName = printAction,
                    ErpIds = erpIds
                });

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// In chuyển đổi
        /// </summary>
        /// <param name="erpId"></param>
        /// <returns></returns>
        [HttpPost("official/{erpId}")]
        public async Task<object> DownloadOfficial([FromRoute] string erpId)
        {
            try
            {
                var request = new DownloadOfficialInvoice02RequestModel
                {
                    ErpId = erpId
                };
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// lấy mẫu số ký hiệu hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("info-template")]
        public async Task<object> GetTemplateNoInvoiceNo()
        {
            try
            {
                var request = new InvoiceTemplateInfoRequestModel();
                var response = await _factory.Mediator.Send(request);

                if (response.Count <= 0)
                    throw new UserFriendlyException(L["Vnis.BE.Invoice.InvoiceDateRange.InvoiceTemplateNotfound"]);

                return new ResultApiViewModel<List<InvoiceTemplateInfoResponseModel>>(new ResultApiModel<List<InvoiceTemplateInfoResponseModel>>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// gửi mail
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("send-mail")]
        public async Task<object> SendMail([FromBody] List<SendMailInvoice02ApiRequestModel> requests)
        {
            try
            {
                foreach (var request in requests)
                {
                    var validator = _validatorFactory.GetValidator<SendMailInvoice02ApiRequestModel>();

                    var validationResult = await validator.HandleAsync(request);
                    if (!validationResult.Success)
                        throw new UserFriendlyException(L[validationResult.Message]);

                    await _factory.Mediator.Send(request);
                }

                return new ResultApiViewModel(true, 0, "Thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// gửi mail với printAction
        /// </summary>
        /// <param name="printAction"></param>
        /// <param name="requests"></param>
        /// <returns></returns>
        [HttpPost("send-mail/{printAction}")]
        public async Task<object> SendMail([FromRoute] string printAction, [FromBody] List<SendMailInvoice02WithPrintActionApiRequestModel> requests)
        {
            try
            {
                foreach (var request in requests)
                {
                    var validator = _validatorFactory.GetValidator<SendMailInvoice02WithPrintActionApiRequestModel>();

                    request.PrintAction = printAction;
                    var validationResult = await validator.HandleAsync(request);
                    if (!validationResult.Success)
                        throw new UserFriendlyException(L[validationResult.Message]);

                    await _factory.Mediator.Send(request);
                }

                return new ResultApiViewModel(true, 0, "Thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// Tạo thông báo sai sót ver1
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        //[HttpPost("invoice-error/create")]
        //public async Task<CreateInvoice02TvanErrorResponseModel> CreateInvoiceTvanErrorAsync([FromBody] CreateInvoice02TvanErrorRequestModel request)
        //{
        //    return await _factory.Mediator.Send(request);
        //}

        /// <summary>
        /// Tạo thông báo sai sót ver2
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/create")]
        public async Task<object> CreateInvoiceTvanErrorAsync([FromBody] CreateInvoice02ErrorTvanRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<CreateInvoice02ErrorTvanResponseModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// Update thông báo sai sót
        /// </summary>
        /// <returns></returns>
        [HttpPost("invoice-error/update")]
        public async Task<object> UpdateInvoiceTvanErrorAsync([FromBody] UpdateInvoice02TvanErrorRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<UpdateInvoice02TvanErrorResponseModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// xóa thông báo sai sót
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/delete")]
        public async Task<object> DeleteInvoiceErrorAsync(DeleteInvoice02TvanErrorRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<DeleteInvoice02ErrorTvanResponse>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// ký sai sót hóa đơn 02
        /// </summary>
        /// <param name="ErpId"></param>
        /// <returns></returns>
        [HttpPost("signserver/invoice02-error")]
        public async Task<object> SignInvoice02ErrorAsync([FromBody] List<string> ErpIds)
        {
            try
            {
                var result = await _factory.Mediator.Send(new SignServerInvoice02ErrorRequestModel
                {
                    ErpIds = ErpIds
                });

                return new ResultApiViewModel<List<SignServerInvoiceErrorResponseModel>>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// api đồng bộ hóa đơn ở oracle về mongo
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("tool/sync-oracle-to-mongo")]
        public async Task<object> SyncInvoiceOracleToMongoByIdAsync([FromBody] SyncInvoiceOracleToMongoRequestModel request)
        {
            try
            {
                var invoiceHeaders = await _repoInvoice02Header.QueryByIdsAsync(request.Ids);
                if (!invoiceHeaders.Any())
                    throw new Exception("Không tìm thấy hóa đơn");

                var idInvoiceHeaders = invoiceHeaders.Select(x => x.Id).ToList();
                var invoiceDetails = (await _repoInvoice02Detail.QueryByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                //var invoiceTaxBreakdowns = (await _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdsAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                var invoiceRefereces = (await _repoInvoice02Reference.GetByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                //var invoiceRefereceOlds = (await _repoInvoice02ReferenceOldDecree.GetByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());

                try
                {
                    var mongoEntitys = _factory.ObjectMapper.Map<List<Invoice02HeaderEntity>, List<MongoInvoice02Entity>>(invoiceHeaders);

                    var mongoBatchIdEntitys = new List<MongoInvoice02BatchIdEntity>();

                    int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                    mongoEntitys.ForEach(x =>
                    {
                        x.TenantGroup = request.TenantGroup;
                        x.InvoiceGroup = 0;
                        x.TotalInvoices = 1;
                        x.IsGeneratedNumber = 1;
                        x.IsSyncedToCore = 1;
                        x.InvoiceDate = isEnableMongoDbLocalTime > 0 ? x.InvoiceDate.ToLocalTime() : x.InvoiceDate;

                        if (x.PaymentDate.HasValue)
                        {
                            x.PaymentDate = isEnableMongoDbLocalTime > 0 ? x.PaymentDate.Value.ToLocalTime() : x.PaymentDate;
                        }

                        if (x.IssuedTime.HasValue)
                        {
                            x.IssuedTime = isEnableMongoDbLocalTime > 0 ? x.IssuedTime.Value.ToLocalTime() : x.IssuedTime.Value;
                        }

                        x.CreationTime = isEnableMongoDbLocalTime > 0 ? x.CreationTime.ToLocalTime() : x.CreationTime;

                        if (x.InvoiceReference != null)
                        {
                            x.InvoiceReference.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? x.InvoiceReference.InvoiceDateReference.ToLocalTime() : x.InvoiceReference.InvoiceDateReference;
                        }

                        if (x.InvoiceReferenceOld != null)
                        {
                            x.InvoiceReferenceOld.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? x.InvoiceReferenceOld.InvoiceDateReference.ToLocalTime() : x.InvoiceReferenceOld.InvoiceDateReference;
                        }

                        x.InvoiceReference = invoiceRefereces.ContainsKey(x.Id)
                                                ? _factory.ObjectMapper.Map<Invoice02ReferenceEntity, Invoice02ReferenceDto>(invoiceRefereces[x.Id].FirstOrDefault())
                                                : null;

                        //x.InvoiceReferenceOld = (invoiceRefereceOlds != null && invoiceRefereceOlds.ContainsKey(x.Id))
                        //                        ? _factory.ObjectMapper.Map<Invoice02ReferenceOldDecreeEntity, Invoice02ReferenceOldDto>(invoiceRefereceOlds[x.Id].FirstOrDefault())
                        //                        : null;

                        mongoBatchIdEntitys.Add(new MongoInvoice02BatchIdEntity
                        {
                            BatchId = x.BatchId,
                            TenantId = x.TenantId,
                            Group = request.TenantGroup,
                            InvoiceTemplateId = x.InvoiceTemplateId,
                            CreationTime = x.CreationTime,
                            TotalInvoices = 1,
                            CreatorStatus = 2,
                            TotalInvoicesWithNumber = 1,
                            GenerateNumberStatus = 2,
                            IsSyncedLicense = 1,
                        });
                    });

                    await _mongoInvoice02Repository.InsertManyAsync(mongoEntitys);
                    await _mongoInvoice02BatchIdRepository.InsertManyAsync(mongoBatchIdEntitys);

                    return request;
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        private async Task<List<long>> GetSEQsNextVal(int level, string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _factory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }

        /// <summary>
        /// Update trạng thái hóa đơn gốc
        /// </summary>
        /// <returns></returns>
        private async Task InvoiceReSyncToMongo(List<long> ids, InvoiceStatus invoiceStatus)
        {
            await _mongoInvoice02Repository.UpdateManyInvoiceStatusAfterAdjustActionAsync(ids, (short)invoiceStatus);
        }

        private async Task InvoiceReSyncToEs(List<long> ids)
        {
            var invoice02ReSyncs = await _mongoInvoice02ReSyncRepository.GetByIdsAsync(ids);

            var invoice02ReSyncInsert = new List<MongoInvoice02ReSyncEntity>();

            if (invoice02ReSyncs.Any())
            {
                invoice02ReSyncs.ForEach(item => { item.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode(); });

                invoice02ReSyncInsert.AddRange(
                    from item in ids
                    let invoice02ReSync = invoice02ReSyncs.FirstOrDefault(x => x.Id == item)
                    where invoice02ReSync == null
                    select new MongoInvoice02ReSyncEntity { Id = item, IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode() }
                );

                await _mongoInvoice02ReSyncRepository.UpdateManyAsync(invoice02ReSyncs);

                if (invoice02ReSyncInsert.Any())
                    await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
            }
            else
            {
                invoice02ReSyncInsert.AddRange(ids.Select(item => new MongoInvoice02ReSyncEntity
                { Id = item, IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode() }));

                if (invoice02ReSyncInsert.Any())
                    await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
            }
        }


        // update lại hóa đơn bị dc/tt về gốc sau khi xóa hóa đơn dc/tt
        private async Task UpdateESInvoiceReferenceAsync(List<Invoice02ReferenceEntity> mongoInvoice02s, InvoiceStatus invoiceStatus)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];
            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
                throw new UserFriendlyException("Chưa có cấu hình TenantGroupsPrivate");

            var group = _factory.CurrentTenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

            var bulkResponse = await client.BulkAsync(b => b
                    .UpdateMany(mongoInvoice02s, (descriptor, update) => descriptor
                        .Id(update.Id)
                        .Script(s => s
                            .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};" +
                                    $"ctx._source.{nameof(Invoice02HeaderEsDto.TemplateNoReference).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.TemplateNoReference).FirstCharToLowerCase()};" +
                                    $"ctx._source.{nameof(Invoice02HeaderEsDto.SerialNoReference).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SerialNoReference).FirstCharToLowerCase()};" +
                                    $"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceNoReference).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceNoReference).FirstCharToLowerCase()};" +
                                    $"ctx._source.{nameof(Invoice02HeaderEsDto.NumberReference).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.NumberReference).FirstCharToLowerCase()};" +
                                    $"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceDateReference).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceDateReference).FirstCharToLowerCase()};"
                                    )
                            .Params(p => p
                                .Add($"{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", (short)invoiceStatus)
                                .Add($"{nameof(Invoice02HeaderEsDto.TemplateNoReference).FirstCharToLowerCase()}", update.TemplateNoReference.ToString())
                                .Add($"{nameof(Invoice02HeaderEsDto.SerialNoReference).FirstCharToLowerCase()}", update.SerialNoReference)
                                .Add($"{nameof(Invoice02HeaderEsDto.InvoiceNoReference).FirstCharToLowerCase()}", update.InvoiceNoReference)
                                .Add($"{nameof(Invoice02HeaderEsDto.NumberReference).FirstCharToLowerCase()}", update.NumberReference)
                                .Add($"{nameof(Invoice02HeaderEsDto.InvoiceDateReference).FirstCharToLowerCase()}", update.InvoiceDateReference)
                                )
                        )
                    ).Refresh(Refresh.True));

            if (!bulkResponse.IsValid)
                Log.Error(@$"RESPONSE UPDATE ES INVOICE REFERENCE: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");

        }
    }
}
