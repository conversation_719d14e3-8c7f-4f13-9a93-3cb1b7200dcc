using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Core.Tvan.Constants;
using Microsoft.Extensions.Localization;
using System.Linq;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateBatch
{
    public class CreateBatchInvoiceCheckProductTypeRule : IValidationRule<CreateBatchInvoice02RequestModel, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateBatchInvoiceCheckProductTypeRule(IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
        }

        public ValidationResult Handle(CreateBatchInvoice02RequestModel input)
        {
            var details = input.Datas.Select(x => x.Invoice).SelectMany(x => x.InvoiceDetails);
            if (details == null || !details.Any())
                return new ValidationResult(true);

            var errorDetailUnitNames = details.Where(x => x.ProductType != TChat.Note.GetHashCode() && string.IsNullOrEmpty(x.UnitName)).Select(x => x.Index).ToList();

            if (errorDetailUnitNames.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.UnitNameIsRequired", new[] { string.Join(",", errorDetailUnitNames) }]);

            return new ValidationResult(true);
        }
    }
}
