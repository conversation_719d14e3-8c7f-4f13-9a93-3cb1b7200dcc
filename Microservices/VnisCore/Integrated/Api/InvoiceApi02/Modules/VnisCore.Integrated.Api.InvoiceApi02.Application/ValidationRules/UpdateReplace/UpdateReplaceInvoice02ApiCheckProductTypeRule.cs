using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Core.Tvan.Constants;
using Microsoft.Extensions.Localization;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateReplace
{
    public class UpdateReplaceInvoice02ApiCheckProductTypeRule : IValidationRuleAsync<UpdateReplaceInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateReplaceInvoice02ApiCheckProductTypeRule(IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice02ApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var errorDetailUnitNames = input.InvoiceDetails.Where(x => x.ProductType != TChat.Note.GetHashCode() && string.IsNullOrEmpty(x.UnitName)).Select(x => x.Index).ToList();

            if (errorDetailUnitNames.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.UnitNameIsRequired", new[] { string.Join(",", errorDetailUnitNames) }]);

            return new ValidationResult(true);
        }
    }
}

