using Core.Application;
using Core.AutoMapper;
using Core.Dto.Shared;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;

using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Business;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitClient;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitClient.Services;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitClient.Workers;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Services;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Approve;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.ApproveAndSign;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Cancel;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Create;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateAdjHeaderWithoutInvoiceNo;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateAdjustmentDetail;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateAdjustmentHeader;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateBatch;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateOldDecree;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateReplace;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Delete;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.DeleteDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.SendMail;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.SendMailWithPrintAction;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Sign;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Update;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateAdjusmentHeader;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateAdjustmentDetail;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateAdjustmentHeader;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateOldDecree;
using VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateReplace;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule),
        typeof(SharedDtoModule),
        typeof(VnisCoreMongoDbModule)
    )]
    public class VnisCoreInvoiceApi02ApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            //context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<INumberService, NumberServiceExtend>();
            context.Services.AddScoped(typeof(IInvoiceService<,,>), typeof(InvoiceService<,,>));
            context.Services.AddScoped<IInvoice02Service, Invoice02Service>();
            context.Services.AddScoped<IExportPdfHttpClient, ExportPdfGrpc>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            context.Services.AddScoped<ISignServerHttpClient, SignServerHttpClient>();
            context.Services.AddScoped<ISendMailHttpClient, SendMailHttpClient>();

            context.Services.AddScoped(typeof(IInvoiceHeaderRepository<>), typeof(InvoiceHeaderRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<IInvoice02HeaderRepository, Invoice02HeaderRepository>();
            context.Services.AddScoped<IInvoice02HeaderFieldRepository, Invoice02HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice02DetailFieldRepository, Invoice02DetailFieldRepository>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IMonitorInvoiceTemplateRepository, MonitorInvoiceTemplateRepository>();
            context.Services.AddScoped<INewRegistrationHeaderRepository, NewRegistrationHeaderRepository>();
            context.Services.AddScoped<IInvoice02ErpIdRepository, Invoice02ErpIdRepository>();
            context.Services.AddScoped<IInvoice02DocumentService, Invoice02DocumentService>();


            context.Services.AddScoped<ICreateAdjustmentHeaderWithoutInvoiceNoBusiness, CreateAdjustmentHeaderWithoutInvoiceNoBusiness>();
            context.Services.AddScoped<IUpdateRootInvoice02ApiBusiness, UpdateRootInvoice02ApiBusiness>();
            context.Services.AddScoped<IUpdateAdjustHeaderInvoice02Business, UpdateAdjustHeaderInvoice02Business>();
            context.Services.AddScoped<IUpdateAdjustDetailInvoice02Business, UpdateAdjustDetailInvoice02Business>();
            context.Services.AddScoped<IUpdateReplaceInvoice02Business, UpdateReplaceInvoice02Business>();
            context.Services.AddScoped<ISyncEsBusiness, SyncEsBusiness>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            context.Services.Configure<DistributedCacheEntryOptions>(options =>
            {
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
            });

            AddInvoice02ApiValidators(context.Services);
            AddRabbitervices(context.Services, context.Services.GetConfiguration());
            AddExportPdfServices(context.Services, context.Services.GetConfiguration());
            AddSignServerServices(context.Services, context.Services.GetConfiguration());
            AddSendMailServices(context.Services, context.Services.GetConfiguration());

            context.Services.AddAutoMapperObjectMapper<VnisCoreInvoiceApi02ApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreInvoiceApi02ApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreInvoiceApi02ApplicationModule).GetTypeInfo().Assembly);
        }

        public static void AddExportPdfServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("exportpdfs", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:ExportPdf:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSignServerServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("signserver", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:Sign:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:Sign:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSendMailServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("sendmail", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:SendMail:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:SendMail:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISendMailHttpClient, SendMailHttpClient>();
        }

        public static void AddRabbitervices(IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<RabbitOption>(configuration.GetSection("RabbitMQ:Connections:Default"));
            services.AddScoped<IInvoiceFactory, InvoiceFactory>();

            services.AddSingleton<IInvoice02CommandHandler, Invoice02CommandHandler>();
            services.AddSingleton<ISignInvoice02CommandHandler, SignInvoice02CommandHandler>();

            services.AddSingleton<IRabbitBus, RabbitBus>();
            services.AddSingleton<IRabbitBusClient, RabbitBusClient>();
            services.AddSingleton<IRabbitService, VnisBackgroundService>();

            //Worker
            services.AddHostedService<Invoice02Worker>();
            //services.AddHostedService<Invoice02ReferenceWorker>();

            //Services
            services.AddScoped<IInvoiceHandler, CreateRootInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CreateRootOldDecreeInvoice02Service>();
            services.AddScoped<IInvoiceHandler, UpdateRootInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentDetailInvoice02Service>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentHeaderInvoice02Service>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentDetailInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CancelInvoice02Service>();
            services.AddScoped<IInvoiceHandler, DeleteInvoice02Service>();
            services.AddScoped<IInvoiceHandler, ApproveInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CreateReplaceInvoice02Service>();
            services.AddScoped<IInvoiceHandler, UpdateReplaceInvoice02Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderWithoutInvoiceNoInvoice02Service>();
            services.AddScoped<ILicenseSharedService, LicenseSharedService>();

        }

        public static void AddInvoice02ApiValidators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateInvoice02RequestModel>(new List<Type>
            {
                typeof(CreateInvoicePreProcess),
                typeof(CreateInvoiceCheckBuyerInformationRule),
                typeof(CreateInvoiceCheckIndexDetailRule),
                typeof(CreateInvoiceCheckExistIdErpRule),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckTemplateCanCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckPaymentMethodRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckDetailExtraRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateBatchInvoice02RequestModel>(new List<Type>
            {
                typeof(CreateBatchInvoiceCheckExistIdErpRule),
                typeof(CreateBatchInvoiceCheckIndexDetailRule),
                typeof(CreateBatchInvoicePreProcess),
                typeof(CreateBatchInvoiceCheckBuyerInformationRule),
                typeof(CreateBatchInvoiceCheckLicenseRule),
                typeof(CreateBatchInvoiceCheckExistTenantInfoRule),
                typeof(CreateBatchInvoiceCheckExistTemplateRule),
                typeof(CreateBatchInvoiceCheckTemplateCanCreateRule),
                typeof(CreateBatchInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateBatchInvoiceCheckExistCurrencyRule),
                typeof(CreateBatchInvoiceCheckExchangeRateRule),
                typeof(CreateBatchInvoiceCheckPaymentMethodRule),
                typeof(CreateBatchInvoiceCheckHeaderExtraRule),
                typeof(CreateBatchInvoiceCheckDetailExtraRule),
                typeof(CreateBatchInvoiceCheckProductTypeRule),
                typeof(CreateBatchInvoiceCheckPendingLastInvoiceDateRule)
            });

            services.AddConfigValidator<CreateInvoice02OldDecreeRequestModel>(new List<Type>
            {
                typeof(CreateInvoiceOldDecreeCheckIndexDetailRule),
                typeof(CreateInvoiceOldDecreePreProcess),
                typeof(CreateInvoiceOldDecreeCheckBuyerInformationRule),
                typeof(CreateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(CreateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(CreateInvoiceOldDecreeCheckExchangeRateRule),
                typeof(CreateInvoiceOldDecreeCheckExistIdErpRule),
                typeof(CreateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(CreateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(CreateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckLicenseRule),
                typeof(CreateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(CreateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
                typeof(CreateInvoiceOldDecreeCheckProductTypeRule),
                typeof(CreateInvoiceOldDecreeCheckTemplateCanCreateRule),
                typeof(CreateInvoiceOldDecreeFormatNumber),
            });

            services.AddConfigValidator<UpdateInvoice02OldDecreeRequestModel>(new List<Type>
            {
                typeof(UpdateInvoiceOldDecreeCheckIndexDetailRule),
                typeof(UpdateInvoiceOldDecreePreProcess),
                typeof(UpdateInvoiceOldDecreeApiCheckBuyerInformationRule),
                typeof(UpdateOldDecreeApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(UpdateInvoiceOldDecreeCheckExchangeRateRule),
                //typeof(UpdateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(UpdateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(UpdateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(UpdateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
                typeof(UpdateInvoiceOldDecreeCheckProductTypeRule),
                typeof(UpdateInvoiceOldDecreeCheckTemplateCanCreateRule),
                typeof(UpdateInvoiceOldDecreeFormatNumber),
            });

            services.AddConfigValidator<ApproveInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(ApproveInvoice02ApiPreProcess),
                typeof(ApproveInvoice02ApiCheckUserRule),
                typeof(ApproveInvoice02ApiCheckStatusRule)
            });

            services.AddConfigValidator<ApproveAndSignInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(ApproveAndSignInvoice02PreProcess),
                typeof(ApproveAndSignInvoice02ApiCheckStatusRule)
            });


            services.AddConfigValidator<UpdateInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(UpdateInvoice02ApiCheckIndexDetailRule),
                typeof(UpdateInvoice02ApiPreProcess),
                typeof(UpdateInvoice02ApiCheckBuyerInformationRule),
                typeof(UpdateInvoice02ApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoice02ApiCheckStatusRule),
                typeof(UpdateInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(UpdateInvoice02ApiCheckExistCurrencyRule),
                typeof(UpdateInvoice02ApiCheckExchangeRateRule),
                typeof(UpdateInvoice02ApiCheckPaymentMethodRule),
                typeof(UpdateInvoice02ApiCheckHeaderExtraRule),
                typeof(UpdateInvoice02ApiCheckDetailExtraRule),
                typeof(UpdateInvoice02ApiCheckProductTypeRule),
                typeof(UpdateInvoice02ApiCheckPendingLastInvoiceDateRule),
            });



            services.AddConfigValidator<CreateReplaceInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(CreateReplaceInvoice02ApiCheckIndexDetailRule),
                typeof(CreateReplaceInvoiceApiPreProcess),
                typeof(CreateReplaceInvoiceApiCheckBuyerInformationRule),
                typeof(CreateReplaceInvoice02ApiCheckExistIdErpRule),
                typeof(CreateReplaceInvoiceApiCheckLicenseRule),
                typeof(CreateReplaceInvoiceApiCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceApiCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceApiCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceApiCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceApiCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceApiCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceApiCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceApiCheckPaymentMethodRule),
                typeof(CreateReplaceInvoiceApiCheckHeaderExtraRule),
                typeof(CreateReplaceInvoice02ApiCheckDetailExtraRule),
                typeof(CreateReplaceInvoice02ApiCheckProductTypeRule),
                typeof(CreateReplaceInvoiceApiCheckPendingLastInvoiceDateRule),
                //typeof(CreateReplaceInvoiceApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<UpdateReplaceInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(UpdateReplaceInvoice02ApiCheckIndexDetailRule),
                typeof(UpdateReplaceInvoice02ApiPreProcess),
                typeof(UpdateReplaceInvoice02ApiCheckBuyerInformationRule),
                typeof(UpdateReplaceApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateReplaceInvoice02ApiCheckStatusRule),
                typeof(UpdateReplaceInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceInvoice02ApiCheckDocumentDateRule),
                typeof(UpdateReplaceInvoice02ApiCheckExistCurrencyRule),
                typeof(UpdateReplaceInvoice02ApiCheckExchangeRateRule),
                typeof(UpdateReplaceInvoice02ApiCheckPaymentMethodRule),
                typeof(UpdateReplaceInvoice02ApiCheckHeaderExtraRule),
                typeof(UpdateReplaceInvoice02ApiCheckDetailExtraRule),
                typeof(UpdateReplaceInvoice02ApiCheckProductTypeRule),
                typeof(UpdateReplaceInvoiceApiCheckPendingLastInvoiceDateRule),
            });


            services.AddConfigValidator<CreateAdjustmentDetailInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustmentDetailInvoice02ApiCheckIndexDetailRule),
                typeof(CreateAdjustmentDetailInvoice02ApiPreProcess),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckExistIdErpRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckLicenseRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckExistTemplateRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckChangeSerialRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentDetailInvoice02ApiCheckDetailExtraRule),
                typeof(CreateAdjustmentDetailInvoiceApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateAdjustmentHeaderInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustmentHeaderInvoice02ApiPreProcess),
                typeof(CreateAdjustmentHeaderInvoiceCheckBuyerInformationRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckExistIdErpRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckLicenseRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckExistTemplateRule),
                typeof(CreateAdjHeaderInvoice02ApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckChangeSerialRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckHeaderExtraRule),
                typeof(CreateAdjustmentHeaderInvoice02ApiCheckPaymentMethodRule),
                typeof(CreateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateAdjustmentHeaderInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustmentHeaderInvoice02ApiPreProcess),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckBuyerInformationRule),
                typeof(UpdateAdjHeaderApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckStatusRule),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckHeaderExtraRule),
                typeof(UpdateAdjustmentHeaderInvoice02ApiCheckPaymentMethodRule),
                typeof(UpdateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<DeleteInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(DeleteInvoice02ApiPreProcess),
                typeof(DeleteInvoice02ApiCheckStatusRule),
                typeof(DeleteInvoice02ApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CancelInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(CancelInvoice02ApiPreProcess),
                typeof(CancelInvoice02ApiCheckStatusRule),
            });

            services.AddConfigValidator<Invoice02ApiSignServerRequestModel>(new List<Type>
            {
                typeof(SignInvoice02ApiPreProcess),
                typeof(SignInvoice02ApiCheckSignRule),
            });


            services.AddConfigValidator<UpdateAdjustmentDetailInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustmentDetailInvoice02ApiCheckIndexDetailRule),
                typeof(UpdateAdjustmentDetailInvoice02ApiPreProcess),
                typeof(UpdateAdjDetailApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentDetailInvoice02ApiCheckStatusRule),
                typeof(UpdateAdjustmentDetailInvoice02ApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentDetailInvoice02ApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentDetailInvoice02ApiCheckDetailExtraRule)
            });


            services.AddConfigValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>(new List<Type>
            {
                typeof(CreateAdjHeaderWithoutInvoiceNoInvoice02ApiPreProcess),
                typeof(CreateAdjHeaderWithoutInvoiceNoInvoice02ApiCheckInvoiceRule),
                typeof(CreateAdjHeaderWithoutInvoiceNoInvoice02ApiCheckInfoErrorRule),
            });


            services.AddConfigValidator<CreateDocumentInfoInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(CreateDocumentInfoInvoice02ApiPreProcess),
                typeof(CreateDocumentInfoInvoice02ApiCheckStatusRule),
                typeof(CreateDocumentInfoInvoice02ApiCheckExistRule),
                typeof(CreateDocumentInfoInvoice02ApiCheckDateRule)
            });

            services.AddConfigValidator<UpdateDocumentInfoInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(UpdateDocumentInfoInvoice02ApiPreProcess),
                typeof(UpdateDocumentInfoInvoice02ApiCheckStatusRule),
                typeof(UpdateDocumentInfoInvoice02ApiCheckDateRule)
            });

            services.AddConfigValidator<DeleteDocumentInfoInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(DeleteDocumentInfoInvoice02ApiPreProcess),
                typeof(DeleteDocumentInfoInvoice02ApiCheckExistRule),
                typeof(DeleteDocumentInfoInvoice02ApiCheckStatusRule)
            });

            services.AddConfigValidator<SendMailInvoice02ApiRequestModel>(new List<Type>
            {
                typeof(SendMailInvoice02ApiPreProcess),
                typeof(SendMailInvoice02ApiCheckSendMailRule),
            });

            services.AddConfigValidator<SendMailInvoice02WithPrintActionApiRequestModel>(new List<Type>
            {
                typeof(SendMailInvoice02ApiWithPrintActionPreProcess),
                typeof(SendMailInvoice02ApiCheckSendMailWithPrintActionRule),
            });
        }
    }
}
