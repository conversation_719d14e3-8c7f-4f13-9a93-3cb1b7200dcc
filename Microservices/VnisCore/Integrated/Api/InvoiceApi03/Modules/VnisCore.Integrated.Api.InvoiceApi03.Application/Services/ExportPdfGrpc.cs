using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Services
{
    public class ExportPdfGrpc : IExportPdfHttpClient
    {

        private readonly HttpClient _client;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;

        public ExportPdfGrpc(HttpClient client,
                                IStringLocalizer<CoreLocalizationResource> localizier,
                                IHttpClientFactory httpClientFactory,
                                IConfiguration configuration,
                                IAppFactory appFactory)
        {
            _client = httpClientFactory.CreateClient("exportpdfs");
            _localizier = localizier;
            _configuration = configuration;
            _appFactory = appFactory;

        }

        public async Task<FileDto> OfficialsAsync(List<long> idInvoiceHeaders)
        {
            try
            {
                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoiceHeaders), Encoding.UTF8, "application/json");
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, "api/ExportPdf/InvoiceDownload/Official/3")
                {
                    Content = httpContent
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _client.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    Log.Error($"Quá trình in chuyển đổi hóa đơn xảy ra lỗi {content}");
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.ExportPdf.OfficialFail"]);
                }

                if (string.IsNullOrEmpty(content))
                    return null;

                var exportInvoiceResponse = JsonConvert.DeserializeObject<FileDto>(content);

                return exportInvoiceResponse;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.ExportPdf.OfficialFail"]);
            }
        }

        public async Task<FileDto> UnOfficialsAsync(List<long> idInvoiceHeaders)
        {
            try
            {
                //liên kết, gọi tới api khác để in
                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoiceHeaders), Encoding.UTF8, "application/json");

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, "api/ExportPdf/InvoiceUnOfficial/3")
                {
                    Content = httpContent
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var result = await _client.SendAsync(request);

                if (result.StatusCode != HttpStatusCode.OK)
                {
                    Log.Error("Có lỗi trong quá trình xử lý!");
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.ExportPdf.UnOfficialFail"]);
                }

                var content = await result.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<FileDto>(content);
            }
            catch (Exception e)
            {
                Log.Error(e, e.Message);
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.ExportPdf.UnOfficialFail"]);
            }
        }
    }
}
