using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Services
{
    public class SendMailHttpClient : ISendMailHttpClient
    {
        private readonly HttpClient _client;
        private readonly ILogger<ExportPdfGrpc> _logger;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public SendMailHttpClient(IHttpClientFactory httpClientFactory,
            ILogger<ExportPdfGrpc> logger,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _logger = logger;
            _appFactory = appFactory;
            _client = httpClientFactory.CreateClient("sendmail");
        }

        public async Task<SendMailInvoice03ApiResponseModel> SendMail(Invoice03HeaderEntity invoiceHeader, string mail)
        {
            try
            {
                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var requestModel = new SendMailInvoiceRequestModel
                {
                    Id = invoiceHeader.Id,
                    Email = mail,
                    InvoiceType = VnisType._03XKNB
                };

                var httpContent = new StringContent(JsonConvert.SerializeObject(requestModel), Encoding.UTF8, "application/json");
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, "api/SendMail/EmailInvoiceNotification/Email")
                {
                    Content = httpContent
                };

                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _client.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"Quá trình gửi mail hóa đơn xảy ra lỗi {content}");
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.SendMail.SendMailFail"]);
                }

                var sendMailInvoiceResponse = new SendMailInvoice03ApiResponseModel
                {
                    Id = invoiceHeader.Id,
                    ErpId = invoiceHeader.ErpId,
                    TransactionId = invoiceHeader.TransactionId,
                    SerialNo = invoiceHeader.SerialNo,
                    InvoiceNo = invoiceHeader.InvoiceNo,
                    TemplateNo = invoiceHeader.TemplateNo,
                };

                return sendMailInvoiceResponse;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.SendMail.SendMailFail"]);
            }
        }
    }
}
