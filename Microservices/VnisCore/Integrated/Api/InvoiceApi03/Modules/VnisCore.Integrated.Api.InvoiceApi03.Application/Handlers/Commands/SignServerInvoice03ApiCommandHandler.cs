using Core.Shared.Factory;
using Core.Shared.Validations;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace Einvoice.Module.Invoice03SignServer.Handlers.Commands
{
    public class SignServerInvoice03ApiCommandHandler : IRequestHandler<Invoice03ApiSignServerRequestModel, Invoice03ApiSignServerResponseModel>
    {
        private readonly IValidationContext _validationContext;
        private readonly IServiceProvider _servicerProvider;
        private readonly IAppFactory _appFactory;


        public SignServerInvoice03ApiCommandHandler(IServiceProvider servicerProvider,
            IValidationContext validationContext,
            IAppFactory appFactory)
        {
            _validationContext = validationContext;
            _servicerProvider = servicerProvider;
            _appFactory = appFactory;
        }


        public async Task<Invoice03ApiSignServerResponseModel> Handle(Invoice03ApiSignServerRequestModel request, CancellationToken cancellationToken)
        {
            var invoice = _validationContext.GetItem<Invoice03HeaderEntity>("Invoice");
            var signService = _servicerProvider.GetService<ISignServerHttpClient>();

            return await signService.SignServer(invoice);
        }
    }

}
