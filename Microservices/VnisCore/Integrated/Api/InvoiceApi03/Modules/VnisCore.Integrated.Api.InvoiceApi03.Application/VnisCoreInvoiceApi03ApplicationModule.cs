using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Business;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.RabbitClient;
using VnisCore.Integrated.Api.InvoiceApi03.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.InvoiceApi03.Application.RabbitClient.Services;
using VnisCore.Integrated.Api.InvoiceApi03.Application.RabbitClient.Workers;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Repositories;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Services;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Approve;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.ApproveAndSign;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Cancel;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Create;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.CreateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.CreateReplace;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Delete;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.DeleteDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.SendMail;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Sign;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Update;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.UpdateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.UpdateReplace;
using VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.ValidationRules.Delete;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule)
    )]
    public class VnisCoreInvoiceApi03ApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<ITaxService, TaxService>();
            context.Services.AddScoped<INumberService, NumberServiceExtend>();
            context.Services.AddScoped(typeof(IInvoiceService<,,>), typeof(InvoiceService<,,>));
            context.Services.AddScoped<IInvoice03Service, Invoice03Service>();
            context.Services.AddScoped<IExportPdfHttpClient, ExportPdfGrpc>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            context.Services.AddScoped<ISignServerHttpClient, SignServerHttpClient>();
            context.Services.AddScoped<ISendMailHttpClient, SendMailHttpClient>();
            context.Services.AddScoped<ILicenseSharedService, LicenseSharedService>();

            context.Services.AddScoped(typeof(IInvoiceHeaderRepository<>), typeof(InvoiceHeaderRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceHeaderExtraRepository<>), typeof(InvoiceHeaderExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailExtraRepository<>), typeof(InvoiceDetailExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<IInvoice03HeaderRepository, Invoice03HeaderRepository>();
            context.Services.AddScoped<IInvoice03DetailRepository, Invoice03DetailRepository>();
            context.Services.AddScoped<IInvoice03HeaderFieldRepository, Invoice03HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice03DetailFieldRepository, Invoice03DetailFieldRepository>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IMonitorInvoiceTemplateRepository, MonitorInvoiceTemplateRepository>();
            context.Services.AddScoped<INewRegistrationHeaderRepository, NewRegistrationHeaderRepository>();
            context.Services.AddScoped<IInvoice03DocumentService, Invoice03DocumentService>();


            context.Services.AddScoped<ICreateInvoice03Business, CreateInvoice03Business>();


            context.Services.Configure<DistributedCacheEntryOptions>(options =>
            {
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
            });

            AddInvoice03ApiValidators(context.Services);
            AddRabbitervices(context.Services, context.Services.GetConfiguration());
            AddExportPdfServices(context.Services, context.Services.GetConfiguration());
            AddSignServerServices(context.Services, context.Services.GetConfiguration());
            AddSendMailServices(context.Services, context.Services.GetConfiguration());

            context.Services.AddAutoMapperObjectMapper<VnisCoreInvoiceApi03ApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreInvoiceApi03ApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreInvoiceApi03ApplicationModule).GetTypeInfo().Assembly);
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();
            context.Services.AddScoped<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddExportPdfServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("exportpdfs", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:ExportPdf:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSendMailServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("sendmail", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:SendMail:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:SendMail:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISendMailHttpClient, SendMailHttpClient>();
        }

        public static void AddSignServerServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("signserver", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:Sign:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:Sign:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }


        public static void AddRabbitervices(IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<RabbitOption>(configuration.GetSection("RabbitMQ:Connections:Default"));
            services.AddScoped<IInvoiceFactory, InvoiceFactory>();

            services.AddSingleton<IInvoice03CommandHandler, Invoice03CommandHandler>();
            services.AddSingleton<ISignInvoice03CommandHandler, SignInvoice03CommandHandler>();

            services.AddSingleton<IRabbitBus, RabbitBus>();
            services.AddSingleton<IRabbitBusClient, RabbitBusClient>();
            services.AddSingleton<IRabbitService, VnisBackgroundService>();

            //Worker
            services.AddHostedService<Invoice03Worker>();
            //services.AddHostedService<Invoice03ReferenceWorker>();

            //Services
            services.AddScoped<IInvoiceHandler, CreateRootInvoice03Service>();
            services.AddScoped<IInvoiceHandler, UpdateRootInvoice03Service>();
            //services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderInvoice03Service>();
            //services.AddScoped<IInvoiceHandler, CreateAdjustmentDetailInvoice03Service>();
            //services.AddScoped<IInvoiceHandler, UpdateAdjustmentHeaderInvoice03Service>();
            //services.AddScoped<IInvoiceHandler, UpdateAdjustmentDetailInvoice03Service>();
            services.AddScoped<IInvoiceHandler, CancelInvoice03Service>();
            services.AddScoped<IInvoiceHandler, DeleteInvoice03Service>();
            services.AddScoped<IInvoiceHandler, ApproveInvoice03Service>();
            services.AddScoped<IInvoiceHandler, CreateReplaceInvoice03Service>();
            services.AddScoped<IInvoiceHandler, UpdateReplaceInvoice03Service>();
            //services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderWithoutInvoiceNoInvoice03Service>();
            services.AddScoped<ILicenseSharedService, LicenseSharedService>();

        }

        public static void AddInvoice03ApiValidators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateInvoice03RequestModel>(new List<Type>
            {
                typeof(CreateInvoiceCheckIndexDetailRule),
                typeof(CreateInvoicePreProcess),
                typeof(CreateInvoiceCheckExistIdErpRule),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckTemplateCanCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckIndexDuplicateRule),
                typeof(CreateInvoiceCheckDetailExtraRule)
            });

            services.AddConfigValidator<ApproveInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(ApproveInvoice03ApiPreProcess),
                typeof(ApproveInvoice03ApiCheckUserRule),
                typeof(ApproveInvoice03ApiCheckStatusRule)
            });

            services.AddConfigValidator<ApproveAndSignInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(ApproveAndSignInvoice03PreProcess),
                typeof(ApproveAndSignInvoice03ApiCheckStatusRule),
            });


            services.AddConfigValidator<UpdateInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(UpdateInvoice03ApiCheckIndexDetailRule),
                typeof(UpdateInvoice03ApiPreProcess),
                typeof(UpdateInvoice03ApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoice03ApiCheckStatusRule),
                typeof(UpdateInvoice03ApiCheckInvoiceDateRangeRule),
                typeof(UpdateInvoice03ApiCheckProductTypeRule),
                typeof(UpdateInvoice03ApiCheckExistCurrencyRule),
                typeof(UpdateInvoice03ApiCheckExchangeRateRule),
                typeof(UpdateInvoice03ApiCheckHeaderExtraRule),
                typeof(UpdateInvoice03ApiCheckDetailExtraRule),
                typeof(UpdateInvoice03ApiCheckIndexDuplicateRule),
            });



            services.AddConfigValidator<CreateReplaceInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(CreateReplaceInvoice03ApiCheckIndexDetailRule),
                typeof(CreateReplaceInvoiceApiPreProcess),
                typeof(CreateReplaceInvoice03ApiCheckExistIdErpRule),
                typeof(CreateReplaceInvoiceApiCheckLicenseRule),
                typeof(CreateReplaceInvoiceApiCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceApiCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceApiCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceApiCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceApiCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceApiCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceApiCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceApiCheckIndexDuplicateRule),
                //typeof(CreateReplaceInvoiceApiCheckPaymentMethodRule),
                //typeof(CreateReplaceInvoiceApiCheckDuplicateBuyerCodeRule),
                //typeof(CreateReplaceInvoiceApiCheckExistTaxRule),
                typeof(CreateReplaceInvoiceApiCheckHeaderExtraRule),
                typeof(CreateReplaceInvoiceApiCheckDetailExtraRule)
            });

            services.AddConfigValidator<UpdateReplaceInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(UpdateReplaceInvoice03ApiCheckIndexDetailRule),
                typeof(UpdateReplaceInvoice03ApiPreProcess),
                typeof(UpdateReplaceApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateReplaceInvoice03ApiCheckStatusRule),
                typeof(UpdateReplaceInvoice03ApiCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceInvoice03ApiCheckDocumentDateRule),
                typeof(UpdateReplaceInvoice03ApiCheckExistCurrencyRule),
                typeof(UpdateReplaceInvoice03ApiCheckExchangeRateRule),
                //typeof(UpdateReplaceInvoice03ApiCheckPaymentMethodRule),
                //typeof(UpdateReplaceInvoice03ApiCheckExistTaxRule),
                typeof(UpdateReplaceInvoice03ApiCheckHeaderExtraRule),
                typeof(UpdateReplaceInvoice03ApiCheckDetailExtraRule),
                typeof(UpdateReplaceInvoice03ApiCheckIndexDuplicateRule),
            });

            services.AddConfigValidator<DeleteInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(DeleteInvoice03ApiPreProcess),
                typeof(DeleteInvoice03ApiCheckStatusRule),
                typeof(DeleteInvoice03ApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CancelInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(CancelInvoice03ApiPreProcess),
                typeof(CancelInvoice03ApiCheckStatusRule),
            });

            services.AddConfigValidator<Invoice03ApiSignServerRequestModel>(new List<Type>
            {
                typeof(SignInvoice03ApiPreProcess),
                typeof(SignInvoice03ApiCheckSignRule),
            });

            services.AddConfigValidator<CreateDocumentInfoInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(CreateDocumentInfoInvoice03ApiPreProcess),
                typeof(CreateDocumentInfoInvoice03ApiCheckStatusRule),
                typeof(CreateDocumentInfoInvoice03ApiCheckExistRule),
                typeof(CreateDocumentInfoInvoice03ApiCheckDateRule)
            });

            services.AddConfigValidator<UpdateDocumentInfoInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(UpdateDocumentInfoInvoice03ApiPreProcess),
                typeof(UpdateDocumentInfoInvoice03ApiCheckStatusRule),
                typeof(UpdateDocumentInfoInvoice03ApiCheckDateRule)
            });

            services.AddConfigValidator<DeleteDocumentInfoInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(DeleteDocumentInfoInvoice03ApiPreProcess),
                typeof(DeleteDocumentInfoInvoice03ApiCheckExistRule),
                typeof(DeleteDocumentInfoInvoice03ApiCheckStatusRule)
            });

            services.AddConfigValidator<SendMailInvoice03ApiRequestModel>(new List<Type>
            {
                typeof(SendMailInvoice03ApiPreProcess),
                typeof(SendMailInvoice03ApiCheckSendMailRule),
            });
        }
    }
}
