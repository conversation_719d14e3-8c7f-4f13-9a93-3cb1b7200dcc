using Core.Shared.Attributes;
using Core.Shared.Models;
using MediatR;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Queries
{
    public class DownloadUnOfficialInvoice03RequestModel : IRequest<PrintApiModel>
    {
        public string ErpId { get; set; }

        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [SerialNo(ErrorMessage = "Ký hiệu hóa đơn không đúng định dạng")]
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        [InvoiceNo(ErrorMessage = "Số hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }
    }
}
