{
  "App": {
    "SelfUrl": "https://localhost:6020",
    "CorsOrigins": "http://localhost:6789,http://localhost:6889",
    "RedirectAllowedUrls": "http://localhost:4200"
  },
  "AppSelfUrl": "https://localhost:6020/",
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "Service": {
    "Name": "VnisCore.Integrated.Api.InvoiceApi01.Host",
    "Title": "VnisCore.Integrated.Api.InvoiceApi01.Host",
    "BaseUrl": "01gtkt",
    "AuthApiName": "VnisCore.Integrated.Api.InvoiceApi01.Host"
  },
  "Redis": {
    "IsEnabled": "true",
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14",
    "Database": 14
  },
  "AuthServer": {
    "Authority": "https://invoice-mass-auth.vnpaytest.vn/",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_Swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "Settings": {
    "InvoicesMaxGroup": "0",
    "IsEnableMongoDbLocalTime": 1,
    "TimeDelayInvoiceGenerateNumberSingle": 5, //minutes
    "TimeDelayLoopInvoiceGenerateNumberInBatchSingle": 1000, //milisecond
    "TimeDelayInvoiceGenerateNumberInBatchSingle": 1000, //milisecond
    "TimeDelayInvoiceGenerateNumberMany": 10, //milisecond, minute
    "TimeDelayLoopInvoiceGenerateNumberMany": 100, //milisecond
    "TimeDelayInvoiceGenerateNumberInBatch": 20, //milisecond
    "MaxTimeDelayInvoiceGenerateNumberInBatch": 100, //milisecond
    "TimeDelayInvoiceGenerateNumber": 60000 //milisecond
  },

  "Minio": {
    "Endpoint": "**************:9000",
    "AccessKey": "vnis",
    "SecretKey": "Vnis@12A",
    "Region": null,
    "SessionToken": null,
    "BucketName": "staging-core50"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "************",
        "Port": 5672,
        "UserName": "admin",
        "Password": "admin@123",
        "VirtualHost": "/massv5-invoice",
        "Workers": {
          "Einvoice": {
            "ConnectionName": "Einvoice",
            "NumberOfConsumer": 1
          },
          "GenerateInvoice01": {
            "ConnectionName": "GenerateInvoice01",
            "NumberOfConsumer": 1
          }
        }
      }
    },
    "EventBus": {
      "ClientName": "einvoice.invoiceapi01",
      "ExchangeName": "einvoice"
    },
    "Settings": {
      "NumberRecordsInMessage": 10
    }
  },
  "Microservices": {
    "ExportPdf": {
      "Endpoint": "http://localhost:6003/",
      "Timeout": 5
    },
    "Sign": {
      "Endpoint": "https://invoice-mass-gateway.vnpaytest.vn/",
      "Timeout": 5
    },
    "SendMail": {
      "Endpoint": "http://localhost:6013/",
      "Timeout": 5
    }
  },
  "XApiKey": "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp",
  "Elasticsearch": {
    "Index": "dev.core50.invoice01",
    "Url": "http://**************:9200",
    "Urls": "http://**************:9200"
  },
  "Logging": {
    "RootFolder": {
      "Folder": "D:\\Work\\invoicev5-backend-services\\Microservices\\VnisCore\\Integrated\\Api\\InvoiceApi01\\VnisCore.Integrated.Api.InvoiceApi01.Host"
    }
  }
}