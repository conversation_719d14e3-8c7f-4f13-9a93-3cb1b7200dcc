using Core.Application;
using Core.AutoMapper;
using Core.Dto.Shared;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;

using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Business;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Services;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Workers;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Services;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Approve;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.ApproveAndSign;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Cancel;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Create;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjHeaderWithoutInvoiceNo;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjust;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentDetail;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentHeader;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateBatch;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateOldDecree;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateReplace;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Delete;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.DeleteDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.SendMail;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.SendMailWithPrintAction;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Sign;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Update;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjusmentHeader;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjust;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjustmentDetail;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjustmentHeader;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateDocumentInfo;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateOldDecree;
using VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateReplace;
using VnisCore.Invoice01.Infrastructure;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule),
        typeof(SharedDtoModule),
        typeof(VnisCoreMongoDbModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
    )]
    public class VnisCoreInvoiceApi01ApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();
            //context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<ITaxService, TaxService>();
            context.Services.AddScoped<INumberService, NumberServiceExtend>();
            context.Services.AddScoped(typeof(IInvoiceService<,,>), typeof(InvoiceService<,,>));
            context.Services.AddScoped<IInvoice01Service, Invoice01Service>();
            context.Services.AddScoped<IExportPdfHttpClient, ExportPdfGrpc>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            context.Services.AddScoped<ISignServerHttpClient, SignServerHttpClient>();
            context.Services.AddScoped<ISendMailHttpClient, SendMailHttpClient>();

            context.Services.AddScoped(typeof(IInvoiceHeaderRepository<>), typeof(InvoiceHeaderRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceOldDecreeRepository<>), typeof(InvoiceReferenceOldDecreeRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceHeaderExtraRepository<>), typeof(InvoiceHeaderExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailExtraRepository<>), typeof(InvoiceDetailExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<IInvoice01HeaderRepository, Invoice01HeaderRepository>();
            context.Services.AddScoped<IInvoice01ErpIdRepository, Invoice01ErpIdRepository>();
            context.Services.AddScoped<IInvoice01HeaderFieldRepository, Invoice01HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice01DetailFieldRepository, Invoice01DetailFieldRepository>();
            context.Services.AddScoped<IInvoice01TaxBreakdownRepository, Invoice01TaxBreakdownRepository>();
            context.Services.AddScoped<IApiInvoice01SpecificProductExtraRepository, ApiInvoice01SpecificProductExtraRepository>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IMonitorInvoiceTemplateRepository, MonitorInvoiceTemplateRepository>();
            context.Services.AddScoped<INewRegistrationHeaderRepository, NewRegistrationHeaderRepository>();
            context.Services.AddScoped<IInvoice01DocumentService, Invoice01DocumentService>();
            context.Services.AddScoped<IUpdateRootInvoice01ApiBusiness, UpdateRootInvoice01ApiBusiness>();
            context.Services.AddScoped<IUpdateReplaceInvoice01Business, UpdateReplaceInvoice01Business>();
            context.Services.AddScoped<IUpdateAdjustHeaderInvoice01Business, UpdateAdjustHeaderInvoice01Business>();
            context.Services.AddScoped<IUpdateAdjustDetailInvoice01Business, UpdateAdjustDetailInvoice01Business>();
            context.Services.AddScoped<ICreateAdjustmentHeaderWithoutInvoiceNoBusiness, CreateAdjustmentHeaderWithoutInvoiceNoBusiness>();
            context.Services.AddTransient<IInvoiceTemplateRepository, InvoiceTemplateRepository>();
            
            //business
            context.Services.AddTransient<IResyncSignStatusToESBusiness, ResyncSignStatusToESBusiness>();

            // business
            context.Services.AddTransient<ISyncVerificationCodeToEsBusiness, SyncVerificationCodeToEsBusiness>();
            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            context.Services.Configure<DistributedCacheEntryOptions>(options =>
            {
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
            });

            AddInvoice01ApiValidators(context.Services);
            AddRabbitervices(context.Services, context.Services.GetConfiguration());
            AddExportPdfServices(context.Services, context.Services.GetConfiguration());
            AddSignServerServices(context.Services, context.Services.GetConfiguration());
            AddSendMailServices(context.Services, context.Services.GetConfiguration());

            context.Services.AddAutoMapperObjectMapper<VnisCoreInvoiceApi01ApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreInvoiceApi01ApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreInvoiceApi01ApplicationModule).GetTypeInfo().Assembly);
        }

        public static void AddExportPdfServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("exportpdfs", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:ExportPdf:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSignServerServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("signserver", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:Sign:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:Sign:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSendMailServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("sendmail", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:SendMail:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:SendMail:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISendMailHttpClient, SendMailHttpClient>();
        }

        public static void AddRabbitervices(IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<RabbitOption>(configuration.GetSection("RabbitMQ:Connections:Default"));
            services.AddScoped<IInvoiceFactory, InvoiceFactory>();

            services.AddSingleton<IInvoice01CommandHandler, Invoice01CommandHandler>();
            services.AddSingleton<ISignInvoice01CommandHandler, SignInvoice01CommandHandler>();

            services.AddSingleton<IRabbitBus, RabbitBus>();
            services.AddSingleton<IRabbitBusClient, RabbitBusClient>();
            services.AddSingleton<IRabbitService, VnisBackgroundService>();

            //Worker
            services.AddHostedService<Invoice01Worker>();
            //services.AddHostedService<Invoice01ReferenceWorker>();

            //Services
            services.AddScoped<IInvoiceHandler, CreateRootInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CreateRootOldDecreeInvoice01Service>();
            services.AddScoped<IInvoiceHandler, UpdateRootInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentDetailInvoice01Service>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentHeaderInvoice01Service>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentDetailInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CancelInvoice01Service>();
            services.AddScoped<IInvoiceHandler, DeleteInvoice01Service>();
            services.AddScoped<IInvoiceHandler, ApproveInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CreateReplaceInvoice01Service>();
            services.AddScoped<IInvoiceHandler, UpdateReplaceInvoice01Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderWithoutInvoiceNoInvoice01Service>();
            services.AddScoped<ILicenseSharedService, LicenseSharedService>();

        }

        public static void AddInvoice01ApiValidators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateInvoice01RequestModel>(new List<Type>
            {
                typeof(CreateInvoiceCheckBuyerInformationRule),
                typeof(CreateInvoiceCheckDiscountTypeRule),
                typeof(CreateInvoiceCheckIndexDetailRule),
                typeof(CreateInvoiceCheckMoneyNegativeRule),
                typeof(CreateInvoiceCheckExistIdErpRule),
                typeof(CreateInvoiceFormatNumber),
                typeof(CreateInvoicePreProcess),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckTemplateCanCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckPaymentMethodRule),
                typeof(CreateInvoiceCheckExistTaxRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckDetailExtraRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckPendingLastInvoiceDateRule),
            });
            
            services.AddConfigValidator<CreateBatchInvoice01RequestModel>(new List<Type>
            {
                typeof(CreateBatchInvoiceCheckBuyerInformationRule),
                typeof(CreateBatchInvoiceCheckDiscountTypeRule),
                typeof(CreateBatchInvoiceCheckIndexDetailRule),
                typeof(CreateBatchInvoiceCheckMoneyNegativeRule),
                typeof(CreateBatchInvoiceCheckExistIdErpRule),
                typeof(CreateBatchInvoiceFormatNumber),
                typeof(CreateBatchInvoicePreProcess),
                typeof(CreateBatchInvoiceCheckLicenseRule),
                typeof(CreateBatchInvoiceCheckExistTenantInfoRule),
                typeof(CreateBatchInvoiceCheckExistTemplateRule),
                typeof(CreateBatchInvoiceCheckTemplateCanCreateRule),
                typeof(CreateBatchInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateBatchInvoiceCheckExistCurrencyRule),
                typeof(CreateBatchInvoiceCheckExchangeRateRule),
                typeof(CreateBatchInvoiceCheckPaymentMethodRule),
                typeof(CreateBatchInvoiceCheckExistTaxRule),
                typeof(CreateBatchInvoiceCheckHeaderExtraRule),
                typeof(CreateBatchInvoiceCheckDetailExtraRule),
                typeof(CreateBatchInvoiceCheckProductTypeRule),
                typeof(CreateBatchInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateInvoice01OldDecreeRequestModel>(new List<Type>
            {
                typeof(CreateInvoiceOldDecreeCheckBuyerInformationRule),
                typeof(CreateInvoiceOldCheckDiscountTypeRule),
                typeof(CreateInvoiceOldDecreeCheckIndexDetailRule),
                typeof(CreateInvoiceOldDecreeCheckExistIdErpRule),
                typeof(CreateInvoiceOldDecreeFormatNumber),
                typeof(CreateInvoiceOldDecreePreProcess),
                typeof(CreateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckLicenseRule),
                typeof(CreateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(CreateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(CreateInvoiceOldDecreeCheckTemplateCanCreateRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(CreateInvoiceOldDecreeCheckExchangeRateRule),
                typeof(CreateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(CreateInvoiceOldDecreeCheckExistTaxRule),
                typeof(CreateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(CreateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(CreateInvoiceOldDecreeCheckProductTypeRule),
                typeof(CreateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<ApproveInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(ApproveInvoice01ApiPreProcess),
                typeof(ApproveInvoice01ApiCheckUserRule),
                typeof(ApproveInvoice01ApiCheckStatusRule)
            });

            services.AddConfigValidator<ApproveAndSignInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(ApproveAndSignInvoice01PreProcess),
                typeof(ApproveAndSignInvoice01ApiCheckStatusRule)
            });


            services.AddConfigValidator<UpdateInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateInvoice01ApiCheckBuyerInformationRule),
                typeof(UpdateInvoiceCheckDiscountTypeRule),
                typeof(UpdateInvoice01ApiCheckIndexDetailRule),
                typeof(UpdateInvoiceCheckMoneyNegativeRule),
                typeof(UpdateInvoice01ApiFormatNumber),
                typeof(UpdateInvoice01ApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoice01ApiPreProcess),
                typeof(UpdateInvoice01ApiCheckStatusRule),
                typeof(UpdateInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(UpdateInvoice01ApiCheckExistCurrencyRule),
                typeof(UpdateInvoice01ApiCheckExchangeRateRule),
                typeof(UpdateInvoice01ApiCheckPaymentMethodRule),
                typeof(UpdateInvoice01ApiCheckExistTaxRule),
                typeof(UpdateInvoice01ApiCheckHeaderExtraRule),
                typeof(UpdateInvoice01ApiCheckDetailExtraRule),
                typeof(UpdateInvoice01ApiCheckProductTypeRule),
                typeof(UpdateInvoice01ApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateInvoice01OldDecreeRequestModel>(new List<Type>
            {
                typeof(UpdateInvoiceOldDecreeApiCheckBuyerInformationRule),
                typeof(UpdasteInvoiceOldCheckDiscountTypeRule),
                typeof(UpdateInvoiceOldDecreeCheckIndexDetailRule),
                typeof(UpdateInvoiceOldDecreePreProcess),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(UpdateInvoiceOldDecreeCheckExchangeRateRule),
                //typeof(UpdateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(UpdateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(UpdateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTaxRule),
                typeof(UpdateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
                typeof(UpdateInvoiceOldDecreeCheckProductTypeRule),
                typeof(UpdateInvoiceOldDecreeCheckTemplateCanCreateRule),
                typeof(UpdateInvoiceOldDecreeFormatNumber),
            });

            services.AddConfigValidator<CreateReplaceInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CreateReplaceInvoice01ApiCheckBuyerInformationRule),
                typeof(CreateReplaceInvoiceCheckDiscountTypeRule),
                typeof(CreateReplaceInvoice01ApiCheckIndexDetailRule),
                typeof(CreateReplaceInvoice01ApiCheckExistIdErpRule),
                typeof(UpdateInvoiceReplaceCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(CreateReplaceInvoice01ApiFormatNumber),
                typeof(CreateReplaceInvoiceApiPreProcess),
                //typeof(CreateReplaceInvoiceApiCheckInvoiceBackDateRule),
                typeof(CreateReplaceInvoiceApiCheckLicenseRule),
                typeof(CreateReplaceInvoiceApiCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceApiCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceApiCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceApiCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceApiCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceApiCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceApiCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceApiCheckPaymentMethodRule),
                //typeof(CreateReplaceInvoiceApiCheckDuplicateBuyerCodeRule),
                typeof(CreateReplaceInvoiceApiCheckExistTaxRule),
                typeof(CreateReplaceInvoiceApiCheckHeaderExtraRule),
                typeof(CreateReplaceInvoice01ApiCheckDetailExtraRule),
                typeof(CreateReplaceInvoice01ApiCheckProductTypeRule),
                typeof(CreateReplaceInvoiceApiCheckPendingLastInvoiceDateRule),
                //typeof(CreateReplaceInvoiceApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<UpdateReplaceInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateReplaceInvoice01ApiCheckBuyerInformationRule),
                typeof(UpdateReplaceInvoiceCheckDiscountTypeRule),
                typeof(UpdateReplaceInvoice01ApiCheckIndexDetailRule),
                typeof(UpdateReplaceInvoice01ApiFormatNumber),
                typeof(UpdateReplaceInvoice01ApiPreProcess),
                typeof(UpdateReplaceInvoice01ApiCheckStatusRule),
                typeof(UpdateReplaceInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceInvoice01ApiCheckDocumentDateRule),
                typeof(UpdateReplaceInvoice01ApiCheckExistCurrencyRule),
                typeof(UpdateReplaceInvoice01ApiCheckExchangeRateRule),
                typeof(UpdateReplaceInvoice01ApiCheckPaymentMethodRule),
                typeof(UpdateReplaceInvoice01ApiCheckExistTaxRule),
                typeof(UpdateReplaceInvoice01ApiCheckHeaderExtraRule),
                typeof(UpdateReplaceInvoice01ApiCheckDetailExtraRule),
                typeof(UpdateReplaceInvoice01ApiCheckProductTypeRule),
                typeof(UpdateReplaceInvoiceApiCheckPendingLastInvoiceDateRule),
            });


            services.AddConfigValidator<CreateAdjustmentDetailInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustmentDetailInvoice01ApiCheckIndexDetailRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckExistIdErpRule),
                typeof(CreateAdjustmentDetailInvoice01ApiFormatNumber),
                typeof(CreateAdjustmentDetailInvoice01ApiPreProcess),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckLicenseRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckExistTemplateRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckChangeSerialRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckExistTaxRule),
                typeof(CreateAdjustmentDetailInvoice01ApiCheckDetailExtraRule),
                typeof(CreateAdjustmentDetailInvoiceApiCheckPendingLastInvoiceDateRule),
                //typeof(CreateAdjustmentDetailInvoice01ApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CreateAdjustmentHeaderInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjHeaderInvoice01ApiCheckBuyerInformationRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckExistIdErpRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiPreProcess),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckLicenseRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckExistTemplateRule),
                typeof(CreateAdjHeaderInvoice01ApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckChangeSerialRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckHeaderExtraRule),
                typeof(CreateAdjustmentHeaderInvoice01ApiCheckPaymentMethodRule),
                typeof(CreateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule),
                //typeof(CreateAdjustmentHeaderInvoice01ApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<UpdateAdjustmentHeaderInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjHeaderCheckBuyerInformationRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiPreProcess),
                typeof(UpdateAdjHeaderCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiCheckStatusRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiCheckHeaderExtraRule),
                typeof(UpdateAdjustmentHeaderInvoice01ApiCheckPaymentMethodRule),
                typeof(UpdateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<DeleteInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(DeleteInvoice01ApiPreProcess),
                typeof(DeleteInvoice01ApiCheckStatusRule),
                typeof(DeleteInvoice01ApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CancelInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CancelInvoice01ApiPreProcess),
                typeof(CancelInvoice01ApiCheckStatusRule),
            });

            services.AddConfigValidator<Invoice01ApiSignServerRequestModel>(new List<Type>
            {
                typeof(SignInvoice01ApiPreProcess),
                typeof(SignInvoice01ApiCheckSignRule),
            });


            services.AddConfigValidator<UpdateAdjustmentDetailInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckIndexDetailRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiFormatNumber),
                typeof(UpdateAdjDetailCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiPreProcess),
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckStatusRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckExistTaxRule),
                typeof(UpdateAdjustmentDetailInvoice01ApiCheckDetailExtraRule),
                typeof(UpdateAdjustmentDetailInvoiceApiCheckPendingLastInvoiceDateRule),
            });


            services.AddConfigValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>(new List<Type>
            {
                typeof(CreateAdjHeaderWithoutInvoiceNoInvoice01ApiPreProcess),
                typeof(CreateAdjHeaderWithoutInvoiceNoInvoice01ApiCheckInvoiceRule),
                //typeof(CreateAdjHeaderWithoutInvoiceNoInvoice01ApiCheckInfoErrorRule),
            });


            services.AddConfigValidator<CreateDocumentInfoInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CreateDocumentInfoInvoice01ApiPreProcess),
                typeof(CreateDocumentInfoInvoice01ApiCheckStatusRule),
                typeof(CreateDocumentInfoInvoice01ApiCheckExistRule),
                typeof(CreateDocumentInfoInvoice01ApiCheckDateRule)
            });

            services.AddConfigValidator<UpdateDocumentInfoInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateDocumentInfoInvoice01ApiPreProcess),
                typeof(UpdateDocumentInfoInvoice01ApiCheckStatusRule),
                typeof(UpdateDocumentInfoInvoice01ApiCheckDateRule)
            });

            services.AddConfigValidator<DeleteDocumentInfoInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(DeleteDocumentInfoInvoice01ApiPreProcess),
                typeof(DeleteDocumentInfoInvoice01ApiCheckExistRule),
                typeof(DeleteDocumentInfoInvoice01ApiCheckStatusRule)
            });

            services.AddConfigValidator<SendMailInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(SendMailInvoice01ApiPreProcess),
                typeof(SendMailInvoice01ApiCheckSendMailRule),
            });

            services.AddConfigValidator<SendMailInvoice01WithPrintActionApiRequestModel>(new List<Type>
            {
                typeof(SendMailInvoice01ApiWithPrintActionPreProcess),
                typeof(SendMailInvoice01ApiCheckSendMailWithPrintActionRule),
            });

            services.AddConfigValidator<CreateAdjustInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustInvoiceApiCheckBuyerInformationRule),
                typeof(CreateAdjustInvoiceCheckIndexDetailRule),
                typeof(CreateAdjustInvoice01ApiCheckExistIdErpRule),
                typeof(CreateAdjustInvoice01ApiFormatNumber),
                typeof(CreateAdjustInvoiceApiPreProcess),
                typeof(CreateAdjustInvoiceApiCheckLicenseRule),
                typeof(CreateAdjustInvoiceApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustInvoiceCheckDiscountTypeRule),
                typeof(CreateAdjustInvoiceApiCheckExistTemplateRule),
                typeof(CreateAdjustInvoiceApiCheckTemplateCreateRule),
                typeof(CreateAdjustInvoiceApiCheckChangeSerialRule),
                typeof(CreateAdjustInvoiceApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustInvoiceApiCheckExistCurrencyRule),
                typeof(CreateAdjustInvoiceApiCheckExchangeRateRule),
                typeof(CreateAdjustInvoiceApiCheckPaymentMethodRule),
                typeof(CreateAdjustInvoiceApiCheckExistTaxRule),
                typeof(CreateAdjustInvoiceApiCheckHeaderExtraRule),
                typeof(CreateAdjustInvoice01ApiCheckDetailExtraRule),
                typeof(CreateAdjustInvoice01ApiCheckProductTypeRule),
                typeof(CreateAdjustInvoiceApiCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateAdjustInvoice01ApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustInvoice01ApiCheckBuyerInformationRule),
                typeof(UpdateAdjustInvoiceCheckIndexDetailRule),
                typeof(UpdateAdjustInvoice01ApiFormatNumber),
                typeof(UpdateAdjustInvoice01ApiPreProcess),
                typeof(UpdateAdjustInvoiceCheckDiscountTypeRule),
                typeof(UpdateAdjustInvoice01ApiCheckProductRule),
                typeof(UpdateAdjustInvoice01ApiCheckStatusRule),
                typeof(UpdateAdjustInvoice01ApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustInvoice01ApiCheckDocumentDateRule),
                typeof(UpdateAdjustInvoice01ApiCheckExistCurrencyRule),
                typeof(UpdateAdjustInvoice01ApiCheckExchangeRateRule),
                typeof(UpdateAdjustInvoice01ApiCheckPaymentMethodRule),
                typeof(UpdateAdjustInvoice01ApiCheckExistTaxRule),
                typeof(UpdateAdjustInvoice01ApiCheckHeaderExtraRule),
                typeof(UpdateAdjustInvoice01ApiCheckDetailExtraRule),
                typeof(UpdateAdjustInvoice01ApiCheckProductTypeRule),
                typeof(UpdateAdjustInvoiceApiCheckPendingLastInvoiceDateRule),
            });
        }
    }
}
