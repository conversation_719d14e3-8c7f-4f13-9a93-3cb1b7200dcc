using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Data;
using Core.Dto.Shared;
using Core.Dto.Shared.Invoices.Invoice01;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Helper;
using Core.Shared.Invoice.Services;
using Core.Shared.Messages;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Shared.Validations;
using Core.TenantManagement;
using Core.Validation;
using Dapper;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using MongoDB.Driver.Linq;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.PrintNote;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Business;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Queries;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitMqEventBus.CreateInvoice01.MessageEventData;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;
using VnisCore.Invoice01.Infrastructure.IRepository;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application
{
    [Route("api/01gtkt")]
    [Authorize]
    public class InvoiceApi01Service : ApplicationService
    {
        private const string APIKEY = "XApiKey";
        private readonly IAppFactory _factory;
        private readonly IValidatorFactory _validatorFactory;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly ISettingService _settingService;
        private readonly IValidationContext _validationContext;
        private readonly IVnisCoreMongoInvoice01BatchIdRepository _mongoInvoice01BatchIdRepository;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IVnisCoreMongoInvoice01ErpIdRepository _mongoInvoice01ErpIdRepository;
        private readonly IConfiguration _configuration;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IUpdateAdjustHeaderInvoice01Business _updateAdjustHeaderInvoice01Business;
        private readonly IUpdateAdjustDetailInvoice01Business _updateAdjustDetailInvoice01Business;
        private readonly ICreateAdjustmentHeaderWithoutInvoiceNoBusiness _createAdjustmentHeaderWithoutInvoiceNoBusiness;
        private readonly IInvoiceReferenceRepository<Invoice01ReferenceEntity> _repoInvoice01Reference;
        private readonly IInvoiceReferenceOldDecreeRepository<Invoice01ReferenceOldDecreeEntity> _repoInvoice01ReferenceOldDecree;
        private readonly IVnisCoreMongoInvoice01LogRepository _vnisCoreMongoInvoice01LogRepository;
        private readonly IVnisCoreMongoInvoice01ReSyncRepository _mongoInvoice01ReSyncRepository;
        private readonly IInvoiceHeaderRepository<Invoice01HeaderEntity> _repoInvoice01Header;
        private readonly IInvoiceDetailRepository<Invoice01DetailEntity> _repoInvoice01Detail;
        private readonly IInvoice01TaxBreakdownRepository _repoInvoiceTaxBreakdown;
        private readonly IApiInvoice01SpecificProductExtraRepository _repoInvoice01SpecificProduct;
        private readonly IInvoice01Service _invoice01Service;
        private readonly IInvoiceTemplateRepository _invoiceTemplateRepository;
        private readonly IResyncSignStatusToESBusiness _resyncSignStatusToESBusiness;
        private readonly ISyncVerificationCodeToEsBusiness _syncVerificationCodeToEsBusiness;
        private readonly IInvoicePrintNoteRepository _invoicePrintNoteRepository;
        private readonly ILockInvoiceService _lockInvoiceService;
        private readonly ITaxReport01Repository _taxReport01Repository;

        public InvoiceApi01Service(IAppFactory appFactory,
            IValidatorFactory validatorFactory,
            IConfiguration configuration,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            ISettingService settingService,
            IValidationContext validationContext,
            IDistributedEventBus distributedEventBus,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
            IVnisCoreMongoInvoice01ErpIdRepository mongoInvoice01ErpIdRepository,
            IVnisCoreMongoInvoice01BatchIdRepository mongoInvoice01BatchIdRepository,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceReferenceRepository<Invoice01ReferenceEntity> repoInvoice01Reference,
            IUpdateAdjustHeaderInvoice01Business updateAdjustHeaderInvoice01Business,
            IUpdateAdjustDetailInvoice01Business updateAdjustDetailInvoice01Business,
            ICreateAdjustmentHeaderWithoutInvoiceNoBusiness createAdjustmentHeaderWithoutInvoiceNoBusiness,
            IVnisCoreMongoInvoice01LogRepository vnisCoreMongoInvoice01LogRepository,
            IInvoiceHeaderRepository<Invoice01HeaderEntity> repoInvoice01Header,
            IInvoiceDetailRepository<Invoice01DetailEntity> repoInvoice01Detail,
            IInvoice01TaxBreakdownRepository repoInvoiceTaxBreakdown,
            IApiInvoice01SpecificProductExtraRepository repoInvoice01SpecificProduct,
            IInvoiceReferenceOldDecreeRepository<Invoice01ReferenceOldDecreeEntity> repoInvoice01ReferenceOldDecree,
            IVnisCoreMongoInvoice01ReSyncRepository mongoInvoice01ReSyncRepository,
            IInvoice01Service invoice01Service,
            IInvoiceTemplateRepository invoiceTemplateRepository,
            IResyncSignStatusToESBusiness resyncSignStatusToESBusiness,
            ISyncVerificationCodeToEsBusiness syncVerificationCodeToEsBusiness,
            IInvoicePrintNoteRepository invoicePrintNoteRepository,
            ITaxReport01Repository taxReport01Repository,
            ILockInvoiceService lockInvoiceService)
        {
            _factory = appFactory;
            _validatorFactory = validatorFactory;
            _invoiceService = invoiceService;
            _settingService = settingService;
            _validationContext = validationContext;
            _configuration = configuration;
            _distributedEventBus = distributedEventBus;
            _mongoInvoice01BatchIdRepository = mongoInvoice01BatchIdRepository;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _mongoInvoice01ErpIdRepository = mongoInvoice01ErpIdRepository;
            _localizier = localizier;
            _updateAdjustHeaderInvoice01Business = updateAdjustHeaderInvoice01Business;
            _repoInvoice01Reference = repoInvoice01Reference;
            _updateAdjustDetailInvoice01Business = updateAdjustDetailInvoice01Business;
            _vnisCoreMongoInvoice01LogRepository = vnisCoreMongoInvoice01LogRepository;
            _createAdjustmentHeaderWithoutInvoiceNoBusiness = createAdjustmentHeaderWithoutInvoiceNoBusiness;
            _mongoInvoice01ReSyncRepository = mongoInvoice01ReSyncRepository;
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoice01Detail = repoInvoice01Detail;
            _repoInvoice01SpecificProduct = repoInvoice01SpecificProduct;
            _repoInvoiceTaxBreakdown = repoInvoiceTaxBreakdown;
            _repoInvoice01ReferenceOldDecree = repoInvoice01ReferenceOldDecree;
            _invoice01Service = invoice01Service;
            _invoiceTemplateRepository = invoiceTemplateRepository;
            _syncVerificationCodeToEsBusiness = syncVerificationCodeToEsBusiness;
            _invoicePrintNoteRepository = invoicePrintNoteRepository;
            _resyncSignStatusToESBusiness = resyncSignStatusToESBusiness;
            _lockInvoiceService = lockInvoiceService;
            _taxReport01Repository = taxReport01Repository;
        }

        /// <summary>
        /// Tra cứu hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PagedResultDto<PagingInvoice01ResponseModel>> Query([FromBody] PagingInvoice01RequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// Tra cứu hóa đơn với method POST
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PagedResultDto<PagingInvoice01ResponseModel>> QueryPost([FromBody] PagingInvoice01RequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        ///// <summary>
        ///// lấy id hóa đơn theo iderp
        ///// </summary>
        ///// <param name="command"></param>
        ///// <returns></returns>
        //[Produces("application/json")]
        //[HttpPost("codes")]
        //[AllowAnonymous]
        //public async Task<List<QueryIdByIdErpResponseModel>> QueryByCodesAsync([FromBody] QueryIdByIdErpRequestModel command)
        //{
        //    var response = await _factory.Mediator.Send(command);
        //    return response;
        //}

        /// <summary>
        /// lấy thông hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("info")]
        public async Task<ReadInvoice01ApiResponseModel> ReadAsync([FromQuery] ReadInvoice01ApiRequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        [Produces("application/json")]
        [HttpGet("info/{erpId}")]
        public async Task<ReadInvoice01ApiResponseModel> GetByErpIdAsync([FromRoute] string erpId)
        {
            var request = new QueryIdByIdErpRequestModel
            {
                ErpId = erpId
            };
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create")]
        public async Task<object> Create([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01RequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());

                #endregion

                var validator = _validatorFactory.GetValidator<CreateInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateInvoice01RequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                var idTaxBreakDowns = await GetSEQsNextVal(data.Sum(x => x.InvoiceTaxBreakdowns.Count), SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                var group = CurrentTenant.Group;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int i = 0; i < item.InvoiceDetails.Count; i++)
                    {
                        var itemDetail = item.InvoiceDetails[indexDetail];

                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[i];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.Id = idTaxBreakDowns[indexTaxBreakDown];
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;
                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                        indexTaxBreakDown++;
                    }

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    Id = Guid.NewGuid(),
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1,
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                                    {
                                        var messageData = new CreateInvoice01HeaderEventSendData
                                        {
                                            Invoices = itemsTake
                                        };
                                        await _distributedEventBus.PublishAsync(messageData);
                                    })
                                );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);

                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn 32
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create32")]
        public async Task<object> Create32([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01OldDecreeRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());
                #endregion

                var validator = _validatorFactory.GetValidator<CreateInvoice01OldDecreeRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateInvoice01OldDecreeRequestModel, MongoInvoice01Entity>(request) };
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                var idInvoiceTaxBreakDowns = await GetSEQsNextVal(data.Sum(x => x.InvoiceTaxBreakdowns.Count), SequenceName.Invoice01TaxBreakdown);
                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;

                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)request.InvoiceStatus;
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ReferenceInvoiceType = (short)request.ReferenceInvoiceType;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus));
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus));

                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceReferenceOld = new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceOldDto
                    {
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? (request.InvoiceReference.InvoiceDate.Date == request.InvoiceReference.InvoiceDate.Date.ToLocalTime() ? request.InvoiceReference.InvoiceDate.Date.AddHours(7) : request.InvoiceReference.InvoiceDate.Date.ToLocalTime()) : request.InvoiceReference.InvoiceDate.Date,
                        InvoiceHeaderId = item.Id,
                        InvoiceNoReference = request.InvoiceReference.InvoiceNo,
                        InvoiceStatus = (short)request.InvoiceStatus.GetHashCode(),
                        Note = request.InvoiceReference.Note,
                        NumberReference = int.Parse(request.InvoiceReference.InvoiceNo),
                        Partition = partition,
                        SerialNoReference = request.InvoiceReference.SerialNo,
                        TemplateNoReference = request.InvoiceReference.TemplateNo,
                        TenantId = tenantId,
                    };

                    foreach (var itemDetail in item.InvoiceDetails)
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakDowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    }

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = item.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }
                    }
                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();
                var startIndex = 0;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus),
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                return result;



                //TODO: ký tự động
                //var response = await _factory.Mediator.Send(request);

                //var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
                //var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
                //if (!hasApprove && setting != null && setting.Value == "1")
                //{
                //    var invoice = new Invoice01HeaderEntity
                //    {
                //        Id = response.Id,
                //        ErpId = request.ErpId,
                //        TransactionId = request.TransactionId,
                //        TemplateNo = request.TemplateNo,
                //        SerialNo = request.SerialNo,
                //        InvoiceNo = response.InvoiceNo,
                //        InvoiceStatus = (short)response.InvoiceStatus,
                //        SignStatus = (short)response.SignStatus,
                //    };
                //    var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

                //    await signService.SignServer(invoice);

                //    response.SignStatus = SignStatus.DaKy;
                //}

                //var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(response));
                //return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        [Produces("application/json")]
        [HttpPost("update32/{erpId}")]
        public async Task<object> Update32([FromRoute] string erpId, [FromBody] UpdateInvoice01OldDecreeRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }
                var validator = _validatorFactory.GetValidator<UpdateInvoice01OldDecreeRequestModel>();
                request.ErpId = erpId;

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());
                #endregion

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoiceRoot = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToUpdate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingCheckUpdateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var response = new UpdateInvoice01OldDecreeResponseModel();

                var invoiceMongo = await _mongoInvoice01Repository.GetById(invoiceRoot.Id);
                if (invoiceMongo == null)
                    throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {erpId}");

                // update oracle
                var entityUpdateMongo = new MongoInvoice01Entity();
                var input = request;
                input.Id = invoiceMongo.Id;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;

                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;

                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;

                entityUpdateMongo.Note = input.Note;

                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;

                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;

                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalDiscountAmountAfterTax = input.TotalDiscountAmountAfterTax;
                entityUpdateMongo.TotalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax;
                entityUpdateMongo.TotalDiscountPercentAfterTax = input.TotalDiscountPercentAfterTax;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.TotalVatAmount = input.TotalVatAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;

                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    {
                                        { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                    }) : null;



                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var entityIndex = entityDetails.Select(x => x.Index);
                var duplicate = commandInvoiceDetails.Select(x => x.Index).Distinct()
                                            .Intersect(entityIndex);

                var newIndex = commandInvoiceDetails.Where(x => !entityIndex.Contains(x.Index)).ToList();

                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                    //thêm mới

                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex.ElementAt(i);
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                var oldTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var removeTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var newTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto
                {
                    TenantId = entityUpdateMongo.TenantId,
                    InvoiceHeaderId = entityUpdateMongo.Id,
                    VatAmount = x.VatAmount,
                    Amount = x.Amount,
                    //VatAmountBackUp = x.VatAmountBackUp,
                    VatPercent = x.VatPercent,
                    VatPercentDisplay = taxes[x.VatPercent].Item2,
                    Name = taxes[x.VatPercent].Item1,
                    Partition = partition
                })
               .ToList();

                var entityTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                //group để bỏ trường hợp Index thêm mới
                var duplicateTaxbreakdown = commandTaxBreakDowns.Select(x => x.VatPercent).Distinct()
                                            .Intersect(entityTaxbreakdowns.Select(x => x.VatPercent));

                var newIndexTaxbreakdown = commandTaxBreakDowns.Where(x => !duplicateTaxbreakdown.Contains(x.VatPercent)).ToList();

                foreach (var item in entityTaxbreakdowns)
                {
                    if (duplicateTaxbreakdown.Contains(item.VatPercent)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var taxbreakdown = commandTaxBreakDowns.FirstOrDefault(x => x.VatPercent == item.VatPercent);
                        taxbreakdown.Id = item.Id;
                        oldTaxbreakdowns.Add(taxbreakdown);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeTaxbreakdowns.Add(item);
                    }
                }

                if (newIndexTaxbreakdown.Any())
                {
                    var totalItemsTaxbreakdown = newIndexTaxbreakdown.Count;
                    var idInvoiceTaxBreakDowns = await GetSEQsNextVal(totalItemsTaxbreakdown, SequenceName.Invoice01TaxBreakdown);
                    var indexTaxBreakDown = 0;

                    //thêm mới
                    foreach (var item in newIndexTaxbreakdown)
                    {
                        item.Id = idInvoiceTaxBreakDowns[indexTaxBreakDown];
                        newTaxbreakdowns.Add(item);

                        indexTaxBreakDown++;
                    }
                }

                entityUpdateMongo.InvoiceTaxBreakdowns = oldTaxbreakdowns;
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(newTaxbreakdowns);

                // 
                var rootTaxBreakdowns = invoiceRoot.InvoiceTaxBreakdowns;

                // Tinh toan chi tiet thue suat
                // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                // Nếu không => Lưu theo input người dùng
                if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                {
                    // Tinh toan chi tiet thue suat
                    entityUpdateMongo.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(entityUpdateMongo);

                    if (entityUpdateMongo.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        int indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(entityUpdateMongo.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = entityUpdateMongo.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }

                        removeTaxbreakdowns = rootTaxBreakdowns.Select(x => new Invoice01TaxBreakdownDto()
                        {
                            Id = x.Id,
                        }).ToList();
                        oldTaxbreakdowns = new List<Invoice01TaxBreakdownDto>();
                        newTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                    }
                }
                else
                {
                    if (!entityUpdateMongo.InvoiceTaxBreakdowns.IsNullOrEmpty())
                    {
                        foreach (var taxBreakdownDto in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            // Cộng tổng tiền
                            taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, entityUpdateMongo);
                        }
                    }
                }

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);

                try
                {
                    var invoice = _factory.ObjectMapper.Map<MongoInvoice01Entity, Invoice01HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice01Service.GenerateUpdateRefactorQuery(invoice, toCurrency, oldDetails, newDetails, removeDetails, oldTaxbreakdowns, newTaxbreakdowns, removeTaxbreakdowns, false, entityUpdateMongo.InvoiceReferenceOld);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    response = new UpdateInvoice01OldDecreeResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    };

                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);
                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateInvoice01OldDecreeResponseModel>(new ResultApiModel<UpdateInvoice01OldDecreeResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-and-sign")]
        public async Task<object> CreateAndSign([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01RequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());
                #endregion

                var validator = _validatorFactory.GetValidator<CreateInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateInvoice01RequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = data.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var taxBreakDownIds = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;

                for (int i = 0; i < data.Count; i++)
                {
                    var item = data[i];
                    item.Id = idInvoiceHeaders[i];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.CreatorId = CurrentUser.Id.Value;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    // tạm thời dùng chung vs enum của synccatlog
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int j = 0; j < item.InvoiceDetails.Count; j++)
                    {
                        var itemDetail = item.InvoiceDetails[j];
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[j];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;
                    }

                    for (int j = 0; j < item.InvoiceTaxBreakdowns.Count; j++)
                    {
                        var itemTaxBreakDown = item.InvoiceTaxBreakdowns[j];
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.Id = taxBreakDownIds[j];
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;
                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                    }

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            int indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }
                }

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(L["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                }));

                //Ký hóa đơn
                var requestSign = new Invoice01ApiSignServerRequestModel
                {
                    ErpId = request.ErpId
                };
                var validatorSign = _validatorFactory.GetValidator<Invoice01ApiSignServerRequestModel>();

                var validationSignResult = await validatorSign.HandleAsync(requestSign);
                if (!validationSignResult.Success)
                    throw new UserFriendlyException(L[validationSignResult.Message]);

                var responseSign = await _factory.Mediator.Send(requestSign);
                result.Data.SignStatus = responseSign.SignStatus;
                result.Succeeded = result.Succeeded ? true : false;

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-and-unofficial")]
        public async Task<object> CreateAndUnOfficial([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01RequestModel request)
        {
            var isDeleteErp = true;
            var result = new ResultApiModel<List<object>>
            {
                Data = new List<object>(),
            };

            CreateInvoice01ResponseModel invoiceCreated = null;
            var response = new CreateAndUnOfficialInvoice01ResponseModel();
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());
                #endregion

                var validator = _validatorFactory.GetValidator<CreateInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                request.TemplateNo = infoRequestModel.TemplateNo;
                request.SerialNo = infoRequestModel.SerialNo;

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateInvoice01RequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);

                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                var idTaxBreakDowns = await GetSEQsNextVal(data.Sum(x => x.InvoiceTaxBreakdowns.Count), SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                var group = CurrentTenant.Group;
                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    for (int i = 0; i < item.InvoiceDetails.Count; i++)
                    {
                        var itemDetail = item.InvoiceDetails[indexDetail];

                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.Id = idInvoiceDetails[i];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    }

                    foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idTaxBreakDowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;
                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                        indexTaxBreakDown++;
                    }

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    Id = Guid.NewGuid(),
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1,
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                var responseCreate = new CreateInvoice01ResponseModel
                {
                    Id = idInvoiceHeaders.FirstOrDefault(),
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.Goc,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = infoRequestModel.TemplateNo,
                    TransactionId = request.TransactionId
                };

                response.Id = responseCreate.Id;
                response.ErpId = responseCreate.ErpId;
                response.TransactionId = responseCreate.TransactionId;
                response.TemplateNo = responseCreate.TemplateNo;
                response.SerialNo = responseCreate.SerialNo;
                response.InvoiceNo = responseCreate.InvoiceNo;
                response.InvoiceStatus = responseCreate.InvoiceStatus;
                response.SignStatus = responseCreate.SignStatus;
                result.Data.Add(response);
                invoiceCreated = responseCreate;

                var requestUnOfficial = new DownloadUnOfficialInvoice01RequestModel
                {
                    ErpId = responseCreate.ErpId
                };

                var responseUnOfficial = await _factory.Mediator.Send(requestUnOfficial);
                response.FileUnOfficial = responseUnOfficial.Data;
                result.Data[0] = response;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                result.Message = ex.Message;
                result.Succeeded = false;

                if (ex.Data.Count > 0)
                {
                    result.Code = 15009;
                    result.Data.Add(ex.Data);
                }
                else
                {
                    if (result.Data.Count > 0)
                    {
                        result.Code = -1;
                        result.Data = result.Data;
                    }
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }

                try
                {
                    if (invoiceCreated != null)
                    {
                        var requestSign = new Invoice01ApiSignServerRequestModel
                        {
                            ErpId = invoiceCreated.ErpId
                        };
                        var validatorSign = _validatorFactory.GetValidator<Invoice01ApiSignServerRequestModel>();

                        var validationSignResult = await validatorSign.HandleAsync(requestSign);
                        if (!validationSignResult.Success)
                            throw new UserFriendlyException(L[validationSignResult.Message]);
                        var responseSign = await _factory.Mediator.Send(requestSign);
                        response.SignStatus = responseSign.SignStatus;
                        result.Data[0] = response;
                        result.Succeeded = result.Succeeded ? true : false;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);

                    if (ex.Data.Count == 0)
                    {
                        result.Succeeded = false;
                        result.Code = -1;
                        result.Message = ex.Message;
                    }
                    //result.Message = $"Ký hóa đơn thứ {result.Data.Count + 1} Id = {request.ElementAt(result.Data.Count).ErpId} lỗi: {ex.Message}";
                }
            }
            return new ResultApiViewModel<List<object>>(result);
        }

        /// <summary>
        /// duyệt hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("approve/{erpId}")]
        public async Task<object> Approve([FromRoute] string erpId)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var request = new ApproveInvoice01ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<ApproveInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var userId = _validationContext.GetItem<Guid>("UserId");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

                // mongoDB
                var invoiceMongoDB = await _mongoInvoice01Repository.GetById(invoice.Id);
                if (invoiceMongoDB == null)
                    throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                var entityUpdateMongo = new MongoInvoice01Entity();
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                    }
                }

                entityUpdateMongo.ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode();
                entityUpdateMongo.ApprovedId = userId;
                entityUpdateMongo.ApprovedTime = DateTime.Now;
                entityUpdateMongo.FullNameApprover = userFullName;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncApprove.GetHashCode();
                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo, true);

                try
                {
                    // oracle DB
                    if (invoice == null)
                        throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                    var queryHeader = $"Update \"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}\" " +
                                 $"Set \"ApproveStatus\" = :ApproveStatus, \"ApprovedId\" = :ApprovedId, \"ApprovedTime\" = :ApprovedTime, \"FullNameApprover\" = :FullNameApprover " +
                                 $"Where \"Id\" = :Id";

                    await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                    {
                        Id = invoice.Id,
                        ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode(),
                        ApprovedId = OracleExtension.ConvertGuidToRaw(userId),
                        ApprovedTime = DateTime.Now,
                        FullNameApprover = userFullName,
                    });

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Approve.GetHashCode(),
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongoDB);
                    Log.Error(ex.Message);
                }


                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// duyệt và ký hoá đơn
        /// </summary>
        /// <param name="idErp"></param>
        /// <returns></returns>
        [HttpPost("approve-and-sign/{erpId}")]
        public async Task<object> ApproveAndSign([FromRoute] string erpId)
        {
            try
            {
                var requestApproveAndSign = new ApproveAndSignInvoice01ApiRequestModel
                {
                    ErpId = erpId
                };

                var validatorApprove = _validatorFactory.GetValidator<ApproveAndSignInvoice01ApiRequestModel>();
                var validationResultApprove = await validatorApprove.HandleAsync(requestApproveAndSign);
                if (!validationResultApprove.Success)
                    throw new UserFriendlyException(L[validationResultApprove.Message]);

                var userId = _validationContext.GetItem<Guid>("UserId");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<MongoInvoice01Entity>("Invoice");

                // mongoDB
                var invoiceMongoDB = await _mongoInvoice01Repository.GetById(invoice.Id);
                if (invoiceMongoDB == null)
                    throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                var entityUpdateMongo = new MongoInvoice01Entity();
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                    }
                }

                entityUpdateMongo.ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode();
                entityUpdateMongo.ApprovedId = userId;
                entityUpdateMongo.ApprovedTime = DateTime.Now;
                entityUpdateMongo.FullNameApprover = userFullName;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncApprove.GetHashCode();
                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo, true);

                try
                {
                    // oracle DB
                    if (invoice == null)
                        throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để duyệt");

                    var queryHeader = $"Update \"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}\" " +
                                 $"Set \"ApproveStatus\" = :ApproveStatus, \"ApprovedId\" = :ApprovedId, \"ApprovedTime\" = :ApprovedTime, \"FullNameApprover\" = :FullNameApprover " +
                                 $"Where \"Id\" = :Id";

                    await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                    {
                        Id = invoice.Id,
                        ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode(),
                        ApprovedId = OracleExtension.ConvertGuidToRaw(userId),
                        ApprovedTime = DateTime.Now,
                        FullNameApprover = userFullName,
                    });

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Approve,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongoDB);
                    Log.Error(ex.Message);
                }

                // Ký
                var requestSign = new Invoice01ApiSignServerRequestModel
                {
                    ErpId = erpId
                };

                var responseSign = await _factory.Mediator.Send(requestSign);

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(responseSign.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
                //Log.Error(ex, ex.Message);

                //return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region old
            //try
            //{
            //    var requestApproveAndSign = new ApproveAndSignInvoice01ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };
            //    var validatorApprove = _validatorFactory.GetValidator<ApproveAndSignInvoice01ApiRequestModel>();
            //    var validationResultApprove = await validatorApprove.HandleAsync(requestApproveAndSign);
            //    if (validationResultApprove.Success)
            //    {
            //        //Duyêt
            //        var requestApprove = new ApproveInvoice01ApiRequestModel
            //        {
            //            ErpId = erpId
            //        };

            //        var responseApprove = await _factory.Mediator.Send(requestApprove);
            //    }
            //    //var repoInvoice01Header = _factory.GetServiceDependency<IInvoiceHeaderRepository<Invoice01HeaderEntity>>();
            //    //var invoice = await repoInvoice01Header.GetByErpIdAsync(_factory.CurrentTenant.Id.Value, erpId);

            //    //Ký
            //    var requestSign = new Invoice01ApiSignServerRequestModel
            //    {
            //        ErpId = erpId
            //    };

            //    var responseSign = await _factory.Mediator.Send(requestSign);
            //    return new ResultApiViewModel<Invoice01ApiSignServerResponseModel>(new ResultApiModel<Invoice01ApiSignServerResponseModel>(responseSign));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }


        /// <summary>
        /// tạo hóa đơn theo lô
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch")]
        public async Task<object> CreateBatchAsync([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] List<CreateInvoice01RequestModel> request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());

                for (int i = 0; i < request.Count; i++)
                {
                    request[i].TemplateNo = infoRequestModel.TemplateNo;
                    request[i].SerialNo = infoRequestModel.SerialNo;
                }

                var requestBatch = new CreateBatchInvoice01RequestModel
                {
                    Datas = request.Select(x => new CreateBatchInvoice01InfoRequestModel
                    {
                        Invoice = x
                    }).ToList()
                };
                var validator = _validatorFactory.GetValidator<CreateBatchInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(requestBatch);
                if (!validationResult.Success)
                {
                    foreach (var erpId in request.Select(x => x.ErpId))
                    {
                        _lockInvoiceService.DeleteErpId(erpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    }
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);


                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("Templates").FirstOrDefault();
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var toCurrencies = _validationContext.GetItem<List<CurrencyEntity>>("ToCurrencies").ToDictionary(x => x.CurrencyCode, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = _factory.ObjectMapper.Map<List<CreateInvoice01RequestModel>, List<MongoInvoice01Entity>>(request);


                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(request.Count, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = data.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var idInvoiceTaxBreakdowns = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                var totalItems = request.Count;

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    if (!string.IsNullOrEmpty(item.BuyerEmail))
                    {
                        if (!item.BuyerEmail.IsManyEmails(';'))
                        {
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                        }
                    }

                    var toCurrency = toCurrencies[item.ToCurrency];
                    var partition = long.Parse(item.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = totalItems;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    item.InvoiceTaxBreakdowns.ForEach(itemTaxBreakDown =>
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakdowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    });

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = totalItems
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= totalItems)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                // sinh số
                //var enityMongos = new List<MongoInvoice01Entity>();
                //var timeDelayInvoiceGenerateNumberInBatch = 20;
                //var maxTimeDelayInvoiceGenerateNumberInBatch = 100;
                //int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                //int.TryParse(_configuration["Settings:MaxTimeDelayInvoiceGenerateNumberInBatch"], out maxTimeDelayInvoiceGenerateNumberInBatch);

                //var now = DateTime.Now;
                //var maxWaitTime = DateTime.Now.AddMilliseconds(request.Count * maxTimeDelayInvoiceGenerateNumberInBatch);

                //await Task.Delay(timeDelayInvoiceGenerateNumberInBatch * request.Count);

                //while (enityMongos.Any(x => x.InvoiceNo == null) || DateTime.Now < maxWaitTime)
                //{
                //    enityMongos = await _mongoInvoice01Repository.GetByIdsAsync(idInvoiceHeaders);

                //    if (enityMongos.Count(x => x.InvoiceNo != null) == idInvoiceHeaders.Count)
                //        break;

                //    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch * request.Count);
                //}

                //var indexMongos = enityMongos.ToDictionary(x => x.Id);

                //chờ sinh số

                var timeEstimatesSeconds = data.Count / 5;
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1s
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberInBatch);
                if (timeDelayInvoiceGenerateNumberInBatch < 1)
                {
                    // nếu thời gian chờ nhỏ hơn 1s => set default = 1s, ngược lại nếu thời gian chờ > 1s thì set bằng timeEstimatesSeconds
                    if (timeEstimatesSeconds < 1)
                        timeDelayInvoiceGenerateNumberInBatch = 1;
                    else timeDelayInvoiceGenerateNumberInBatch = timeEstimatesSeconds;
                }

                // thời delay mỗi lần kiểm tra trạng thái dữ liệu đã sinh số chưa
                var timeDelayLoopInvoiceGenerateNumberMany = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberMany"], out timeDelayLoopInvoiceGenerateNumberMany);

                var timeEstimatesMinutes = data.Count / 10;
                // tổng số thời gian chờ sinh số : default = 1 phút
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberMany);
                if (timeDelayInvoiceGenerateNumberMany < 1)
                {
                    // nếu thời gian chờ nhỏ hơn 1 phút => set default = 1 phút, ngược lại nếu thời gian chờ > 1 phút thì set bằng timeEstimatesMinutes
                    if (timeEstimatesMinutes < 1)
                        timeDelayInvoiceGenerateNumberMany = 1;
                    else timeDelayInvoiceGenerateNumberMany = timeEstimatesMinutes;
                }

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumberMany);

                var enityMongo = new MongoInvoice01Entity();

                await Task.Delay(TimeSpan.FromSeconds(timeDelayInvoiceGenerateNumberInBatch));

                while (DateTime.Now < maxWaitTime)
                {
                    var isSuscess = await _mongoInvoice01BatchIdRepository.GetByBatchIdDoneGenerateNumberAsync(batchId);

                    if (isSuscess)
                        break;

                    await Task.Delay(TimeSpan.FromSeconds(timeDelayLoopInvoiceGenerateNumberMany));
                }

                var enityMongos = await _mongoInvoice01Repository.GetByIdsAsync(idInvoiceHeaders);

                var result = new List<CreateInvoice01ResponseModel>();

                foreach (var itemMongo in enityMongos)
                {
                    var item = new CreateInvoice01ResponseModel
                    {
                        Id = itemMongo.Id,
                        ErpId = itemMongo.ErpId,
                        InvoiceNo = itemMongo.InvoiceNo,
                        InvoiceStatus = InvoiceStatus.Goc,
                        SerialNo = itemMongo.SerialNo,
                        SignStatus = SignStatus.ChoKy,
                        TemplateNo = itemMongo.TemplateNo,
                        TransactionId = itemMongo.TransactionId
                    };
                    result.Add(item);
                }

                return new ResultApiViewModel<object>
                {
                    Code = 0,
                    Message = "Thành công",
                    Succeeded = true,
                    Data = result,
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongos = _validationContext.GetItem<List<MongoInvoice01ErpIdEntity>>("InvoiceErpIds");

                        if (erpIdMongos != null)
                            await _mongoInvoice01ErpIdRepository.DeleteManyAsync(erpIdMongos);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }

                //Log.Error(e.Message);
                //Log.Error(e.StackTrace);
                //return new ResultApiViewModel(false, -1, e.Message); //TODO: 
            }
        }

        /// <summary>
        /// tạo hóa đơn theo lô - nước sạch
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch-ctw")]
        public async Task<object> CreateBatchCtwAsync([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] List<CreateInvoice01RequestModel> request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());

                for (int i = 0; i < request.Count; i++)
                {
                    request[i].TemplateNo = infoRequestModel.TemplateNo;
                    request[i].SerialNo = infoRequestModel.SerialNo;
                }

                var requestBatch = new CreateBatchInvoice01RequestModel
                {
                    Datas = request.Select(x => new CreateBatchInvoice01InfoRequestModel
                    {
                        Invoice = x
                    }).ToList()
                };
                var validator = _validatorFactory.GetValidator<CreateBatchInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(requestBatch);
                if (!validationResult.Success)
                {
                    foreach (var erpId in request.Select(x => x.ErpId))
                    {
                        _lockInvoiceService.DeleteErpId(erpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    }
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                var result = new ResultApiModel<List<object>>
                {
                    Data = new List<object>(),
                };

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
                var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrencies = _validationContext.GetItem<List<CurrencyEntity>>("ToCurrencies").ToDictionary(x => x.CurrencyCode, x => x);
                var template = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("Templates").FirstOrDefault();

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var totalItems = request.Count;
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(totalItems, SequenceName.Invoice01Header);

                var totalItemsDetail = request.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                var totalItemsTaxBreakdown = request.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var idTaxBreakdowns = await GetSEQsNextVal(totalItemsTaxBreakdown, SequenceName.Invoice01TaxBreakdown);

                var data = _factory.ObjectMapper.Map<List<CreateInvoice01RequestModel>, List<MongoInvoice01Entity>>(request);

                var invoiceHeaderIds = await GetSEQsNextVal(request.Count, SequenceName.Invoice01Header);

                var group = CurrentTenant.Group;

                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakdown = 0;

                foreach (var item in data)
                {
                    if (!string.IsNullOrEmpty(item.BuyerEmail))
                    {
                        if (!item.BuyerEmail.IsManyEmails(';'))
                        {
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                        }
                    }

                    var partition = long.Parse(item.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                    var toCurrency = toCurrencies[item.ToCurrency];
                    item.Id = invoiceHeaderIds[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.InvoiceStatus = (short)InvoiceStatus.Goc.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = totalItems;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);


                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;


                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        //TODO: nhiều trường dữ liệu không đúng giá trị
                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idTaxBreakdowns[indexTaxBreakdown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakdown++;
                    }

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            int indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= idInvoiceHeaders.Count)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();
                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = idInvoiceHeaders.Count
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var maxTimeDelayInvoiceGenerateNumberInBatch = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:MaxTimeDelayInvoiceGenerateNumberInBatch"], out maxTimeDelayInvoiceGenerateNumberInBatch);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(request.Count * maxTimeDelayInvoiceGenerateNumberInBatch);

                var enityMongos = new List<MongoInvoice01Entity>();
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch * request.Count);

                while (enityMongos.Any(x => x.InvoiceNo == null) || DateTime.Now < maxWaitTime)
                {
                    enityMongos = await _mongoInvoice01Repository.GetByIdsAsync(invoiceHeaderIds);

                    if (enityMongos.Count(x => x.InvoiceNo != null) == invoiceHeaderIds.Count)
                        break;

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch * request.Count);
                }

                var indexMongos = enityMongos.ToDictionary(x => x.Id);

                foreach (var item in data)
                {
                    result.Data.Add(new CreateInvoice01ResponseModel
                    {
                        Id = item.Id,
                        ErpId = item.ErpId,
                        InvoiceStatus = InvoiceStatus.Goc,
                        SerialNo = item.SerialNo,
                        SignStatus = SignStatus.ChoKy,
                        TemplateNo = item.TemplateNo,
                        TransactionId = item.TransactionId,
                        InvoiceNo = indexMongos.ContainsKey(item.Id) ? indexMongos[item.Id].InvoiceNo : null
                    });
                }

                result.Message = "Thành công";
                result.Succeeded = true;

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = true,
                        Data = ex.Data.Values
                    }; //TODO
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongos = _validationContext.GetItem<List<MongoInvoice01ErpIdEntity>>("InvoiceErpIds");

                        if (erpIdMongos != null)
                            await _mongoInvoice01ErpIdRepository.DeleteManyAsync(erpIdMongos);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn theo lô và ký
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch-and-sign")]
        public async Task<object> CreateBatchAndSignAsync([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] List<CreateInvoice01RequestModel> request)
        {
            var isDeleteErp = true;
            var result = new ResultApiModel<List<CreateInvoice01ResponseModel>>
            {
                Data = new List<CreateInvoice01ResponseModel>(),
                Succeeded = true
            };

            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (infoRequestModel == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy mẫu số hóa đơn hoặc ký hiệu hóa đơn");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());

                for (int i = 0; i < request.Count; i++)
                {
                    request[i].TemplateNo = infoRequestModel.TemplateNo;
                    request[i].SerialNo = infoRequestModel.SerialNo;
                }

                var requestBatch = new CreateBatchInvoice01RequestModel
                {
                    Datas = request.Select(x => new CreateBatchInvoice01InfoRequestModel
                    {
                        Invoice = x
                    }).ToList()
                };
                var validator = _validatorFactory.GetValidator<CreateBatchInvoice01RequestModel>();
                var validationResult = await validator.HandleAsync(requestBatch);
                if (!validationResult.Success)
                {
                    foreach (var erpId in request.Select(x => x.ErpId))
                    {
                        _lockInvoiceService.DeleteErpId(erpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    }
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("Templates").FirstOrDefault();
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrencies = _validationContext.GetItem<List<CurrencyEntity>>("ToCurrencies").ToDictionary(x => x.CurrencyCode, x => x);
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = _factory.ObjectMapper.Map<List<CreateInvoice01RequestModel>, List<MongoInvoice01Entity>>(request);

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);


                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(request.Count, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = data.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var idInvoiceTaxBreakdowns = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                var totalItems = request.Count;

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    if (!string.IsNullOrEmpty(item.BuyerEmail))
                    {
                        if (!item.BuyerEmail.IsManyEmails(';'))
                        {
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                        }
                    }

                    var partition = long.Parse(item.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                    var toCurrency = toCurrencies[item.ToCurrency];

                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.Goc;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus.GetHashCode();
                    item.ApproveCancelStatus = (short)approveCancelStatus.GetHashCode();
                    item.ApproveDeleteStatus = (short)approveDeleteStatus.GetHashCode();

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerEmail = item.BuyerEmail?.Trim();

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = totalItems;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = infoRequestModel.SerialNo.Substring(0, 1);

                    item.TemplateNo = infoRequestModel.TemplateNo;
                    item.SerialNo = infoRequestModel.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.Goc);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    item.InvoiceTaxBreakdowns.ForEach(itemTaxBreakDown =>
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakdowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    });

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();



                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = totalItems
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);
                Log.Information(@$"Created BatchId into Mongo {batchId}");
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= totalItems)
                        break;
                }
                Log.Information("Created Invoice into Mongo");
                await Task.WhenAll(tasks);
                isDeleteErp = false;

                #region
                //chờ sinh số
                //var timeEstimatesSeconds = data.Count / 5;
                //// thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1s
                //int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberInBatch);
                //if (timeDelayInvoiceGenerateNumberInBatch < 1)
                //{
                //    // nếu thời gian chờ nhỏ hơn 1s => set default = 1s, ngược lại nếu thời gian chờ > 1s thì set bằng timeEstimatesSeconds
                //    if (timeEstimatesSeconds < 1)
                //        timeDelayInvoiceGenerateNumberInBatch = 1;
                //    else timeDelayInvoiceGenerateNumberInBatch = timeEstimatesSeconds;
                //}

                //// thời delay mỗi lần kiểm tra trạng thái dữ liệu đã sinh số chưa
                //var timeDelayLoopInvoiceGenerateNumberMany = 1000;
                //int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberMany"], out timeDelayLoopInvoiceGenerateNumberMany);

                //var timeEstimatesMinutes = data.Count / 10;
                //// tổng số thời gian chờ sinh số : default = 1 phút
                //int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberMany"], out var timeDelayInvoiceGenerateNumberMany);
                //if (timeDelayInvoiceGenerateNumberMany < 1)
                //{
                //    // nếu thời gian chờ nhỏ hơn 1 phút => set default = 1 phút, ngược lại nếu thời gian chờ > 1 phút thì set bằng timeEstimatesMinutes
                //    if (timeEstimatesMinutes < 1)
                //        timeDelayInvoiceGenerateNumberMany = 1;
                //    else timeDelayInvoiceGenerateNumberMany = timeEstimatesMinutes;
                //}

                //var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumberMany);

                //var enityMongo = new MongoInvoice01Entity();

                //await Task.Delay(TimeSpan.FromSeconds(timeDelayInvoiceGenerateNumberInBatch));

                //DateTime.Now < maxWaitTime
                #endregion

                // phải chờ sinh số xong mới call api ký đc
                while (true)
                {
                    var isSuscess = await _mongoInvoice01BatchIdRepository.GetByBatchIdDoneGenerateNumberAsync(batchId);
                    if (isSuscess)
                        break;

                    var isGenNumberErr = await _mongoInvoice01BatchIdRepository.GetByBatchIdGenerateNumberErrorAsync(batchId);
                    if (isGenNumberErr)
                        break;

                    // await Task.Delay(TimeSpan.FromSeconds(timeDelayLoopInvoiceGenerateNumberMany));
                }

                var enityMongos = await _mongoInvoice01Repository.GetByIdsAsync(idInvoiceHeaders);

                var indexMongos = enityMongos.ToDictionary(x => x.Id);
                foreach (var item in data)
                {
                    result.Data.Add(new CreateInvoice01ResponseModel
                    {
                        Id = item.Id,
                        ErpId = item.ErpId,
                        InvoiceNo = indexMongos.ContainsKey(item.Id) ? indexMongos[item.Id].InvoiceNo : null,
                        InvoiceStatus = InvoiceStatus.Goc,
                        SerialNo = item.SerialNo,
                        SignStatus = SignStatus.ChoKy,
                        TemplateNo = item.TemplateNo,
                        TransactionId = item.TransactionId
                    });
                }

                // TODO: refactor ký nhiều
                foreach (var item in result.Data)
                {
                    var requestSign = new Invoice01ApiSignServerRequestModel
                    {
                        ErpId = item.ErpId
                    };
                    var validatorSign = _validatorFactory.GetValidator<Invoice01ApiSignServerRequestModel>();

                    var validationSignResult = await validatorSign.HandleAsync(requestSign);
                    if (!validationSignResult.Success)
                    {
                        throw new UserFriendlyException(L[validationSignResult.Message]);
                    }
                    else
                    {
                        var response = await _factory.Mediator.Send(requestSign);

                        item.SignStatus = response.SignStatus;
                    }
                }

                result.Message = "Thành công";
                result.Succeeded = true;

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongos = _validationContext.GetItem<List<MongoInvoice01ErpIdEntity>>("InvoiceErpIds");

                        if (erpIdMongos != null)
                        {
                            Log.Information(@$"Delete ErpId when create Error api {erpIdMongos.ToArray()}");
                            await _mongoInvoice01ErpIdRepository.DeleteManyAsync(erpIdMongos);
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    };
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message);
                }
            }
        }

        /// <summary>
        /// Tạo hóa đơn thay thế 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("replace")]
        public async Task<object> CreateReplace([FromBody] CreateReplaceInvoice01ApiRequestModel request)
        {
            var isDeleteErp = true;
            // Kiểm Hóa đơn gốc có đang được xử lý không
            bool isProcessing = false;
            long lockId = 0;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                var validator = _validatorFactory.GetValidator<CreateReplaceInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                if (!validationResult.Success)
                {
                    lockId = _validationContext.GetItem<long?>("RootId").HasValue ? _validationContext.GetItem<long>("RootId") : 0;
                    isProcessing = _validationContext.GetItem<bool?>("IsProcessing") == null ? false : _validationContext.GetItem<bool>("IsProcessing");
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var rootInvoice = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");
                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var rootInvoiceDetails = await _repoInvoice01Detail.QueryByIdInvoiceHeaderRawAsync(CurrentTenant.Id.Value, rootInvoice.Id);
                var rootInvoiceTaxbreakDowns = await _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdRawAsync(rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateReplaceInvoice01ApiRequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);


                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = data.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var idInvoiceTaxBreakdowns = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.ThayThe;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerCode = request.BuyerCode;
                    item.BuyerFullName = request.BuyerFullName;
                    item.BuyerLegalName = request.BuyerLegalName;
                    item.BuyerTaxCode = request.BuyerTaxCode;
                    item.BuyerAddressLine = request.BuyerAddressLine;
                    item.BuyerDistrictName = request.BuyerDistrictName;
                    item.BuyerCityName = request.BuyerCityName;
                    item.BuyerCountryCode = request.BuyerCountryCode;
                    item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                    item.BuyerFaxNumber = request.BuyerFaxNumber;
                    item.BuyerEmail = request.BuyerEmail?.Trim();
                    item.BuyerBankName = request.BuyerBankName;
                    item.BuyerBankAccount = request.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = request.Note;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceTaxBreakdowns.ForEach(itemTaxBreakDown =>
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakdowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    });

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    item.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiThayThe.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var timeDelayInvoiceGenerateNumber = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumber"], out timeDelayInvoiceGenerateNumber);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();

                var isError = true;
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                while (!string.IsNullOrEmpty(enityMongo?.InvoiceNo) || DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;

                    }

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                await InvoiceReSyncToMongo(new List<long> { rootInvoice.Id }, InvoiceStatus.BiThayThe);

                await InvoiceReSyncToEs(new List<long> { rootInvoice.Id }, SyncElasticSearchStatus.PendingSyncInvoiceReplaced);

                #region Thêm nội dung ghi chú khi in hóa đơn
                if (request.InvoicePrintNote != null)
                {
                    try
                    {
                        var insertModel = new InvoicePrintNoteEntity()
                        {
                            TenantId = CurrentTenant.Id.Value,
                            Note = request.InvoicePrintNote.Note,
                            IsShowNote = request.InvoicePrintNote.IsShowNote,
                            InvoiceHeaderId = enityMongo.Id,
                            InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                            CreationTime = DateTime.Now,
                            CreatorId = CurrentUser.Id,

                        };

                        await _invoicePrintNoteRepository.InsertAsync(insertModel, true);
                    }
                    catch (Exception e)
                    {

                        throw;
                    }
                }
                #endregion

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.ThayThe,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo
                }));

                return result;

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
                // Xóa cache Processing
                if (lockId != 0 && isProcessing != true)
                {
                    _lockInvoiceService.UnLockInvoiceProcessed(lockId, _factory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
        }

        /// <summary>
        /// Tạo hóa đơn thay thế 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-adjust")]
        public async Task<object> CreateAdjust([FromBody] CreateAdjustInvoice01ApiRequestModel request)
        {
            var isDeleteErp = true;
            long lockId = 0;
            // Kiểm Hóa đơn gốc có đang được xử lý không
            bool isProcessing = false;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                var validator = _validatorFactory.GetValidator<CreateAdjustInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                lockId = _validationContext.GetItem<long?>("RootId").HasValue ? _validationContext.GetItem<long>("RootId") : 0;
                if (!validationResult.Success)
                {
                    _lockInvoiceService.DeleteErpId(request.ErpId, CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    isProcessing = _validationContext.GetItem<bool?>("IsProcessing") == null ? false : _validationContext.GetItem<bool?>("IsProcessing").Value;
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var rootInvoice = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");
                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                lockId = rootInvoice.Id;

                var rootInvoiceDetails = await _repoInvoice01Detail.QueryByIdInvoiceHeaderRawAsync(CurrentTenant.Id.Value, rootInvoice.Id);
                var rootInvoiceTaxbreakDowns = await _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdRawAsync(rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateAdjustInvoice01ApiRequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);


                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = data.Sum(x => x.InvoiceTaxBreakdowns.Count);
                var idInvoiceTaxBreakdowns = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.DieuChinh;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.Number = null;
                    item.InvoiceNo = null;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerCode = request.BuyerCode;
                    item.BuyerFullName = request.BuyerFullName;
                    item.BuyerLegalName = request.BuyerLegalName;
                    item.BuyerTaxCode = request.BuyerTaxCode;
                    item.BuyerAddressLine = request.BuyerAddressLine;
                    item.BuyerDistrictName = request.BuyerDistrictName;
                    item.BuyerCityName = request.BuyerCityName;
                    item.BuyerCountryCode = request.BuyerCountryCode;
                    item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                    item.BuyerFaxNumber = request.BuyerFaxNumber;
                    item.BuyerEmail = request.BuyerEmail?.Trim();
                    item.BuyerBankName = request.BuyerBankName;
                    item.BuyerBankAccount = request.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = request.Note;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.ToCurrency = toCurrency.CurrencyCode;

                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.InvoiceTemplateId = template.TemplateId;
                    item.FromCurrency = fromCurrency.CurrencyCode;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinh);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinh);
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceTaxBreakdowns.ForEach(itemTaxBreakDown =>
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakdowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    });

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        var unit = (units != null && units.ContainsKey(itemDetail.UnitName?.Trim())) ? units[itemDetail.UnitName?.Trim()] : null;

                        var productCode = itemDetail.ProductCode?.Trim();
                        var product = string.IsNullOrEmpty(productCode)
                                    ? null
                                    : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        itemDetail.ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode;
                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = unit?.UnitRounding ?? 4;
                        itemDetail.UnitId = unit?.UnitId ?? 0;
                        itemDetail.ProductId = product == null ? 0 : product.ProductId;
                        itemDetail.HideQuantity = product?.HideQuantity ?? false;
                        itemDetail.HideUnit = product?.HideUnit ?? false;
                        itemDetail.HideUnitPrice = product?.HideUnitPrice ?? false;
                        itemDetail.Partition = partition;

                        indexDetail++;
                    });

                    item.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinh.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };

                    // Tinh toan chi tiet thue suat
                    // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                    // Nếu không => Lưu theo input người dùng
                    if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                    {
                        var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                        if (isOnlyCKTM)
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                        }
                        else
                        {
                            item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                        }

                        if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                        {
                            // Tính toán lại
                            // Gen lại sequence
                            indexTaxBreakDown = 0;
                            var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                            foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                            {
                                var tax = taxes[itemTaxBreakDown.VatPercent];

                                itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                                itemTaxBreakDown.Name = tax?.Item1;
                                itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                                itemTaxBreakDown.Partition = partition;
                                itemTaxBreakDown.InvoiceHeaderId = item.Id;
                                itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                                indexTaxBreakDown++;
                            }
                        }
                    }
                    else
                    {
                        if (!item.InvoiceTaxBreakdowns.IsNullOrEmpty())
                        {
                            foreach (var taxBreakdownDto in item.InvoiceTaxBreakdowns)
                            {
                                // Cộng tổng tiền
                                taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, item);
                            }
                        }
                    }

                    item.DiscountType = rootInvoice.DiscountType;

                    indexHeader++;
                };

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var timeDelayInvoiceGenerateNumber = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumber"], out timeDelayInvoiceGenerateNumber);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();

                var isError = true;
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                while (!string.IsNullOrEmpty(enityMongo?.InvoiceNo) || DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;

                    }

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                await InvoiceReSyncToMongo(new List<long> { rootInvoice.Id }, InvoiceStatus.BiDieuChinh);

                await InvoiceReSyncToEs(new List<long> { rootInvoice.Id }, SyncElasticSearchStatus.PendingSyncInvoiceAdjusted);

                #region Thêm nội dung ghi chú khi in hóa đơn
                if (request.InvoicePrintNote != null)
                {
                    try
                    {
                        var insertModel = new InvoicePrintNoteEntity()
                        {
                            TenantId = CurrentTenant.Id.Value,
                            Note = request.InvoicePrintNote.Note,
                            IsShowNote = request.InvoicePrintNote.IsShowNote,
                            InvoiceHeaderId = enityMongo.Id,
                            InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                            CreationTime = DateTime.Now,
                            CreatorId = CurrentUser.Id,

                        };

                        await _invoicePrintNoteRepository.InsertAsync(insertModel, true);
                    }
                    catch (Exception e)
                    {

                        throw;
                    }
                }
                #endregion

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinh,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo
                }));

                return result;

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
                // Xóa cache Processing
                if (lockId != 0 && isProcessing != true)
                {
                    _lockInvoiceService.UnLockInvoiceProcessed(lockId, _factory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
        }

        [Produces("application/json")]
        [HttpPost("adjust/update/{erpId}")]
        public async Task<object> UpdateAdjust([FromRoute] string erpId, [FromBody] UpdateAdjustInvoice01ApiRequestModel request)
        {
            // Kiểm Hóa đơn gốc có đang được xử lý không
            bool isProcessing = false;
            long lockId = 0;
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<UpdateAdjustInvoice01ApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                lockId = _validationContext.GetItem<long?>("RootId").HasValue ? _validationContext.GetItem<long>("RootId") : 0;
                if (!validationResult.Success)
                {
                    isProcessing = _validationContext.GetItem<bool?>("IsProcessing") == null ? false : _validationContext.GetItem<bool?>("IsProcessing").Value;
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                lockId = invoice.Id;

                var invoiceMongo = await _mongoInvoice01Repository.GetByErpId(erpId, CurrentTenant.Id.Value);
                if (invoiceMongo == null)
                    throw new UserFriendlyException("Không tìm thấy hóa đơn");

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                // check sync catalog
                var settingCheckUpdateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var entityUpdateMongo = new MongoInvoice01Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                // ND70
                entityUpdateMongo.StoreCode = input.StoreCode;
                entityUpdateMongo.StoreName = input.StoreName;
                entityUpdateMongo.IsFinancialLeaseInvoice = input.IsFinancialLeaseInvoice;
                entityUpdateMongo.BudgetUnitCode = input.BudgetUnitCode;
                entityUpdateMongo.BuyerIDNumber = input.BuyerIDNumber;
                entityUpdateMongo.BuyerPassportNumber = input.BuyerPassportNumber;

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;
                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;
                entityUpdateMongo.Note = input.Note;
                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinh))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinh))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;
                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;
                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalDiscountAmountAfterTax = input.TotalDiscountAmountAfterTax;
                entityUpdateMongo.TotalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax;
                entityUpdateMongo.TotalDiscountPercentAfterTax = input.TotalDiscountPercentAfterTax;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.TotalVatAmount = input.TotalVatAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;
                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                {
                { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                }) : null;

                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null,
                        InvoiceSpecificProductExtras = item.InvoiceSpecificProductExtras != null && item.InvoiceSpecificProductExtras.Any()
                                        ? item.InvoiceSpecificProductExtras.Select(x =>
                                            new Invoice01SpecificProductDto()
                                            {
                                                SpecificProductFieldId = x.SpecificProductFieldId,
                                                Type = x.Type,
                                                FieldName = x.FieldName,
                                                FieldValue = x.FieldValue,
                                            })
                                        .ToList() : null,
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var duplicate = commandInvoiceDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));
                var newIndex = commandInvoiceDetails.Where(x => !duplicate.Contains(x.Index)).ToList();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                    await GetSeQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail, 1);

                    //thêm mới
                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex[i];
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                var oldTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var removeTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var newTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto
                {
                    TenantId = entityUpdateMongo.TenantId,
                    InvoiceHeaderId = entityUpdateMongo.Id,
                    VatAmount = x.VatAmount,
                    Amount = x.Amount,
                    //VatAmountBackUp = x.VatAmountBackUp,
                    VatPercent = x.VatPercent,
                    VatPercentDisplay = taxes[x.VatPercent].Item2,
                    Name = taxes[x.VatPercent].Item1,
                    Partition = partition
                })
               .ToList();

                var entityTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                //group để bỏ trường hợp Index thêm mới
                var duplicateTaxbreakdown = commandTaxBreakDowns.ToDictionary(x => x.VatPercent, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityTaxbreakdowns.Select(x => x.VatPercent));
                var newIndexTaxbreakdown = commandTaxBreakDowns.Where(x => !duplicateTaxbreakdown.Contains(x.VatPercent)).ToList();

                foreach (var item in entityTaxbreakdowns)
                {
                    if (duplicateTaxbreakdown.Contains(item.VatPercent)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var taxbreakdown = commandTaxBreakDowns.FirstOrDefault(x => x.VatPercent == item.VatPercent);
                        taxbreakdown.Id = item.Id;
                        oldTaxbreakdowns.Add(taxbreakdown);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeTaxbreakdowns.Add(item);
                    }
                }

                if (newIndexTaxbreakdown.Any())
                {
                    var totalItemsTaxbreakdown = newIndexTaxbreakdown.Count;
                    var currentTaxbreakdownId = await GetSEQsNextVal(totalItemsTaxbreakdown, SequenceName.Invoice01TaxBreakdown);

                    //thêm mới
                    for (int i = 0; i < newIndexTaxbreakdown.Count; i++)
                    {
                        var item = newIndexTaxbreakdown[i];
                        item.Id = currentTaxbreakdownId[i];
                        newTaxbreakdowns.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceTaxBreakdowns = oldTaxbreakdowns;
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(newTaxbreakdowns);

                // Tinh toan chi tiet thue suat
                // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                // Nếu không => Lưu theo input người dùng
                if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                {
                    // 
                    var rootTaxBreakdowns = invoiceMongo.InvoiceTaxBreakdowns;
                    // Tinh toan chi tiet thue suat
                    entityUpdateMongo.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(entityUpdateMongo);

                    if (entityUpdateMongo.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        int indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(entityUpdateMongo.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = entityUpdateMongo.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }

                        removeTaxbreakdowns = rootTaxBreakdowns.Select(x => new Invoice01TaxBreakdownDto()
                        {
                            Id = x.Id,
                        }).ToList();
                        oldTaxbreakdowns = new List<Invoice01TaxBreakdownDto>();
                        newTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                    }
                }
                else
                {
                    if (!entityUpdateMongo.InvoiceTaxBreakdowns.IsNullOrEmpty())
                    {
                        foreach (var taxBreakdownDto in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            // Cộng tổng tiền
                            taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, entityUpdateMongo);
                        }
                    }
                }

                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);

                var rootInvoice = await _repoInvoice01Header.GetByIdRawAsync(invoice.TenantId, invoice.Id);
                if (rootInvoice == null)
                    return new ResultApiViewModel<UpdateReplaceInvoice01ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice01ApiResponseModel>(new UpdateReplaceInvoice01ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    }));

                try
                {
                    var data = _factory.ObjectMapper.Map<MongoInvoice01Entity, Invoice01HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice01Service.GenerateUpdateRefactorQuery(data, toCurrency, oldDetails, newDetails, removeDetails, oldTaxbreakdowns, newTaxbreakdowns, removeTaxbreakdowns, false, null);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

                    //lấy dữ liệu bảng InvoiceReference để update invoiceDateReference
                    var invoiceRef = await _repoInvoice01Reference.GetByInvoiceReferenceIdAsync(invoice.Id);
                    if (invoiceRef != null)
                    {
                        invoiceRef.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? invoice.InvoiceDate.Date.ToLocalTime() : invoice.InvoiceDate.Date;
                        await _repoInvoice01Reference.UpdateAsync(invoiceRef);
                    }

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                #region Thêm nội dung ghi chú khi in hóa đơn
                if (input.InvoicePrintNote != null)
                {
                    var invoicePrintNote = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(entityUpdateMongo.Id, (short)VnisType._01GTKT.GetHashCode(), tenantId);
                    if (invoicePrintNote != null)
                    {
                        // Update 
                        invoicePrintNote.Note = input.InvoicePrintNote.Note;
                        invoicePrintNote.IsShowNote = input.InvoicePrintNote.IsShowNote;
                        await _invoicePrintNoteRepository.UpdateAsync(invoicePrintNote);
                    }
                    else
                    {
                        await _invoicePrintNoteRepository.InsertAsync(new InvoicePrintNoteEntity()
                        {
                            TenantId = tenantId,
                            Note = input.InvoicePrintNote.Note,
                            IsShowNote = input.InvoicePrintNote.IsShowNote,
                            InvoiceHeaderId = entityUpdateMongo.Id,
                            InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                            CreationTime = DateTime.Now,
                            CreatorId = CurrentUser.Id,
                        });
                    }
                }
                #endregion

                return new ResultApiViewModel<UpdateReplaceInvoice01ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice01ApiResponseModel>(new UpdateReplaceInvoice01ApiResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    TransactionId = invoice.TransactionId,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                }));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
            finally
            {
                // Xóa cache Processing
                if (lockId != 0 && isProcessing != true)
                {
                    _lockInvoiceService.UnLockInvoiceProcessed(lockId, _factory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
        }

        /// <summary>
        /// tạo hóa đơn điều chỉnh tăng giảm
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("adjustment-detail")]
        public async Task<object> CreateAdjustmentDetail([FromBody] CreateAdjustmentDetailInvoice01ApiRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                var tenantId = _factory.CurrentTenant.Id.Value;
                var validator = _validatorFactory.GetValidator<CreateAdjustmentDetailInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(tenantId);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var fromCurrency = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.FromCurrency.GetHashCode());
                var toCurrencyData = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
                var toCurrency = new CurrencyEntity
                {
                    CurrencyCode = toCurrencyData.ToCurrencyCode,
                    Conversion = toCurrencyData.ToCurrencyConversion,
                    Rounding = toCurrencyData.ToCurrencyRounding,
                    NameVi = toCurrencyData.NameVi,
                    MinimumNameVi = toCurrencyData.MinimumNameVi,
                    NameEn = toCurrencyData.NameEn,
                    MinimumNameEn = toCurrencyData.MinimumNameEn
                };

                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var rootInvoice = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");

                var rootInvoiceDetails = await _repoInvoice01Detail.QueryByIdInvoiceHeaderRawAsync(tenantId, rootInvoice.Id);

                //var rootInvoiceTaxbreakDowns = _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdRawAsync(rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingCheckCreateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateCustomer.ToString());
                var settingCheckProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateProduct.ToString());
                var settingCheckUnit = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceCreateUnit.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isCreateCustomer = settingCheckCreateCustomer != null && settingCheckCreateCustomer.Value == "1";
                var isCreateProduct = settingCheckProduct != null && settingCheckProduct.Value == "1";
                var isCreateUnit = settingCheckUnit != null && settingCheckUnit.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateAdjustmentDetailInvoice01ApiRequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);
                var idInvoiceTaxBreakDowns = await GetSEQsNextVal(data.Sum(x => x.InvoiceTaxBreakdowns.Count), SequenceName.Invoice01TaxBreakdown);
                var totalItemsDetail = data.Sum(x => x.InvoiceDetails.Count);
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;

                foreach (var item in data)
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.CreatorId = CurrentUser.Id.Value;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerFullName = CurrentTenant.FullNameVi;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.TenantId = CurrentTenant.Id.Value;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.StoreCode = rootInvoice.StoreCode;
                    item.StoreName = rootInvoice.StoreName;
                    item.IsFinancialLeaseInvoice = rootInvoice.IsFinancialLeaseInvoice;
                    item.BudgetUnitCode = rootInvoice.BudgetUnitCode;
                    item.BuyerIDNumber = rootInvoice.BuyerIDNumber;
                    item.BuyerPassportNumber = rootInvoice.BuyerPassportNumber;

                    item.BuyerCode = rootInvoice.BuyerCode;
                    item.BuyerFullName = rootInvoice.BuyerFullName;
                    item.BuyerLegalName = rootInvoice.BuyerLegalName;
                    item.BuyerTaxCode = rootInvoice.BuyerTaxCode;
                    item.BuyerAddressLine = rootInvoice.BuyerAddressLine;
                    item.BuyerDistrictName = rootInvoice.BuyerDistrictName;
                    item.BuyerCityName = rootInvoice.BuyerCityName;
                    item.BuyerCountryCode = rootInvoice.BuyerCountryCode;
                    item.BuyerPhoneNumber = rootInvoice.BuyerPhoneNumber;
                    item.BuyerFaxNumber = rootInvoice.BuyerFaxNumber;
                    item.BuyerEmail = rootInvoice.BuyerEmail?.Trim();
                    item.BuyerBankName = rootInvoice.BuyerBankName;
                    item.BuyerBankAccount = rootInvoice.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = rootInvoice.Note;

                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);
                    item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);

                    item.InvoiceStatus = (short)InvoiceStatus.DieuChinhTangGiam.GetHashCode();
                    item.SignStatus = (short)SignStatus.ChoKy;
                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    item.RoundingCurrency = rootInvoice.RoundingCurrency;
                    item.FromCurrency = rootInvoice.FromCurrency;
                    item.CurrencyConversion = rootInvoice.CurrencyConversion;
                    item.ToCurrency = rootInvoice.ToCurrency;
                    item.ExchangeRate = rootInvoice.ExchangeRate;
                    item.PaymentMethod = rootInvoice.PaymentMethod;

                    item.FromCurrency = fromCurrency.FromCurrencyCode;
                    item.ToCurrency = toCurrency.CurrencyCode;
                    item.RoundingCurrency = toCurrency.Rounding;
                    item.CurrencyConversion = toCurrency.Conversion;
                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.Source = (short)InvoiceSource.Api;
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    // ReSharper disable once PossibleInvalidOperationException
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;

                    item.ExtraProperties = rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any()
                                            ? rootInvoice.ExtraProperties : null;

                    // sync catalog
                    item.IsSyncedCustomerToCore = isCreateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedProductToCore = isCreateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsSyncedUnitToCore = isCreateUnit ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.InvoiceReference = new Invoice01ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0
                                            ? (rootInvoice.InvoiceDate.Date == rootInvoice.InvoiceDate.Date.ToLocalTime() ? rootInvoice.InvoiceDate.Date.AddHours(7) : rootInvoice.InvoiceDate.Date.ToLocalTime())
                                            : rootInvoice.InvoiceDate.Date,

                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhTangGiam.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = tenantId
                    };

                    item.InvoiceDetails.ForEach(itemDetail =>
                    {
                        if (!indexes.ContainsKey(itemDetail.Index))
                            //throw new Exception("Thông tin điều chỉnh tăng giảm chi tiết hóa đơn không đúng");
                            throw new Exception(_localizier["Vnis.BE.Invoice01.IndexInvoiceDetailIncorrect"]);

                        var index = item.InvoiceDetails.FindAll(x => x.Index == itemDetail.Index);
                        if (index.Count > 1)
                            throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                        var rootDetail = indexes[itemDetail.Index];

                        itemDetail.Id = idInvoiceDetails[indexDetail];
                        itemDetail.InvoiceHeaderId = item.Id;
                        // ReSharper disable once PossibleInvalidOperationException
                        itemDetail.TenantId = CurrentTenant.Id.Value;
                        itemDetail.VatPercentDisplay = taxes[itemDetail.VatPercent].Item2;
                        itemDetail.RoundingUnit = rootDetail.RoundingUnit;
                        itemDetail.UnitId = rootDetail.UnitId;
                        itemDetail.UnitName = rootDetail.UnitName;
                        itemDetail.ProductName = rootDetail.ProductName;
                        itemDetail.ProductType = rootDetail.ProductType;
                        itemDetail.ProductCode = rootDetail.ProductCode;
                        itemDetail.ProductId = rootDetail.ProductId;
                        itemDetail.HideQuantity = rootDetail.HideQuantity;
                        itemDetail.HideUnit = rootDetail.HideUnit;
                        itemDetail.HideUnitPrice = rootDetail.HideUnitPrice;
                        itemDetail.Partition = partition;
                        itemDetail.ExtraProperties = rootDetail.ExtraProperties;

                        indexDetail++;
                    });

                    item.InvoiceTaxBreakdowns?.ForEach(itemTaxBreakDown =>
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.Id = idInvoiceTaxBreakDowns[indexTaxBreakDown];
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = item.Id;

                        indexTaxBreakDown++;
                    });

                    // Tinh toan chi tiet thue suat
                    var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                    if (isOnlyCKTM)
                    {
                        item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                    }
                    else
                    {
                        item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                    }

                    if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = item.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }
                    }

                    // Xac dinh loai thue suat
                    item.DiscountType = rootInvoice.DiscountType;

                    indexDetail++;

                    Log.Information($"rootInvoice.InvoiceDate.Date = {rootInvoice.InvoiceDate.Date}, ToLocalTime() = {rootInvoice.InvoiceDate.Date.ToLocalTime()}, AddHour(7) = {rootInvoice.InvoiceDate.Date.AddHours(7)}");
                }
                //data.ForEach(async item =>
                //{

                //});

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    // ReSharper disable once PossibleInvalidOperationException
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var numberTakeItems = numberRecordsInMessage;

                var tasks = new List<Task>();
                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };
                            await _distributedEventBus.PublishAsync(messageData);
                        }));

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                // thời gian delay trước khi kiểm tra hd đã sinh số chưa. default : 1000 => 1s
                var timeDelayInvoiceGenerateNumberInBatch = 1000;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatchSingle"], out timeDelayInvoiceGenerateNumberInBatch);

                var timeDelayLoopInvoiceGenerateNumberInBatchSingle = 1000;
                int.TryParse(_configuration["Settings:TimeDelayLoopInvoiceGenerateNumberInBatchSingle"], out timeDelayLoopInvoiceGenerateNumberInBatchSingle);

                // tổng số thời gian chờ sinh số : default = 1 phút
                var timeDelayInvoiceGenerateNumber = 1;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberSingle"], out timeDelayInvoiceGenerateNumber);

                var maxWaitTime = DateTime.Now.AddMinutes(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;

                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);

                while (DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;
                    }

                    await Task.Delay(timeDelayLoopInvoiceGenerateNumberInBatchSingle);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                await InvoiceReSyncToMongo(new List<long> { rootInvoice.Id }, InvoiceStatus.BiDieuChinhTangGiam);

                await InvoiceReSyncToEs(new List<long> { rootInvoice.Id }, SyncElasticSearchStatus.PendingSyncInvoiceAdjustedDetail);

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhTangGiam,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo,
                    TransactionId = rootInvoice.TransactionId
                }));
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// Tạo hóa đơn điều chỉnh định danh 
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header")]
        public async Task<object> CreateAdjustmentHeader([FromBody] CreateAdjustmentHeaderInvoice01ApiRequestModel request)
        {
            var isDeleteErp = true;
            try
            {
                if (!CurrentTenant.IsAvailable)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                }

                if (request == null)
                {
                    isDeleteErp = false;
                    throw new UserFriendlyException("Không tìm thấy dữ liệu hóa đơn");
                }

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }
                var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var batchId = Guid.NewGuid();
                var datetimeNow = DateTime.Now;

                var approveStatus = await _invoiceService.GetApproveStatusAsync(CurrentTenant.Id.Value);
                var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(CurrentTenant.Id.Value);
                var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(CurrentTenant.Id.Value);

                var rootInvoice = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");

                var rootInvoiceDetails = await _repoInvoice01Detail.QueryByIdInvoiceHeaderRawAsync(CurrentTenant.Id.Value, rootInvoice.Id);
                var rootInvoice01SpecificProducts = await _repoInvoice01SpecificProduct.QueryByInvoiceHeaderIdAsync(rootInvoice.Id);
                var rootInvoiceTaxbreakDowns = await _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdRawAsync(rootInvoice.Id);
                var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                // check sync catalog
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var data = new List<MongoInvoice01Entity> { _factory.ObjectMapper.Map<CreateAdjustmentHeaderInvoice01ApiRequestModel, MongoInvoice01Entity>(request) };

                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                int.TryParse(_configuration["RabbitMQ:Settings:NumberRecordsInMessage"], out var numberRecordsInMessage);
                if (numberRecordsInMessage == 0)
                    numberRecordsInMessage = 10;

                int.TryParse(_configuration["Settings:InvoicesMaxGroup"], out var invoicesMaxGroup);

                // Invoice 01 Header
                var idInvoiceHeaders = await GetSEQsNextVal(1, SequenceName.Invoice01Header);

                // Invoice 01 Detail
                var totalItemsDetail = rootInvoiceDetails.Count;
                var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);

                // Invoice 01 TaxBreakDown
                var totalItemsTaxBreakDown = rootInvoiceTaxbreakDowns.Count;
                var idInvoiceTaxBreakDowns = await GetSEQsNextVal(totalItemsTaxBreakDown, SequenceName.Invoice01TaxBreakdown);

                var partition = long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                var group = CurrentTenant.Group;
                var indexHeader = 0;
                var indexDetail = 0;
                var indexTaxBreakDown = 0;


                data.ForEach(async item =>
                {
                    item.Id = idInvoiceHeaders[indexHeader];
                    item.TenantId = CurrentTenant.Id.Value;
                    item.CreatorId = CurrentUser.Id.Value;

                    item.Source = (short)InvoiceSource.Api;
                    item.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;
                    item.SignStatus = (short)SignStatus.ChoKy;

                    item.ApproveStatus = (short)approveStatus;
                    item.ApproveCancelStatus = (short)approveCancelStatus;
                    item.ApproveDeleteStatus = (short)approveDeleteStatus;

                    //item.RegistrationHeaderId = (short)approveDeleteStatus.GetHashCode();
                    //item.RegistrationDetailId = (short)approveDeleteStatus.GetHashCode();
                    item.Number = null;
                    item.InvoiceNo = null;
                    item.SerialNo = request.SerialNo;
                    item.InvoiceTemplateId = template.TemplateId;
                    item.FullNameCreator = CurrentUser.FullName;
                    item.UserNameCreator = CurrentUser.UserName;

                    item.SellerId = CurrentTenant.Id.Value;
                    item.SellerCode = CurrentTenant.TenantCode;
                    item.SellerTaxCode = CurrentTenant.TaxCode;
                    item.SellerAddressLine = CurrentTenant.Address;
                    item.SellerCountryCode = CurrentTenant.Country;
                    item.SellerDistrictName = CurrentTenant.District;
                    item.SellerCityName = CurrentTenant.City;
                    item.SellerPhoneNumber = CurrentTenant.Phones;
                    item.SellerFaxNumber = CurrentTenant.Fax;
                    item.SellerEmail = CurrentTenant.Emails;
                    item.SellerBankName = CurrentTenant.BankName;
                    item.SellerBankAccount = CurrentTenant.BankAccount;
                    item.SellerLegalName = CurrentTenant.LegalName;
                    item.SellerFullName = CurrentTenant.FullNameVi;

                    item.BuyerCode = request.BuyerCode;
                    item.BuyerFullName = request.BuyerFullName;
                    item.BuyerLegalName = request.BuyerLegalName;
                    item.BuyerTaxCode = request.BuyerTaxCode;
                    item.BuyerAddressLine = request.BuyerAddressLine;
                    item.BuyerDistrictName = request.BuyerDistrictName;
                    item.BuyerCityName = request.BuyerCityName;
                    item.BuyerCountryCode = request.BuyerCountryCode;
                    item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                    item.BuyerFaxNumber = request.BuyerFaxNumber;
                    item.BuyerEmail = request.BuyerEmail?.Trim();
                    item.BuyerBankName = request.BuyerBankName;
                    item.BuyerBankAccount = request.BuyerBankAccount;

                    item.TransactionId = rootInvoice.TransactionId;
                    item.Note = request.Note;

                    item.TotalDiscountAmountBeforeTax = rootInvoice.TotalDiscountAmountBeforeTax;
                    item.TotalVatAmount = rootInvoice.TotalVatAmount;
                    item.TotalDiscountPercentAfterTax = rootInvoice.TotalDiscountPercentAfterTax;
                    item.TotalDiscountAmountAfterTax = rootInvoice.TotalDiscountAmountAfterTax;

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    //item.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    //item.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(CurrentTenant.Id.Value, toCurrency, item.TotalPaymentAmount, InvoiceStatus.ThayThe);
                    item.PaymentAmountWords = rootInvoice.PaymentAmountWords;
                    item.PaymentAmountWordsEn = rootInvoice.PaymentAmountWordsEn;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;
                    item.PaymentDate = isEnableMongoDbLocalTime > 0 ? (item.InvoiceDate.Date == item.InvoiceDate.Date.ToLocalTime() ? item.InvoiceDate.Date.AddHours(7) : item.InvoiceDate.Date.ToLocalTime()) : item.InvoiceDate.Date;

                    item.RoundingCurrency = rootInvoice.RoundingCurrency;
                    item.FromCurrency = rootInvoice.FromCurrency;
                    item.CurrencyConversion = rootInvoice.CurrencyConversion;
                    item.ToCurrency = rootInvoice.ToCurrency;
                    item.ExchangeRate = rootInvoice.ExchangeRate;
                    item.PaymentMethod = request.PaymentMethod;
                    item.TotalAmount = rootInvoice.TotalAmount;
                    item.TotalPaymentAmount = rootInvoice.TotalPaymentAmount;


                    item.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    item.TenantGroup = group;
                    item.BatchId = batchId;
                    item.TotalInvoices = 1;
                    item.Partition = partition;
                    item.InvoiceGroup = StringHelper.RandomGroup(invoicesMaxGroup);
                    item.PrefixSerialNo = request.SerialNo.Substring(0, 1);
                    //item.ExtraProperties = rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any()
                    //    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    //    {
                    //                                            { "invoiceHeaderExtras", rootInvoice.ExtraProperties.JsonSerialize()},
                    //    }) : null;

                    item.TemplateNo = request.TemplateNo;
                    item.SerialNo = request.SerialNo;
                    item.PaymentAmountWords = rootInvoice.PaymentAmountWords;
                    item.PaymentAmountWordsEn = rootInvoice.PaymentAmountWordsEn;
                    item.InvoiceDateYear = (short)item.InvoiceDate.Year;
                    item.InvoiceDateQuater = item.InvoiceDate.GetQuarter();
                    item.InvoiceDateMonth = (short)item.InvoiceDate.Month;
                    item.InvoiceDateWeek = item.InvoiceDate.GetWeek();
                    item.InvoiceDateNumber = (short)item.InvoiceDate.Day;
                    item.TenantId = CurrentTenant.Id.Value;

                    var invoiceTaxBreakDown = new List<Invoice01TaxBreakdownDto>();
                    rootInvoiceTaxbreakDowns.ForEach(taxBreakDown =>
                    {
                        invoiceTaxBreakDown.Add(new Invoice01TaxBreakdownDto
                        {
                            Id = idInvoiceTaxBreakDowns[indexTaxBreakDown],
                            TenantId = CurrentTenant.Id.Value,
                            Partition = partition,
                            InvoiceHeaderId = item.Id,
                            VatAmount = taxBreakDown.VatAmount,
                            VatAmountBackUp = taxBreakDown.VatAmountBackUp,
                            Amount = taxBreakDown.Amount,
                            VatPercent = taxBreakDown.VatPercent,
                            VatPercentDisplay = taxBreakDown.VatPercentDisplay,
                            Name = taxBreakDown.Name
                        });

                        indexTaxBreakDown++;
                    });

                    item.InvoiceTaxBreakdowns = invoiceTaxBreakDown;

                    item.InvoiceReference = new Invoice01ReferenceDto
                    {
                        InvoiceHeaderId = item.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? rootInvoice.InvoiceDate.Date.ToLocalTime() : rootInvoice.InvoiceDate.Date,
                        InvoiceNoReference = rootInvoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode(),
                        InvoiceReferenceId = rootInvoice.Id,
                        NumberReference = rootInvoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = rootInvoice.SerialNo,
                        TemplateNoReference = rootInvoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };

                    var invoiceDetails = new List<Invoice01DetailDto>();

                    rootInvoiceDetails.ForEach(itemDetail =>
                    {
                        if (!indexes.ContainsKey(itemDetail.Index))
                            throw new Exception("Thông tin chi tiết hóa đơn gốc không đúng");

                        var rootDetail = indexes[itemDetail.Index];
                        invoiceDetails.Add(new Invoice01DetailDto
                        {
                            Id = idInvoiceDetails[indexDetail],
                            Index = rootDetail.Index,
                            Note = rootDetail.Note,
                            InvoiceHeaderId = item.Id,
                            TenantId = CurrentTenant.Id.Value,
                            VatPercentDisplay = rootDetail.VatPercentDisplay,
                            RoundingUnit = rootDetail.RoundingUnit,
                            UnitPrice = rootDetail.UnitPrice,
                            PaymentAmount = rootDetail.PaymentAmount,
                            Amount = rootDetail.Amount,
                            UnitId = rootDetail.UnitId,
                            UnitName = rootDetail.UnitName,
                            ProductName = rootDetail.ProductName,
                            ProductType = rootDetail.ProductType,
                            ProductCode = rootDetail.ProductCode,
                            ProductId = rootDetail.ProductId,
                            HideQuantity = rootDetail.HideQuantity,
                            HideUnit = rootDetail.HideUnit,
                            HideUnitPrice = rootDetail.HideUnitPrice,
                            Partition = partition,
                            Quantity = rootDetail.Quantity,
                            IsPromotion = rootDetail.IsPromotion,
                            DiscountAmountBeforeTax = rootDetail.DiscountAmountBeforeTax,
                            DiscountPercentBeforeTax = rootDetail.DiscountPercentBeforeTax,
                            VatPercent = rootDetail.VatPercent,
                            VatAmount = rootDetail.VatAmount,
                            ExtraProperties = rootDetail.ExtraProperties != null && rootDetail.ExtraProperties.Any()
                                        ? rootDetail.ExtraProperties : null,
                            InvoiceSpecificProductExtras = rootInvoice01SpecificProducts
                                .Where(x => x.InvoiceDetailId == idInvoiceDetails[indexDetail])
                                .Select(x => new Invoice01SpecificProductDto
                                {
                                    SpecificProductFieldId = x.SpecificProductFieldId,
                                    Type = x.Type,
                                    FieldName = x.FieldName,
                                    FieldValue = x.FieldValue,
                                }).ToList()
                        });

                        indexDetail++;

                        item.InvoiceDetails = invoiceDetails;
                    });

                    // Tinh toan chi tiet thue suat
                    var isOnlyCKTM = ReCaculateInvoiceUtil.IsOnlyCKTM(item);
                    if (isOnlyCKTM)
                    {
                        item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDownOnlyCKTM(item);
                    }
                    else
                    {
                        item.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(item);
                    }

                    if (item.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(item.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in item.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = item.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }
                    }

                    // Xac dinh loai thue suat
                    if (item.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                    {
                        item.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(item).GetHashCode();
                    }

                    indexHeader++;
                });

                var numberTakeItems = numberRecordsInMessage;

                var invoiceTemplateIds = data.Select(x => x.InvoiceTemplateId).Distinct();

                var invoice01BatchIds = invoiceTemplateIds.Select(item => new MongoInvoice01BatchIdEntity
                {
                    BatchId = batchId,
                    TenantId = CurrentTenant.Id.Value,
                    Group = CurrentTenant.Group,
                    CreationTime = datetimeNow,
                    InvoiceTemplateId = item,
                    TotalInvoices = 1
                }).ToList();

                await _mongoInvoice01BatchIdRepository.InsertManyAsync(invoice01BatchIds);

                var tasks = new List<Task>();

                var startIndex = 0;

                while (true)
                {
                    var itemsTake = data.Skip(startIndex).Take(numberTakeItems - startIndex).ToList();
                    tasks.Add(
                        Task.Run(async () =>
                        {
                            var messageData = new CreateInvoice01HeaderEventSendData
                            {
                                Invoices = itemsTake,
                                TenantId = CurrentTenant.Id.Value
                            };

                            await _distributedEventBus.PublishAsync(messageData);
                        })
                    );

                    startIndex = numberTakeItems;
                    numberTakeItems += numberTakeItems;
                    if (startIndex >= 1)
                        break;
                }

                await Task.WhenAll(tasks);
                isDeleteErp = false;

                //chờ sinh số
                var timeDelayInvoiceGenerateNumberInBatch = 20;
                var timeDelayInvoiceGenerateNumber = 100;
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumberInBatch"], out timeDelayInvoiceGenerateNumberInBatch);
                int.TryParse(_configuration["Settings:TimeDelayInvoiceGenerateNumber"], out timeDelayInvoiceGenerateNumber);

                var now = DateTime.Now;
                var maxWaitTime = DateTime.Now.AddMilliseconds(timeDelayInvoiceGenerateNumber);

                var enityMongo = new MongoInvoice01Entity();
                var isError = true;
                await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                while (!string.IsNullOrEmpty(enityMongo?.InvoiceNo) || DateTime.Now < maxWaitTime)
                {
                    enityMongo = await _mongoInvoice01Repository.GetById(idInvoiceHeaders.FirstOrDefault());

                    if (enityMongo?.InvoiceNo != null)
                    {
                        isError = false;
                        break;

                    }

                    await Task.Delay(timeDelayInvoiceGenerateNumberInBatch);
                }

                if (isError)
                {
                    invoice01BatchIds.ForEach(item =>
                    {
                        item.GenerateNumberStatus = -1;
                    });
                    await _mongoInvoice01BatchIdRepository.UpdateManyAsync(invoice01BatchIds);

                    throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);
                }

                await InvoiceReSyncToMongo(new List<long> { rootInvoice.Id }, InvoiceStatus.BiDieuChinhDinhDanh);

                await InvoiceReSyncToEs(new List<long> { rootInvoice.Id }, SyncElasticSearchStatus.PendingSyncInvoiceAdjustedHeader);

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = data.FirstOrDefault().Id,
                    ErpId = request.ErpId,
                    InvoiceNo = enityMongo?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh,
                    SerialNo = request.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = request.TemplateNo
                }));

                return result;

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
            finally
            {
                if (isDeleteErp)
                {
                    try
                    {
                        var erpIdMongo = _validationContext.GetItem<MongoInvoice01ErpIdEntity>("InvoiceErpId");

                        if (erpIdMongo != null)
                            await _mongoInvoice01ErpIdRepository.DeleteAsync(erpIdMongo);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e, e.Message);
                    }
                }
            }
        }

        /// <summary>
        /// Xóa hủy hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("cancel/{erpId}")]
        public async Task<object> CancelAsync([FromRoute] string erpId)
        {
            try
            {
                var request = new CancelInvoice01ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<CancelInvoice01ApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");


                var hasApproveDelete = await _invoiceService.HasApproveCancelAsync(tenantId);

                // MongoDB
                var invoiceMongoDB = await _mongoInvoice01Repository.GetById(invoice.Id);
                if (invoiceMongoDB != null)
                {
                    var entityUpdateMongo = new MongoInvoice01Entity();
                    var input = request;
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                        }
                    }

                    if (hasApproveDelete)
                        entityUpdateMongo.ApproveStatus = (short)ApproveStatus.ChoDuyet;
                    else
                        entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.XoaHuy;

                    entityUpdateMongo.CancelTime = DateTime.Now;
                    entityUpdateMongo.CancelId = userId;
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncCancel.GetHashCode();
                    await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);
                    //throw new UserFriendlyException($"Không tìm thấy hóa đơn");
                }



                var response = new InvoiceCommandResponseModel();

                try
                {
                    if (hasApproveDelete)
                    {
                        var query = $@"UPDATE ""Invoice01Header"" SET
                                  ""ApproveCancelStatus"" = {(short)ApproveStatus.ChoDuyet},
                                  ""CancelTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""CancelId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                        await InvoiceReSyncToEs(new List<long> { invoice.Id }, SyncElasticSearchStatus.PendingSyncUpdate);
                    }
                    else
                    {
                        var query = $@"UPDATE ""Invoice01Header"" SET
                                  ""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy},
                                  ""CancelTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""CancelId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                        await InvoiceReSyncToEs(new List<long> { invoice.Id }, SyncElasticSearchStatus.PendingSyncUpdate);
                    }

                    response = new InvoiceCommandResponseModel
                    {
                        Id = invoice.Id,
                        TenantId = tenantId,
                        Type = VnisType._01GTKT,
                        UserId = userId,
                        UserFullName = userFullName,
                        UserName = userName,
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        Resource = InvoiceSource.Api,
                        State = InvoiceActionState.Cancel,
                        ActionLogInvoice = ActionLogInvoice.Cancel,
                        Action = InvoiceAction.Cancel,
                        //ConnectionId = message.ConnectionId,
                        InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                        {
                            OldInvoiceDate = invoice.InvoiceDate,
                            OldTotalAmount = invoice.TotalAmount,
                            OldTotalPaymentAmount = invoice.TotalPaymentAmount,
                            OldTotalVatAmount = invoice.TotalVatAmount
                        }
                    };

                    invoice.CancelId = userId;

                    //update lại ngày hóa đơn cuối cùng
                    await _invoiceService.UpdateLastInvoiceDateAfterCancelDeleteRawAsync(tenantId, invoice);

                    response.Method = HubMethod.InvoiceStatus;
                    response.InvoiceStatus = InvoiceStatus.XoaHuy;
                    response.ApproveStatus = ApproveStatus.KhongQuyTrinhDuyet;
                    response.ActionAt = DateTime.Now;
                    response.ActionAtUtc = DateTime.UtcNow;
                }
                catch (Exception ex)
                {
                    if (invoiceMongoDB != null)
                        await _mongoInvoice01Repository.UpdateAsync(invoiceMongoDB);

                    Log.Error(ex.Message);
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = response.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = response.InvoiceNo,
                    InvoiceStatus = response.InvoiceStatus,
                    SerialNo = response.SerialNo,
                    SignStatus = response.SignStatus,
                    TemplateNo = response.TemplateNo,
                    TransactionId = invoiceMongoDB.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data.Values
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }

                //Log.Error(ex, ex.Message);

                //return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region old
            //try
            //{
            //    var request = new CancelInvoice01ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };

            //    var validator = _validatorFactory.GetValidator<CancelInvoice01ApiRequestModel>();

            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    return new ResultApiViewModel<CancelInvoice01ApiResponseModel>(new ResultApiModel<CancelInvoice01ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }

        /// <summary>
        /// Xóa hóa đơn
        /// </summary>
        /// <param name="erpId"></param>
        /// <returns></returns>
        [HttpPost("delete/{erpId}")]
        public async Task<object> DeleteAsync([FromRoute] string erpId)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var request = new DeleteInvoice01ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<DeleteInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

                var hasApproveDelete = await _invoiceService.HasApproveDeleteAsync(CurrentTenant.Id.Value);
                // MongoDB
                var invoiceMongoDB = await _mongoInvoice01Repository.GetById(invoice.Id);
                if (invoiceMongoDB != null)
                {
                    var entityUpdateMongo = new MongoInvoice01Entity();
                    var input = request;
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongoDB, null), null);
                        }
                    }

                    if (hasApproveDelete)
                    {
                        entityUpdateMongo.ApproveStatus = (short)ApproveStatus.ChoDuyet;
                    }
                    else
                    {
                        entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.XoaBo;
                    }

                    entityUpdateMongo.DeleteTime = DateTime.Now;
                    entityUpdateMongo.DeleteId = userId;
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncDelete.GetHashCode();
                    await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);

                    //throw new UserFriendlyException($"Không tìm thấy thông tin hóa đơn có ErpId: {erpId} để xóa");
                }


                var invoiceStatus = (short)ApproveStatus.ChoDuyet;
                try
                {
                    if (hasApproveDelete)
                    {
                        var query = $@"UPDATE ""Invoice01Header"" SET
                                  ""ApproveDeleteStatus"" = {(short)ApproveStatus.ChoDuyet},
                                  ""DeleteTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""DeleteId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                        await InvoiceReSyncToEs(new List<long> { invoice.Id }, SyncElasticSearchStatus.PendingSyncUpdate);
                        invoiceStatus = (short)ApproveStatus.ChoDuyet;
                    }
                    else
                    {
                        var query = $@"UPDATE ""Invoice01Header"" SET
                                  ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo},
                                  ""DeleteTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                  ""DeleteId"" = '{OracleExtension.ConvertGuidToRaw(userId)}'
                            WHERE ""Id"" = {invoice.Id}";

                        await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                        await InvoiceReSyncToEs(new List<long> { invoice.Id }, SyncElasticSearchStatus.PendingSyncUpdate);
                        invoiceStatus = (short)InvoiceStatus.XoaBo;

                        #region Xóa hóa đơn khỏi BTH theo ND70
                        // Xóa hóa đơn khỏi bảng tổng hợp
                        var detailMappingEntities = _validationContext.GetItem<List<TaxReport01DetailMappingEntity>>("TaxReport01DetailMappings");
                        if (detailMappingEntities != null && detailMappingEntities.Any())
                        {
                            foreach (var item in detailMappingEntities)
                            {
                                // Lấy danh sách id hóa đơn thuộc bảng tổng hợp
                                var invoiceIds = item.InvoiceIds?.Split(',').Select(long.Parse).ToList() ?? new List<long>();

                                // Nếu id hóa đơn thuộc bảng tổng hợp đã tồn tại trong danh sách hóa đơn đang xóa
                                if (invoiceIds.Any(x => x == invoice.Id))
                                {
                                    try
                                    {
                                        invoiceIds.Remove(invoice.Id);

                                        await _taxReport01Repository.UpdateInvoiceHeadersInDetailMapping(item.Id, invoiceIds, CurrentTenant.Id.Value);
                                    }
                                    catch (Exception)
                                    {
                                        // Không cần bắn ra exception vì khi 
                                        Log.Error($"Xóa hóa đơn khỏi bảng tổng hợp lỗi: {item.Id} - {invoice.Id}");
                                    }
                                }
                            }
                        }
                        #endregion
                    }

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Delete,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    if (invoiceMongoDB != null)
                        await _mongoInvoice01Repository.UpdateAsync(invoiceMongoDB);

                    Log.Error(ex.Message);
                    throw new UserFriendlyException($"{ex.Message}");
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus.GetHashCode().ToString()),
                    SerialNo = invoice.SerialNo,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus.GetHashCode().ToString()),
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region old
            //try
            //{
            //    var request = new DeleteInvoice01ApiRequestModel
            //    {
            //        ErpId = erpId
            //    };
            //    var validator = _validatorFactory.GetValidator<DeleteInvoice01ApiRequestModel>();

            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);
            //    return new ResultApiViewModel<DeleteInvoice01ApiResponseModel>(new ResultApiModel<DeleteInvoice01ApiResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }


        /// <summary>
        /// tạo hóa đơn điều chỉnh định danh không sinh số
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header-without-invoiceNo")]
        public async Task<object> CreateAdjustmentHeaderWithoutInvoiceNo(CreateAdjustmentHeaderWithoutInvoiceNoRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                var partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));

                // check sync catalog
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var invoiceMongo = await _mongoInvoice01Repository.GetById(invoice.Id);
                if (invoiceMongo != null)
                {
                    var entityUpdateMongo = new MongoInvoice01Entity();
                    // TODO: dùng tạm, tìm phương án xử lý khác
                    foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                    {
                        if (property.CanWrite)
                        {
                            property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                        }
                    }

                    entityUpdateMongo.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;
                    entityUpdateMongo.InvoiceReference = new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceDto
                    {
                        InvoiceHeaderId = invoice.Id,
                        InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? invoice.InvoiceDate.Date.ToLocalTime() : invoice.InvoiceDate.Date,
                        InvoiceNoReference = invoice.InvoiceNo,
                        InvoiceStatus = (short)InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode(),
                        InvoiceReferenceId = invoice.Id,
                        NumberReference = invoice.Number.Value,
                        Partition = partition,
                        SerialNoReference = invoice.SerialNo,
                        TemplateNoReference = invoice.TemplateNo,
                        TenantId = CurrentTenant.Id.Value
                    };
                    entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                    // sync catalog
                    entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                    await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);
                }

                try
                {
                    var message = new InvoiceCommandRequestModel
                    {
                        Resource = InvoiceSource.Api,
                        InvoiceId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        TenantCode = CurrentTenant.TenantCode,
                        TaxCode = CurrentTenant.TaxCode,
                        Address = CurrentTenant.Address,
                        Country = CurrentTenant.Country,
                        District = CurrentTenant.District,
                        City = CurrentTenant.City,
                        Phones = CurrentTenant.Phones,
                        Fax = CurrentTenant.Fax,
                        Email = CurrentTenant.Emails,
                        LegalName = CurrentTenant.LegalName,
                        BankName = CurrentTenant.BankName,
                        BankAccount = CurrentTenant.BankAccount,
                        SellerFullName = CurrentTenant.FullNameVi,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        UserFullName = CurrentUser.FullName,
                        Date = invoice.InvoiceDate,
                        SerialNo = request.SerialNo,
                        TemplateNo = request.TemplateNo,
                        InvoiceNo = request.InvoiceNo,
                        Number = invoice.Number,
                        Action = InvoiceAction.CreateAdjustmentHeaderWithoutInvoiceNo,
                        Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                        Type = VnisType._01GTKT,
                        InvoiceRootId = invoice?.Id
                    };

                    await _createAdjustmentHeaderWithoutInvoiceNoBusiness.CreateAsync(message);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);

                    //nếu update oracle lỗi thì update lại trạng thái cũ ở mongo nếu có
                    if (invoiceMongo != null)
                    {
                        await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    }

                    return new ResultApiViewModel(false, -1, ex.Message);
                }

                var result = new ResultApiViewModel<CreateInvoice01ResponseModel>(new ResultApiModel<CreateInvoice01ResponseModel>(new CreateInvoice01ResponseModel
                {
                    Id = invoice.Id,
                    ErpId = request.ErpId,
                    InvoiceNo = invoice?.InvoiceNo,
                    InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh,
                    SerialNo = invoice.SerialNo,
                    SignStatus = SignStatus.ChoKy,
                    TemplateNo = invoice.TemplateNo,
                    TransactionId = invoice.TransactionId
                }));
                result.Code = 0;
                result.Message = "Thành công";

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }

            #region
            //try
            //{
            //    var tenantId = _factory.CurrentTenant.Id.Value;
            //    var validator = _validatorFactory.GetValidator<CreateAdjustmentHeaderWithoutInvoiceNoRequestModel>();
            //    var validationResult = await validator.HandleAsync(request);
            //    if (!validationResult.Success)
            //        throw new UserFriendlyException(L[validationResult.Message]);

            //    var response = await _factory.Mediator.Send(request);

            //    var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
            //    var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
            //    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
            //    if (!hasApprove && setting != null && setting.Value == "1")
            //    {
            //        var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

            //        await signService.SignServer(new List<long> { invoice.Id });

            //        response.SignStatus = SignStatus.DaKy;
            //    }

            //    return new ResultApiViewModel<CreateAdjustmentHeaderWithoutInvoiceNoResponseModel>(new ResultApiModel<CreateAdjustmentHeaderWithoutInvoiceNoResponseModel>(response));
            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);

            //    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            //}
            #endregion
        }


        /// <summary>
        /// sửa hóa đơn gốc
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("update/{erpId}")]
        public async Task<object> Update([FromRoute] string erpId, [FromBody] UpdateInvoice01ApiRequestModel request)
        {
            // Kiểm Hóa đơn gốc có đang được xử lý không
            bool isProcessing = false;
            long lockId = 0;
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<UpdateInvoice01ApiRequestModel>();
                request.ErpId = erpId;

                #region Xác định lại thuế suất TH sản phẩm loại CKTM
                var configCreateOnlyCTKTMProduct = await _settingService.GetByCodeAsync(_factory.CurrentTenant.Id.Value, SettingKey.ConfigCreateOnlyCKTMProduct.ToString());
                #endregion

                var validationResult = await validator.HandleAsync(request);
                lockId = _validationContext.GetItem<long?>("RootId").HasValue ? _validationContext.GetItem<long>("RootId") : 0;
                if (!validationResult.Success)
                {
                    isProcessing = _validationContext.GetItem<bool?>("IsProcessing") == null ? false : _validationContext.GetItem<bool?>("IsProcessing").Value;
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoiceRoot = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCaculateTaxBreakDown.ToString());
                // check sync catalog
                var settingCheckUpdateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var response = new UpdateInvoice01ApiResponseModel();

                var invoiceMongo = await _mongoInvoice01Repository.GetById(invoiceRoot.Id);
                if (invoiceMongo == null)
                    throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {erpId}");

                //await _updateRootInvoice01ApiBusiness.UpdateMongoAsync(request);

                // update oracle
                var entityUpdateMongo = new MongoInvoice01Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                // ND70
                entityUpdateMongo.StoreCode = input.StoreCode;
                entityUpdateMongo.StoreName = input.StoreName;
                entityUpdateMongo.IsFinancialLeaseInvoice = input.IsFinancialLeaseInvoice;
                entityUpdateMongo.BudgetUnitCode = input.BudgetUnitCode;
                entityUpdateMongo.BuyerIDNumber = input.BuyerIDNumber;
                entityUpdateMongo.BuyerPassportNumber = input.BuyerPassportNumber;

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;

                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;

                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;

                entityUpdateMongo.Note = input.Note;

                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;

                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;

                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalDiscountAmountAfterTax = input.TotalDiscountAmountAfterTax;
                entityUpdateMongo.TotalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax;
                entityUpdateMongo.TotalDiscountPercentAfterTax = input.TotalDiscountPercentAfterTax;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.TotalVatAmount = input.TotalVatAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;

                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                    ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                    {
                                        { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                    }) : null;



                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null,
                        InvoiceSpecificProductExtras = item.InvoiceSpecificProductExtras != null && item.InvoiceSpecificProductExtras.Any()
                                        ? item.InvoiceSpecificProductExtras.Select(x =>
                                            new Invoice01SpecificProductDto()
                                            {
                                                SpecificProductFieldId = x.SpecificProductFieldId,
                                                Type = x.Type,
                                                FieldName = x.FieldName,
                                                FieldValue = x.FieldValue,
                                            })
                                        .ToList() : null,
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var entityIndex = entityDetails.Select(x => x.Index);
                var duplicate = commandInvoiceDetails.Select(x => x.Index).Distinct()
                                            .Intersect(entityIndex);

                var newIndex = commandInvoiceDetails.Where(x => !entityIndex.Contains(x.Index)).ToList();

                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                    //thêm mới

                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex.ElementAt(i);
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                var oldTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var removeTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var newTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto
                {
                    TenantId = entityUpdateMongo.TenantId,
                    InvoiceHeaderId = entityUpdateMongo.Id,
                    VatAmount = x.VatAmount,
                    Amount = x.Amount,
                    //VatAmountBackUp = x.VatAmountBackUp,
                    VatPercent = x.VatPercent,
                    VatPercentDisplay = taxes[x.VatPercent].Item2,
                    Name = taxes[x.VatPercent].Item1,
                    Partition = partition
                })
               .ToList();

                var entityTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                //group để bỏ trường hợp Index thêm mới
                var duplicateTaxbreakdown = commandTaxBreakDowns.Select(x => x.VatPercent).Distinct()
                                            .Intersect(entityTaxbreakdowns.Select(x => x.VatPercent));

                var newIndexTaxbreakdown = commandTaxBreakDowns.Where(x => !duplicateTaxbreakdown.Contains(x.VatPercent)).ToList();

                foreach (var item in entityTaxbreakdowns)
                {
                    if (duplicateTaxbreakdown.Contains(item.VatPercent)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var taxbreakdown = commandTaxBreakDowns.FirstOrDefault(x => x.VatPercent == item.VatPercent);
                        taxbreakdown.Id = item.Id;
                        oldTaxbreakdowns.Add(taxbreakdown);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeTaxbreakdowns.Add(item);
                    }
                }

                if (newIndexTaxbreakdown.Any())
                {
                    var totalItemsTaxbreakdown = newIndexTaxbreakdown.Count;
                    var idInvoiceTaxBreakDowns = await GetSEQsNextVal(totalItemsTaxbreakdown, SequenceName.Invoice01TaxBreakdown);
                    var indexTaxBreakDown = 0;

                    //thêm mới
                    foreach (var item in newIndexTaxbreakdown)
                    {
                        item.Id = idInvoiceTaxBreakDowns[indexTaxBreakDown];
                        newTaxbreakdowns.Add(item);

                        indexTaxBreakDown++;
                    }
                }

                entityUpdateMongo.InvoiceTaxBreakdowns = oldTaxbreakdowns;
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(newTaxbreakdowns);

                // Tinh toan chi tiet thue suat
                // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                // Nếu không => Lưu theo input người dùng
                if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                {
                    var rootTaxBreakdowns = invoiceRoot.InvoiceTaxBreakdowns;
                    // Tinh toan chi tiet thue suat
                    entityUpdateMongo.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(entityUpdateMongo);

                    if (entityUpdateMongo.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        int indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(entityUpdateMongo.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = entityUpdateMongo.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }

                        removeTaxbreakdowns = rootTaxBreakdowns.Select(x => new Invoice01TaxBreakdownDto()
                        {
                            Id = x.Id,
                        }).ToList();
                        oldTaxbreakdowns = new List<Invoice01TaxBreakdownDto>();
                        newTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                    }
                }
                else
                {
                    if (!entityUpdateMongo.InvoiceTaxBreakdowns.IsNullOrEmpty())
                    {
                        foreach (var taxBreakdownDto in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            // Cộng tổng tiền
                            taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, entityUpdateMongo);
                        }
                    }
                }

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);

                try
                {
                    var invoice = _factory.ObjectMapper.Map<MongoInvoice01Entity, Invoice01HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice01Service.GenerateUpdateRefactorQuery(invoice, toCurrency, oldDetails, newDetails, removeDetails, oldTaxbreakdowns, newTaxbreakdowns, removeTaxbreakdowns, false, null);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    response = new UpdateInvoice01ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    };

                    await _invoiceService.UpdateLastInvoiceDateAsync(entityUpdateMongo.TenantId, entityUpdateMongo.InvoiceTemplateId, entityUpdateMongo.Number.Value);
                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateInvoice01ApiResponseModel>(new ResultApiModel<UpdateInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
            finally
            {
                // Xóa cache Processing
                if (lockId != 0 && isProcessing != true)
                {
                    _lockInvoiceService.UnLockInvoiceProcessed(lockId, _factory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
        }


        /// <summary>
        /// sửa hóa đơn thay thế
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("replace/update/{erpId}")]
        public async Task<object> UpdateReplace([FromRoute] string erpId, [FromBody] UpdateReplaceInvoice01ApiRequestModel request)
        {
            // Kiểm Hóa đơn gốc có đang được xử lý không
            bool isProcessing = false;
            long lockId = 0;
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<UpdateReplaceInvoice01ApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                lockId = _validationContext.GetItem<long?>("RootId").HasValue ? _validationContext.GetItem<long>("RootId") : 0;
                if (!validationResult.Success)
                {
                    isProcessing = _validationContext.GetItem<bool?>("IsProcessing") == null ? false : _validationContext.GetItem<bool?>("IsProcessing").Value;
                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
                var products = dataInfoCreate.Where(x => x.Type == ValidateDataType.Product.GetHashCode()).ToDictionary(x => x.ProductCode, x => x);
                var units = dataInfoCreate.Where(x => x.Type == ValidateDataType.Unit.GetHashCode()).ToDictionary(x => x.UnitName, x => x);
                var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var invoiceMongo = await _mongoInvoice01Repository.GetByErpId(erpId, CurrentTenant.Id.Value);
                if (invoiceMongo == null)
                    throw new UserFriendlyException("Không tìm thấy hóa đơn");

                // Lấy cấu hình tính toán lại Chi tiết thuế suất
                var configCaculateTaxBreakDown = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigCaculateTaxBreakDown.ToString());

                // check sync catalog
                var settingCheckUpdateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());
                var settingCheckAutoSendMail = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.SendMailAfterSign.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";
                var isAutoSendMail = settingCheckAutoSendMail != null && settingCheckAutoSendMail.Value == "1";

                var entityUpdateMongo = new MongoInvoice01Entity();
                var input = request;
                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                // ND70
                entityUpdateMongo.StoreCode = input.StoreCode;
                entityUpdateMongo.StoreName = input.StoreName;
                entityUpdateMongo.IsFinancialLeaseInvoice = input.IsFinancialLeaseInvoice;
                entityUpdateMongo.BudgetUnitCode = input.BudgetUnitCode;
                entityUpdateMongo.BuyerIDNumber = input.BuyerIDNumber;
                entityUpdateMongo.BuyerPassportNumber = input.BuyerPassportNumber;

                entityUpdateMongo.BuyerAddressLine = input.BuyerAddressLine;
                entityUpdateMongo.BuyerBankAccount = input.BuyerBankAccount;
                entityUpdateMongo.BuyerBankName = input.BuyerBankName;
                entityUpdateMongo.BuyerCityName = input.BuyerCityName;
                entityUpdateMongo.BuyerCode = input.BuyerCode;
                entityUpdateMongo.BuyerCountryCode = input.BuyerCountryCode;
                entityUpdateMongo.BuyerDistrictName = input.BuyerDistrictName;
                entityUpdateMongo.BuyerEmail = input.BuyerEmail?.Trim();
                entityUpdateMongo.BuyerFaxNumber = input.BuyerFaxNumber;
                entityUpdateMongo.BuyerFullName = input.BuyerFullName;
                entityUpdateMongo.BuyerLegalName = input.BuyerLegalName;
                entityUpdateMongo.BuyerPhoneNumber = input.BuyerPhoneNumber;
                entityUpdateMongo.BuyerTaxCode = input.BuyerTaxCode;
                entityUpdateMongo.ExchangeRate = input.ExchangeRate;
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;
                entityUpdateMongo.Note = input.Note;
                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.ThayThe))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.ThayThe))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentMethod = input.PaymentMethod;
                entityUpdateMongo.ToCurrency = toCurrency.CurrencyCode;
                entityUpdateMongo.RoundingCurrency = toCurrency.Rounding;
                entityUpdateMongo.CurrencyConversion = toCurrency.Conversion;
                entityUpdateMongo.FromCurrency = fromCurrency.CurrencyCode;
                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalDiscountAmountAfterTax = input.TotalDiscountAmountAfterTax;
                entityUpdateMongo.TotalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax;
                entityUpdateMongo.TotalDiscountPercentAfterTax = input.TotalDiscountPercentAfterTax;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.TotalVatAmount = input.TotalVatAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;
                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedCustomerToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();
                //entityUpdateMongo.IsSyncedUnitToCore = 0;
                //entityUpdateMongo.IsSyncedProductToCore = 0;
                //entityUpdateMongo.IsSyncedCustomerToCore = 0;

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsCreatedContentEmail = isAutoSendMail ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                entityUpdateMongo.ExtraProperties = input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any()
                ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                {
                { "invoiceHeaderExtras", input.InvoiceHeaderExtras.JsonSerialize()},
                }) : null;

                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var newDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var unit = (units != null && units.ContainsKey(item.UnitName?.Trim())) ? units[item.UnitName?.Trim()] : null;

                    var productCode = item.ProductCode?.Trim();
                    var product = string.IsNullOrEmpty(productCode)
                                ? null
                                : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = product == null ? 0 : product.ProductId,
                        ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                        ProductName = item.ProductName,
                        ProductType = item.ProductType,
                        Quantity = item.Quantity,
                        UnitId = unit?.UnitId ?? 0,
                        UnitName = item.UnitName?.Trim(),
                        HideQuantity = product?.HideQuantity ?? false,
                        HideUnit = product?.HideUnit ?? false,
                        HideUnitPrice = product?.HideUnitPrice ?? false,
                        RoundingUnit = unit?.UnitRounding ?? 4,
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        Partition = partition,
                        ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                                        ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                                        {
                                        { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                                        }) : null,
                        InvoiceSpecificProductExtras = item.InvoiceSpecificProductExtras != null && item.InvoiceSpecificProductExtras.Any()
                                        ? item.InvoiceSpecificProductExtras.Select(x =>
                                            new Invoice01SpecificProductDto()
                                            {
                                                SpecificProductFieldId = x.SpecificProductFieldId,
                                                Type = x.Type,
                                                FieldName = x.FieldName,
                                                FieldValue = x.FieldValue,
                                            })
                                        .ToList() : null,
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var duplicate = commandInvoiceDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));
                var newIndex = commandInvoiceDetails.Where(x => !duplicate.Contains(x.Index)).ToList();

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                if (newIndex.Any())
                {
                    var totalItemsDetail = newIndex.Count;
                    var idInvoiceDetails = await GetSEQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail);
                    await GetSeQsNextVal(totalItemsDetail, SequenceName.Invoice01Detail, 1);

                    //thêm mới
                    for (int i = 0; i < newIndex.Count; i++)
                    {
                        var item = newIndex[i];
                        item.Id = idInvoiceDetails[i];
                        newDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;
                entityUpdateMongo.InvoiceDetails.AddRange(newDetails);

                var oldTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var removeTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var newTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto
                {
                    TenantId = entityUpdateMongo.TenantId,
                    InvoiceHeaderId = entityUpdateMongo.Id,
                    VatAmount = x.VatAmount,
                    Amount = x.Amount,
                    //VatAmountBackUp = x.VatAmountBackUp,
                    VatPercent = x.VatPercent,
                    VatPercentDisplay = taxes[x.VatPercent].Item2,
                    Name = taxes[x.VatPercent].Item1,
                    Partition = partition
                })
               .ToList();

                var entityTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                //group để bỏ trường hợp Index thêm mới
                var duplicateTaxbreakdown = commandTaxBreakDowns.ToDictionary(x => x.VatPercent, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityTaxbreakdowns.Select(x => x.VatPercent));
                var newIndexTaxbreakdown = commandTaxBreakDowns.Where(x => !duplicateTaxbreakdown.Contains(x.VatPercent)).ToList();

                foreach (var item in entityTaxbreakdowns)
                {
                    if (duplicateTaxbreakdown.Contains(item.VatPercent)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var taxbreakdown = commandTaxBreakDowns.FirstOrDefault(x => x.VatPercent == item.VatPercent);
                        taxbreakdown.Id = item.Id;
                        oldTaxbreakdowns.Add(taxbreakdown);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeTaxbreakdowns.Add(item);
                    }
                }

                if (newIndexTaxbreakdown.Any())
                {
                    var totalItemsTaxbreakdown = newIndexTaxbreakdown.Count;
                    var currentTaxbreakdownId = await GetSEQsNextVal(totalItemsTaxbreakdown, SequenceName.Invoice01TaxBreakdown);

                    //thêm mới
                    for (int i = 0; i < newIndexTaxbreakdown.Count; i++)
                    {
                        var item = newIndexTaxbreakdown[i];
                        item.Id = currentTaxbreakdownId[i];
                        newTaxbreakdowns.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceTaxBreakdowns = oldTaxbreakdowns;
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(newTaxbreakdowns);

                // Tinh toan chi tiet thue suat
                // Nếu bật cấu hình tự động tính thì tính lại Chi tiết thuế suất
                // Nếu không => Lưu theo input người dùng
                if (configCaculateTaxBreakDown != null && configCaculateTaxBreakDown.Value == "1")
                {
                    var rootTaxBreakdowns = invoiceMongo.InvoiceTaxBreakdowns;
                    // Tinh toan chi tiet thue suat
                    entityUpdateMongo.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(entityUpdateMongo);

                    if (entityUpdateMongo.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                    {
                        // Tính toán lại
                        // Gen lại sequence
                        int indexTaxBreakDown = 0;
                        var reIdTaxBreakDowns = await GetSEQsNextVal(entityUpdateMongo.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                        foreach (var itemTaxBreakDown in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            var tax = taxes[itemTaxBreakDown.VatPercent];

                            itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                            itemTaxBreakDown.Name = tax?.Item1;
                            itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                            itemTaxBreakDown.Partition = partition;
                            itemTaxBreakDown.InvoiceHeaderId = entityUpdateMongo.Id;
                            itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                            indexTaxBreakDown++;
                        }

                        removeTaxbreakdowns = rootTaxBreakdowns.Select(x => new Invoice01TaxBreakdownDto()
                        {
                            Id = x.Id,
                        }).ToList();
                        oldTaxbreakdowns = new List<Invoice01TaxBreakdownDto>();
                        newTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                    }
                }
                else
                {
                    if (!entityUpdateMongo.InvoiceTaxBreakdowns.IsNullOrEmpty())
                    {
                        foreach (var taxBreakdownDto in entityUpdateMongo.InvoiceTaxBreakdowns)
                        {
                            // Cộng tổng tiền
                            taxBreakdownDto.Amount = ReCaculateInvoiceUtil.GetAmount(taxBreakdownDto.VatPercent, entityUpdateMongo);
                        }
                    }
                }

                // Xac dinh loai thue suat
                if (entityUpdateMongo.DiscountType == DiscountType.KhongChietKhau.GetHashCode())
                {
                    entityUpdateMongo.DiscountType = (short)ReCaculateInvoiceUtil.GetAutoDiscountType(entityUpdateMongo).GetHashCode();
                }

                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);

                var rootInvoice = await _repoInvoice01Header.GetByIdRawAsync(invoice.TenantId, invoice.Id);
                if (rootInvoice == null)
                    return new ResultApiViewModel<UpdateReplaceInvoice01ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice01ApiResponseModel>(new UpdateReplaceInvoice01ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = invoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)
                    }));

                try
                {
                    var data = _factory.ObjectMapper.Map<MongoInvoice01Entity, Invoice01HeaderEntity>(entityUpdateMongo);

                    var query = await _invoice01Service.GenerateUpdateRefactorQuery(data, toCurrency, oldDetails, newDetails, removeDetails, oldTaxbreakdowns, newTaxbreakdowns, removeTaxbreakdowns, false, null);

                    var excute = await _factory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

                    //lấy dữ liệu bảng InvoiceReference để update invoiceDateReference
                    var invoiceRef = await _repoInvoice01Reference.GetByInvoiceReferenceIdAsync(invoice.Id);
                    if (invoiceRef != null)
                    {
                        invoiceRef.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? invoice.InvoiceDate.Date.ToLocalTime() : invoice.InvoiceDate.Date;
                        await _repoInvoice01Reference.UpdateAsync(invoiceRef);
                    }

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                #region Thêm nội dung ghi chú khi in hóa đơn
                if (input.InvoicePrintNote != null)
                {
                    var invoicePrintNote = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(entityUpdateMongo.Id, (short)VnisType._01GTKT.GetHashCode(), tenantId);
                    if (invoicePrintNote != null)
                    {
                        // Update 
                        invoicePrintNote.Note = input.InvoicePrintNote.Note;
                        invoicePrintNote.IsShowNote = input.InvoicePrintNote.IsShowNote;
                        await _invoicePrintNoteRepository.UpdateAsync(invoicePrintNote);
                    }
                    else
                    {
                        await _invoicePrintNoteRepository.InsertAsync(new InvoicePrintNoteEntity()
                        {
                            TenantId = tenantId,
                            Note = input.InvoicePrintNote.Note,
                            IsShowNote = input.InvoicePrintNote.IsShowNote,
                            InvoiceHeaderId = entityUpdateMongo.Id,
                            InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                            CreationTime = DateTime.Now,
                            CreatorId = CurrentUser.Id,
                        });
                    }
                }
                #endregion

                return new ResultApiViewModel<UpdateReplaceInvoice01ApiResponseModel>(new ResultApiModel<UpdateReplaceInvoice01ApiResponseModel>(new UpdateReplaceInvoice01ApiResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    TransactionId = invoice.TransactionId,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                }));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
            finally
            {
                // Xóa cache Processing
                if (lockId != 0 && isProcessing != true)
                {
                    _lockInvoiceService.UnLockInvoiceProcessed(lockId, _factory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
        }

        /// <summary>
        /// sửa hóa đơn điều chỉnh định danh
        /// </summary>
        /// <param name="erpId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-header/update/{erpId}")]
        public async Task<object> UpdateAdjustmentHeader([FromRoute] string erpId, [FromBody] UpdateAdjustmentHeaderInvoice01ApiRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");

                if (!string.IsNullOrEmpty(request.BuyerEmail))
                {
                    if (!request.BuyerEmail.IsManyEmails(';'))
                    {
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                    }
                }

                var validator = _validatorFactory.GetValidator<UpdateAdjustmentHeaderInvoice01ApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);


                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var tenant = _validationContext.GetItem<Tenant>("Tenant");

                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
                if (invoice == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceNotFound"]);

                if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
                {
                    var headerExtras = request.InvoiceHeaderExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    request.InvoiceHeaderExtras = headerExtras;
                }

                var response = new UpdateAdjustmentHeaderInvoice01ApiResponseModel();

                request.Id = invoice.Id;

                var invoicesMongoDB = await _mongoInvoice01Repository.GetByIdsAsync(new List<long> { request.Id });
                await _updateAdjustHeaderInvoice01Business.UpdateMongoAsync(request);

                try
                {
                    var responseInvoice = await _updateAdjustHeaderInvoice01Business.UpdateOracleAsync(
                       new InvoiceCommandRequestModel
                       {
                           Resource = InvoiceSource.Api,
                           InvoiceId = invoice.Id,
                           InvoiceNo = invoice.InvoiceNo,
                           Number = invoice.Number,
                           TenantId = tenantId,
                           UserId = userId,
                           UserName = userName,
                           UserFullName = userFullName,
                           Date = request.InvoiceDate,
                           SerialNo = invoice.SerialNo,
                           TemplateNo = invoice.TemplateNo,
                           Action = InvoiceAction.UpdateAdjustmentHeader,
                           Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                           Type = VnisType._01GTKT,
                       });

                    response = new UpdateAdjustmentHeaderInvoice01ApiResponseModel
                    {
                        Id = responseInvoice.Id,
                        ErpId = invoice.ErpId,
                        TransactionId = invoice.TransactionId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = responseInvoice.InvoiceNo,
                        InvoiceStatus = responseInvoice.InvoiceStatus,
                        SignStatus = responseInvoice.SignStatus
                    };

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Delete,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateManyAsync(invoicesMongoDB);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateAdjustmentHeaderInvoice01ApiResponseModel>(new ResultApiModel<UpdateAdjustmentHeaderInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// sửa hóa đơn điều chỉnh tăng giảm
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("adjustment-detail/update/{erpId}")]
        public async Task<object> UpdateAdjustmentDetail([FromRoute] string erpId, [FromBody] UpdateAdjustmentDetailInvoice01ApiRequestModel request)
        {
            try
            {
                if (!CurrentTenant.IsAvailable)
                    throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                var validator = _validatorFactory.GetValidator<UpdateAdjustmentDetailInvoice01ApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var tenantId = _validationContext.GetItem<Guid>("TenantId");
                var userId = _validationContext.GetItem<Guid>("UserId");
                var userName = _validationContext.GetItem<string>("UserName");
                var userFullName = _validationContext.GetItem<string>("UserFullName");
                var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
                //var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
                var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

                var dataInfoCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
                var toCurrencyData = dataInfoCreate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
                var toCurrency = new CurrencyEntity
                {
                    CurrencyCode = toCurrencyData.ToCurrencyCode,
                    Conversion = toCurrencyData.ToCurrencyConversion,
                    Rounding = toCurrencyData.ToCurrencyRounding,
                    NameVi = toCurrencyData.NameVi,
                    MinimumNameVi = toCurrencyData.MinimumNameVi,
                    NameEn = toCurrencyData.NameEn,
                    MinimumNameEn = toCurrencyData.MinimumNameEn
                };

                // check sync catalog
                var settingCheckUpdateCustomer = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateCustomer.ToString());
                var settingCheckUpdateProduct = await _settingService.GetByCodeAsync(CurrentTenant.Id.Value, SettingKey.InvoiceUpdateProduct.ToString());

                var isUpdateCustomer = settingCheckUpdateCustomer != null && settingCheckUpdateCustomer.Value == "1";
                var isUpdateProduct = settingCheckUpdateProduct != null && settingCheckUpdateProduct.Value == "1";

                var invoiceMongo = await _mongoInvoice01Repository.GetById(invoice.Id);
                var input = request;
                var entityUpdateMongo = new MongoInvoice01Entity();

                // TODO: dùng tạm, tìm phương án xử lý khác
                foreach (PropertyInfo property in typeof(MongoInvoice01Entity).GetProperties())
                {
                    if (property.CanWrite)
                    {
                        property.SetValue(entityUpdateMongo, property.GetValue(invoiceMongo, null), null);
                    }
                }

                var partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm));
                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
                entityUpdateMongo.InvoiceDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;
                entityUpdateMongo.PaymentDate = isEnableMongoDbLocalTime > 0 ? (input.InvoiceDate.Date == input.InvoiceDate.Date.ToLocalTime() ? input.InvoiceDate.Date.AddHours(7) : input.InvoiceDate.Date.ToLocalTime()) : input.InvoiceDate.Date;

                entityUpdateMongo.InvoiceDateYear = (short)input.InvoiceDate.Year;
                entityUpdateMongo.InvoiceDateQuater = input.InvoiceDate.GetQuarter();
                entityUpdateMongo.InvoiceDateMonth = (short)input.InvoiceDate.Month;
                entityUpdateMongo.InvoiceDateWeek = input.InvoiceDate.GetWeek();
                entityUpdateMongo.InvoiceDateNumber = (short)input.InvoiceDate.Day;
                entityUpdateMongo.PaymentAmountWords = (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam))?.Trim().Replace("'", "''");
                entityUpdateMongo.PaymentAmountWordsEn = (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, input.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam))?.Trim().Replace("'", "''");
                entityUpdateMongo.TotalAmount = input.TotalAmount;
                entityUpdateMongo.TotalDiscountAmountAfterTax = input.TotalDiscountAmountAfterTax;
                entityUpdateMongo.TotalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax;
                entityUpdateMongo.TotalDiscountPercentAfterTax = input.TotalDiscountPercentAfterTax;
                entityUpdateMongo.TotalPaymentAmount = input.TotalPaymentAmount;
                entityUpdateMongo.TotalVatAmount = input.TotalVatAmount;
                entityUpdateMongo.LastModificationTime = DateTime.Now;
                entityUpdateMongo.LastModifierId = CurrentUser.Id;
                entityUpdateMongo.Partition = partition;

                // update chưa đồng bộ tt danh mục về core
                entityUpdateMongo.IsSyncedUnitToCore = 0;
                entityUpdateMongo.IsSyncedProductToCore = 0;
                entityUpdateMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();
                //entityUpdateMongo.IsSyncedUnitToCore = 0;
                //entityUpdateMongo.IsSyncedProductToCore = 0;

                // sync catalog
                entityUpdateMongo.IsSyncedCustomerToCore = isUpdateCustomer ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;
                entityUpdateMongo.IsSyncedProductToCore = isUpdateProduct ? (short)SyncCatalogStatus.UnSyncToCore : (short)SyncCatalogStatus.WithoutConfigSync;

                var entityDetails = entityUpdateMongo.InvoiceDetails;
                var oldDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var removeDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                var commandInvoiceDetails = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>();
                foreach (var item in input.InvoiceDetails)
                {
                    var index = input.InvoiceDetails.FindAll(x => x.Index == item.Index);
                    if (index.Count > 1)
                        throw new Exception(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.IndexIsDuplicated"]);

                    var entityOld = entityDetails.FirstOrDefault(x => x.Index == item.Index);
                    commandInvoiceDetails.Add(new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto
                    {
                        TenantId = tenantId,
                        InvoiceHeaderId = entityUpdateMongo.Id,
                        Index = item.Index,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductId = entityOld.ProductId,
                        ProductCode = entityOld.ProductCode,
                        ProductName = entityOld.ProductName,
                        ProductType = entityOld.ProductType,
                        Quantity = item.Quantity,
                        UnitId = entityOld.UnitId,
                        UnitName = entityOld.UnitName,
                        HideQuantity = entityOld.HideQuantity,
                        HideUnit = entityOld.HideUnit,
                        HideUnitPrice = entityOld.HideUnitPrice,
                        RoundingUnit = entityOld.RoundingUnit,
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        Partition = partition,
                        ExtraProperties = entityOld.ExtraProperties,
                        InvoiceSpecificProductExtras = item.InvoiceSpecificProductExtras != null && item.InvoiceSpecificProductExtras.Any()
                                        ? item.InvoiceSpecificProductExtras.Select(x =>
                                            new Invoice01SpecificProductDto()
                                            {
                                                SpecificProductFieldId = x.SpecificProductFieldId,
                                                Type = x.Type,
                                                FieldName = x.FieldName,
                                                FieldValue = x.FieldValue,
                                            })
                                        .ToList() : null,
                        //ExtraProperties = item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any()
                        //                ? new ExtraPropertyDictionary(new Dictionary<string, object>()
                        //                {
                        //                { "invoiceDetailExtras", item.InvoiceDetailExtras.JsonSerialize()},
                        //                }) : null
                    });
                }

                //group để bỏ trường hợp Index thêm mới
                var duplicate = commandInvoiceDetails.ToDictionary(x => x.Index, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityDetails.Select(x => x.Index));

                foreach (var item in entityDetails)
                {
                    if (duplicate.Contains(item.Index)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var detail = commandInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                        detail.Id = item.Id;
                        oldDetails.Add(detail);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeDetails.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceDetails = oldDetails;

                var oldTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var removeTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var newTaxbreakdowns = new List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>();
                var commandTaxBreakDowns = input.InvoiceTaxBreakdowns?.Select(x => new global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto
                {
                    TenantId = entityUpdateMongo.TenantId,
                    InvoiceHeaderId = entityUpdateMongo.Id,
                    VatAmount = x.VatAmount,
                    Amount = x.Amount,
                    //VatAmountBackUp = x.VatAmountBackUp,
                    VatPercent = x.VatPercent,
                    VatPercentDisplay = taxes[x.VatPercent].Item2,
                    Name = taxes[x.VatPercent].Item1,
                    Partition = partition
                })
               .ToList();

                var entityTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                //group để bỏ trường hợp Index thêm mới
                var duplicateTaxbreakdown = commandTaxBreakDowns.ToDictionary(x => x.VatPercent, x => x)
                                            .Select(x => x.Key)
                                            .Intersect(entityTaxbreakdowns.Select(x => x.VatPercent));
                var newIndexTaxbreakdown = commandTaxBreakDowns.Where(x => !duplicateTaxbreakdown.Contains(x.VatPercent)).ToList();

                foreach (var item in entityTaxbreakdowns)
                {
                    if (duplicateTaxbreakdown.Contains(item.VatPercent)) // sửa
                    {
                        //lấy dữ liệu hóa đơn ở api để cho vào detail này
                        var taxbreakdown = commandTaxBreakDowns.FirstOrDefault(x => x.VatPercent == item.VatPercent);
                        taxbreakdown.Id = item.Id;
                        oldTaxbreakdowns.Add(taxbreakdown);
                    }
                    else
                    {
                        //k phải thì bỏ qua, check tiếp vì bị xóa
                        removeTaxbreakdowns.Add(item);
                    }
                }

                if (newIndexTaxbreakdown.Any())
                {
                    var totalItemsTaxbreakdown = newIndexTaxbreakdown.Count;
                    var currentTaxbreakdownId = await GetSEQsNextVal(totalItemsTaxbreakdown, SequenceName.Invoice01TaxBreakdown);

                    //thêm mới
                    for (int i = 0; i < newIndexTaxbreakdown.Count; i++)
                    {
                        var item = newIndexTaxbreakdown[i];
                        item.Id = currentTaxbreakdownId[i];
                        newTaxbreakdowns.Add(item);
                    }
                }

                entityUpdateMongo.InvoiceTaxBreakdowns = new List<Invoice01TaxBreakdownDto>();
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(oldTaxbreakdowns);
                entityUpdateMongo.InvoiceTaxBreakdowns.AddRange(newTaxbreakdowns);

                // 
                var rootTaxBreakdowns = invoiceMongo.InvoiceTaxBreakdowns;
                // Tinh toan chi tiet thue suat
                entityUpdateMongo.InvoiceTaxBreakdowns = ReCaculateInvoiceUtil.GetTaxBreakDown(entityUpdateMongo);

                if (entityUpdateMongo.InvoiceTaxBreakdowns.Select(x => x.Id == 0).Any())
                {
                    // Tính toán lại
                    // Gen lại sequence
                    int indexTaxBreakDown = 0;
                    var reIdTaxBreakDowns = await GetSEQsNextVal(entityUpdateMongo.InvoiceTaxBreakdowns.Count, SequenceName.Invoice01TaxBreakdown);
                    foreach (var itemTaxBreakDown in entityUpdateMongo.InvoiceTaxBreakdowns)
                    {
                        var tax = taxes[itemTaxBreakDown.VatPercent];

                        itemTaxBreakDown.Id = reIdTaxBreakDowns[indexTaxBreakDown];
                        itemTaxBreakDown.Name = tax?.Item1;
                        itemTaxBreakDown.TenantId = CurrentTenant.Id.Value;
                        itemTaxBreakDown.Partition = partition;
                        itemTaxBreakDown.InvoiceHeaderId = entityUpdateMongo.Id;
                        itemTaxBreakDown.VatPercentDisplay = tax?.Item2;

                        indexTaxBreakDown++;
                    }

                    removeTaxbreakdowns = rootTaxBreakdowns.Select(x => new Invoice01TaxBreakdownDto()
                    {
                        Id = x.Id,
                    }).ToList();
                    oldTaxbreakdowns = new List<Invoice01TaxBreakdownDto>();
                    newTaxbreakdowns = entityUpdateMongo.InvoiceTaxBreakdowns;
                }

                await _mongoInvoice01Repository.UpdateAsync(entityUpdateMongo);



                var response = new UpdateAdjustmentDetailInvoice01ApiResponseModel();

                //request.Id = invoice.Id;
                //var invoicesMongo = await _mongoInvoice01Repository.GetByIdsAsync(new List<long> { request.Id });
                //await _updateAdjustDetailInvoice01Business.UpdateMongoAsync(request);

                try
                {
                    foreach (var item in request.InvoiceDetails)
                    {
                        if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                        {
                            var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                            item.InvoiceDetailExtras = detailExtras;
                        }
                    }

                    var responseInvoice = await _updateAdjustDetailInvoice01Business.UpdateOracleAsync(
                    new InvoiceCommandRequestModel
                    {
                        Resource = InvoiceSource.Api,
                        InvoiceId = invoice.Id,
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        TenantId = tenantId,
                        UserId = userId,
                        UserName = userName,
                        UserFullName = userFullName,
                        Date = request.InvoiceDate,
                        SerialNo = invoice.SerialNo,
                        TemplateNo = invoice.TemplateNo,
                        Action = InvoiceAction.UpdateAdjustmentDetail,
                        Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                        Type = VnisType._01GTKT,
                    });

                    response = new UpdateAdjustmentDetailInvoice01ApiResponseModel
                    {
                        Id = invoice.Id,
                        ErpId = invoice.ErpId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        InvoiceNo = responseInvoice.InvoiceNo,
                        InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                        SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                        TransactionId = invoice.TransactionId
                    };

                    // insert log
                    var invoice01LogEntity = new MongoInvoice01LogEntity
                    {
                        InvoiceHeaderId = invoice.Id,
                        TenantId = CurrentTenant.Id.Value,
                        UserId = CurrentUser.Id.Value,
                        UserName = CurrentUser.UserName,
                        Action = (short)ActionLogInvoice.Update,
                        CreationTime = DateTime.Now,
                        Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
                    };

                    await _vnisCoreMongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);
                }
                catch (Exception ex)
                {
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo);
                    throw new UserFriendlyException(ex.Message);
                }

                return new ResultApiViewModel<UpdateAdjustmentDetailInvoice01ApiResponseModel>(new ResultApiModel<UpdateAdjustmentDetailInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// API ký hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("sign-server/{erpId}")]
        public async Task<object> Sign([FromRoute] string erpId)
        {
            try
            {
                var request = new Invoice01ApiSignServerRequestModel
                {
                    ErpId = erpId
                };
                var validator = _validatorFactory.GetValidator<Invoice01ApiSignServerRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var result = new ResultApiViewModel<Invoice01ApiSignServerResponseModel>(new ResultApiModel<Invoice01ApiSignServerResponseModel>());
                var response = await _factory.Mediator.Send(request);

                result = new ResultApiViewModel<Invoice01ApiSignServerResponseModel>(new ResultApiModel<Invoice01ApiSignServerResponseModel>(response));

                Log.Information(@$"Result: {result.JsonSerialize()}");
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
            }
        }


        /// <summary>
        /// tạo biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("documents/{erpId}")]
        public async Task<object> CreateDocument([FromRoute] string erpId, [FromForm] CreateDocumentInfoInvoice01ApiRequestModel request)
        {
            try
            {
                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<CreateDocumentInfoInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<CreateDocumentInfoInvoice01ApiResponseModel>(new ResultApiModel<CreateDocumentInfoInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }


        /// <summary>
        /// update biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("documents/update/{erpId}")]
        public async Task<object> UpdateDocument([FromRoute] string erpId, [FromForm] UpdateDocumentInfoInvoice01ApiRequestModel request)
        {
            try
            {
                request.ErpId = erpId;

                var validator = _validatorFactory.GetValidator<UpdateDocumentInfoInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<UpdateDocumentInfoInvoice01ApiResponseModel>(new ResultApiModel<UpdateDocumentInfoInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// xóa biên bản cho hóa đơn thay thế/điều chỉnh/xóa bỏ
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [HttpPost("documents/delete/{erpId}")]
        public async Task<object> DeleteDocument([FromRoute] string erpId)
        {
            try
            {
                var request = new DeleteDocumentInfoInvoice01ApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<DeleteDocumentInfoInvoice01ApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<DeleteDocumentInfoInvoice01ApiResponseModel>(new ResultApiModel<DeleteDocumentInfoInvoice01ApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// In thể hiện
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("unofficial/{erpId}")]
        public async Task<object> DownloadUnOfficial([FromRoute] string erpId)
        {
            try
            {
                var request = new DownloadUnOfficialInvoice01RequestModel
                {
                    ErpId = erpId
                };

                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// In thể hiện
        /// </summary>
        /// <param name="erpIds"></param>
        /// <param name="printAction"></param>
        /// <returns></returns>
        [HttpPost("unofficial/invoice-or-notice/{printAction}")]
        public async Task<object> DownloadUnOfficial([FromRoute] string printAction, [FromBody] List<string> erpIds)
        {
            try
            {
                var result = await _factory.Mediator.Send(new DownloadUnOfficialInvoice01WithPrintActionRequestModel
                {
                    InvoiceTypeName = printAction,
                    ErpIds = erpIds
                });

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// In chuyển đổi
        /// </summary>
        /// <param name="erpId"></param>
        /// <returns></returns>
        [HttpPost("official/{erpId}")]
        public async Task<object> DownloadOfficial([FromRoute] string erpId)
        {
            try
            {
                var request = new DownloadOfficialInvoice01RequestModel
                {
                    ErpId = erpId
                };
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// download file zip bao gồm cả pdf và xml hóa đơn
        /// </summary>
        /// <param name="erpIds"></param>
        /// <returns></returns>
        [HttpPost("download")]
        public async Task<object> DownloadUnOfficialAndXmlAsync([FromBody] List<string> erpIds)
        {
            try
            {
                var request = new DownloadUnOfficialAndXmlInvoice01RequestModel
                {
                    ErpIds = erpIds
                };

                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<PrintApiModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// lấy mẫu số ký hiệu hóa đơn
        /// </summary>
        /// <returns></returns>
        #region old
        //[Produces("application/json")]
        //[HttpGet("info-template")]
        //public async Task<object> GetTemplateNoInvoiceNo()
        //{
        //    try
        //    {
        //        var request = new InvoiceTemplateInfoRequestModel();
        //        var response = await _factory.Mediator.Send(request);

        //        if (response.Count <= 0)
        //            throw new UserFriendlyException(L["Vnis.BE.Invoice.InvoiceDateRange.InvoiceTemplateNotfound"]);

        //        return new ResultApiViewModel<List<InvoiceTemplateInfoResponseModel>>(new ResultApiModel<List<InvoiceTemplateInfoResponseModel>>(response));
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, ex.Message);

        //        return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
        //    }
        //}
        #endregion
        [Produces("application/json")]
        [HttpGet("info-template")]
        public async Task<object> GetInfoTemplate(InfoTemplateRequest request)
        {
            try
            {
                DateTime? date = null;
                if (!request.InvoiceDate.IsNullOrEmpty())
                {
                    string strRegex = @"^([0-9]{4}-((0[13-9]|1[012])-(0[1-9]|[12][0-9]|30)|(0[13578]|1[02])-31|02-(0[1-9]|1[0-9]|2[0-8]))|([0-9]{2}(([2468][048]|[02468][48])|[13579][26])|([13579][26]|[02468][048]|0[0-9]|1[0-6])00)-02-29)$";

                    Regex re = new Regex(strRegex);
                    if (re.IsMatch(request.InvoiceDate))
                    {
                        date = DateTime.Parse(request.InvoiceDate);
                    }
                    else
                        return new ResultApiViewModel(false, -1, "Dữ liệu Ngày truyền vào không đúng định dạng YYYY-MM-DD");
                }
                var data = await _invoiceTemplateRepository.GetInfoTemplate(date, CurrentTenant.Id.Value, CurrentUser.Id.Value);

                if (data.Count <= 0)
                    throw new UserFriendlyException(L["Vnis.BE.Invoice.InvoiceDateRange.InvoiceTemplateNotfound"]);

                return new ResultApiViewModel<List<InvoiceTemplateInfoResponseModel>>(new ResultApiModel<List<InvoiceTemplateInfoResponseModel>>(data));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }
        /// <summary>
        /// gửi mail
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("send-mail")]
        public async Task<object> SendMail([FromBody] List<SendMailInvoice01ApiRequestModel> requests)
        {
            try
            {
                foreach (var request in requests)
                {
                    var validator = _validatorFactory.GetValidator<SendMailInvoice01ApiRequestModel>();

                    var validationResult = await validator.HandleAsync(request);
                    if (!validationResult.Success)
                        throw new UserFriendlyException(L[validationResult.Message]);

                    await _factory.Mediator.Send(request);
                }

                return new ResultApiViewModel(true, 0, "Thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// gửi mail với printAction
        /// </summary>
        /// <param name="printAction"></param>
        /// <param name="requests"></param>
        /// <returns></returns>
        [HttpPost("send-mail/{printAction}")]
        public async Task<object> SendMail([FromRoute] string printAction, [FromBody] List<SendMailInvoice01WithPrintActionApiRequestModel> requests)
        {
            try
            {
                foreach (var request in requests)
                {
                    var validator = _validatorFactory.GetValidator<SendMailInvoice01WithPrintActionApiRequestModel>();

                    request.PrintAction = printAction;
                    var validationResult = await validator.HandleAsync(request);
                    if (!validationResult.Success)
                        throw new UserFriendlyException(L[validationResult.Message]);

                    await _factory.Mediator.Send(request);
                }

                return new ResultApiViewModel(true, 0, "Thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// Tạo thông báo sai sót ver1
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        //[HttpPost("invoice-error/create")]
        //public async Task<CreateInvoice01TvanErrorResponseModel> CreateInvoiceTvanErrorAsync([FromBody] CreateInvoice01TvanErrorRequestModel request)
        //{
        //    return await _factory.Mediator.Send(request);
        //}

        /// <summary>
        /// Tạo thông báo sai sót ver2
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/create")]
        public async Task<object> CreateInvoiceTvanErrorAsync([FromBody] CreateInvoice01ErrorTvanRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<CreateInvoice01ErrorTvanResponseModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// Update thông báo sai sót
        /// </summary>
        /// <returns></returns>
        [HttpPost("invoice-error/update")]
        public async Task<object> UpdateInvoiceTvanErrorAsync([FromBody] UpdateInvoice01TvanErrorRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<UpdateInvoice01TvanErrorResponseModel>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// xóa thông báo sai sót
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/delete")]
        public async Task<object> DeleteInvoiceErrorAsync(DeleteInvoice01TvanErrorRequestModel request)
        {
            try
            {
                var result = await _factory.Mediator.Send(request);

                return new ResultApiViewModel<DeleteInvoice01ErrorTvanResponse>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// ký sai sót hóa đơn 01
        /// </summary>
        /// <param name="ErpId"></param>
        /// <returns></returns>
        [HttpPost("signserver/invoice01-error")]
        public async Task<object> SignInvoice01ErrorAsync([FromBody] List<string> ErpIds)
        {
            try
            {
                var result = await _factory.Mediator.Send(new SignServerInvoice01ErrorRequestModel
                {
                    ErpIds = ErpIds
                });

                return new ResultApiViewModel<List<SignServerInvoiceErrorResponseModel>>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// api đồng bộ hóa đơn ở oracle về mongo
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("tool/sync-oracle-to-mongo")]
        public async Task<object> SyncInvoiceOracleToMongoByIdAsync([FromBody] SyncInvoiceOracleToMongoRequestModel request)
        {
            var httpContextAccessor = _factory.GetServiceDependency<IHttpContextAccessor>();
            if (!httpContextAccessor.HttpContext.Request.Headers.TryGetValue(APIKEY, out var extractedApiKey))
                throw new UserFriendlyException("Không tìm thấy API KEY");

            var apiKey = _configuration[$"{APIKEY}"];
            if (apiKey.IsNullOrEmpty())
                throw new UserFriendlyException("Chưa có cấu hình API KEY");

            if (!apiKey.Equals(extractedApiKey))
                throw new UserFriendlyException("API KEY không đúng");

            try
            {
                var invoiceHeaders = await _repoInvoice01Header.QueryByIdsAsync(request.Ids);
                if (!invoiceHeaders.Any())
                    throw new Exception("Không tìm thấy hóa đơn");

                var idInvoiceHeaders = invoiceHeaders.Select(x => x.Id).ToList();
                var invoiceDetails = (await _repoInvoice01Detail.QueryByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                var invoiceTaxBreakdowns = (await _repoInvoiceTaxBreakdown.QueryByInvoiceHeaderIdsAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                var invoiceRefereces = (await _repoInvoice01Reference.GetByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());
                var invoiceRefereceOlds = (await _repoInvoice01ReferenceOldDecree.GetByIdInvoiceHeadersAsync(idInvoiceHeaders)).GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());

                try
                {
                    var mongoEntitys = _factory.ObjectMapper.Map<List<Invoice01HeaderEntity>, List<MongoInvoice01Entity>>(invoiceHeaders);

                    var mongoBatchIdEntitys = new List<MongoInvoice01BatchIdEntity>();

                    int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                    mongoEntitys.ForEach(x =>
                    {
                        x.TenantGroup = request.TenantGroup;
                        x.InvoiceGroup = 0;
                        x.TotalInvoices = 1;
                        x.IsGeneratedNumber = 1;
                        x.IsSyncedToCore = 1;
                        x.InvoiceDate = isEnableMongoDbLocalTime > 0 ? x.InvoiceDate.ToLocalTime() : x.InvoiceDate;

                        if (x.PaymentDate.HasValue)
                        {
                            x.PaymentDate = isEnableMongoDbLocalTime > 0 ? x.PaymentDate.Value.ToLocalTime() : x.PaymentDate;
                        }

                        if (x.IssuedTime.HasValue)
                        {
                            x.IssuedTime = isEnableMongoDbLocalTime > 0 ? x.IssuedTime.Value.ToLocalTime() : x.IssuedTime.Value;
                        }

                        x.CreationTime = isEnableMongoDbLocalTime > 0 ? x.CreationTime.ToLocalTime() : x.CreationTime;

                        if (x.InvoiceReference != null)
                        {
                            x.InvoiceReference.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? x.InvoiceReference.InvoiceDateReference.ToLocalTime() : x.InvoiceReference.InvoiceDateReference;
                        }

                        if (x.InvoiceReferenceOld != null)
                        {
                            x.InvoiceReferenceOld.InvoiceDateReference = isEnableMongoDbLocalTime > 0 ? x.InvoiceReferenceOld.InvoiceDateReference.ToLocalTime() : x.InvoiceReferenceOld.InvoiceDateReference;
                        }

                        x.InvoiceReference = invoiceRefereces.ContainsKey(x.Id)
                                                ? _factory.ObjectMapper.Map<Invoice01ReferenceEntity, Invoice01ReferenceDto>(invoiceRefereces[x.Id].FirstOrDefault())
                                                : null;

                        x.InvoiceReferenceOld = (invoiceRefereceOlds != null && invoiceRefereceOlds.ContainsKey(x.Id))
                                                ? _factory.ObjectMapper.Map<Invoice01ReferenceOldDecreeEntity, Invoice01ReferenceOldDto>(invoiceRefereceOlds[x.Id].FirstOrDefault())
                                                : null;

                        mongoBatchIdEntitys.Add(new MongoInvoice01BatchIdEntity
                        {
                            BatchId = x.BatchId,
                            TenantId = x.TenantId,
                            Group = request.TenantGroup,
                            InvoiceTemplateId = x.InvoiceTemplateId,
                            CreationTime = x.CreationTime,
                            TotalInvoices = 1,
                            CreatorStatus = 2,
                            TotalInvoicesWithNumber = 1,
                            GenerateNumberStatus = 2,
                            IsSyncedLicense = 1,
                        });
                    });

                    await _mongoInvoice01Repository.InsertManyAsync(mongoEntitys);
                    await _mongoInvoice01BatchIdRepository.InsertManyAsync(mongoBatchIdEntitys);

                    return request;
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        [AllowAnonymous]
        [HttpPost("tool/sync-verificationcode-to-es")]
        public async Task<object> SyncVerificationCodeToEsAsync(SyncVerificationCodeToEssModel syncVerificationCodeToEssModel)
        {
            var httpContextAccessor = _factory.GetServiceDependency<IHttpContextAccessor>();
            if (!httpContextAccessor.HttpContext.Request.Headers.TryGetValue(APIKEY, out var extractedApiKey))
                throw new UserFriendlyException("Không tìm thấy API KEY");

            var apiKey = _configuration[$"{APIKEY}"];
            if (apiKey.IsNullOrEmpty())
                throw new UserFriendlyException("Chưa có cấu hình API KEY");

            if (!apiKey.Equals(extractedApiKey))
                throw new UserFriendlyException("API KEY không đúng");

            try
            {
                await _syncVerificationCodeToEsBusiness.SyncVerificationCodeToEsAsync(syncVerificationCodeToEssModel);
                return new
                {
                    Message = "Thành công",
                    Code = 200
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(ex.Message);
            }
        }


        /// <summary>
        /// đồng bộ lại trạng thái ký của hóa đơn lên ES
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("tool/resync-signstatus-to-es")]
        public async Task<object> ResyncSignStatusToEsAsync([FromBody] ResyncSignStatusToESModel resyncSignStatusToESModel)
        {
            var httpContextAccessor = _factory.GetServiceDependency<IHttpContextAccessor>();
            if (!httpContextAccessor.HttpContext.Request.Headers.TryGetValue("XApiKey", out var extractedApiKey))
                throw new UserFriendlyException("Không tìm thấy API KEY");

            var apiKey = _configuration[$"XApiKey"];
            if (apiKey.IsNullOrEmpty())
                throw new UserFriendlyException("Chưa có cấu hình API KEY");

            if (!apiKey.Equals(extractedApiKey))
                throw new UserFriendlyException("API KEY không đúng");

            try
            {
                await _resyncSignStatusToESBusiness.ResyncSignStatusToEsAsync(resyncSignStatusToESModel);
                return new
                {
                    Message = "Thành công",
                    Code = 200
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(ex.Message);
            }

        }

        /// <summary>
        /// API này chỉ dành cho fake cấp mã hóa đơn ở môi trường TEST
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("fake-verfication-code")]
        public async Task<object> FakeVerificationCodeAsync(List<long> ids)
        {
            if (!ids.Any())
                throw new UserFriendlyException("Danh sách ids không được để trống");

            var invoicesMongo = await _mongoInvoice01Repository.GetByIdsAsync(ids);
            if (!invoicesMongo.Any())
                throw new UserFriendlyException("Không tìm thấy hóa đơn ở MongoDB");

            await _mongoInvoice01Repository.FakeVerificationCodeAsync(invoicesMongo.Select(x => x.Id).ToList());

            return new
            {
                Message = "Thành công",
                Code = 200
            };
        }


        //private async Task<long> GetSeQsLastNumber(string sequenceName)
        //{
        //    var sql = $@"SELECT last_number FROM user_sequences WHERE sequence_name = '{sequenceName}'";

        //    var result = await _factory.VnisCoreOracle.Connection.QueryFirstAsync<long>(sql);

        //    return result;
        //}

        private async Task GetSeQsNextVal(int increment, string sequenceName, int numberRecordsInMessage)
        {
            //TODO: cần tối ưu viết sang store
            await _factory.VnisCoreOracle.Connection.ExecuteAsync($"ALTER SEQUENCE \"{sequenceName}\" INCREMENT BY {increment + numberRecordsInMessage}");
            await _factory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync($"SELECT \"{sequenceName}\".NEXTVAL FROM dual");
            await _factory.VnisCoreOracle.Connection.ExecuteAsync($"ALTER SEQUENCE \"{sequenceName}\" INCREMENT BY 1");
            await _factory.CurrentUnitOfWork.SaveChangesAsync();
        }

        //TODO: dua vao shared
        private async Task<List<long>> GetSEQsNextVal(int level, string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _factory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }

        /// <summary>
        /// Update trạng thái hóa đơn gốc
        /// </summary>
        /// <returns></returns>
        private async Task InvoiceReSyncToMongo(List<long> ids, InvoiceStatus invoiceStatus)
        {
            await _mongoInvoice01Repository.UpdateManyInvoiceStatusAfterAdjustActionAsync(ids, (short)invoiceStatus, 2);
        }
        private async Task InvoiceReSyncToEs(List<long> ids, SyncElasticSearchStatus syncElasticSearchStatus)
        {
            var invoice01ReSyncs = await _mongoInvoice01ReSyncRepository.GetByIdsAsync(ids);

            var invoice01ReSyncInsert = new List<MongoInvoice01ReSyncEntity>();

            if (invoice01ReSyncs.Any())
            {
                invoice01ReSyncs.ForEach(item => { item.IsSyncedToElasticSearch = (short)syncElasticSearchStatus; });

                invoice01ReSyncInsert.AddRange(
                    from item in ids
                    let invoice01ReSync = invoice01ReSyncs.FirstOrDefault(x => x.Id == item)
                    where invoice01ReSync == null
                    select new MongoInvoice01ReSyncEntity { Id = item, IsSyncedToElasticSearch = (short)syncElasticSearchStatus }
                );

                await _mongoInvoice01ReSyncRepository.UpdateManyAsync(invoice01ReSyncs);

                if (invoice01ReSyncInsert.Any())
                    await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
            }
            else
            {
                invoice01ReSyncInsert.AddRange(ids.Select(item => new MongoInvoice01ReSyncEntity
                { Id = item, IsSyncedToElasticSearch = (short)syncElasticSearchStatus }));

                if (invoice01ReSyncInsert.Any())
                    await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
            }
        }
    }
}
