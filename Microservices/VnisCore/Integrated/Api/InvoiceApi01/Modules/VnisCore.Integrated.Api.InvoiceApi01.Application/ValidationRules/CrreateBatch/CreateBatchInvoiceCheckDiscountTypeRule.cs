using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateBatch
{
    public class CreateBatchInvoiceCheckDiscountTypeRule : IValidationRuleAsync<CreateBatchInvoice01RequestModel, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly ISettingService _settingService;

        public CreateBatchInvoiceCheckDiscountTypeRule(ISettingService settingService,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _settingService = settingService;
        }

        public async Task<ValidationResult> HandleAsync(CreateBatchInvoice01RequestModel input)
        {
            // <PERSON>ểm tra loại chiết khấu
            foreach (var invoice in input.Datas)
            {
                var validDiscountTypes = new int[]
                {
                DiscountType.KhongChietKhau.GetHashCode(),
                DiscountType.ChietKhauHangHoa.GetHashCode(),
                DiscountType.TongChietKhau.GetHashCode()
                };

                if (!validDiscountTypes.Contains(invoice.Invoice.DiscountType))
                {
                    return new ValidationResult(false, "Loại chiết khấu không đúng định dạng. Vui lòng kiểm tra lại");
                }

                if (invoice.Invoice.DiscountType == DiscountType.KhongChietKhau.GetHashCode() && invoice.Invoice.TotalDiscountAmountBeforeTax != 0 && !invoice.Invoice.InvoiceDetails.IsNullOrEmpty())
                {
                    // Tất cả sản phẩm đều là Không có chiết khấu
                    if (invoice.Invoice.InvoiceDetails.Where(d => d.ProductType != ProductType.ChietKhauThuongMai.GetHashCode() && d.DiscountAmountBeforeTax == 0).Count() == invoice.Invoice.InvoiceDetails.Count)
                    {
                        var group = invoice.Invoice.InvoiceDetails.GroupBy(x => x.VatPercent);
                        if (group.Count() > 1)
                        {
                            return new ValidationResult(false, "Hóa đơn thuộc loại Chiết khấu tổng: Các sản phẩm phải cùng phần trăm thuế suất. Vui lòng kiểm tra lại");
                        }
                    }
                }

                if (invoice.Invoice.DiscountType == DiscountType.TongChietKhau.GetHashCode() && invoice.Invoice.InvoiceDetails != null)
                {
                    var group = invoice.Invoice.InvoiceDetails.GroupBy(x => x.VatPercent);
                    if (group.Count() > 1)
                    {
                        return new ValidationResult(false, "Hóa đơn thuộc loại Chiết khấu tổng: Các sản phẩm phải cùng phần trăm thuế suất. Vui lòng kiểm tra lại");
                    }

                    if (invoice.Invoice.InvoiceDetails.Where(x => x.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any())
                    {
                        return new ValidationResult(false, "Hóa đơn thuộc loại Chiết khấu tổng: Các sản phẩm không được thuộc loại Chiêt khấu thương mại. Vui lòng kiểm tra lại");
                    }

                    if (invoice.Invoice.InvoiceDetails.Where(x => x.DiscountPercentBeforeTax != 0 || x.DiscountAmountBeforeTax != 0).Any())
                    {
                        return new ValidationResult(false, "Hóa đơn thuộc loại Chiết khấu tổng: Các sản phẩm không được có tiền chiết khấu. Vui lòng kiểm tra lại");
                    }

                }
            }

            return new ValidationResult(true);
        }
    }
}
