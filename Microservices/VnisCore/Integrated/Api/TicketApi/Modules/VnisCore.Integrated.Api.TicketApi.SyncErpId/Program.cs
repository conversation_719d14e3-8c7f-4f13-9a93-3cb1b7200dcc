// using Core.Host.Shared;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

using Serilog;

using System;

namespace VnisCore.Integrated.Api.TicketApi.SyncErpId
{
    public class Program
    {
        public static int Main(string[] args)
        {
            // ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting console host.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }

        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
                Host.CreateDefaultBuilder(args)
                .ConfigureServices((hostContext, services) =>
                {
                    ConfigureServices(services, hostContext.Configuration);
                    services.AddHostedService<TicketErpIdHostedService>();
                })
                .UseAutofac()
                .UseSerilog();

        private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddApplication<TicketErpIdModule>();

            return services.BuildServiceProvider();
        }
    }
}
