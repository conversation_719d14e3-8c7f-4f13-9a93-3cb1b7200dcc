using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;
using Core.Shared.RabbitMqConstants;
using Core.Shared.Validations;

using MediatR;

using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using RabbitMQ.Client;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Integrated.Api.TicketApi.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Events;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient;

namespace VnisCore.Integrated.Api.TicketApi.Application.Handlers.Commands
{
    public class UpdateTicketApiCommandHandler : IRequestHandler<UpdateTicketApiRequestModel, UpdateTicketApiResponseModel>
    {
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly ITicketCommandHandler _ticketHandler;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateTicketApiCommandHandler(
            IAppFactory appFactory,
            IValidationContext validationContext,
            ITicketCommandHandler ticketCommandHandler,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _ticketHandler = ticketCommandHandler;
            _appFactory = appFactory;
        }

        public async Task<UpdateTicketApiResponseModel> Handle(UpdateTicketApiRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var userId = _validationContext.GetItem<Guid>("UserId");
            var userName = _validationContext.GetItem<string>("UserName");
            var userFullName = _validationContext.GetItem<string>("UserFullName");

            var invoice = _validationContext.GetItem<TicketHeaderEntity>("Invoice");

            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var ticketHeaderExtraRequestModels = request.InvoiceHeaderExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                request.InvoiceHeaderExtras = ticketHeaderExtraRequestModels;
            }

            foreach (var item in request.InvoiceDetails)
            {
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    item.InvoiceDetailExtras = detailExtras;
                }
            }

            //HandleTicket
            var responseInvoice = await _ticketHandler.PublishAsync(
                new InvoiceCommandRequestModel
                {
                    InvoiceId = invoice.Id,
                    Action = InvoiceAction.UpdateRoot,
                    Resource = InvoiceSource.Api,
                    TenantId = tenantId,
                    UserId = userId,
                    UserName = userName,
                    UserFullName = userFullName,
                    Date = request.InvoiceDate,
                    SerialNo = request.SerialNo,
                    TemplateNo = request.TemplateNo,
                    Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                    Type = VnisType._05TVDT
                },
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Commands, string.Format(RabbitMqKey.Routings.UpdateRootInvoice, "05")),
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Rpc, string.Format(RabbitMqKey.Routings.GeneratedInvoice, "05")));

            if (!responseInvoice.Succeeded)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Update.UpdateInvoiceFail", new string[] { responseInvoice.Exception?.Message }]);

            var response = new UpdateTicketApiResponseModel
            {
                Id = responseInvoice.Data.Id,
                ErpId = invoice.ErpId,
                TransactionId = invoice.TransactionId,
                TemplateNo = invoice.TemplateNo,
                SerialNo = invoice.SerialNo,
                InvoiceNo = responseInvoice.Data.InvoiceNo,
                InvoiceStatus = responseInvoice.Data.InvoiceStatus,
                SignStatus = responseInvoice.Data.SignStatus
            };

            await _appFactory.Mediator.Publish(new AfterUpdatedTicketApiEventModel
            {
                Data = new DataAfterUpdatedTicketApiModel
                {
                    Id = invoice.Id,
                    TenantId = tenantId,
                    Type = VnisType._05TVDT,
                    UserId = userId,
                    UserFullName = userFullName,
                    InvoiceDate = request.InvoiceDate,
                    SerialNo = request.SerialNo,
                    TemplateNo = request.TemplateNo,
                    UserName = userFullName,
                }
            }, cancellationToken);

            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            //sync Customer
            await distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._05TVDT,
            }));

            //sync Unit
            await distributedEventBus.PublishAsync(new SyncUnitEventSendData(new SyncUnitRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._05TVDT,
            }));

            return response;
        }
    }
}
