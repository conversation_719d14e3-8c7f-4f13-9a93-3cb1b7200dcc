using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Messages;
using Core.Shared.RabbitMqConstants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Services;

namespace VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Workers
{
    public class TicketWorker : RabbitBackgroundService<InvoiceCommandRequestModel, RabbitResponseModel<InvoiceCommandResponseModel>>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;

        public TicketWorker(
            IServiceProvider serviceProvider,
            IConfiguration configuration,
            IRabbitBusClient rabbitBusClient) : base(rabbitBusClient)
        {
            _serviceProvider = serviceProvider;
            _configuration = configuration;

            InitQueue(RabbitMqKey.Queues.TicketHandleRequests);
            InitBinding(RabbitKey.Exchanges.Commands, new List<string>
            {
                string.Format(RabbitMqKey.Routings.CreateRootInvoice, "05"),
                string.Format(RabbitMqKey.Routings.DeleteInvoice, "05"),
                string.Format(RabbitMqKey.Routings.UpdateRootInvoice, "05"),

            });

        }

        public override int GetNumberOfconsumer()
        {
            var config = _configuration.GetSection("RabbitMQ:Connections:Default:Workers:GenerateTicket:NumberOfConsumer").Value;
            return int.Parse(config);
        }

        public override async Task HandleAsync(BasicDeliverEventArgs ea, InvoiceCommandRequestModel request)
        {
            Log.Debug($"TicketWorker - TenantCode: {request.TenantId} | Type: {request.Type} | TemplateNo: {request.TemplateNo} | SerialNo: {request.SerialNo} | Date: {request.Date}");

            RabbitResponseModel<InvoiceCommandResponseModel> response = null;
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var factory = scope.ServiceProvider.GetService<IInvoiceFactory>();
                var service = factory.GetHandler(request.Action, request.Type);

                response = new RabbitResponseModel<InvoiceCommandResponseModel>
                {
                    Data = await service.HandleRequestAsync(request),
                    Succeeded = true,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                response = new RabbitResponseModel<InvoiceCommandResponseModel>
                {
                    Exception = ex,
                    Succeeded = false,
                    Data = null
                };
            }
            finally
            {
                try
                {
                    var properties = Channel.CreateBasicProperties();
                    properties.Persistent = true;

                    //Nếu message từ API
                    if (request.Resource == InvoiceSource.Api)
                    {
                        properties.CorrelationId = ea.BasicProperties.CorrelationId;

                        Publish(
                            publicationAddress: ea.BasicProperties.ReplyToAddress,
                            properties: properties,
                            message: response
                        );
                    }
                    //Nếu message từ FORM/EXCEL
                    else
                    {

                    }

                    //if (response.Exception != null)
                    //{
                    //    response.Data = new InvoiceCommandResponseModel
                    //    {
                    //        InvoiceNo = request.Data.InvoiceNo,
                    //        TemplateNo = request.Data.TemplateNo,
                    //        SerialNo = request.Data.SerialNo,
                    //        Method = HubMethod.Exception,
                    //        Resource = request.Data.Resource,
                    //        TenantId = request.Data.TenantId,
                    //        Type = request.Data.Type,
                    //        UserId = request.Data.UserId,
                    //        ConnectionId = request.Data.ConnectionId,
                    //        State = request.Data.Action == InvoiceAction.ImportExcel ? EnumExtension.ToEnum<InvoiceActionState>($"Error{InvoiceAction.CreateRoot}") : EnumExtension.ToEnum<InvoiceActionState>($"Error{request.Data.Action}")
                    //    };

                    //    if (request.Data.InvoiceId.HasValue)
                    //        response.Data.Id = request.Data.InvoiceId.Value;
                    //    else
                    //    {
                    //        var requestData = JsonConvert.DeserializeObject<object>(CompressionExtensions.Unzip(request.Data.Data));
                    //        var prop = JObject.Parse(requestData.ToString()).Properties().FirstOrDefault(x => x.Name == "Id");
                    //        if (prop != null && long.TryParse(prop.Value.ToString(), out long invoiceCode))
                    //            response.Data.Id = invoiceCode;
                    //    }

                    //    Publish(
                    //        publicationAddress: new PublicationAddress(
                    //                        ExchangeType.Topic,
                    //                        RabbitKey.Exchanges.Events,
                    //                        string.Format(RabbitMqKey.Routings.ExceptionInvoice, request.Data.Type.GetHashCode().ToString("00"))),
                    //        properties: properties,
                    //        message: response
                    //    );
                    //}

                    BasicAck(ea);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }
            }
        }
    }
}
