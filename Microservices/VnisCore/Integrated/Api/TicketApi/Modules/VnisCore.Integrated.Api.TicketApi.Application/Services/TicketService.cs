using Core;
using Core.Application.Dtos;
using Core.Data;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.TenantManagement;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls.TCTResponse;
using Core.Uow;
using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.TicketApi.Application.Dto;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Queries;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;
using static Core.Tvan.Models.Xmls.TCTResponse.DLieuInvoiceErrorResponseModel;
using static VnisCore.Integrated.Api.TicketApi.Application.Constants.StaticData;

namespace VnisCore.Integrated.Api.TicketApi.Application.Services
{
    public class TicketService : ITicketService
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public TicketService(IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService)
        {
            _localizier = localizier;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _serviceProvider = serviceProvider;
        }

        public async Task<PagedResultDto<PagingTicketResponseModel>> GetListAsync(Guid tenantId, Guid userId, PagingTicketRequestModel input)
        {
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            //if (input.IsNullInvoice)
            //    condition.Append($@"AND ""Number"" IS NULL ");
            //else
            //    condition.Append($@"AND ""Number"" IS NOT NULL ");

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (""UserNameCreator"" LIKE N'%{input.Keyword}%' 
                                    OR ""BuyerEmail"" LIKE '%{input.Keyword}%' 
                                    OR ""BuyerFullName"" LIKE N'%{input.Keyword}%' 
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR ""PaymentMethod"" LIKE N'%{input.Keyword}%' 
                                    OR ""BuyerPhoneNumber"" LIKE '%{input.Keyword}%' 
                                    OR ""SerialNo"" LIKE '%{input.Keyword}%' 
                                    OR ""ErpId"" LIKE '%{input.Keyword}%' 
                                    OR ""TransactionId"" LIKE '%{input.Keyword}%' 
                                    OR ""CreatorErp"" LIKE N'%{input.Keyword}%' 
                                    OR ""BuyerCode"" LIKE N'%{input.Keyword}%' 
                                    ) ");

                //$"OR TemplateNo LIKE '%{input.Keyword}%' 
                //$"OR InvoiceStatus LIKE '%{input.Keyword}%' 
                //$"OR SignStatus LIKE '%{input.Keyword}%' 

            }

            //if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
            //    condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            //if (input.IssuedTime.HasValue)
            //{
            //    var issueTimeDate = input.IssuedTime.Value.Date;
            //    var issueTimePreviousDate = issueTimeDate.AddDays(-1);
            //    var issueTimeDateAddDate = issueTimeDate.AddDays(1);

            //    condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
            //                        AND ""IssuedTime"" < '{issueTimeDateAddDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                        AND ""IssuedTime"" > '{issueTimePreviousDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                        ");
            //}

            //if (input.IssueFromDate.HasValue)
            //{
            //    var issueFromDate = input.IssueFromDate.Value.Date;
            //    condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
            //                        AND ""IssuedTime"" >= '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            //}

            //if (input.IssueToDate.HasValue)
            //{
            //    var issueToDate = input.IssueToDate.Value.Date.AddDays(1);
            //    condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
            //                        AND ""IssuedTime"" < '{issueToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            //}

            //if (input.CancelFromDate.HasValue)
            //    condition.Append($@"AND 
            //                        (
            //                            (
            //                                ""CancelTime"" IS NOT NULL 
            //                                AND ""CancelTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                                AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
            //                            )
            //                            OR 
            //                            (
            //                                ""DeleteTime"" IS NOT NULL 
            //                                AND ""DeleteTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                                AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
            //                            )
            //                        )");

            //if (input.CancelToDate.HasValue)
            //{
            //    condition.Append($@"AND 
            //                        (
            //                            (
            //                                ""CancelTime"" IS NOT NULL 
            //                                AND ""CancelTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                                AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
            //                            )
            //                            OR 
            //                            (
            //                                ""DeleteTime"" IS NOT NULL 
            //                                AND ""DeleteTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                                AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
            //                            )
            //                        )");
            //}

            //if (input.IssuedAt.HasValue)
            //{
            //    var issuedAt = input.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt);
            //}

            //if (!string.IsNullOrEmpty(input.Customers))
            //{
            //    condition.Append($@"AND (""BuyerCode"" LIKE '%{input.Customers}%' 
            //                            OR ""BuyerLegalName"" LIKE N'%{input.Customers}%' 
            //                            OR ""BuyerTaxCode"" LIKE '%{input.Customers}%' 
            //                            OR ""BuyerEmail"" LIKE '%{input.Customers}%' 
            //                        ) ");
            //}

            //if (!string.IsNullOrEmpty(input.Customer))
            //{
            //    condition.Append($@"AND (""BuyerFullName"" LIKE N'%{input.Customers}%' 
            //                            OR ""BuyerPhoneNumber"" LIKE '%{input.Customers}%' 
            //                            OR ""BuyerEmail"" LIKE '%{input.Customers}%' 
            //                        ) ");
            //}

            //if (!string.IsNullOrEmpty(input.BuyerTaxCode))
            //{
            //    condition.Append($@" AND ""BuyerTaxCode"" LIKE '%{input.BuyerTaxCode}%' ");
            //}

            //if (!string.IsNullOrEmpty(input.BuyerFullName))
            //{
            //    condition.Append($@" AND ""BuyerFullName"" LIKE N'%{input.BuyerFullName}%' ");
            //}

            //// Tìm kiếm theo InvoiceTemplateIds
            //if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
            //    condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            //else
            //{
            //    //lấy danh sách mẫu dc xem
            //    condition.Append(@$" AND ""InvoiceTemplateId"" in 
            //                        (
            //                            SELECT  T.""Id"" 
            //                            FROM ""InvoiceTemplate"" T
            //                            INNER JOIN(
            //                                SELECT ""InvoiceTemplateId""
            //                                FROM ""UserReadTemplate""
            //                                WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
            //                            )
            //                            A ON T.""Id"" = A.""InvoiceTemplateId""
            //                            WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
            //                            AND T.""TemplateNo"" = {1} 
            //                        ) ");
            //}

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            //if (input.TotalPaymentAmount.HasValue)
            //{
            //    condition.Append($@"AND ""TotalPaymentAmount"" = {input.TotalPaymentAmount.Value} ");
            //}

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (!string.IsNullOrEmpty(input.TransactionId))
                condition.Append($@"AND ""TransactionId"" = '{input.TransactionId}' ");

            if (!string.IsNullOrEmpty(input.ErpId))
                condition.Append($@"AND ""ErpId"" = '{input.ErpId}' ");

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            //if (input.PrintedTime.HasValue)
            //{
            //    condition.Append($@"AND ""PrintedTime"" IS NOT NULL 
            //                              AND ""PrintedTime"" >= '{input.PrintedTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
            //                              AND ""PrintedTime"" < DATE '{input.PrintedTime.Value.ToString("yyyyy-MM-dd", CultureInfo.GetCultureInfo("en-US"))}' + 1");
            //}

            //if (!string.IsNullOrEmpty(input.UserNamePrinted))
            //    condition.Append($@"AND ""UserNamePrinted"" LIKE '%{input.UserNamePrinted}%' ");

            //if (!string.IsNullOrEmpty(input.BuyerBankAccount))
            //    condition.Append($@"AND ""BuyerBankAccount"" LIKE '%{input.BuyerBankAccount}%' ");

            //if (!string.IsNullOrEmpty(input.BuyerCode))
            //    condition.Append($@"AND ""BuyerCode"" LIKE '%{input.BuyerCode}%' ");

            //if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Any())
            //{
            //    condition.Append("AND (");

            //    int index = 0;
            //    int numberOfHeaderExtra = input.InvoiceHeaderExtras.Count;

            //    foreach (var item in input.InvoiceHeaderExtras)
            //    {
            //        if (!item.FieldValue.IsNullOrEmpty())
            //        {
            //            if ((numberOfHeaderExtra - index) == 1)
            //            {
            //                condition.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
            //            }
            //            else
            //            {
            //                condition.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
            //            }
            //        }

            //        index++;
            //    }

            //    condition.Append(")");
            //}

            // tìm kiếm theo detailExtra
            var joinDetailExtra = new StringBuilder();
            var conditionDetailExtra = new StringBuilder();
            //if (input.InvoiceDetailExtras != null && input.InvoiceDetailExtras.Any())
            //{
            //    conditionDetailExtra.Append("AND (");

            //    int index = 0;
            //    int numberOfDetailExtra = input.InvoiceDetailExtras.Count;

            //    foreach (var item in input.InvoiceDetailExtras)
            //    {
            //        if (!item.FieldValue.IsNullOrEmpty())
            //        {
            //            if ((numberOfDetailExtra - index) == 1)
            //            {
            //                conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
            //            }
            //            else
            //            {
            //                conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
            //            }
            //        }

            //        index++;
            //    }

            //    conditionDetailExtra.Append(")");

            //    joinDetailExtra.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""TicketDetail"" WHERE ""TenantId"" = '{rawTenantId}' {conditionDetailExtra}) B ON B.""InvoiceHeaderId"" = A.""Id""");
            //}


            var sql = new StringBuilder();

            //  "FileDocumentId",   
            sql.Append($@"SELECT *  FROM
                            (SELECT * FROM (
                                SELECT A.*, rownum rn                                                          
                                FROM                                                                           
                                    (                                                                          
                                        SELECT  ""Id"",                                                         
                                                ""ErpId"",                                                       
                                                ""TransactionId"",                                             
                                                ""TemplateNo"",                                                
                                                ""SerialNo"",                                                  
                                                ""InvoiceNo"",                                                 
                                                ""Number"",                                                    
                                                ""InvoiceStatus"",                                             
                                                ""SignStatus"",                                                
                                                ""ApproveStatus"",                                             
                                                ""ApproveCancelStatus"",                                             
                                                ""ApproveDeleteStatus"",                                             
                                                ""InvoiceDate"",                                               
                                                ""TotalAmount"",                                               
                                                ""TotalVatAmount"",                                            
                                                ""TotalPaymentAmount"",                                        
                                                ""BuyerFullName"",                                             
                                                ""BuyerEmail"",                                                
                                                ""BuyerCode"",                                                 
                                                ""PrintedTime"",                                               
                                                ""FullNameCreator"",                                           
                                                ""UserNameCreator"",                                           
                                                ""CreatorErp"",                                                
                                                ""Note"",                                                      
                                                ""Source"",                                                    
                                                ""IsViewed"",                                                  
                                                ""IsOpened"",                                                  
                                                ""IsDeclared"",                                                  
                                                ""VerificationCode"",                                                  
                                                ""ExtraProperties"",                                                  
                                                COUNT(*) OVER () TotalItems                                   
                                        FROM ""TicketHeader""                                               
                                        WHERE ""TenantId"" = '{rawTenantId}' {condition}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A                                                                        
                                WHERE rownum <= {input.SkipCount + input.MaxResultCount} 
                                )
                            WHERE rn > {input.SkipCount}
                        ) 
                        InvoiceHeader   
                        -- sai sót
                        LEFT JOIN 
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceErrorReason, ""InvoiceHeaderId"", ""Status"" as TvanInfoInvoiceErrorStatus, ""Action"" as InvoiceErrorType
                            FROM ""TvanInfoTicketError""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._301.GetHashCode()}
                        ) TvanInfoInvoiceError
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceError.""InvoiceHeaderId""
                        --có mã(lỗi 01 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceHasCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoTicketHasCode""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoInvoiceHasCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceHasCode.""InvoiceHeaderId""
                        -- Rà soát
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoCheckInvoiceReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoCheckTicket""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._302.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoCheckInvoice
                        ON InvoiceHeader.""Id"" = TvanInfoCheckInvoice.""InvoiceHeaderId""
                        --Không mã(lỗi 01 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceWithoutCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoTicketWithoutCode""
                            WHERE ""TenantId"" = '{rawTenantId}'  AND ""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} AND ""Reason"" IS NOT NULL
                        ) TvanInfoInvoiceWithoutCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceWithoutCode.""InvoiceHeaderId""  ");

            var query = sql.ToString();

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingTicketResponseModel>(query);

            var result = new PagedResultDto<PagingTicketResponseModel>();
            if (data != null && data.Any())
            {
                var items = data.OrderByDescending(x => x.InvoiceNo).ThenByDescending(x => x.SerialNo).ToList();
                items.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingTicketResponseModel.PagingTicketHeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }

                    x.GDTResponseStatus = 0;

                    if (x.SerialNo.StartsWith('C'))
                    {
                        x.GDTResponseStatus = x.TvanInfoInvoiceErrorStatus;
                    }
                    else if (!string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) || !string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason))
                    {
                        x.GDTResponseStatus = -1;
                    }

                    var errorsMess = new string[] { @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceErrorReason) ? "" : $"Lý do sai sót: {string.Join("; ", JsonConvert.DeserializeObject<List<LDoHDonModel>>(x.TvanInfoInvoiceErrorReason)?.Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn có mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceHasCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceWithoutCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn không mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceWithoutCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason) ? "" : $"Thông báo về hóa đơn cần rà soát: {x.TvanInfoCheckInvoiceReason}")}" };

                    x.InvoiceErrorMessage = string.Join("\n", errorsMess.Where(x => !string.IsNullOrEmpty(x)));
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = items;
            }

            return result;
        }

        public async Task<TicketHeaderEntity> CreateAsync(CreateTicketRequestModel request, InvoiceSource invoiceSource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceStatus invoiceStatus, SignStatus signStatus, ApproveStatus approveStatus, Tenant tenant, Dictionary<TenantMetaData, MetadataModel> tenantInfoMetadata, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);
            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = tenantId,
                CreationTime = DateTime.Now,
                CreatorId = userId,

                Source = (short)invoiceSource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = _invoiceService.GetTransactionId(invoiceSource, request.TransactionId),
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,
                InvoiceStatus = (short)invoiceStatus.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = tenantId,
                SellerTaxCode = tenant.TaxCode,
                SellerAddressLine = tenant.Address,
                SellerCountryCode = tenant.Country,
                SellerDistrictName = tenant.District,
                SellerCityName = tenant.City,
                SellerPhoneNumber = tenantInfoMetadata.ContainsKey(TenantMetaData.Phone) ? tenantInfoMetadata[TenantMetaData.Phone].Value : null,
                SellerFaxNumber = tenantInfoMetadata.ContainsKey(TenantMetaData.Fax) ? tenantInfoMetadata[TenantMetaData.Fax].Value : null,
                SellerEmail = tenantInfoMetadata.ContainsKey(TenantMetaData.Email) ? tenantInfoMetadata[TenantMetaData.Email].Value : null,
                SellerBankName = tenantInfoMetadata.ContainsKey(TenantMetaData.BankName) ? tenantInfoMetadata[TenantMetaData.BankName].Value : null,
                SellerBankAccount = tenantInfoMetadata.ContainsKey(TenantMetaData.BankAccount) ? tenantInfoMetadata[TenantMetaData.BankAccount].Value : null,
                SellerLegalName = tenantInfoMetadata.ContainsKey(TenantMetaData.LegalName) ? tenantInfoMetadata[TenantMetaData.LegalName].Value : null,
                SellerFullName = tenant.FullNameVi,

                RoundingCurrency = toCurrency.Rounding,
                FromCurrency = fromCurrency.CurrencyCode,
                CurrencyConversion = toCurrency.Conversion,
                ToCurrency = toCurrency.CurrencyCode,
                ExchangeRate = request.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc),
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = fullNameCreator,
                UserNameCreator = userNameCreator,

                BuyerCode = request.BuyerCode?.Trim(),
                BuyerId = null,
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail?.Trim(),
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,

                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            };

            var repoHeader = _serviceProvider.GetService<IRepository<TicketHeaderEntity, long>>();
            await repoHeader.InsertAsync(header);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!headerFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = headerFields[item.FieldName];
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        var repoHeaderExtra = _serviceProvider.GetService<IRepository<TicketHeaderExtraEntity, long>>();
            //        await repoHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}

            //Details
            var details = new List<TicketDetailEntity>();
            foreach (var item in request.InvoiceDetails)
            {
                var unit = units.ContainsKey(item.UnitName?.Trim()) ? units[item.UnitName?.Trim()] : null;
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                details.Add(new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = tenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    UnitName = item.UnitName?.Trim(),
                    ProductId = product == null ? 0 : product.Id,
                    ProductCode = item.ProductCode?.Trim(),
                    UnitId = unit == null ? 0 : unit.Id,
                    RoundingUnit = unit == null ? 4 : unit.Rounding,
                    UnitPrice = item.UnitPrice,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
                });
            }

            var repoDetail = _serviceProvider.GetService<IRepository<TicketDetailEntity, long>>();
            await repoDetail.InsertManyAsync(details);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Detail Extras
            //Sau khi SaveChanges, các Id của bảng InvoiceDetail sẽ có dữ liệu
            //Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
            var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    var items = indexes[detail.Index];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        //Nếu ko có DetailField thì bỏ qua, ko insert
            //        if (!detailFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = detailFields[item.FieldName];
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            TenantId = tenantId,
            //            InvoiceDetailId = detail.Id,
            //            InvoiceDetailFieldId = field.Id,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            //        });
            //    }
            //}

            //if (detailExtras.Count > 0)
            //{
            //    var repoDetailExtra = _serviceProvider.GetService<IRepository<TicketDetailExtraEntity, long>>();
            //    await repoDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = tax.Item1,
                    TenantId = tenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = 0,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                var repoTaxBreakdown = _serviceProvider.GetService<IRepository<TicketTaxBreakdownEntity, long>>();
                await repoTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            await transaction.CompleteAsync();

            return header;
        }


        public async Task UpdateAsync(Guid userId, UpdateTicketApiRequestModel request, TicketHeaderEntity header, List<TicketDetailEntity> details,
            List<TicketTaxBreakdownEntity> taxbreakdowns, CurrencyEntity toCurrency, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields,
            Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            var repoInvoiceHeader = _serviceProvider.GetService<IRepository<TicketHeaderEntity, long>>();
            header.ToCurrency = toCurrency.CurrencyCode;
            header.InvoiceDate = request.InvoiceDate;
            header.InvoiceDateMonth = (short)request.InvoiceDate.Month;
            header.InvoiceDateNumber = (short)request.InvoiceDate.Day;
            header.InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter();
            header.InvoiceDateWeek = (short)request.InvoiceDate.GetWeek();
            header.InvoiceDateYear = (short)request.InvoiceDate.Year;
            header.Note = request.Note;
            header.PaymentMethod = request.PaymentMethod;
            header.RoundingCurrency = toCurrency.Rounding;
            header.CurrencyConversion = toCurrency.Conversion;
            header.ExchangeRate = request.ExchangeRate;
            header.TotalAmount = request.TotalAmount;
            header.TotalVatAmount = request.TotalVatAmount;
            header.TotalPaymentAmount = request.TotalPaymentAmount;
            header.TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax;
            header.TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax;
            header.TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax;
            header.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(header.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus)));
            header.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(header.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus)));
            header.BuyerCode = request.BuyerCode?.Trim();
            header.BuyerEmail = request.BuyerEmail?.Trim();
            header.BuyerFullName = request.BuyerFullName;
            header.BuyerLegalName = request.BuyerLegalName;
            header.BuyerTaxCode = request.BuyerTaxCode?.Trim();
            header.BuyerAddressLine = request.BuyerAddressLine;
            header.BuyerDistrictName = request.BuyerDistrictName;
            header.BuyerCityName = request.BuyerCityName;
            header.BuyerPhoneNumber = request.BuyerPhoneNumber;
            header.BuyerFaxNumber = request.BuyerFaxNumber;
            header.BuyerBankAccount = request.BuyerBankAccount;
            header.BuyerBankName = request.BuyerBankName;
            header.Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"));
            //repoInvoiceHeader.Update(header);

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductId = product?.Id,
                    Index = item.Index,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    TenantId = header.TenantId,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue,
                        TenantId = header.TenantId,
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                    }).ToList()
                });
            }

            //var commandHeaderExtras = request.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            //{
            //    FieldValue = x.FieldValue,
            //    InvoiceHeaderId = header.Id,
            //    TenantId = header.TenantId,
            //    InvoiceHeaderFieldId = headerFields[x.FieldName].Id,
            //    FieldName = x.FieldName
            //})
            //.ToList();

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = header.TenantId,
                InvoiceHeaderId = header.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = 0,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            await UpdateInvoiceDetailAsync(details, commandInvoiceDetails, detailFields);
            //await UpdateInvoiceHeaderExtraAsync(headerExtras, commandHeaderExtras);
            await UpdateInvoiceTaxbreakdownAsync(taxbreakdowns, commandTaxBreakDowns);

            header.LastModificationTime = DateTime.Now;
            header.LastModifierId = userId;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();
        }

        public async Task UpdateRawAsync(UpdateTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> details, Guid tenantId, Guid userId, List<TicketTaxBreakdownEntity> taxBreakDowns,
            CurrencyEntity toCurrency, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields,
            Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""StoreCode"" = N'{request.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{request.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{request.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{request.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{request.BuyerPassportNumber?.Replace("'", "''")}',

                                    ""ToCurrency"" = '{toCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note?.Replace("'", "''")}',
                                    ""PaymentMethod"" = '{request.PaymentMethod}',
                                    ""RoundingCurrency"" = {toCurrency.Rounding},
                                    ""CurrencyConversion"" = {toCurrency.Conversion},
                                    ""ExchangeRate"" = {request.ExchangeRate},
                                    ""TotalAmount"" = {request.TotalAmount},
                                    ""TotalVatAmount"" = {request.TotalVatAmount},
                                    ""TotalPaymentAmount"" = {request.TotalPaymentAmount},
                                    ""TotalDiscountAmountBeforeTax"" = {request.TotalDiscountAmountBeforeTax},
                                    ""TotalPerAfterTax"" = {request.TotalDiscountPercentAfterTax},
                                    ""TotalDiscountAmountAfterTax"" = {request.TotalDiscountAmountAfterTax},
                                    ""PaymentAmountWords"" = N'{await _invoiceService.ReadMoneyViAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus)))}',
                                    ""PaymentAmountWordsEn"" = N'{await _invoiceService.ReadMoneyEnAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus)))}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail?.Trim()}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine?.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{request.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{rootInvoice.CreatorErp}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value)
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    TenantId = tenantId,
                    InvoiceHeaderId = rootInvoice.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = (short)item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        TenantId = tenantId,
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                        FieldValue = y.FieldValue,
                        FieldName = y.FieldName,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = tenantId,
                InvoiceHeaderId = rootInvoice.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmount,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await RawUpdateInvoiceDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate.Date);
            await RawUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, taxBreakDowns, commandTaxBreakDowns, request.InvoiceDate.Date);

            var query = rawSql.Append($@" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task<TicketHeaderEntity> CreateAdjustmentHeaderAsync(CreateAdjustmentHeaderTicketApiRequestModel request, InvoiceSource invoiceSource, TicketHeaderEntity rootInvoice,
            List<TicketDetailEntity> rootInvoiceDetails, List<TicketTaxBreakdownEntity> rootInvoiceTaxbreakDowns, InvoiceStatus invoiceStatus,
            SignStatus signStatus, ApproveStatus approveStatus, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, Dictionary<string, TicketHeaderFieldEntity> headerFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = tenantId,
                CreationTime = DateTime.Now,
                CreatorId = userId,

                Source = (short)invoiceSource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                TransactionId = rootInvoice.TransactionId,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,
                InvoiceStatus = (short)invoiceStatus.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = tenantId,
                SellerTaxCode = rootInvoice.SellerTaxCode,
                SellerAddressLine = rootInvoice.SellerAddressLine,
                SellerCountryCode = rootInvoice.SellerCountryCode,
                SellerDistrictName = rootInvoice.SellerDistrictName,
                SellerCityName = rootInvoice.SellerCityName,
                SellerPhoneNumber = rootInvoice.SellerPhoneNumber,
                SellerFaxNumber = rootInvoice.SellerFaxNumber,
                SellerEmail = rootInvoice.SellerEmail,
                SellerBankName = rootInvoice.SellerBankName,
                SellerBankAccount = rootInvoice.SellerBankAccount,
                SellerLegalName = rootInvoice.SellerLegalName,
                SellerFullName = rootInvoice.SellerFullName,

                RoundingCurrency = rootInvoice.RoundingCurrency,
                FromCurrency = rootInvoice.FromCurrency,
                CurrencyConversion = rootInvoice.CurrencyConversion,
                ToCurrency = rootInvoice.ToCurrency,
                ExchangeRate = rootInvoice.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,

                FullNameCreator = fullNameCreator,
                UserNameCreator = userNameCreator,

                BuyerCode = null,
                BuyerId = null,
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail?.Trim(),
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = rootInvoice.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = rootInvoice.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = rootInvoice.TotalDiscountPercentAfterTax,
                TotalVatAmount = rootInvoice.TotalVatAmount,
                TotalAmount = rootInvoice.TotalAmount,
                TotalPaymentAmount = rootInvoice.TotalPaymentAmount,
                PaymentAmountWords = rootInvoice.PaymentAmountWords,
                PaymentAmountWordsEn = rootInvoice.PaymentAmountWordsEn,
                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            };
            var repoHeader = _appFactory.Repository<TicketHeaderEntity, long>();
            await repoHeader.InsertAsync(header);

            //Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!headerFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = headerFields[item.FieldName];
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = tenantId
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        var repoHeaderExtra = _appFactory.Repository<TicketHeaderExtraEntity, long>();
            //        await repoHeaderExtra.InsertManyAsync(headerExtras);
            //    }
            //}

            //Details
            var details = new Dictionary<long, TicketDetailEntity>();
            foreach (var item in rootInvoiceDetails)
            {
                details.Add(item.Id, new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = tenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    UnitName = item.UnitName?.Trim(),
                    ProductCode = item.ProductCode,
                    UnitId = item.UnitId,
                    RoundingUnit = item.RoundingUnit,
                    UnitPrice = item.UnitPrice,
                    HideQuantity = item.HideQuantity,
                    HideUnit = item.HideUnit,
                    HideUnitPrice = item.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = item.VatPercentDisplay,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
                });
            }
            var repoDetail = _appFactory.Repository<TicketDetailEntity, long>();
            await repoDetail.InsertManyAsync(details.Values);

            //Detail Extras
            //var groupDetailExtras = rootInvoiceDetailExtras.GroupBy(x => x.InvoiceDetailId)
            //                                               .ToDictionary(x => x.Key, x => x.ToList());

            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    if (!groupDetailExtras.ContainsKey(detail.Key))
            //        continue;

            //    var items = groupDetailExtras[detail.Key];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceDetailId = detail.Key,
            //            InvoiceDetailFieldId = item.InvoiceDetailFieldId,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    var repoDetailExtra = _appFactory.Repository<TicketDetailExtraEntity, long>();
            //    await repoDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in rootInvoiceTaxbreakDowns)
            {
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = item.Name,
                    TenantId = tenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = item.VatAmountBackUp,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = item.VatPercentDisplay,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                var repoTaxBreakdown = _appFactory.Repository<TicketTaxBreakdownEntity, long>();
                await repoTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            //FileDocument
            //var fileDocument = new TicketDocumentInfo
            //{
            //    FileCode = request.IdFileDocument.Value,
            //    InvoiceCode = code,
            //    TenantId = context.TenantId,
            //    DocumentDate = request.DocumentDate,
            //    DocumentNo = request.DocumentNo,
            //    DocumentReason = request.DocumentReason,
            //};
            //var repoFile = _TicketUoW.GetRepository<ITicketDocumentInfoRepository>();
            //await repoFile.InsertAsync(fileDocument);
            //await _TicketUoW.CommitAsync();

            //InvoiceReference
            var invoiceReference = new TicketReferenceEntity
            {
                CreationTime = DateTime.Now,
                CreatorId = userId,
                InvoiceHeaderId = header.Id,
                InvoiceDateReference = rootInvoice.InvoiceDate,
                InvoiceNoReference = rootInvoice.InvoiceNo,
                InvoiceReferenceId = rootInvoice.Id,
                NumberReference = rootInvoice.Number ?? 0,
                SerialNoReference = rootInvoice.SerialNo,
                TemplateNoReference = rootInvoice.TemplateNo,
                TenantId = tenantId,
                InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh.GetHashCode(),
                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            };

            var repoInvoiceReference = _appFactory.Repository<TicketReferenceEntity, long>();
            await repoInvoiceReference.InsertAsync(invoiceReference);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();

            return header;
        }

        public async Task<TicketHeaderDto> CreateAdjustmentHeaderRawAsync(CreateAdjustmentHeaderTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> details,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, Dictionary<string, TicketHeaderFieldEntity> headerFields, List<TicketTaxBreakdownEntity> taxBreakdowns)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(rootInvoice.SellerId);

            var generateHeaderId = await GetSEQsNextVal(1, SEQ_Name.SEQ_TicketHeader);
            var headerId = generateHeaderId.First();
            var lstDetailId = await GetSEQsNextVal(details.Count, SEQ_Name.SEQ_TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",       
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   

                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)resource },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ rootInvoice.TransactionId }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},                                                                                                   
                                            {(short)approveCancelStatus.GetHashCode()},                                                                                                   
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                   
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawSellerId}', 
                                            '{rootInvoice.SellerCode}',
                                            '{rootInvoice.SellerTaxCode}',                                                                                                        
                                            N'{rootInvoice.SellerAddressLine}',                                                                                                       
                                            N'{rootInvoice.SellerCountryCode}',                                                                                                       
                                            N'{rootInvoice.SellerDistrictName}',                                                                                                      
                                            N'{rootInvoice.SellerCityName}',                                                                                                          
                                            '{rootInvoice.SellerPhoneNumber}',                                         
                                            '{rootInvoice.SellerFaxNumber}',                                             
                                            '{rootInvoice.SellerEmail}',                                         
                                            N'{rootInvoice.SellerBankName}',                                  
                                            N'{rootInvoice.SellerBankAccount}',                            
                                            N'{rootInvoice.SellerLegalName}',                                
                                            N'{rootInvoice.SellerFullName }',                                                                                                   
                                            { rootInvoice.RoundingCurrency },                                                                                                       
                                            '{ rootInvoice.FromCurrency }',                                                                                               
                                            { rootInvoice.CurrencyConversion },                                                                                                     
                                            '{ rootInvoice.ToCurrency }',                                                                                                 
                                            { rootInvoice.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ rootInvoice.PaymentAmountWords }',        
                                            N'{ rootInvoice.PaymentAmountWordsEn }',        
                                            { rootInvoice.TotalAmount },                                                                                                                                      
                                            { rootInvoice.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine?.Replace("'", "''") }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail?.Trim() }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { rootInvoice.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { rootInvoice.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { rootInvoice.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { rootInvoice.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();
            //StringBuilder sqlValueDetailExtras = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();


            #region DETAILS SQL

            var i = 0;
            var dictMappingDetailId = new Dictionary<long, long>();

            foreach (var item in details.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];
                dictMappingDetailId.Add(item.Id, detailId);

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    detailExtraProperties = JsonConvert.SerializeObject(item.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                              
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmountBeforeTax },                                   
                                                        { item.DiscountPercentBeforeTax },                                  
                                                        N'{ item.Note }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { item.ProductId },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { item.ProductType },                                            
                                                        '{ item.ProductCode }',                                     
                                                        { item.Quantity },                                                  
                                                        { item.UnitId },                                   
                                                        N'{ item.UnitName?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { item.RoundingUnit },                             
                                                        { (item.HideQuantity ? 1 : 0) },    
                                                        { (item.HideUnit ? 1 : 0) },        
                                                        { (item.HideUnitPrice ? 1 : 0) },  
                                                        { item.VatAmount },                                                 
                                                        { item.VatPercent },                                                
                                                        '{ item.VatPercentDisplay?.Replace("'", "''") }',    
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                                   
                                                        N'{detailExtraProperties?.Replace("'", "''")}'                                                                  
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL

            foreach (var item in taxBreakdowns)
            {
                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT   
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ item.Name }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmountBackUp },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ item.VatPercentDisplay?.Replace("'", "''") }',                                              
                                                              { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",
                                        ""ExtraProperties""
                                ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (    
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""TicketReference"" (             
                                        ""CreationTime"",                           
                                        ""InvoiceDateReference"",                                  
                                        ""InvoiceHeaderId"",                                      
                                        ""InvoiceNoReference"",                                 
                                        ""InvoiceReferenceId"",                           
                                        ""NumberReference"",                                
                                        ""SerialNoReference"",                         
                                        ""TemplateNoReference"",
                                        ""TenantId"",
                                        ""InvoiceStatus"",
                                        ""Note"",
                                        ""Partition""
                                    )                                                  
                                    VALUES (
                                        '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        '{rootInvoice.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        {headerId},
                                        '{rootInvoice.InvoiceNo}',
                                        {rootInvoice.Id},
                                        {rootInvoice.Number ?? 0},
                                        '{rootInvoice.SerialNo}',
                                        {rootInvoice.TemplateNo},
                                        '{rawTenantId}',
                                        {(short)InvoiceStatus.DieuChinhDinhDanh.GetHashCode()},
                                        N'{rootInvoice.Note?.Replace("'", "''")}',
                                        {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                    ); ");

            #endregion


            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task<TicketHeaderEntity> ReplaceAsync(CreateReplaceTicketApiRequestModel request, TicketHeaderEntity rootInvoice, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string userFullName, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = tenantId,
                CreationTime = DateTime.Now,
                CreatorId = userId,

                Source = (short)resource.GetHashCode(),
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = rootInvoice.TransactionId,
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = request.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,
                InvoiceStatus = (short)InvoiceStatus.ThayThe.GetHashCode(),
                SignStatus = (short)signStatus.GetHashCode(),
                ApproveStatus = (short)approveStatus.GetHashCode(),

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = rootInvoice.SellerId,
                SellerTaxCode = rootInvoice.SellerTaxCode,
                SellerAddressLine = rootInvoice.SellerAddressLine,
                SellerCountryCode = rootInvoice.SellerCountryCode,
                SellerDistrictName = rootInvoice.SellerDistrictName,
                SellerCityName = rootInvoice.SellerCityName,
                SellerPhoneNumber = rootInvoice.SellerPhoneNumber,
                SellerFaxNumber = rootInvoice.SellerFaxNumber,
                SellerEmail = rootInvoice.SellerEmail,
                SellerBankName = rootInvoice.SellerBankName,
                SellerBankAccount = rootInvoice.SellerBankAccount,
                SellerLegalName = rootInvoice.SellerLegalName,
                SellerFullName = rootInvoice.SellerFullName,

                RoundingCurrency = toCurrency.Rounding,
                FromCurrency = fromCurrency.CurrencyCode,
                CurrencyConversion = toCurrency.Conversion,
                ToCurrency = toCurrency.CurrencyCode,
                ExchangeRate = request.ExchangeRate,
                PaymentMethod = request.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.ThayThe),
                PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.ThayThe),
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = userFullName,
                UserNameCreator = userNameCreator,

                BuyerCode = request.BuyerCode?.Trim(),
                BuyerFullName = request.BuyerFullName,
                BuyerLegalName = request.BuyerLegalName,
                BuyerTaxCode = request.BuyerTaxCode?.Trim(),
                BuyerAddressLine = request.BuyerAddressLine,
                BuyerDistrictName = request.BuyerDistrictName,
                BuyerCityName = request.BuyerCityName,
                BuyerCountryCode = request.BuyerCountryCode,
                BuyerPhoneNumber = request.BuyerPhoneNumber,
                BuyerFaxNumber = request.BuyerFaxNumber,
                BuyerEmail = request.BuyerEmail?.Trim(),
                BuyerBankName = request.BuyerBankName,
                BuyerBankAccount = request.BuyerBankAccount,


                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,
                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            };

            var repoHeader = _serviceProvider.GetService<IRepository<TicketHeaderEntity, long>>();
            await repoHeader.InsertAsync(header);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Header Extras
            //if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in request.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!headerFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = headerFields[item.FieldName];
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = field.Id,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        var repoHeaderExtra = _serviceProvider.GetService<IRepository<TicketHeaderExtraEntity, long>>();
            //        await repoHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}

            //Details
            var details = new List<TicketDetailEntity>();
            foreach (var item in request.InvoiceDetails)
            {
                var unit = units.ContainsKey(item.UnitName?.Trim()) ? units[item.UnitName?.Trim()] : null;
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                details.Add(new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = tenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    UnitName = item.UnitName?.Trim(),
                    ProductId = product == null ? 0 : product.Id,
                    UnitId = unit == null ? 0 : unit.Id,
                    RoundingUnit = unit == null ? 4 : unit.Rounding,
                    UnitPrice = item.UnitPrice,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
                });
            }
            var repoDetail = _serviceProvider.GetService<IRepository<TicketDetailEntity, long>>();
            await repoDetail.InsertManyAsync(details);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Detail Extras
            //Sau khi SaveChanges, các Id của bảng InvoiceDetail sẽ có dữ liệu
            //Dựa vào Index trên request để mapping DetailExtra nào ứng với Detail nào
            var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    var items = indexes[detail.Index];

            //    //Nếu ko có DetailExtra thì bỏ qua, ko insert
            //    if (items == null || items.Count == 0)
            //        continue;

            //    foreach (var item in items)
            //    {
            //        //Nếu ko có DetailField thì bỏ qua, ko insert
            //        if (!detailFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = detailFields[item.FieldName];
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceDetailId = detail.Id,
            //            InvoiceDetailFieldId = field.Id,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    var repoDetailExtra = _serviceProvider.GetService<IRepository<TicketDetailExtraEntity, long>>();
            //    await repoDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    InvoiceHeaderId = header.Id,
                    Name = tax.Item1,
                    TenantId = tenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = 0,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                var repoTaxBreakdown = _serviceProvider.GetService<IRepository<TicketTaxBreakdownEntity, long>>();
                await repoTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            //InvoiceReference
            var invoiceReference = new TicketReferenceEntity
            {
                CreationTime = DateTime.Now,
                CreatorId = userId,
                InvoiceDateReference = rootInvoice.InvoiceDate,
                InvoiceHeaderId = header.Id,
                InvoiceNoReference = rootInvoice.InvoiceNo,
                InvoiceReferenceId = rootInvoice.Id,
                NumberReference = rootInvoice.Number ?? 0,
                SerialNoReference = rootInvoice.SerialNo,
                TemplateNoReference = rootInvoice.TemplateNo,
                TenantId = tenantId,
                InvoiceStatus = InvoiceStatus.ThayThe.GetHashCode(),
                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
                Note = request.Note
            };
            var repoInvoiceReference = _serviceProvider.GetService<IRepository<TicketReferenceEntity, long>>();
            await repoInvoiceReference.InsertAsync(invoiceReference);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();

            return header;
        }

        public async Task<TicketHeaderDto> ReplaceRawAsync(CreateReplaceTicketApiRequestModel request, TenantInfoModel tenant, TicketHeaderEntity rootInvoice, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource resource,
            Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products,
            Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.TicketHeader)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",    

                                            ""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber"", 

                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',
                                           
                                            '{request.StoreCode?.Replace("'", "''")}',
                                            '{request.StoreName?.Replace("'", "''")}',
                                            '{request.BudgetUnitCode?.Replace("'", "''")}',
                                            '{request.BuyerIDNumber?.Replace("'", "''")}',
                                            '{request.BuyerPassportNumber?.Replace("'", "''")}',

                                            {(short)InvoiceSource.Api.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{request.ErpId}',                                                                                                                                          
                                            '{request.CreatorErp?.Replace("'", "''")}',                                                                                                                                     
                                            '{_invoiceService.GetTransactionId(InvoiceSource.Api, rootInvoice.TransactionId)}',                                                                                
                                            {template.Id},                                                                                                               
                                            {request.TemplateNo},                                                                                                                                       
                                            '{request.SerialNo}',                                                                                                                                       
                                            N'{request.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.ThayThe.GetHashCode()},                                                                                                                   
                                            {(short)signStatus.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankAccount}',                                  
                                            '{tenant.BankName?.Replace("'", "''")}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName?.Replace("'", "''") : null)}',                                
                                            N'{tenant.FullNameVi?.Replace("'", "''")}',                                                                                             
                                            {toCurrency.Rounding},                                                                                                       
                                            '{fromCurrency.CurrencyCode}',                                                                                               
                                            {toCurrency.Conversion},                                                                                                     
                                            '{toCurrency.CurrencyCode}',                                                                                                 
                                            {request.ExchangeRate.ToString().Replace(',', '.')},                                                                                                                                     
                                            '{request.PaymentMethod}',                                                                                                                                  
                                            '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''")}',        
                                            N'{(await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''")}',        
                                            {request.TotalAmount.ToString().Replace(',', '.')},                                                                                                                                      
                                            {request.TotalPaymentAmount.ToString().Replace(',', '.')},                                                                                                                               
                                            '{fullNameCreator?.Replace("'", "''")}',                                                                                                                                   
                                            '{userNameCreator}',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{request.BuyerCode?.Trim()}',                                                                                                                              
                                            '{request.BuyerFullName.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerLegalName?.Replace("'", "''")}',                                                                                                                                 
                                            '{request.BuyerTaxCode?.Trim()}',                                                                                                                           
                                            '{request.BuyerAddressLine?.Replace("'", "''")}',                                                                                                                               
                                            '{request.BuyerDistrictName?.Replace("'", "''")}',                                                                                                                              
                                            '{request.BuyerCityName?.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerCountryCode}',                                                                                                                               
                                            '{request.BuyerPhoneNumber}',                                                                                                                               
                                            '{request.BuyerFaxNumber}',                                                                                                                                 
                                            '{request.BuyerEmail?.Trim()}',                                                                                                                                     
                                            '{request.BuyerBankName?.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerBankAccount}',                                                                                                                               
                                            {request.TotalDiscountAmountBeforeTax.ToString().Replace(',', '.')},                                                                                                                     
                                            {request.TotalDiscountAmountAfterTax.ToString().Replace(',', '.')},                                                                                                                      
                                            {request.TotalDiscountPercentAfterTax.ToString().Replace(',', '.')},                                                                                                                     
                                            {request.TotalVatAmount.ToString().Replace(',', '.')},                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            if (!request.InvoiceDetails.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Create.InvoiceDetailCanNotEmpty"]);

            StringBuilder sqlInsertDetails = new StringBuilder();

            var i = 0;

            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim())
                            ? null
                            : ((products != null && products.ContainsKey(item.ProductCode?.Trim())) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlInsertDetails.Append($@"UNION ALL SELECT   
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount.ToString().Replace(',', '.')},                                                    
                                                        {item.DiscountAmountBeforeTax.ToString().Replace(',', '.')},                                   
                                                        {item.DiscountPercentBeforeTax.ToString().Replace(',', '.')},                                  
                                                        N'{item.Note?.Replace("'", "''")}',                                                   
                                                        N'{item.PaymentAmount.ToString().Replace(',', '.')}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {(short)item.ProductType.GetHashCode()},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                     
                                                        {item.Quantity.ToString().Replace(',', '.')},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{item.UnitName?.Trim()?.Replace("'", "''")}',                                       
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {((productType != null && productType.HideQuantity) ? 1 : 0)},    
                                                        {((productType != null && productType.HideUnit) ? 1 : 0)},        
                                                        {((productType != null && productType.HideUnitPrice) ? 1 : 0)},   
                                                        {item.VatAmount.ToString().Replace(',', '.')},                                                 
                                                        {item.VatPercent.ToString().Replace(',', '.')},        
                                                        '{taxes[item.VatPercent].Item2?.Replace("'", "''")}',        
                                                        {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                
                                                        0,
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL
            if (request.InvoiceTaxBreakdowns == null || !request.InvoiceTaxBreakdowns.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Create.InvoiceTaxBreakdownsCanNotEmpty"]);

            StringBuilder sqlInsertTaxBreakdowns = new StringBuilder();

            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];

                sqlInsertTaxBreakdowns.Append($@"UNION ALL SELECT     
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              {headerId},                                                      
                                                              '{rawTenantId}',                                             
                                                              '{tax.Item1}',                                               
                                                              {item.VatAmount.ToString().Replace(',', '.')},                                            
                                                              {item.VatAmount.ToString().Replace(',', '.')},                                      
                                                              {item.VatPercent.ToString().Replace(',', '.')},
                                                              N'{tax.Item2?.Replace("'", "''")}',                                             
                                                              {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}   
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlInsertDetails.Length > 0)
            {
                sqlInsertDetails = sqlInsertDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlInsertDetails} ; ");
            }

            if (sqlInsertTaxBreakdowns.Length > 0)
            {
                sqlInsertTaxBreakdowns = sqlInsertTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (   
                                        ""Id"",    
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlInsertTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""TicketReference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{rootInvoice.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {headerId},
                                                    '{rootInvoice.InvoiceNo}',
                                                    {rootInvoice.Id},
                                                    {rootInvoice.Number ?? 0},
                                                    '{rootInvoice.SerialNo}',
                                                    {rootInvoice.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.ThayThe.GetHashCode()},
                                                    N'{request.Note?.Replace("'", "''")}',
                                                    {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task<TicketHeaderDto> AdjustRawAsync(CreateAdjustTicketApiRequestModel request, TenantInfoModel tenant, TicketHeaderEntity rootInvoice, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource resource,
            Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products,
            Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.TicketHeader)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",    

                                            ""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber"", 
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',
                                           
                                            '{request.StoreCode?.Replace("'", "''")}',
                                            '{request.StoreName?.Replace("'", "''")}',
                                            '{request.BudgetUnitCode?.Replace("'", "''")}',
                                            '{request.BuyerIDNumber?.Replace("'", "''")}',
                                            '{request.BuyerPassportNumber?.Replace("'", "''")}',

                                            {(short)InvoiceSource.Api.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{request.ErpId}',                                                                                                                                          
                                            '{request.CreatorErp?.Replace("'", "''")}',                                                                                                                                     
                                            '{_invoiceService.GetTransactionId(InvoiceSource.Api, rootInvoice.TransactionId)}',                                                                                
                                            {template.Id},                                                                                                               
                                            {request.TemplateNo},                                                                                                                                       
                                            '{request.SerialNo}',                                                                                                                                       
                                            N'{request.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.DieuChinh.GetHashCode()},                                                                                                                   
                                            {(short)signStatus.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankAccount}',                                  
                                            '{tenant.BankName?.Replace("'", "''")}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName?.Replace("'", "''") : null)}',                                
                                            N'{tenant.FullNameVi?.Replace("'", "''")}',                                                                                             
                                            {toCurrency.Rounding},                                                                                                       
                                            '{fromCurrency.CurrencyCode}',                                                                                               
                                            {toCurrency.Conversion},                                                                                                     
                                            '{toCurrency.CurrencyCode}',                                                                                                 
                                            {request.ExchangeRate.ToString().Replace(',', '.')},                                                                                                                                     
                                            '{request.PaymentMethod}',                                                                                                                                  
                                            '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''")}',        
                                            N'{(await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''")}',        
                                            {request.TotalAmount.ToString().Replace(',', '.')},                                                                                                                                      
                                            {request.TotalPaymentAmount.ToString().Replace(',', '.')},                                                                                                                               
                                            '{fullNameCreator?.Replace("'", "''")}',                                                                                                                                   
                                            '{userNameCreator}',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{request.BuyerCode?.Trim()}',                                                                                                                              
                                            '{request.BuyerFullName.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerLegalName?.Replace("'", "''")}',                                                                                                                                 
                                            '{request.BuyerTaxCode?.Trim()}',                                                                                                                           
                                            '{request.BuyerAddressLine?.Replace("'", "''")}',                                                                                                                               
                                            '{request.BuyerDistrictName?.Replace("'", "''")}',                                                                                                                              
                                            '{request.BuyerCityName?.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerCountryCode}',                                                                                                                               
                                            '{request.BuyerPhoneNumber}',                                                                                                                               
                                            '{request.BuyerFaxNumber}',                                                                                                                                 
                                            '{request.BuyerEmail?.Trim()}',                                                                                                                                     
                                            '{request.BuyerBankName?.Replace("'", "''")}',                                                                                                                                  
                                            '{request.BuyerBankAccount}',                                                                                                                               
                                            {request.TotalDiscountAmountBeforeTax.ToString().Replace(',', '.')},                                                                                                                     
                                            {request.TotalDiscountAmountAfterTax.ToString().Replace(',', '.')},                                                                                                                      
                                            {request.TotalDiscountPercentAfterTax.ToString().Replace(',', '.')},                                                                                                                     
                                            {request.TotalVatAmount.ToString().Replace(',', '.')},                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            if (!request.InvoiceDetails.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Create.InvoiceDetailCanNotEmpty"]);

            StringBuilder sqlInsertDetails = new StringBuilder();

            var i = 0;

            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim())
                            ? null
                            : ((products != null && products.ContainsKey(item.ProductCode?.Trim())) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlInsertDetails.Append($@"UNION ALL SELECT   
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount.ToString().Replace(',', '.')},                                                    
                                                        {item.DiscountAmountBeforeTax.ToString().Replace(',', '.')},                                   
                                                        {item.DiscountPercentBeforeTax.ToString().Replace(',', '.')},                                  
                                                        N'{item.Note?.Replace("'", "''")}',                                                   
                                                        N'{item.PaymentAmount.ToString().Replace(',', '.')}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {(short)item.ProductType.GetHashCode()},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                     
                                                        {item.Quantity.ToString().Replace(',', '.')},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{item.UnitName?.Trim()?.Replace("'", "''")}',                                       
                                                        {item.UnitPrice.ToString().Replace(',', '.')},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {((productType != null && productType.HideQuantity) ? 1 : 0)},    
                                                        {((productType != null && productType.HideUnit) ? 1 : 0)},        
                                                        {((productType != null && productType.HideUnitPrice) ? 1 : 0)},   
                                                        {item.VatAmount.ToString().Replace(',', '.')},                                                 
                                                        {item.VatPercent.ToString().Replace(',', '.')},        
                                                        '{taxes[item.VatPercent].Item2?.Replace("'", "''")}',        
                                                        {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                
                                                        0,
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL
            if (request.InvoiceTaxBreakdowns == null || !request.InvoiceTaxBreakdowns.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Create.InvoiceTaxBreakdownsCanNotEmpty"]);

            StringBuilder sqlInsertTaxBreakdowns = new StringBuilder();

            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];

                sqlInsertTaxBreakdowns.Append($@"UNION ALL SELECT     
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              {headerId},                                                      
                                                              '{rawTenantId}',                                             
                                                              '{tax.Item1}',                                               
                                                              {item.VatAmount},                                            
                                                              {item.VatAmount},                                      
                                                              {item.VatPercent},
                                                              N'{tax.Item2?.Replace("'", "''")}',                                             
                                                              {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}   
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlInsertDetails.Length > 0)
            {
                sqlInsertDetails = sqlInsertDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlInsertDetails} ; ");
            }

            if (sqlInsertTaxBreakdowns.Length > 0)
            {
                sqlInsertTaxBreakdowns = sqlInsertTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (   
                                        ""Id"",    
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlInsertTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""TicketReference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{rootInvoice.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {headerId},
                                                    '{rootInvoice.InvoiceNo}',
                                                    {rootInvoice.Id},
                                                    {rootInvoice.Number ?? 0},
                                                    '{rootInvoice.SerialNo}',
                                                    {rootInvoice.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.DieuChinh.GetHashCode()},
                                                    N'{request.Note?.Replace("'", "''")}',
                                                    {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task UpdateReplaceRawAsync(UpdateReplaceTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> details, List<TicketTaxBreakdownEntity> taxBreakDowns, SignStatus signStatus,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes,
            Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET  
                                    ""StoreCode"" = N'{request.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{request.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{request.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{request.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{request.BuyerPassportNumber?.Replace("'", "''")}',

                                    ""ToCurrency"" = '{request.Currency}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note}',
                                    ""PaymentMethod"" = '{request.PaymentMethod}',
                                    ""RoundingCurrency"" = {toCurrency.Rounding},
                                    ""CurrencyConversion"" = {toCurrency.Conversion},
                                    ""ExchangeRate"" = {request.ExchangeRate.ToString().Replace(',', '.')},
                                    ""TotalAmount"" = {request.TotalAmount.ToString().Replace(',', '.')},
                                    ""TotalVatAmount"" = {request.TotalVatAmount.ToString().Replace(',', '.')},
                                    ""TotalPaymentAmount"" = {request.TotalPaymentAmount.ToString().Replace(',', '.')},
                                    ""TotalDiscountAmountBeforeTax"" = {request.TotalDiscountAmountBeforeTax.ToString().Replace(',', '.')},
                                    ""TotalPerAfterTax"" = {request.TotalDiscountPercentAfterTax.ToString().Replace(',', '.')},
                                    ""TotalDiscountAmountAfterTax"" = {request.TotalDiscountAmountAfterTax.ToString().Replace(',', '.')},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail?.Trim()}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine?.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = '{request.BuyerBankName?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value)
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Index = item.Index,
                    InvoiceHeaderId = rootInvoice.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = (short)item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = tenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldName = y.FieldName,
                        FieldValue = y.FieldValue,
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                        TenantId = tenantId,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = tenantId,
                InvoiceHeaderId = rootInvoice.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmount,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await RawUpdateInvoiceDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate.Date);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, request.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, request.InvoiceDate.Date);
            await RawUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, taxBreakDowns, commandTaxBreakDowns, request.InvoiceDate.Date);

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task UpdateAdjustRawAsync(UpdateAdjustTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> details, List<TicketTaxBreakdownEntity> taxBreakDowns, SignStatus signStatus,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes,
            Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""StoreCode"" = N'{request.StoreCode?.Replace("'", "''")}',
                                    ""StoreName"" = N'{request.StoreName?.Replace("'", "''")}',
                                    ""BudgetUnitCode"" = N'{request.BudgetUnitCode?.Replace("'", "''")}',
                                    ""BuyerIDNumber"" = N'{request.BuyerIDNumber?.Replace("'", "''")}',
                                    ""BuyerPassportNumber"" = N'{request.BuyerPassportNumber?.Replace("'", "''")}',
                                    ""ToCurrency"" = '{request.Currency}',
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note}',
                                    ""PaymentMethod"" = '{request.PaymentMethod}',
                                    ""RoundingCurrency"" = {toCurrency.Rounding},
                                    ""CurrencyConversion"" = {toCurrency.Conversion},
                                    ""ExchangeRate"" = {request.ExchangeRate.ToString().Replace(',', '.')},
                                    ""TotalAmount"" = {request.TotalAmount.ToString().Replace(',', '.')},
                                    ""TotalVatAmount"" = {request.TotalVatAmount.ToString().Replace(',', '.')},
                                    ""TotalPaymentAmount"" = {request.TotalPaymentAmount.ToString().Replace(',', '.')},
                                    ""TotalDiscountAmountBeforeTax"" = {request.TotalDiscountAmountBeforeTax.ToString().Replace(',', '.')},
                                    ""TotalPerAfterTax"" = {request.TotalDiscountPercentAfterTax.ToString().Replace(',', '.')},
                                    ""TotalDiscountAmountAfterTax"" = {request.TotalDiscountAmountAfterTax.ToString().Replace(',', '.')},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Replace("'", "''")}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail?.Trim()}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine?.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = '{request.BuyerBankName?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value)
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Index = item.Index,
                    InvoiceHeaderId = rootInvoice.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = (short)item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = tenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldName = y.FieldName,
                        FieldValue = y.FieldValue,
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                        TenantId = tenantId,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = tenantId,
                InvoiceHeaderId = rootInvoice.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmount,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await RawUpdateInvoiceDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate.Date);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, request.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, request.InvoiceDate.Date);
            await RawUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, taxBreakDowns, commandTaxBreakDowns, request.InvoiceDate.Date);

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task<TicketHeaderEntity> CreateAdjustmentDetailAsync(CreateAdjustmentDetailTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> rootInvoiceDetails, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, SignStatus signStatus, ApproveStatus approveStatus, InvoiceSource source, Guid tenantId, Guid userId, string userFullName, string userNameCreator, InvoiceTemplateEntity template)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            //Header
            var header = new TicketHeaderEntity
            {
                TenantId = tenantId,
                CreationTime = DateTime.Now,
                CreatorId = userId,
                Source = (short)source,
                BatchId = Guid.NewGuid(),
                ErpId = request.ErpId,
                CreatorErp = request.CreatorErp,
                TransactionId = rootInvoice.TransactionId,
                InvoiceTemplateId = template.Id,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                Note = rootInvoice.Note,
                InvoiceDate = request.InvoiceDate,
                InvoiceDateMonth = (short)request.InvoiceDate.Month,
                InvoiceDateNumber = (short)request.InvoiceDate.Day,
                InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter(),
                InvoiceDateWeek = (short)request.InvoiceDate.GetWeek(),
                InvoiceDateYear = (short)request.InvoiceDate.Year,
                InvoiceStatus = (short)InvoiceStatus.DieuChinhTangGiam,
                SignStatus = (short)signStatus,
                ApproveStatus = (short)approveStatus,

                //Lưu lúc sinh số
                RegistrationHeaderId = null,
                RegistrationDetailId = null,
                Number = null,
                InvoiceNo = null,

                SellerId = rootInvoice.SellerId,
                SellerTaxCode = rootInvoice.SellerTaxCode,
                SellerAddressLine = rootInvoice.SellerAddressLine,
                SellerCountryCode = rootInvoice.SellerCountryCode,
                SellerDistrictName = rootInvoice.SellerDistrictName,
                SellerCityName = rootInvoice.SellerCityName,
                SellerPhoneNumber = rootInvoice.SellerPhoneNumber,
                SellerFaxNumber = rootInvoice.SellerFaxNumber,
                SellerEmail = rootInvoice.SellerEmail,
                SellerBankName = rootInvoice.SellerBankName,
                SellerBankAccount = rootInvoice.SellerBankAccount,
                SellerLegalName = rootInvoice.SellerLegalName,
                SellerFullName = rootInvoice.SellerFullName,

                RoundingCurrency = rootInvoice.RoundingCurrency,
                FromCurrency = rootInvoice.FromCurrency,
                CurrencyConversion = rootInvoice.CurrencyConversion,
                ToCurrency = rootInvoice.ToCurrency,
                ExchangeRate = rootInvoice.ExchangeRate,
                PaymentMethod = rootInvoice.PaymentMethod,
                PaymentDate = request.InvoiceDate,
                PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam),
                PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam),
                TotalAmount = request.TotalAmount,
                TotalPaymentAmount = request.TotalPaymentAmount,
                FullNameCreator = userFullName,
                UserNameCreator = userNameCreator,

                BuyerId = rootInvoice.BuyerId,
                BuyerErpId = rootInvoice.BuyerErpId,
                BuyerCode = rootInvoice.BuyerCode?.Trim(),
                BuyerFullName = rootInvoice.BuyerFullName,
                BuyerLegalName = rootInvoice.BuyerLegalName,
                BuyerTaxCode = rootInvoice.BuyerTaxCode?.Trim(),
                BuyerAddressLine = rootInvoice.BuyerAddressLine,
                BuyerDistrictName = rootInvoice.BuyerDistrictName,
                BuyerCityName = rootInvoice.BuyerCityName,
                BuyerCountryCode = rootInvoice.BuyerCountryCode,
                BuyerPhoneNumber = rootInvoice.BuyerPhoneNumber,
                BuyerFaxNumber = rootInvoice.BuyerFaxNumber,
                BuyerEmail = rootInvoice.BuyerEmail?.Trim(),
                BuyerBankName = rootInvoice.BuyerBankName,
                BuyerBankAccount = rootInvoice.BuyerBankAccount,

                TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax,
                TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax,
                TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax,
                TotalVatAmount = request.TotalVatAmount,

                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            };

            var repoHeader = _serviceProvider.GetService<IRepository<TicketHeaderEntity, long>>();
            await repoHeader.InsertAsync(header);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Header Extras
            //if (rootInvoiceHeaderExtras != null && rootInvoiceHeaderExtras.Count > 0)
            //{
            //    var headerExtras = new List<TicketHeaderExtraEntity>();
            //    foreach (var item in rootInvoiceHeaderExtras)
            //    {
            //        headerExtras.Add(new TicketHeaderExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceHeaderFieldId = item.InvoiceHeaderFieldId,
            //            InvoiceHeaderId = header.Id,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            //        });
            //    }

            //    if (headerExtras.Count > 0)
            //    {
            //        var repoHeaderExtra = _serviceProvider.GetService<IRepository<TicketHeaderExtraEntity, long>>();
            //        await repoHeaderExtra.InsertManyAsync(headerExtras);
            //        await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //    }
            //}

            //Details
            var details = new Dictionary<long, TicketDetailEntity>();
            var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);
            foreach (var item in request.InvoiceDetails)
            {
                if (!indexes.ContainsKey(item.Index))
                    throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.CreateAdjDetail.IndexInvoiceDetailIncorrect"]);

                var rootDetail = indexes[item.Index];

                details.Add(rootDetail.Id, new TicketDetailEntity
                {
                    Index = item.Index,
                    TenantId = tenantId,
                    InvoiceHeaderId = header.Id,
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Note = rootDetail.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = rootDetail.ProductId,
                    ProductName = rootDetail.ProductName,
                    Quantity = item.Quantity,
                    UnitName = rootDetail.UnitName?.Trim(),
                    ProductCode = rootDetail.ProductCode?.Trim(),
                    UnitId = rootDetail.UnitId,
                    RoundingUnit = rootDetail.RoundingUnit,
                    UnitPrice = item.UnitPrice,
                    HideQuantity = rootDetail.HideQuantity,
                    HideUnit = rootDetail.HideUnit,
                    HideUnitPrice = rootDetail.HideUnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
                });
            }
            var repoDetail = _serviceProvider.GetService<IRepository<TicketDetailEntity, long>>();
            await repoDetail.InsertManyAsync(details.Values);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Detail Extras
            //var indexes = request.InvoiceDetails.ToDictionary(x => x.Index, x => x.InvoiceDetailExtras);
            //var groupDetailExtras = rootInvoiceDetailExtras.GroupBy(x => x.InvoiceDetailId)
            //                                               .ToDictionary(x => x.Key, x => x.ToList());

            //var detailExtras = new List<TicketDetailExtraEntity>();
            //foreach (var detail in details)
            //{
            //    if (!groupDetailExtras.ContainsKey(detail.Key))
            //        continue;

            //    var items = groupDetailExtras[detail.Key];
            //    foreach (var item in items)
            //    {
            //        detailExtras.Add(new TicketDetailExtraEntity
            //        {
            //            FieldValue = item.FieldValue,
            //            InvoiceDetailId = detail.Key,
            //            InvoiceDetailFieldId = item.InvoiceDetailFieldId,
            //            TenantId = tenantId,
            //            Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
            //        });
            //    }
            //}
            //if (detailExtras.Count > 0)
            //{
            //    var repoDetailExtra = _serviceProvider.GetService<IRepository<TicketDetailExtraEntity, long>>();
            //    await repoDetailExtra.InsertManyAsync(detailExtras);
            //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            //}

            //TaxBreakdowns
            var taxBreakdowns = new List<TicketTaxBreakdownEntity>();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];
                taxBreakdowns.Add(new TicketTaxBreakdownEntity
                {
                    Name = tax.Item1,
                    TenantId = tenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = 0,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = tax.Item2,
                    InvoiceHeaderId = header.Id,
                    Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm")),
                });
            }
            if (taxBreakdowns.Count > 0)
            {
                var repoTaxBreakdown = _serviceProvider.GetService<IRepository<TicketTaxBreakdownEntity, long>>();
                await repoTaxBreakdown.InsertManyAsync(taxBreakdowns);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            //InvoiceReference
            var invoiceReference = new TicketReferenceEntity
            {
                CreatorId = userId,
                CreationTime = DateTime.Now,
                InvoiceDateReference = rootInvoice.InvoiceDate,
                InvoiceHeaderId = header.Id,
                InvoiceNoReference = rootInvoice.InvoiceNo,
                InvoiceReferenceId = rootInvoice.Id,
                NumberReference = rootInvoice.Number ?? 0,
                SerialNoReference = rootInvoice.SerialNo,
                TemplateNoReference = rootInvoice.TemplateNo,
                TenantId = tenantId,
                InvoiceStatus = InvoiceStatus.DieuChinhTangGiam.GetHashCode(),
                Partition = long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))
            };
            var repoInvoiceReference = _serviceProvider.GetService<IRepository<TicketReferenceEntity, long>>();
            await repoInvoiceReference.InsertAsync(invoiceReference);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();

            return header;
        }

        public async Task<TicketHeaderDto> CreateAdjustmentDetailRawAsync(CreateAdjustmentDetailTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> rootInvoiceDetails,
            ApproveStatus approveStatus, InvoiceSource resource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceTemplateEntity template,
            CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.TicketHeader)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (rootInvoice.ExtraProperties != null && rootInvoice.ExtraProperties.Any())
            {
                headerExtraProperties = JsonConvert.SerializeObject(rootInvoice.ExtraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"       
                                BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",    
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""
                                        )                                                                       
                                        VALUES(                                                                 
                                                { headerId },                                                                                                                                                   
                                                '{ rawTenantId }',                                                                                                                                          
                                                '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                                '{ rawUserId }',                                                                                                                                            
                                                { (short)resource },                                                                                                                    
                                                '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                                '{ request.ErpId }',                                                                                                                                          
                                                '{ request.CreatorErp }',                                                                                                                                     
                                                '{ rootInvoice.TransactionId}',                                                                                
                                                { template.Id },                                                                                                               
                                                { template.TemplateNo },                                                                                                                                       
                                                '{ template.SerialNo }',                                                                                                                                       
                                                N'{ rootInvoice.Note }',                                                                                                                                          
                                                '{ request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                                {(short)InvoiceStatus.DieuChinhTangGiam.GetHashCode()},                                                                                                                   
                                                {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                                {(short)approveStatus.GetHashCode()},                                                                                                   
                                                {(short)approveCancelStatus.GetHashCode()},                                                                                                   
                                                {(short)approveDeleteStatus.GetHashCode()},                                                                                                   
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                '{rawTenantId}',  
                                                '{rootInvoice.SellerCode}',
                                                '{rootInvoice.SellerTaxCode}',                                                                                                        
                                                N'{rootInvoice.SellerAddressLine}',                                                                                                       
                                                N'{rootInvoice.SellerCountryCode}',                                                                                                       
                                                N'{rootInvoice.SellerDistrictName}',                                                                                                      
                                                N'{rootInvoice.SellerCityName}',                                                                                                          
                                                '{rootInvoice.SellerPhoneNumber}',                                         
                                                '{rootInvoice.SellerFaxNumber}',                                             
                                                '{rootInvoice.SellerEmail}',                                         
                                                N'{rootInvoice.SellerBankName}',                                  
                                                N'{rootInvoice.SellerBankAccount}',                            
                                                N'{rootInvoice.SellerLegalName}',                                
                                                N'{rootInvoice.SellerFullName }',                                                                                                   
                                                { rootInvoice.RoundingCurrency },                                                                                                       
                                                '{ rootInvoice.FromCurrency }',                                                                                               
                                                { rootInvoice.CurrencyConversion },                                                                                                     
                                                '{ rootInvoice.ToCurrency }',                                                                                                 
                                                { rootInvoice.ExchangeRate },                                                                                                                                     
                                                '{ rootInvoice.PaymentMethod }',                                                                                                                                  
                                                '{ request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                                N'{ await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam) }',        
                                                N'{ await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.DieuChinhTangGiam) }',        
                                                { request.TotalAmount },                                                                                                                                      
                                                { request.TotalPaymentAmount },                                                                                                                               
                                                N'{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                                '{ userNameCreator }',                                                                                                                                       
                                                NULL,                                                                                                                                                       
                                                '{ rootInvoice.BuyerCode?.Trim() }',                                                                                                                              
                                                N'{ rootInvoice.BuyerFullName.Replace("'", "''") }',                                                                                                                                  
                                                N'{ rootInvoice.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                                '{ rootInvoice.BuyerTaxCode?.Trim() }',                                                                                                                           
                                                N'{ rootInvoice.BuyerAddressLine?.Replace("'", "''") }',                                                                                                                               
                                                N'{ rootInvoice.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                                N'{ rootInvoice.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                                '{ rootInvoice.BuyerCountryCode }',                                                                                                                               
                                                '{ rootInvoice.BuyerPhoneNumber }',                                                                                                                               
                                                '{ rootInvoice.BuyerFaxNumber }',                                                                                                                                 
                                                '{ rootInvoice.BuyerEmail?.Trim() }',                                                                                                                                     
                                                N'{ rootInvoice.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                                '{ rootInvoice.BuyerBankAccount }',                                                                                                                               
                                                { request.TotalDiscountAmountBeforeTax },                                                                                                                     
                                                { request.TotalDiscountAmountAfterTax },                                                                                                                      
                                                { request.TotalDiscountPercentAfterTax },                                                                                                                     
                                                { request.TotalVatAmount },                                                                                                                                   
                                                1,                                                                                                                                                          
                                                {request.InvoiceDate.Year},                                                                                                                                   
                                                {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                                {request.InvoiceDate.Month},                                                                                                                                  
                                                {request.InvoiceDate.GetWeek()},                                                                                                                              
                                                {request.InvoiceDate.Day},                                                                                                                                    
                                                0,                                                                                                                                                          
                                                0,                                                                                                                                                          
                                                {long.Parse(request.InvoiceDate.ToString($"yyyyMMddHHmm"))},                                                                                                   
                                                N'{headerExtraProperties}'                                                                                                
                                        ); ");

            #endregion


            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();
            //StringBuilder sqlValueDetailExtras = new StringBuilder();
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();

            #region DETAILS SQL

            var detailIds = new List<long>();
            var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);
            int i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var idDetail = lstDetailId[i];
                if (!indexes.ContainsKey(item.Index))
                    //throw new Exception("Thông tin điều chỉnh tăng giảm chi tiết hóa đơn không đúng");
                    throw new Exception(_localizier["Vnis.BE.Ticket.IndexInvoiceDetailIncorrect"]);

                var rootDetail = indexes[item.Index];
                var hideQuantity = rootDetail.HideQuantity ? 1 : 0;
                var hideUnit = rootDetail.HideUnit ? 1 : 0;
                var hideUnitPrice = rootDetail.HideUnitPrice ? 1 : 0;
                detailIds.Add(rootDetail.Id);

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(idDetail, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue?.Replace("'", "''"),
                        FieldName = x.FieldName,
                    }).ToList(), detailFields);

                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                               
                                                          { idDetail},                                              
                                                          { i + 1 },                                                     
                                                          '{ rawTenantId }',                                                  
                                                          { headerId },                                                           
                                                          { item.Amount },                                                    
                                                          { item.DiscountAmountBeforeTax },                                   
                                                          { item.DiscountPercentBeforeTax },                                  
                                                          N'{ item.Note?.Replace("'", "''") }',                                                   
                                                          N'{ item.PaymentAmount }',                                          
                                                          { rootDetail.ProductId },                             
                                                          N'{ rootDetail.ProductName?.Replace("'", "''")}',                                            
                                                          { rootDetail.ProductType },                                            
                                                          '{ rootDetail.ProductCode?.Trim() }',                                     
                                                          { item.Quantity },                                                  
                                                          { rootDetail.UnitId },                                   
                                                          N'{ rootDetail.UnitName?.Trim()?.Replace("'", "''")}',                                       
                                                          { item.UnitPrice },                                                 
                                                          { rootDetail.RoundingUnit },                             
                                                          { hideQuantity},    
                                                          { hideUnit },        
                                                          { hideUnitPrice },   
                                                          { item.VatAmount },                                                 
                                                          { item.VatPercent },                                                
                                                          '{ taxes[item.VatPercent].Item2?.Replace("'", "''") }',    
                                                          { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                          0,                                                                   
                                                          N'{detailExtraProperties}'                                                                   
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL

            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT            
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ tax.Item1 }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmount },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ tax.Item2 }',                                              
                                                              { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                             
                                        ""DiscountAmountBeforeTax"",            
                                        ""DiscountPercentBeforeTax"",           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                       
                                        ""HideQuantity"",                       
                                        ""HideUnit"",                           
                                        ""HideUnitPrice"",                      
                                        ""VatAmount"",                          
                                        ""VatPercent"",                         
                                        ""VatPercentDisplay"",                  
                                        ""Partition"",                          
                                        ""IsPromotion"",                         
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (    
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            #region INVOICE REFERENCE SQL
            StringBuilder sqlInsertTicketReference = new StringBuilder();

            sqlInsertTicketReference.Append($@"  INSERT INTO ""TicketReference"" (             
                                                    ""CreationTime"",                           
                                                    ""CreatorId"",                                  
                                                    ""TenantId"",                                      
                                                    ""InvoiceHeaderId"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""TemplateNoReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""InvoiceNoReference"",                                  
                                                    ""NumberReference"",                                  
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceStatus"",                                  
                                                    ""Partition""                                                                 
                                                )                                                  
                                                VALUES ");

            //Add values
            sqlInsertTicketReference.Append($@"  (                                                               
                                                      '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                      '{ rawUserId }',                                             
                                                      '{ rawTenantId }',                                               
                                                      { headerId },                                            
                                                      { rootInvoice.Id},                                      
                                                      { rootInvoice.TemplateNo },                                           
                                                      '{ rootInvoice.SerialNo }',                                              
                                                      '{ rootInvoice.InvoiceNo}',    
                                                      { rootInvoice.Number},    
                                                      '{ rootInvoice.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                      { InvoiceStatus.DieuChinhTangGiam.GetHashCode()},    
                                                      { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}    
                                                  ) ");

            sqlInsertTicketReference.Append("; ");

            if (sqlInsertTicketReference.Length > 0)
                rawSql.Append(sqlInsertTicketReference);
            #endregion


            var query = rawSql.Append($" END; ").ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task CreateAdjustmentHeaderWithoutInvoiceNoAsync(TicketHeaderEntity invoice)
        {
            invoice.InvoiceStatus = (short)InvoiceStatus.DieuChinhDinhDanh;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }


        public async Task UpdateReplaceAsync(Guid userId, TicketHeaderEntity entityHeader, List<TicketDetailEntity> entityInvoiceDetails, List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, UpdateReplaceTicketApiRequestModel request, CurrencyEntity toCurrency, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = new Dictionary<string, string>();
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                headerExtraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue
                }).ToList(), headerFields);
            }
            #endregion

            entityHeader.ToCurrency = toCurrency.CurrencyCode;
            entityHeader.InvoiceDate = request.InvoiceDate;
            entityHeader.InvoiceDateMonth = (short)request.InvoiceDate.Month;
            entityHeader.InvoiceDateNumber = (short)request.InvoiceDate.Day;
            entityHeader.InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter();
            entityHeader.InvoiceDateWeek = (short)request.InvoiceDate.GetWeek();
            entityHeader.InvoiceDateYear = (short)request.InvoiceDate.Year;
            entityHeader.Note = request.Note;
            entityHeader.PaymentMethod = request.PaymentMethod;
            entityHeader.RoundingCurrency = toCurrency.Rounding;
            entityHeader.CurrencyConversion = toCurrency.Conversion;
            entityHeader.ExchangeRate = request.ExchangeRate;
            entityHeader.TotalAmount = request.TotalAmount;
            entityHeader.TotalVatAmount = request.TotalVatAmount;
            entityHeader.TotalPaymentAmount = request.TotalPaymentAmount;
            entityHeader.TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax;
            entityHeader.TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax;
            entityHeader.TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax;
            entityHeader.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.BuyerCode = request.BuyerCode?.Trim();
            entityHeader.BuyerEmail = request.BuyerEmail?.Trim();
            entityHeader.BuyerFullName = request.BuyerFullName;
            entityHeader.BuyerLegalName = request.BuyerLegalName;
            entityHeader.BuyerTaxCode = request.BuyerTaxCode?.Trim();
            entityHeader.BuyerAddressLine = request.BuyerAddressLine;
            entityHeader.BuyerDistrictName = request.BuyerDistrictName;
            entityHeader.BuyerCityName = request.BuyerCityName;
            entityHeader.BuyerPhoneNumber = request.BuyerPhoneNumber;
            entityHeader.BuyerFaxNumber = request.BuyerFaxNumber;
            entityHeader.BuyerBankAccount = request.BuyerBankAccount;
            entityHeader.BuyerBankName = request.BuyerBankName;
            entityHeader.ExtraProperties = new ExtraPropertyDictionary(headerExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value));

            var commandInvoiceDetails = new List<TicketDetailModel>();

            foreach (var item in request.InvoiceDetails)
            {
                var product = string.IsNullOrEmpty(item.ProductCode?.Trim()) ? null : (products.ContainsKey(item.ProductCode?.Trim()) ? products[item.ProductCode?.Trim()] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId != null)
                    productType = productTypes.ContainsKey(product.ProductTypeId.Value) ? productTypes[product.ProductTypeId.Value] : null;

                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    ProductCode = item.ProductCode?.Trim(),
                    Index = item.Index,
                    InvoiceHeaderId = entityHeader.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductName = item.ProductName,
                    Quantity = item.Quantity,
                    TenantId = entityHeader.TenantId,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    HideQuantity = productType == null ? false : productType.HideQuantity,
                    HideUnit = productType == null ? false : productType.HideUnit,
                    HideUnitPrice = productType == null ? false : productType.HideUnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue,
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                        TenantId = entityHeader.TenantId
                    }).ToList()
                });
            }

            //var commandHeaderExtras = request.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            //{
            //    FieldValue = x.FieldValue,
            //    InvoiceHeaderId = entityHeader.Id,
            //    TenantId = entityHeader.TenantId,
            //    InvoiceHeaderFieldId = headerFields[x.FieldName],
            //})
            //.ToList();

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = entityHeader.TenantId,
                InvoiceHeaderId = entityHeader.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = 0,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            await UpdateInvoiceDetailAsync(entityInvoiceDetails, commandInvoiceDetails, detailFields);
            //await UpdateInvoiceHeaderExtraAsync(entityInvoiceHeaderExtras, commandHeaderExtras);
            await UpdateInvoiceTaxbreakdownAsync(entityInvoiceTaxBreakDowns, commandTaxBreakDowns);
            //await UpdateInvoiceFileDocumentAsync(entityHeader, request.IdFileDocument, request.DocumentDate, request.DocumentNo, request.DocumentReason);

            entityHeader.LastModificationTime = DateTime.Now;
            entityHeader.LastModifierId = userId;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();
        }

        public async Task UpdateAdjustmentHeaderAsync(Guid userId, TicketHeaderEntity entityHeader, UpdateAdjustmentHeaderTicketApiRequestModel request, Dictionary<string, TicketHeaderFieldEntity> headerFields)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = new Dictionary<string, string>();
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                headerExtraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue
                }).ToList(), headerFields);

            }
            #endregion

            entityHeader.InvoiceDate = request.InvoiceDate;
            entityHeader.InvoiceDateMonth = (short)request.InvoiceDate.Month;
            entityHeader.InvoiceDateNumber = (short)request.InvoiceDate.Day;
            entityHeader.InvoiceDateQuater = (short)request.InvoiceDate.GetQuarter();
            entityHeader.InvoiceDateWeek = (short)request.InvoiceDate.GetWeek();
            entityHeader.InvoiceDateYear = (short)request.InvoiceDate.Year;
            entityHeader.Note = request.Note;
            entityHeader.PaymentMethod = request.PaymentMethod;
            entityHeader.BuyerCode = request.BuyerCode;
            entityHeader.BuyerEmail = request.BuyerEmail?.Trim();
            entityHeader.BuyerFullName = request.BuyerFullName;
            entityHeader.BuyerLegalName = request.BuyerLegalName;
            entityHeader.BuyerTaxCode = request.BuyerTaxCode?.Trim();
            entityHeader.BuyerAddressLine = request.BuyerAddressLine;
            entityHeader.BuyerDistrictName = request.BuyerDistrictName;
            entityHeader.BuyerCityName = request.BuyerCityName;
            entityHeader.BuyerPhoneNumber = request.BuyerPhoneNumber;
            entityHeader.BuyerFaxNumber = request.BuyerFaxNumber;
            entityHeader.BuyerBankAccount = request.BuyerBankAccount;
            entityHeader.BuyerBankName = request.BuyerBankName;
            entityHeader.LastModificationTime = DateTime.Now;
            entityHeader.LastModifierId = userId;
            entityHeader.ExtraProperties = new ExtraPropertyDictionary(headerExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value));

            //var commandHeaderExtras = request.InvoiceHeaderExtras?.Select(x => new TicketHeaderExtraModel
            //{
            //    FieldValue = x.FieldValue,
            //    InvoiceHeaderId = entityHeader.Id,
            //    TenantId = entityHeader.TenantId,
            //    InvoiceHeaderFieldId = headerFields[x.FieldName],
            //    FieldName = x.FieldName
            //})
            //.ToList();

            //await UpdateInvoiceHeaderExtraAsync(entityInvoiceHeaderExtras, commandHeaderExtras);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();
        }

        public async Task UpdateAdjustmentHeaderRawAsync(UpdateAdjustmentHeaderTicketApiRequestModel request, TicketHeaderEntity rootInvoice,
            Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, Dictionary<string, TicketHeaderFieldEntity> headerFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            };
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""TicketHeader""
                                SET 
                                    ""InvoiceDate"" = '{request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{request.Note?.Replace("'", "''")}',
                                    ""PaymentMethod"" = '{request.PaymentMethod?.Replace("'", "''")}',
                                    ""BuyerCode"" = '{request.BuyerCode}',
                                    ""BuyerEmail"" = '{request.BuyerEmail?.Trim()}',
                                    ""BuyerFullName"" = N'{request.BuyerFullName.Replace("'", "''")}',
                                    ""BuyerLegalName"" = N'{request.BuyerLegalName?.Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{request.BuyerTaxCode?.Trim()}',
                                    ""BuyerAddressLine"" = N'{request.BuyerAddressLine?.Replace("'", "''")}',
                                    ""BuyerDistrictName"" = N'{request.BuyerDistrictName?.Replace("'", "''")}',
                                    ""BuyerCityName"" = N'{request.BuyerCityName?.Replace("'", "''")}',
                                    ""BuyerPhoneNumber"" = '{request.BuyerPhoneNumber}',
                                    ""BuyerFaxNumber"" = '{request.BuyerFaxNumber}',
                                    ""BuyerBankAccount"" = '{request.BuyerBankAccount}',
                                    ""BuyerBankName"" = N'{request.BuyerBankName?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{rootInvoice.CreatorErp}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {rootInvoice.Id}; 
                        ");

            #endregion

            var query = rawSql.Append($@" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        public async Task UpdateInvoiceReferenceAsync(Guid userId, TicketHeaderEntity rootInvoice, TicketHeaderEntity replaceOrAdjInvoice)
        {
            //update hóa đơn gốc
            if (replaceOrAdjInvoice == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.UpdateInvoiceReference.InvoiceReferenceNotFound"]);

            // update thông tin hóa đơn gốc
            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
                rootInvoice.InvoiceStatus = (short)InvoiceStatus.BiThayThe;

            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                rootInvoice.InvoiceStatus = (short)InvoiceStatus.BiDieuChinhDinhDanh;

            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                rootInvoice.InvoiceStatus = (short)InvoiceStatus.BiDieuChinhTangGiam;

            // insert thông tin hóa đơn gốc vào bảng reference
            var repoInvoiceReference = _appFactory.Repository<TicketReferenceEntity, long>();
            await repoInvoiceReference.InsertAsync(new TicketReferenceEntity
            {
                CreationTime = DateTime.Now,
                CreatorId = userId,
                InvoiceDateReference = replaceOrAdjInvoice.InvoiceDate,
                InvoiceHeaderId = rootInvoice.Id,
                InvoiceNoReference = replaceOrAdjInvoice.InvoiceNo,
                InvoiceReferenceId = replaceOrAdjInvoice.Id,
                NumberReference = replaceOrAdjInvoice.Number ?? 0,
                SerialNoReference = replaceOrAdjInvoice.SerialNo,
                TemplateNoReference = replaceOrAdjInvoice.TemplateNo,
                InvoiceStatus = rootInvoice.InvoiceStatus,
                TenantId = rootInvoice.TenantId,
                Partition = long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))
            });

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        public async Task UpdateInvoiceReferenceRawAsync(Guid userId, TicketHeaderEntity rootInvoice, long invoiceHeaderId, int templateNo, string serialNo, string invoiceNo, int? number, DateTime invoiceDate)
        {
            // update thông tin hóa đơn gốc
            short invoiceStt = 0;
            if (rootInvoice.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiThayThe;

            if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiDieuChinhDinhDanh;

            if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiDieuChinhTangGiam;

            if (rootInvoice.InvoiceStatus == InvoiceStatus.DieuChinh.GetHashCode())
                invoiceStt = (short)InvoiceStatus.BiDieuChinh;

            var TicketReference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<TicketReferenceEntity>>();
            var TicketRef = await TicketReference.GetInvoiceReferenceRawAsync(invoiceHeaderId);

            StringBuilder raw = new StringBuilder();
            if (TicketRef != null)
            {
                //var rootInvoiceHeader = await repoTicketHeader.GetByIdRawAsync(message.TenantId, TicketRef.InvoiceReferenceId);
                raw.Append($"BEGIN Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                        $"Set \"InvoiceStatus\" = '{invoiceStt}'" +
                        $"Where \"Id\" = {TicketRef.InvoiceReferenceId} ;");

                StringBuilder sqlInsertTicketReference = new StringBuilder();

                sqlInsertTicketReference.Append($@"  INSERT INTO ""TicketReference"" (             
                                                ""CreationTime"",                           
                                                ""CreatorId"",                                  
                                                ""TenantId"",                                      
                                                ""InvoiceHeaderId"",                                 
                                                ""InvoiceReferenceId"",                           
                                                ""TemplateNoReference"",                                
                                                ""SerialNoReference"",                         
                                                ""InvoiceNoReference"",                                  
                                                ""NumberReference"",                                  
                                                ""InvoiceDateReference"",                                  
                                                ""InvoiceStatus"",                                  
                                                ""Partition""                                                                 
                                            )                                                  
                                            VALUES ");

                //Add values
                sqlInsertTicketReference.Append($@"  (                                                               
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                    '{ OracleExtension.ConvertGuidToRaw(userId) }',                                             
                                                    '{ OracleExtension.ConvertGuidToRaw(rootInvoice.TenantId) }',                                               
                                                    { rootInvoice.Id },                                            
                                                    { invoiceHeaderId},                                      
                                                    { templateNo },                                           
                                                    '{ serialNo }',                                              
                                                    '{ invoiceNo}',    
                                                    { number},    
                                                    '{ invoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                    { rootInvoice.InvoiceStatus},    
                                                    { long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))}    
                                                ) ");

                sqlInsertTicketReference.Append("; ");

                if (sqlInsertTicketReference.Length > 0)
                    raw.Append(sqlInsertTicketReference);

                var queryResult = raw.Append($" END; ").ToString();
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);
            }
        }

        public async Task UpdateInvoiceReferenceRawAsync(Guid userId, TicketHeaderEntity rootInvoice, long invoiceHeaderId, int templateNo, string serialNo, string invoiceNo, int? number, DateTime invoiceDate, short invoiceStatus)
        {
            // update thông tin hóa đơn gốc
            var TicketReference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<TicketReferenceEntity>>();
            var TicketRef = await TicketReference.GetInvoiceReferenceRawAsync(invoiceHeaderId);

            StringBuilder raw = new StringBuilder();
            if (TicketRef != null)
            {
                //var rootInvoiceHeader = await repoTicketHeader.GetByIdRawAsync(message.TenantId, TicketRef.InvoiceReferenceId);
                raw.Append($"BEGIN Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                        $"Set \"InvoiceStatus\" = '{invoiceStatus}'" +
                        $"Where \"Id\" = {TicketRef.InvoiceReferenceId} ;");

                StringBuilder sqlInsertTicketReference = new StringBuilder();

                sqlInsertTicketReference.Append($@"  INSERT INTO ""TicketReference"" (             
                                                ""CreationTime"",                           
                                                ""CreatorId"",                                  
                                                ""TenantId"",                                      
                                                ""InvoiceHeaderId"",                                 
                                                ""InvoiceReferenceId"",                           
                                                ""TemplateNoReference"",                                
                                                ""SerialNoReference"",                         
                                                ""InvoiceNoReference"",                                  
                                                ""NumberReference"",                                  
                                                ""InvoiceDateReference"",                                  
                                                ""InvoiceStatus"",                                  
                                                ""Partition""                                                                 
                                            )                                                  
                                            VALUES ");

                //Add values
                sqlInsertTicketReference.Append($@"  (                                                               
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                    '{OracleExtension.ConvertGuidToRaw(userId)}',                                             
                                                    '{OracleExtension.ConvertGuidToRaw(rootInvoice.TenantId)}',                                               
                                                    {rootInvoice.Id},                                            
                                                    {invoiceHeaderId},                                      
                                                    {templateNo},                                           
                                                    '{serialNo}',                                              
                                                    '{invoiceNo}',    
                                                    {number},    
                                                    '{invoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                    {rootInvoice.InvoiceStatus},    
                                                    {long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))}    
                                                ) ");

                sqlInsertTicketReference.Append("; ");

                if (sqlInsertTicketReference.Length > 0)
                    raw.Append(sqlInsertTicketReference);

                var queryResult = raw.Append($" END; ").ToString();
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);
            }
        }

        public async Task UpdateAdjustmentDetailAsync(Guid userId, TicketHeaderEntity entityHeader, List<TicketDetailEntity> entityInvoiceDetails, List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, UpdateAdjustmentDetailTicketApiRequestModel request, Dictionary<string, TicketDetailFieldEntity> detailFields, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted,
                IsTransactional = true
            }, true);

            //entityHeader.Note = entityHeader.Note;
            entityHeader.InvoiceDate = request.InvoiceDate;
            entityHeader.TotalAmount = request.TotalAmount;
            entityHeader.TotalVatAmount = request.TotalVatAmount;
            entityHeader.TotalPaymentAmount = request.TotalPaymentAmount;
            entityHeader.TotalDiscountAmountBeforeTax = request.TotalDiscountAmountBeforeTax;
            entityHeader.TotalDiscountPercentAfterTax = request.TotalDiscountPercentAfterTax;
            entityHeader.TotalDiscountAmountAfterTax = request.TotalDiscountAmountAfterTax;
            entityHeader.PaymentAmountWords = await _invoiceService.ReadMoneyViAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.PaymentAmountWordsEn = await _invoiceService.ReadMoneyEnAsync(entityHeader.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(entityHeader.InvoiceStatus)));
            entityHeader.LastModificationTime = DateTime.Now;
            entityHeader.LastModifierId = userId;

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                var detail = entityInvoiceDetails.FirstOrDefault(x => x.Index == item.Index);
                //var detailExtras = entityInvoiceDetailExtras.Where(x => x.InvoiceDetailId == detail.Id);

                if (detail != null)
                {
                    commandInvoiceDetails.Add(new TicketDetailModel
                    {
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                        DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                        ProductCode = detail.ProductCode?.Trim(),
                        ProductId = detail.ProductId,
                        UnitId = detail.UnitId,
                        Index = item.Index,
                        InvoiceHeaderId = entityHeader.Id,
                        Note = item.Note,
                        PaymentAmount = item.PaymentAmount,
                        ProductName = detail.ProductName,
                        Quantity = item.Quantity,
                        TenantId = entityHeader.TenantId,
                        UnitName = detail.UnitName?.Trim(),
                        UnitPrice = item.UnitPrice,
                        VatAmount = item.VatAmount,
                        VatPercent = item.VatPercent,
                        HideQuantity = detail.HideQuantity,
                        HideUnit = detail.HideUnit,
                        HideUnitPrice = detail.HideUnitPrice,
                        RoundingUnit = detail.RoundingUnit,
                        VatPercentDisplay = taxes[item.VatPercent].Item2,
                        //InvoiceDetailExtras = detailExtras?.Select(y => new TicketDetailExtraModel
                        //{
                        //    FieldValue = y.FieldValue,
                        //    InvoiceDetailFieldId = y.InvoiceDetailFieldId,
                        //    TenantId = entityHeader.TenantId,
                        //    InvoiceDetailId = y.InvoiceDetailId
                        //}).ToList()
                    });
                }
            }

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = entityHeader.TenantId,
                InvoiceHeaderId = entityHeader.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = 0,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            }).ToList();

            await UpdateInvoiceDetailAsync(entityInvoiceDetails, commandInvoiceDetails, detailFields);
            await UpdateInvoiceTaxbreakdownAsync(entityInvoiceTaxBreakDowns, commandTaxBreakDowns);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            await transaction.CompleteAsync();
        }


        public async Task UpdateAdjustmentDetailRawAsync(UpdateAdjustmentDetailTicketApiRequestModel request, TicketHeaderEntity rootInvoice, List<TicketDetailEntity> details, List<TicketTaxBreakdownEntity> taxBreakDowns, Guid tenantId, Guid userId, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            rawSql.Append($"Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                          $"Set \"InvoiceDate\" = '{request.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'," +
                          $"\"TotalAmount\" = {request.TotalAmount}," +
                          $"\"TotalVatAmount\" = {request.TotalVatAmount}, \"LastModifierId\" = '{ rawUserId }', " +
                          $"\"TotalPaymentAmount\" = {request.TotalPaymentAmount}," +
                          $"\"TotalDiscountAmountBeforeTax\" = {request.TotalDiscountAmountBeforeTax}," +
                          $"\"TotalDiscountAmountAfterTax\" = {request.TotalDiscountAmountAfterTax}," +
                          $"\"PaymentAmountWords\" = N'{(await _invoiceService.ReadMoneyViAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Trim().Replace("'", "''")}'," +
                          $"\"PaymentAmountWordsEn\" = N'{(await _invoiceService.ReadMoneyEnAsync(rootInvoice.TenantId, toCurrency, request.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(rootInvoice.InvoiceStatus))))?.Trim().Replace("'", "''")}'" +
                          $"Where \"Id\" = {rootInvoice.Id}; ");

            var commandInvoiceDetails = new List<TicketDetailModel>();
            foreach (var item in request.InvoiceDetails)
            {
                commandInvoiceDetails.Add(new TicketDetailModel
                {
                    Amount = item.Amount,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    Index = item.Index,
                    InvoiceHeaderId = rootInvoice.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    Quantity = item.Quantity,
                    TenantId = tenantId,
                    UnitPrice = item.UnitPrice,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = taxes[item.VatPercent].Item2,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new TicketDetailExtraModel
                    {
                        FieldValue = y.FieldValue?.Replace("'", "''"),
                        InvoiceDetailFieldId = detailFields[y.FieldName].Id,
                        TenantId = tenantId,
                    }).ToList()
                });
            }

            var commandTaxBreakDowns = request.InvoiceTaxBreakdowns?.Select(x => new TicketTaxBreakdownModel
            {
                TenantId = tenantId,
                InvoiceHeaderId = rootInvoice.Id,
                VatAmount = x.VatAmount,
                VatAmountBackUp = x.VatAmount,
                VatPercent = x.VatPercent,
                VatPercentDisplay = taxes[x.VatPercent].Item2,
                Name = taxes[x.VatPercent].Item1
            })
           .ToList();

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            await GenerateUpdateInvoiceDetailAdjustmentDetailAsync(rawSql, rawTenantId, details, detailFields, commandInvoiceDetails, request.InvoiceDate);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, input.InvoiceDate.Date);
            await RawUpdateInvoiceTaxbreakdownAsync(rawSql, rawTenantId, taxBreakDowns, commandTaxBreakDowns, request.InvoiceDate);

            var query = rawSql.Append($" END; ").ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        //, Tenant tenant, Dictionary<TenantMetaData, MetadataModel> tenantInfoMetadata
        public async Task<TicketHeaderDto> CreateRawAsync(CreateTicketRequestModel request, TenantInfoModel tenant, InvoiceSource invoiceSource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, InvoiceStatus invoiceStatus, SignStatus signStatus, ApproveStatus approveStatus, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.TicketHeader)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusDbAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusDbAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",   

                                            ""StoreCode"" ,
                                            ""StoreName"",
                                            ""BudgetUnitCode"",
                                            ""BuyerIDNumber"",
                                            ""BuyerPassportNumber"",
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                           
                                            '{request.StoreCode?.Replace("'", "''")}',
                                            '{request.StoreName?.Replace("'", "''")}',
                                            '{request.BudgetUnitCode?.Replace("'", "''")}',
                                            '{request.BuyerIDNumber?.Replace("'", "''")}',
                                            '{request.BuyerPassportNumber?.Replace("'", "''")}',

                                            { (short)invoiceSource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ _invoiceService.GetTransactionId(invoiceSource, request.TransactionId) }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankName?.Replace("'", "''")}',                                  
                                            '{tenant.BankAccount}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName : null)}',                                
                                            N'{ tenant.FullNameVi?.Replace("'", "''") }',                                                                                             
                                            { toCurrency.Rounding },                                                                                                       
                                            '{ fromCurrency.CurrencyCode }',                                                                                               
                                            { toCurrency.Conversion },                                                                                                     
                                            '{ toCurrency.CurrencyCode }',                                                                                                 
                                            { request.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            { request.TotalAmount },                                                                                                                                      
                                            { request.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail?.Trim() }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { request.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { request.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { request.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { request.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            StringBuilder sqlValueDetails = new StringBuilder();

            var i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmountBeforeTax },                                   
                                                        { item.DiscountPercentBeforeTax },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { (short)item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },   
                                                        { item.VatAmount },                                                 
                                                        { item.VatPercent },                                                
                                                        '{ taxes[item.VatPercent].Item2?.Replace("'", "''") }',    
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT   
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ tax.Item1 }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmount },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ tax.Item2?.Replace("'", "''") }',                                              
                                                              { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmountBeforeTax"",            
                                              ""DiscountPercentBeforeTax"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                      
                                              ""VatAmount"",                          
                                              ""VatPercent"",                         
                                              ""VatPercentDisplay"",                  
                                              ""Partition"",                          
                                              ""IsPromotion"",                        
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (     
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task<TicketHeaderDto> CreateOldDecreeRawAsync(CreateTicketOldDecreeRequestModel request, TenantInfoModel tenant, InvoiceSource invoiceSource, Guid tenantId, Guid userId, string fullNameCreator, string userNameCreator, SignStatus signStatus, ApproveStatus approveStatus, InvoiceTemplateEntity template, CurrencyEntity fromCurrency, CurrencyEntity toCurrency, Dictionary<decimal, Tuple<string, string>> taxes, Dictionary<string, ProductEntity> products, Dictionary<long, ProductTypeEntity> productTypes, Dictionary<string, UnitEntity> units, Dictionary<string, TicketHeaderFieldEntity> headerFields, Dictionary<string, TicketDetailFieldEntity> detailFields)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var headerId = (await GetSEQsNextVal(1, SequenceName.TicketHeader)).FirstOrDefault();
            var lstDetailId = await GetSEQsNextVal(request.InvoiceDetails.Count, SequenceName.TicketDetail);

            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusDbAsync(tenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusDbAsync(tenantId);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var extraProperties = GetHeaderExtraProperties(request.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue?.Replace("'", "''")
                }).ToList(), headerFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""TicketHeader"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",                                                
                                            ""ApproveCancelStatus"",                                                
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",                                                     
                                            ""SellerCode"",                                                
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentMethod"",                                                
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerCode"",                                                    
                                            ""BuyerFullName"",                                                
                                            ""BuyerLegalName"",                                               
                                            ""BuyerTaxCode"",                                                 
                                            ""BuyerAddressLine"",                                             
                                            ""BuyerDistrictName"",                                            
                                            ""BuyerCityName"",                                                
                                            ""BuyerCountryCode"",                                             
                                            ""BuyerPhoneNumber"",                                             
                                            ""BuyerFaxNumber"",                                               
                                            ""BuyerEmail"",                                                   
                                            ""BuyerBankName"",                                                
                                            ""BuyerBankAccount"",                                             
                                                                                                        
                                            ""TotalDiscountAmountBeforeTax"",                                 
                                            ""TotalDiscountAmountAfterTax"",                                  
                                            ""TotalPerAfterTax"",                                             
                                            ""TotalVatAmount"",                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",
                                            ""ReferenceInvoiceType"",
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)invoiceSource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ request.ErpId }',                                                                                                                                          
                                            '{ request.CreatorErp }',                                                                                                                                     
                                            '{ _invoiceService.GetTransactionId(invoiceSource, request.TransactionId) }',                                                                                
                                            { template.Id },                                                                                                               
                                            { request.TemplateNo },                                                                                                                                       
                                            '{ request.SerialNo }',                                                                                                                                       
                                            N'{ request.Note?.Replace("'", "''") }',                                                                                                                                          
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)request.InvoiceStatus.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus},                                                                                                    
                                            {(short)approveCancelStatus},                                                                                                    
                                            {(short)approveDeleteStatus},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',                                                                                                                                            
                                            '{tenant.TenantCode}',                                                                                                        
                                            '{tenant.TaxCode}',                                                                                                        
                                            N'{tenant.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{tenant.District?.Replace("'", "''")}',                                                                                                      
                                            N'{tenant.City?.Replace("'", "''")}',                                                                                                          
                                            '{(!string.IsNullOrEmpty(tenant.Phones) ? tenant.Phones : null)}',                                         
                                            '{(!string.IsNullOrEmpty(tenant.Fax) ? tenant.Fax : null)}',                                             
                                            '{(!string.IsNullOrEmpty(tenant.Email) ? tenant.Email : null)}',                                         
                                            '{tenant.BankName?.Replace("'", "''")}',                                  
                                            '{tenant.BankAccount}',                            
                                            N'{(!string.IsNullOrEmpty(tenant.LegalName) ? tenant.LegalName : null)}',                                
                                            N'{ tenant.FullNameVi?.Replace("'", "''") }',                                                                                             
                                            { toCurrency.Rounding },                                                                                                       
                                            '{ fromCurrency.CurrencyCode }',                                                                                               
                                            { toCurrency.Conversion },                                                                                                     
                                            '{ toCurrency.CurrencyCode }',                                                                                                 
                                            { request.ExchangeRate },                                                                                                                                     
                                            '{ request.PaymentMethod }',                                                                                                                                  
                                            '{ request.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(tenantId, toCurrency, request.TotalPaymentAmount, InvoiceStatus.Goc))?.Replace("'", "''") }',        
                                            { request.TotalAmount },                                                                                                                                      
                                            { request.TotalPaymentAmount },                                                                                                                               
                                            '{ fullNameCreator?.Replace("'", "''") }',                                                                                                                                   
                                            '{ userNameCreator }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ request.BuyerCode?.Trim() }',                                                                                                                              
                                            '{ request.BuyerFullName }',                                                                                                                                  
                                            '{ request.BuyerLegalName?.Replace("'", "''") }',                                                                                                                                 
                                            '{ request.BuyerTaxCode?.Trim() }',                                                                                                                           
                                            '{ request.BuyerAddressLine }',                                                                                                                               
                                            '{ request.BuyerDistrictName?.Replace("'", "''") }',                                                                                                                              
                                            '{ request.BuyerCityName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerCountryCode }',                                                                                                                               
                                            '{ request.BuyerPhoneNumber }',                                                                                                                               
                                            '{ request.BuyerFaxNumber }',                                                                                                                                 
                                            '{ request.BuyerEmail?.Trim() }',                                                                                                                                     
                                            '{ request.BuyerBankName?.Replace("'", "''") }',                                                                                                                                  
                                            '{ request.BuyerBankAccount }',                                                                                                                               
                                            { request.TotalDiscountAmountBeforeTax },                                                                                                                     
                                            { request.TotalDiscountAmountAfterTax },                                                                                                                      
                                            { request.TotalDiscountPercentAfterTax },                                                                                                                     
                                            { request.TotalVatAmount },                                                                                                                                   
                                            1,                                                                                                                                                          
                                            {request.InvoiceDate.Year},                                                                                                                                   
                                            {request.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {request.InvoiceDate.Month},                                                                                                                                  
                                            {request.InvoiceDate.GetWeek()},                                                                                                                              
                                            {request.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            {request.ReferenceInvoiceType.GetHashCode()},
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            #endregion

            #region DETAILS SQL
            StringBuilder sqlValueDetails = new StringBuilder();

            var i = 0;
            foreach (var item in request.InvoiceDetails.OrderBy(x => x.Index))
            {
                var detailId = lstDetailId[i];

                var unit = (units != null && units.ContainsKey(item.UnitName?.Trim()))
                        ? units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((products != null && products.ContainsKey(productCode)) ? products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (productTypes != null && productTypes.ContainsKey(product.ProductTypeId.Value))
                                ? productTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''")
                    }).ToList(), detailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { i + 1 },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                    
                                                        { item.DiscountAmountBeforeTax },                                   
                                                        { item.DiscountPercentBeforeTax },                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                            
                                                        { (short)item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { ((productType != null && productType.HideQuantity) ? 1 : 0) },    
                                                        { ((productType != null && productType.HideUnit) ? 1 : 0) },        
                                                        { ((productType != null && productType.HideUnitPrice) ? 1 : 0) },   
                                                        { item.VatAmount },                                                 
                                                        { item.VatPercent },                                                
                                                        '{ taxes[item.VatPercent].Item2?.Replace("'", "''") }',    
                                                        { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },        
                                                        0,                                                               
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            #region TAXBREAKDOWN SQL
            StringBuilder sqlValueTaxBreakdowns = new StringBuilder();
            foreach (var item in request.InvoiceTaxBreakdowns)
            {
                var tax = taxes[item.VatPercent];

                sqlValueTaxBreakdowns.Append($@"UNION ALL SELECT   
                                                              {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                              { headerId },                                                      
                                                              '{ rawTenantId }',                                             
                                                              '{ tax.Item1 }',                                               
                                                              { item.VatAmount },                                            
                                                              { item.VatAmount },                                      
                                                              { item.VatPercent },                                           
                                                              N'{ tax.Item2?.Replace("'", "''") }',                                              
                                                              { long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }    
                                                          FROM DUAL ");
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""TicketDetail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""DiscountAmountBeforeTax"",            
                                              ""DiscountPercentBeforeTax"",           
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                       
                                              ""HideQuantity"",                       
                                              ""HideUnit"",                           
                                              ""HideUnitPrice"",                      
                                              ""VatAmount"",                          
                                              ""VatPercent"",                         
                                              ""VatPercentDisplay"",                  
                                              ""Partition"",                          
                                              ""IsPromotion"",                        
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            if (sqlValueTaxBreakdowns.Length > 0)
            {
                sqlValueTaxBreakdowns = sqlValueTaxBreakdowns.Remove(0, 9);

                rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (     
                                        ""Id"",
                                        ""InvoiceHeaderId"",                           
                                        ""TenantId"",                                  
                                        ""Name"",                                      
                                        ""VatAmount"",                                 
                                        ""VatAmountBackUp"",                           
                                        ""VatPercent"",                                
                                        ""VatPercentDisplay"",                         
                                        ""Partition""                                  
                                    ) {sqlValueTaxBreakdowns} ; ");
            }

            rawSql.Append($@"  INSERT INTO ""TicketReferenceOldDecree"" (              
                                ""CreationTime"",                           
                                ""InvoiceDateReference"",                                  
                                ""InvoiceHeaderId"",                                      
                                ""InvoiceNoReference"",                                 
                                ""NumberReference"",                                
                                ""SerialNoReference"",                         
                                ""TemplateNoReference"",
                                ""TenantId"",
                                ""InvoiceStatus"",
                                ""Note"",
                                ""Partition""
                            )                                                  
                            VALUES (
                                '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                '{request.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                {headerId},
                                '{request.InvoiceReference.InvoiceNo}',
                                {int.Parse(request.InvoiceReference.InvoiceNo)},
                                '{request.InvoiceReference.SerialNo}',
                                '{request.InvoiceReference.TemplateNo}',
                                '{rawTenantId}',
                                {(short)request.InvoiceStatus.GetHashCode()},
                                N'{request.InvoiceReference.Note?.Replace("'", "''")}',
                                {long.Parse(request.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                            ); ");

            var query = rawSql.Append($" END; ").ToString();

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

            return new TicketHeaderDto
            {
                Id = headerId
            };
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }

        private async Task UpdateInvoiceTaxbreakdownAsync(List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, List<TicketTaxBreakdownModel> invoiceTaxBreakdowns)
        {
            var modelTaxbreakDowns = invoiceTaxBreakdowns.GroupBy(x => x.VatPercent).ToDictionary(x => x.Key, x => x.ToList());
            var removeTax = new List<TicketTaxBreakdownModel>();

            var repoInvoiceTaxbreakdown = _serviceProvider.GetService<IRepository<TicketTaxBreakdownEntity, long>>();

            //sửa
            foreach (var item in entityInvoiceTaxBreakDowns)
            {
                if (modelTaxbreakDowns.ContainsKey(item.VatPercent))
                {
                    var taxBreakdownModel = modelTaxbreakDowns[item.VatPercent].First();
                    item.VatAmount = taxBreakdownModel.VatAmount;
                    item.VatAmountBackUp = taxBreakdownModel.VatAmountBackUp;
                    item.Name = taxBreakdownModel.Name;
                }
                else //xóa
                    await repoInvoiceTaxbreakdown.DeleteAsync(item);
            }

            //thêm mới
            foreach (var item in modelTaxbreakDowns)
            {
                var newTaxBreakDown = item.Value.FirstOrDefault();
                if (!entityInvoiceTaxBreakDowns.Any(x => x.VatPercent == item.Key))
                {
                    await repoInvoiceTaxbreakdown.InsertAsync(new TicketTaxBreakdownEntity
                    {
                        Name = newTaxBreakDown.Name,
                        VatPercent = newTaxBreakDown.VatPercent,
                        VatAmount = newTaxBreakDown.VatAmount,
                        VatAmountBackUp = newTaxBreakDown.VatAmountBackUp,
                        InvoiceHeaderId = newTaxBreakDown.InvoiceHeaderId,
                        VatPercentDisplay = newTaxBreakDown.VatPercentDisplay,
                        TenantId = newTaxBreakDown.TenantId
                    });
                }
            }
        }

        private async Task UpdateInvoiceDetailAsync(IEnumerable<TicketDetailEntity> entityInvoiceDetails, List<TicketDetailModel> modelDetails, Dictionary<string, TicketDetailFieldEntity> entityDetailFields)
        {
            var repoInvoiceDetail = _serviceProvider.GetService<IRepository<TicketDetailEntity, long>>();
            //var repoInvoiceDetailExtra = _serviceProvider.GetService<IInvoiceDetailExtraRepository<TicketDetailExtraEntity>>();
            var removeDetails = new List<TicketDetailEntity>();
            //var removeDetailExtras = new List<TicketDetailExtraEntity>();

            //var detailExtras = (await repoInvoiceDetailExtra.QueryByInvoiceDetailIdsAsync(entityInvoiceDetails.Select(x => x.Id).ToList()))
            //                   .GroupBy(x => x.InvoiceDetailId)
            //                   .ToDictionary(x => x.Key, x => x.ToList());

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityInvoiceDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityInvoiceDetails)
            {
                if (duplicate.Contains(item.Index)) // sửa
                {
                    var detailExtraProperties = new Dictionary<string, string>();
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        detailExtraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue,
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                    }

                    item.Amount = detail.Amount;
                    item.DiscountAmountBeforeTax = detail.DiscountAmountBeforeTax;
                    item.DiscountPercentBeforeTax = detail.DiscountPercentBeforeTax;
                    item.Index = detail.Index;
                    item.Note = detail.Note;
                    item.PaymentAmount = detail.PaymentAmount;
                    item.ProductCode = detail.ProductCode?.Trim();
                    item.ProductName = detail.ProductName;
                    item.Quantity = detail.Quantity;
                    item.UnitName = detail.UnitName?.Trim();
                    item.VatAmount = detail.VatAmount;
                    item.VatPercent = detail.VatPercent;
                    item.VatPercentDisplay = detail.VatPercentDisplay;
                    item.HideQuantity = detail.HideQuantity;
                    item.HideUnit = detail.HideUnit;
                    item.HideUnitPrice = detail.HideUnitPrice;
                    item.UnitPrice = detail.UnitPrice;
                    item.ExtraProperties = new ExtraPropertyDictionary(detailExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value));
                    //var entityDetailExtra = detailExtras.ContainsKey(item.Id) ? detailExtras[item.Id] : null;
                    //await UpdateInvoiceDetailExtrasAsync(detail.TenantId, item.Id, detail.InvoiceDetailExtras, entityDetailExtra);
                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                    //if (detailExtras.ContainsKey(item.Id))
                    //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                }
            }

            //var detailExtraRepo = _serviceProvider.GetService<IRepository<TicketDetailExtraEntity, long>>();

            //thêm mới
            foreach (var item in newDetails)
            {
                var newDetail = new TicketDetailEntity
                {
                    Amount = item.Amount,
                    Note = item.Note,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    Index = item.Index,
                    Quantity = item.Quantity,
                    DiscountAmountBeforeTax = item.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = item.DiscountPercentBeforeTax,
                    PaymentAmount = item.PaymentAmount,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = item.VatPercentDisplay,
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    TenantId = item.TenantId,
                    HideQuantity = item.HideQuantity,
                    HideUnit = item.HideUnit,
                    HideUnitPrice = item.HideUnitPrice,
                };

                await repoInvoiceDetail.InsertAsync(newDetail);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                //thêm các detail extra
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtraProperties = GetDetailExtraProperties(newDetail.Id, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue,
                        FieldName = x.FieldName,
                    }).ToList(), entityDetailFields);

                    newDetail.ExtraProperties = new ExtraPropertyDictionary(detailExtraProperties.ToDictionary(x => x.Key, x => (object)x.Value));
                    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
                }
            }

            foreach (var item in removeDetails)
                await repoInvoiceDetail.DeleteAsync(item);

            //TODO: thêm xóa detail extra tương ứng
            //foreach (var item in removeDetailExtras)
            //    await detailExtraRepo.DeleteAsync(item);
        }

        private async Task RawUpdateInvoiceTaxbreakdownAsync(StringBuilder rawSql, string rawTenantId, List<TicketTaxBreakdownEntity> entityInvoiceTaxBreakDowns, List<TicketTaxBreakdownModel> invoiceTaxBreakdowns, DateTime invoiceDate)
        {
            var modelTaxbreakDowns = invoiceTaxBreakdowns.GroupBy(x => x.VatPercent).ToDictionary(x => x.Key, x => x.ToList());
            var lstAdd = new List<TicketTaxBreakdownEntity>();
            var lstModify = new List<TicketTaxBreakdownEntity>();
            var lstRemove = new List<TicketTaxBreakdownEntity>();

            // SỬA
            foreach (var item in entityInvoiceTaxBreakDowns)
            {
                if (modelTaxbreakDowns.ContainsKey(item.VatPercent))
                {
                    var taxBreakdownModel = modelTaxbreakDowns[item.VatPercent].First();
                    item.VatAmount = taxBreakdownModel.VatAmount;
                    item.VatAmountBackUp = taxBreakdownModel.VatAmountBackUp;
                    item.Name = taxBreakdownModel.Name;

                    lstModify.Add(item);
                }
                else // XÓA
                    lstRemove.Add(item);
            }

            // THÊM MỚI
            foreach (var item in modelTaxbreakDowns)
            {
                var newTaxBreakDown = item.Value.FirstOrDefault();
                if (!entityInvoiceTaxBreakDowns.Any(x => x.VatPercent == item.Key))
                {
                    lstAdd.Add(new TicketTaxBreakdownEntity
                    {
                        Name = newTaxBreakDown.Name,
                        VatPercent = newTaxBreakDown.VatPercent,
                        VatAmount = newTaxBreakDown.VatAmount,
                        VatAmountBackUp = newTaxBreakDown.VatAmountBackUp,
                        InvoiceHeaderId = newTaxBreakDown.InvoiceHeaderId,
                        VatPercentDisplay = newTaxBreakDown.VatPercentDisplay,
                        TenantId = newTaxBreakDown.TenantId
                    });
                }
            }

            if (lstAdd != null && lstAdd.Any())
            {
                var rawTaxBreakdown = new StringBuilder();

                foreach (var iAdd in lstAdd)
                {
                    rawTaxBreakdown.Append($@"UNION ALL SELECT
                                                            {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_TicketTaxBreakdown}""'),
                                                            N'{iAdd.Name}',
                                                            {iAdd.VatPercent.ToString().Replace(',', '.')},
                                                            {iAdd.VatAmount.ToString().Replace(',', '.')},
                                                            {iAdd.VatAmountBackUp.ToString().Replace(',', '.')},
                                                            {iAdd.InvoiceHeaderId},
                                                            N'{iAdd.VatPercentDisplay?.Replace("'", "''")}',
                                                            '{rawTenantId}',
                                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) }
                                                         FROM DUAL ");
                }

                if (rawTaxBreakdown.Length > 0)
                {
                    rawTaxBreakdown = rawTaxBreakdown.Remove(0, 9);

                    rawSql.Append($@"  INSERT INTO ""TicketTaxBreakdown"" (
                                            ""Id"",
                                            ""Name"",
                                            ""VatPercent"",
                                            ""VatAmount"",
                                            ""VatAmountBackUp"",
                                            ""InvoiceHeaderId"",
                                            ""VatPercentDisplay"",
                                            ""TenantId"",
                                            ""Partition""
                                        ) {rawTaxBreakdown} ");
                }

                rawSql.Append("; ");
            }

            if (lstModify != null && lstModify.Any())
            {
                foreach (var iModify in lstModify)
                {
                    rawSql.Append($@"   UPDATE ""TicketTaxBreakdown""
                                        SET
                                            ""VatAmount"" = {iModify.VatAmount},
                                            ""VatAmountBackUp"" = {iModify.VatAmountBackUp},
                                            ""Name"" = N'{iModify.Name?.Replace("'", "''")}'
                                        WHERE ""Id"" = {iModify.Id};
                                    ");
                }
            }

            if (lstRemove != null && lstRemove.Any())
            {
                rawSql.Append($@"   DELETE FROM ""TicketTaxBreakdown"" WHERE ""Id"" IN ( { String.Join(",", lstRemove.Select(x => x.Id)) } ); ");
            }
        }

        private async Task RawUpdateInvoiceDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<TicketDetailEntity> entityDetails, Dictionary<string, TicketDetailFieldEntity> entityDetailFields, List<TicketDetailModel> modelDetails, DateTime invoiceDate)
        {
            //đánh lại index detail
            int k = 1;
            foreach (var item in modelDetails.OrderBy(x => x.Index))
            {
                item.Index = k;
                k++;
            }

            var removeDetails = new List<TicketDetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index)).OrderBy(x => x.Index);

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue?.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""TicketDetail""
                                        SET
                                            ""Amount"" = {detail.Amount.ToString().Replace(',', '.')},
                                            ""DiscountAmountBeforeTax"" = {detail.DiscountAmountBeforeTax.ToString().Replace(',', '.')},
                                            ""DiscountPercentBeforeTax"" = {detail.DiscountPercentBeforeTax.ToString().Replace(',', '.')},
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = {detail.PaymentAmount.ToString().Replace(',', '.')},
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName?.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" = {detail.Quantity.ToString().Replace(',', '.')},
                                            ""UnitName"" = N'{detail.UnitName?.Trim()?.Replace("'", "''")}',
                                            ""VatAmount"" = {detail.VatAmount.ToString().Replace(',', '.')},
                                            ""VatPercent"" = {detail.VatPercent.ToString().Replace(',', '.')},
                                            ""VatPercentDisplay"" = N'{detail.VatPercentDisplay?.Replace("'", "''")}',
                                            ""HideQuantity"" = {(detail.HideQuantity ? 1 : 0)},
                                            ""HideUnit"" = {(detail.HideUnit ? 1 : 0)},
                                            ""HideUnitPrice"" = {(detail.HideUnitPrice ? 1 : 0)},
                                            ""UnitPrice"" = {detail.UnitPrice.ToString().Replace(',', '.')},
                                            ""ExtraProperties"" = N'{detailExtraProperties}'
                                        WHERE ""Id"" = {item.Id};
                                    ");

                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                }
            }

            // THÊM MỚI
            if (newDetails != null && newDetails.Any())
            {
                var lstDetailId = await GetSEQsNextVal(newDetails.Count(), SEQ_Name.SEQ_TicketDetail);

                var i = 0;
                foreach (var item in newDetails)
                {
                    var detailId = lstDetailId[i];

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue?.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"  INSERT INTO ""TicketDetail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""DiscountAmountBeforeTax"",
                                            ""DiscountPercentBeforeTax"",
                                            ""PaymentAmount"",
                                            ""VatAmount"",
                                            ""VatPercent"",
                                            ""VatPercentDisplay"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""HideQuantity"",
                                            ""HideUnit"",
                                            ""HideUnitPrice"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""IsPromotion"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {detailId},
                                            {item.Amount.ToString().Replace(',', '.')},
                                            N'{item.Note?.Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()}',
                                            N'{item.ProductName?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{item.UnitName?.Trim()?.Replace("'", "''")}',
                                            {item.UnitPrice.ToString().Replace(',', '.')},
                                            {item.Index},
                                            {item.Quantity.ToString().Replace(',', '.')},
                                            {item.DiscountAmountBeforeTax.ToString().Replace(',', '.')},
                                            {item.DiscountPercentBeforeTax.ToString().Replace(',', '.')},
                                            {item.PaymentAmount.ToString().Replace(',', '.')},
                                            {item.VatAmount.ToString().Replace(',', '.')},
                                            {item.VatPercent.ToString().Replace(',', '.')},
                                            N'{item.VatPercentDisplay?.Replace("'", "''")}',
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}',
                                            { (item.HideQuantity ? 1 : 0) },    
                                            { (item.HideUnit ? 1 : 0) },        
                                            { (item.HideUnitPrice ? 1 : 0) },   
                                            0,
                                            4,
                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
                                            0,
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        ); ");

                    i++;
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""TicketDetail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }

        private Dictionary<string, string> GetHeaderExtraProperties(List<CommonHeaderExtraModel> invoiceHeaderExtras, Dictionary<string, TicketHeaderFieldEntity> entityHeaderFields)
        {
            var headerExtraProperties = new Dictionary<string, string>();

            var headerExtras = new List<InvoiceHeaderExtraModel>();
            foreach (var item in invoiceHeaderExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityHeaderFields.ContainsKey(item.FieldName))
                    continue;

                var field = entityHeaderFields[item.FieldName];
                headerExtras.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = item.FieldName,
                    FieldValue = item.FieldValue,
                    InvoiceHeaderFieldId = field.Id
                });

            }
            headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));

            return headerExtraProperties;
        }

        private Dictionary<string, string> GetDetailExtraProperties(long detailId, List<CommonDetailExtraModel> invoiceDetailExtras, Dictionary<string, TicketDetailFieldEntity> entityDetailFields)
        {
            var detailExtraProperties = new Dictionary<string, string>();

            var detailExtras = new List<InvoiceDetailExtraModel>();
            foreach (var extra in invoiceDetailExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityDetailFields.ContainsKey(extra.FieldName))
                    continue;

                var field = entityDetailFields[extra.FieldName];
                detailExtras.Add(new InvoiceDetailExtraModel
                {
                    InvoiceDetailId = detailId,
                    InvoiceDetailFieldId = field.Id,
                    FieldValue = extra.FieldValue,
                    FieldName = field.FieldName,
                });
            }
            detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));

            return detailExtraProperties;
        }

        private async Task GenerateUpdateInvoiceDetailAdjustmentDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<TicketDetailEntity> entityDetails, Dictionary<string, TicketDetailFieldEntity> entityDetailFields, List<TicketDetailModel> modelDetails, DateTime invoiceDate)
        {
            var removeDetails = new List<TicketDetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""TicketDetail""
                                        SET
                                            ""Amount"" = {detail.Amount},
                                            ""DiscountAmountBeforeTax"" = {detail.DiscountAmountBeforeTax},
                                            ""DiscountPercentBeforeTax"" = {detail.DiscountPercentBeforeTax},
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = {detail.PaymentAmount},
                                            ""Quantity"" = {detail.Quantity},
                                            ""VatAmount"" = {detail.VatAmount},
                                            ""VatPercent"" = {detail.VatPercent},
                                            ""VatPercentDisplay"" = N'{detail.VatPercentDisplay?.Replace("'", "''")}',
                                            ""UnitPrice"" = {detail.UnitPrice}
                                        WHERE ""Id"" = {item.Id};
                                    ");
                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                    //if (detailExtras.ContainsKey(item.Id))
                    //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""TicketDetail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }
    }
}
