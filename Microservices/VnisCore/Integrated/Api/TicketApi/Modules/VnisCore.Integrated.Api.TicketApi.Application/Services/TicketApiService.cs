using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Invoice.Services;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Shared.Validations;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Integrated.Api.TicketApi.Application.Business;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands.InvoiceError;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands.InvoiceError;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Queries;

namespace VnisCore.Integrated.Api.TicketApi.Application
{
    [Route("api/05tvdt")]
    [Authorize]
    public class TicketApiService : ApplicationService, ITicketApiService
    {
        private readonly IAppFactory _factory;
        private readonly IValidatorFactory _validatorFactory;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly ISettingService _settingService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ICancelTicketBusiness _cancelTicketBusiness;
        private readonly IRedisCacheService _redisCacheService;

        public TicketApiService(IAppFactory appFactory,
            IValidatorFactory validatorFactory,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            ISettingService settingService,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ICancelTicketBusiness cancelTicketBusiness,
            IRedisCacheService redisCacheService)
        {
            _factory = appFactory;
            _validatorFactory = validatorFactory;
            _invoiceService = invoiceService;
            _settingService = settingService;
            _localizer = localizer;
            _cancelTicketBusiness = cancelTicketBusiness;
            _redisCacheService = redisCacheService;
        }

        /// <summary>
        /// Tra cứu hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PagedResultDto<PagingTicketResponseModel>> Query([FromBody] PagingTicketRequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// Tra cứu hóa đơn với method POST
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PagedResultDto<PagingTicketResponseModel>> QueryPost([FromBody] PagingTicketRequestModel request)
        {
            var response = await _factory.Mediator.Send(request);
            return response;
        }

        /// <summary>
        /// gửi mail
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("send-mail")]
        public async Task<object> SendMail([FromBody] List<SendMailTicketApiRequestModel> requests)
        {
            try
            {
                foreach (var request in requests)
                {
                    var validator = _validatorFactory.GetValidator<SendMailTicketApiRequestModel>();

                    var validationResult = await validator.HandleAsync(request);
                    if (!validationResult.Success)
                        throw new UserFriendlyException(L[validationResult.Message]);

                    await _factory.Mediator.Send(request);
                }

                return new ResultApiViewModel(true, 0, "Thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// tạo hóa đơn theo lô
        /// </summary>
        /// <param name="request"></param>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("create-batch")]
        public async Task<object> CreateBatchAsync([FromQuery] CreateTicketInfoRequestModel infoRequestModel, [FromBody] List<CreateTicketRequestModel> request)
        {
            var tenantId = _factory.CurrentTenant.Id.Value;
            var result = new ResultApiModel<List<object>>
            {
                Data = new List<object>(),
            };

            try
            {
                var hasApprove = await _invoiceService.HasApproveDbAsync(tenantId);
                var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
                if (setting == null)
                {
                    setting = await _settingService.GetByCodeAsync(Guid.Empty, SettingKey.AutoSignServer.ToString());
                }

                var inputErpIds = request.Select(x => x.ErpId).ToList();
                var duplicateInputErpIds = inputErpIds.GroupBy(x => x).Where(x => x.Count() > 1).ToList();
                if (duplicateInputErpIds.Any())
                    throw new UserFriendlyException(_localizer["Vnis.BE.Ticket.Api.Intergration.DuplicateErpId", string.Join(", ", duplicateInputErpIds.Select(x => x.Key))]);

                for (int i = 0; i < request.Count; i++)
                {
                    var item = request.ElementAt(i);
                    try
                    {
                        if (!string.IsNullOrEmpty(item.BuyerEmail))
                        {
                            if (!item.BuyerEmail.IsEmails(';') || item.BuyerEmail.Count(x => x == '@') > 1)
                            {
                                throw new Exception(_localizer["Vnis.BE.Ticket.Api.Intergration.BuyerEmailNotAllowMoreThanOne"]);
                            }
                        }

                        item.TemplateNo = infoRequestModel.TemplateNo;
                        item.SerialNo = infoRequestModel.SerialNo;

                        var validator = _validatorFactory.GetValidator<CreateTicketRequestModel>();
                        var validationResult = await validator.HandleAsync(item);
                        if (!validationResult.Success)
                        {
                            // Xóa key ErpId trường hợp lỗi HD
                            var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, item.ErpId);
                            _redisCacheService.Remove(key);

                            throw new UserFriendlyException(L[validationResult.Message]);
                        }

                        var response = await _factory.Mediator.Send(item);

                        if (!hasApprove && setting != null && setting.Value == "1")
                        {
                            var invoice = new TicketHeaderEntity
                            {
                                Id = response.Id,
                                ErpId = response.ErpId,
                                TransactionId = response.TransactionId,
                                TemplateNo = response.TemplateNo,
                                SerialNo = response.SerialNo,
                                InvoiceNo = response.InvoiceNo,
                                InvoiceStatus = (short)response.InvoiceStatus,
                                SignStatus = (short)response.SignStatus,
                            };
                            var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

                            await signService.SignServer(invoice);

                            response.SignStatus = SignStatus.DaKy;
                        }

                        result.Data.Add(response);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                        // Xóa key ErpId trường hợp lỗi HD
                        var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, item.ErpId);
                        _redisCacheService.Remove(key);
                        throw ex;
                    }
                }

                result.Succeeded = true;
                return new ResultApiViewModel<List<object>>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                result.Succeeded = false;
                result.Message = $"Hóa đơn thứ {result.Data.Count + 1} Id = {request.ElementAt(result.Data.Count).ErpId} lỗi: {ex.Message}";

                if (ex.Data.Count > 0)
                {
                    result.Code = 15009;
                    result.Data.Add(ex.Data);
                }

                return new ResultApiViewModel<List<object>>(result);
            }
            //TODO: sau làm lại response cho api như 2.3 để có thể trả ra response create-batch hợp lý hơn trong trường hợp đã tạo đc x hóa đơn, tới hóa đơn x+1 lỗi thì vẫn trả ra thông tin x hóa đơn đã được tạo kèm thông tin lỗi
        }

        /// <summary>
        /// sửa hóa đơn gốc
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("update/{erpId}")]
        public async Task<object> Update([FromRoute] string erpId, [FromBody] UpdateTicketApiRequestModel request)
        {
            try
            {
                var validator = _validatorFactory.GetValidator<UpdateTicketApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<UpdateTicketApiResponseModel>(new ResultApiModel<UpdateTicketApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// Tạo hóa đơn điều chỉnh nhiều lần
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("adjust")]
        public async Task<object> CreateAdjust([FromBody] CreateAdjustTicketApiRequestModel request)
        {
            try
            {
                request.InvoiceDate = request.InvoiceDate.Date;

                var tenantId = _factory.CurrentTenant.Id.Value;
                var validator = _validatorFactory.GetValidator<CreateAdjustTicketApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    // Xóa key ErpId trường hợp lỗi HD
                    var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, request.ErpId);
                    _redisCacheService.Remove(key);

                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var response = await _factory.Mediator.Send(request);

                var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
                var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
                if (!hasApprove && setting != null && setting.Value == "1")
                {
                    var invoice = new TicketHeaderEntity
                    {
                        Id = response.Id,
                        ErpId = response.ErpId,
                        TransactionId = response.TransactionId,
                        TemplateNo = response.TemplateNo,
                        SerialNo = response.SerialNo,
                        InvoiceNo = response.InvoiceNo,
                        InvoiceStatus = (short)response.InvoiceStatus,
                        SignStatus = (short)response.SignStatus,
                    };
                    var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

                    var signResponse = await signService.SignServer(invoice);

                    response.SignStatus = signResponse.SignStatus;
                }

                return new ResultApiViewModel<CreateAdjustTicketResponseModel>(new ResultApiModel<CreateAdjustTicketResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }

                // Xóa key ErpId trường hợp lỗi HD
                var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, request.ErpId);
                _redisCacheService.Remove(key);
            }
        }

        [HttpPost("adjust/update/{erpId}")]
        public async Task<object> UpdateAdjust([FromRoute] string erpId, [FromBody] UpdateAdjustTicketApiRequestModel request)
        {
            try
            {
                request.InvoiceDate = request.InvoiceDate.Date;
                var validator = _validatorFactory.GetValidator<UpdateAdjustTicketApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<UpdateAdjustTicketApiResponseModel>(new ResultApiModel<UpdateAdjustTicketApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }
        /// <summary>
        /// Tạo hóa đơn Thay thế nhiều lần
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("replace")]
        public async Task<object> CreateReplace([FromBody] CreateReplaceTicketApiRequestModel request)
        {
            try
            {
                request.InvoiceDate = request.InvoiceDate.Date;

                var tenantId = _factory.CurrentTenant.Id.Value;
                var validator = _validatorFactory.GetValidator<CreateReplaceTicketApiRequestModel>();
                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                {
                    // Xóa key ErpId trường hợp lỗi HD
                    var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, request.ErpId);
                    _redisCacheService.Remove(key);

                    throw new UserFriendlyException(L[validationResult.Message]);
                }

                var response = await _factory.Mediator.Send(request);

                var hasApprove = await _invoiceService.HasApproveAsync(tenantId);
                var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.AutoSignServer.ToString());
                if (!hasApprove && setting != null && setting.Value == "1")
                {
                    var invoice = new TicketHeaderEntity
                    {
                        Id = response.Id,
                        ErpId = response.ErpId,
                        TransactionId = response.TransactionId,
                        TemplateNo = response.TemplateNo,
                        SerialNo = response.SerialNo,
                        InvoiceNo = response.InvoiceNo,
                        InvoiceStatus = (short)response.InvoiceStatus,
                        SignStatus = (short)response.SignStatus,
                    };
                    var signService = _factory.GetServiceDependency<ISignServerHttpClient>();

                    var signResponse = await signService.SignServer(invoice);

                    response.SignStatus = signResponse.SignStatus;
                }

                return new ResultApiViewModel<CreateReplaceTicketResponseModel>(new ResultApiModel<CreateReplaceTicketResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (ex.Data.Count > 0)
                {
                    return new ResultApiViewModel<object>
                    {
                        Code = 15009,
                        Message = ex.Message,
                        Succeeded = false,
                        Data = ex.Data,
                    }; //TODO: 
                }
                else
                {
                    return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
                }
                // Xóa key ErpId trường hợp lỗi HD
                var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _factory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, request.ErpId);
                _redisCacheService.Remove(key);
            }
        }

        [HttpPost("replace/update/{erpId}")]
        public async Task<object> ReplaceAdjust([FromRoute] string erpId, [FromBody] UpdateReplaceTicketApiRequestModel request)
        {
            try
            {
                request.InvoiceDate = request.InvoiceDate.Date;
                var validator = _validatorFactory.GetValidator<UpdateReplaceTicketApiRequestModel>();
                request.ErpId = erpId;

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<UpdateReplaceTicketApiResponseModel>(new ResultApiModel<UpdateReplaceTicketApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// Xóa hóa đơn
        /// </summary>
        /// <param name="infoRequestModel"></param>
        /// <returns></returns>
        [HttpPost("delete/{erpId}")]
        public async Task<object> DeleteAsync([FromRoute] string erpId)
        {
            try
            {
                var request = new DeleteTicketApiRequestModel
                {
                    ErpId = erpId
                };
                var validator = _validatorFactory.GetValidator<DeleteTicketApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);

                //TODO: Bổ sung logic xóa hóa đơn khỏi BTH

                return new ResultApiViewModel<DeleteTicketApiResponseModel>(new ResultApiModel<DeleteTicketApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// API ký hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("sign-server/{erpId}")]
        public async Task<object> Sign([FromRoute] string erpId)
        {
            try
            {

                var request = new TicketApiSignServerRequestModel
                {
                    ErpId = erpId
                };
                var validator = _validatorFactory.GetValidator<TicketApiSignServerRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _factory.Mediator.Send(request);
                return new ResultApiViewModel<TicketApiSignServerResponseModel>(new ResultApiModel<TicketApiSignServerResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

        /// <summary>
        /// In thể hiện
        /// </summary>
        /// <returns></returns>
        [HttpPost("unofficials")]
        public async Task<object> DownloadUnOfficials([FromBody] List<string> erpIds)
        {
            var result = new ResultApiModel<List<object>>
            {
                Data = new List<object>(),
            };
            try
            {
                foreach (var erpId in erpIds)
                {
                    var request = new DownloadUnOfficialTicketRequestModel
                    {
                        ErpId = erpId
                    };

                    var response = await _factory.Mediator.Send(request);

                    var invoice = new ResultModel<PrintApiModel>
                    {
                        Code = 0,
                        Data = response,
                        Errors = null,
                        Message = "Thành công",
                        Succeeded = true
                    };
                    result.Data.Add(invoice);
                }
                result.Succeeded = true;
                return new ResultApiViewModel<List<object>>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// Tạo thông báo sai sót ver2
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/create")]
        public async Task<CreateTicketErrorTvanResponseModel> CreateInvoiceTvanErrorAsync([FromBody] CreateTicketErrorTvanRequestModel request)
        {
            return await _factory.Mediator.Send(request);
        }

        /// <summary>
        /// Update thông báo sai sót
        /// </summary>
        /// <returns></returns>
        [HttpPost("invoice-error/update")]
        public async Task<UpdateTicketTvanErrorResponseModel> UpdateInvoiceTvanErrorAsync([FromBody] UpdateTicketTvanErrorRequestModel request)
        {
            return await _factory.Mediator.Send(request);
        }

        /// <summary>
        /// xóa thông báo sai sót
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("invoice-error/delete")]
        public async Task<DeleteTicketErrorTvanResponse> DeleteInvoiceErrorAsync(DeleteTicketTvanErrorRequestModel request)
        {
            return await _factory.Mediator.Send(request);
        }

        /// <summary>
        /// ký sai sót ticket
        /// </summary>
        /// <param name="ErpId"></param>
        /// <returns></returns>
        [HttpPost("signserver/ticket-error")]
        public async Task<object> SignTicketErrorAsync([FromBody] List<string> ErpIds)
        {
            try
            {
                var result = await _factory.Mediator.Send(new SignServerTicketErrorRequestModel
                {
                    ErpIds = ErpIds
                });

                return new ResultApiViewModel<List<SignServerTicketErrorResponseModel>>
                {
                    Code = 0,
                    Data = result,
                    Errors = null,
                    Message = "Thành công",
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message);
            }
        }

        /// <summary>
        /// Xóa hủy hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("cancel/{erpId}")]
        public async Task<object> CancelAsync([FromRoute] string erpId)
        {
            try
            {
                var request = new CancelTicketApiRequestModel
                {
                    ErpId = erpId
                };

                var validator = _validatorFactory.GetValidator<CancelTicketApiRequestModel>();

                var validationResult = await validator.HandleAsync(request);
                if (!validationResult.Success)
                    throw new UserFriendlyException(L[validationResult.Message]);

                var response = await _cancelTicketBusiness.CancelAsync(request);

                return new ResultApiViewModel<CancelTicketApiResponseModel>(new ResultApiModel<CancelTicketApiResponseModel>(response));
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                return new ResultApiViewModel(false, -1, ex.Message); //TODO: 
            }
        }

    }
}
