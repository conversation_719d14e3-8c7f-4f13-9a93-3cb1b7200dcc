using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;

using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Integrated.Api.TicketApi.Application.Business;
using VnisCore.Integrated.Api.TicketApi.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Services;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Workers;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;
using VnisCore.Integrated.Api.TicketApi.Application.Services;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Approve;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.ApproveAndSign;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Cancel;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Create;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjHeaderWithoutInvoiceNo;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjust;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjustmentDetail;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjustmentHeader;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateDocumentInfo;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateOldDecree;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateReplace;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Delete;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.DeleteDocumentInfo;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.SendMail;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.SendMailWithPrintAction;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Sign;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Update;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjusmentHeader;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjust;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjustmentDetail;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjustmentHeader;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateDocumentInfo;
using VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateReplace;
using VnisCore.Ticket.Infrastructure;

namespace VnisCore.Integrated.Api.TicketApi.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule),
        typeof(VnisCoreMongoDbModule),
        typeof(VnisCoreTicketInfrastructureModule)
    )]
    public class VnisCoreTicketApiApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<ITaxService, TaxService>();
            context.Services.AddScoped<INumberService, NumberServiceExtend>();
            context.Services.AddScoped(typeof(IInvoiceService<,,>), typeof(InvoiceService<,,>));
            context.Services.AddScoped<ITicketService, TicketService>();
            //context.Services.AddScoped<IExportPdfHttpClient, ExportPdfGrpc>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            //context.Services.AddScoped<ISignServerHttpClient, SignServerHttpClient>();
            //context.Services.AddScoped<ISendMailHttpClient, SendMailHttpClient>();

            context.Services.AddScoped(typeof(IInvoiceHeaderRepository<>), typeof(InvoiceHeaderRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<ITicketHeaderRepository, TicketHeaderRepository>();
            context.Services.AddScoped<ITicketHeaderFieldRepository, TicketHeaderFieldRepository>();
            context.Services.AddScoped<ITicketDetailFieldRepository, TicketDetailFieldRepository>();
            context.Services.AddScoped<ITicketTaxBreakdownRepository, TicketTaxBreakdownRepository>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IMonitorInvoiceTemplateRepository, MonitorInvoiceTemplateRepository>();
            context.Services.AddScoped<INewRegistrationHeaderRepository, NewRegistrationHeaderRepository>();
            context.Services.AddScoped<ITicketDocumentService, TicketDocumentService>();
            context.Services.AddScoped<ITicketErpIdRepository, TicketErpIdRepository>();

            // business
            context.Services.AddScoped<ICancelTicketBusiness, CancelTicketBusiness>();
            context.Services.AddScoped<ITicketCacheBusiness, TicketCacheBusiness>();


            context.Services.Configure<DistributedCacheEntryOptions>(options =>
            {
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
            });

            AddTicketApiValidators(context.Services);
            AddRabbitervices(context.Services, context.Services.GetConfiguration());
            AddExportPdfServices(context.Services, context.Services.GetConfiguration());
            AddSignServerServices(context.Services, context.Services.GetConfiguration());
            AddSendMailServices(context.Services, context.Services.GetConfiguration());

            context.Services.AddAutoMapperObjectMapper<VnisCoreTicketApiApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreTicketApiApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreTicketApiApplicationModule).GetTypeInfo().Assembly);
        }

        public static void AddExportPdfServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("exportpdfs", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:ExportPdf:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSignServerServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("signserver", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:Sign:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:Sign:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISignServerHttpClient, SignServerHttpClient>();
        }

        public static void AddSendMailServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("sendmail", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:SendMail:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:SendMail:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISendMailHttpClient, SendMailHttpClient>();
        }

        public static void AddRabbitervices(IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<RabbitOption>(configuration.GetSection("RabbitMQ:Connections:Default"));
            services.AddScoped<IInvoiceFactory, InvoiceFactory>();

            services.AddSingleton<ITicketCommandHandler, TicketCommandHandler>();
            services.AddSingleton<ISignTicketCommandHandler, SignTicketCommandHandler>();

            services.AddSingleton<IRabbitBus, RabbitBus>();
            services.AddSingleton<IRabbitBusClient, RabbitBusClient>();
            services.AddSingleton<IRabbitService, VnisBackgroundService>();

            //Worker
            services.AddHostedService<TicketWorker>();
            //services.AddHostedService<TicketReferenceWorker>();

            //Services
            services.AddScoped<IInvoiceHandler, CreateRootInvoice05Service>();
            services.AddScoped<IInvoiceHandler, CreateRootOldDecreeTicketService>();
            services.AddScoped<IInvoiceHandler, UpdateRootInvoice05Service>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderTicketService>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentDetailTicketService>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentHeaderTicketService>();
            services.AddScoped<IInvoiceHandler, UpdateAdjustmentDetailTicketService>();
            services.AddScoped<IInvoiceHandler, CancelTicketService>();
            services.AddScoped<IInvoiceHandler, DeleteInvoice05Service>();
            services.AddScoped<IInvoiceHandler, ApproveTicketService>();
            services.AddScoped<IInvoiceHandler, CreateReplaceTicketService>();
            services.AddScoped<IInvoiceHandler, UpdateReplaceTicketService>();
            services.AddScoped<IInvoiceHandler, CreateAdjustmentHeaderWithoutInvoiceNoTicketService>();
            services.AddScoped<ILicenseSharedService, LicenseSharedService>();

        }

        public static void AddTicketApiValidators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateTicketRequestModel>(new List<Type>
            {
                typeof(CreateInvoiceCheckIndexDetailRule),
                typeof(CreateInvoicePreProcess),
                typeof(CreateInvoiceCheckExistIdErpRule),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckTemplateCanCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckPaymentMethodRule),
                typeof(CreateInvoiceCheckExistTaxRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckDetailExtraRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckIndexDuplicateRule),
            });

            services.AddConfigValidator<CreateTicketOldDecreeRequestModel>(new List<Type>
            {
                typeof(CreateInvoiceOldDecreeCheckIndexDetailRule),
                typeof(CreateInvoiceOldDecreePreProcess),
                typeof(CreateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckExistIdErpRule),
                typeof(CreateInvoiceOldDecreeCheckLicenseRule),
                typeof(CreateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(CreateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(CreateInvoiceOldDecreeCheckTemplateCanCreateRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(CreateInvoiceOldDecreeCheckExchangeRateRule),
                typeof(CreateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(CreateInvoiceOldDecreeCheckExistTaxRule),
                typeof(CreateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(CreateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(CreateInvoiceOldDecreeCheckProductTypeRule),
            });

            services.AddConfigValidator<ApproveTicketApiRequestModel>(new List<Type>
            {
                typeof(ApproveTicketApiPreProcess),
                typeof(ApproveTicketApiCheckUserRule),
                typeof(ApproveTicketApiCheckStatusRule)
            });

            services.AddConfigValidator<ApproveAndSignTicketApiRequestModel>(new List<Type>
            {
                typeof(ApproveAndSignTicketPreProcess),
                typeof(ApproveAndSignTicketApiCheckStatusRule)
            });


            services.AddConfigValidator<UpdateTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateTicketApiCheckIndexDetailRule),
                typeof(UpdateTicketApiPreProcess),
                typeof(UpdateTicketApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateTicketApiCheckStatusRule),
                typeof(UpdateTicketApiCheckInvoiceDateRangeRule),
                typeof(UpdateTicketApiCheckExistCurrencyRule),
                typeof(UpdateTicketApiCheckExchangeRateRule),
                typeof(UpdateTicketApiCheckPaymentMethodRule),
                typeof(UpdateTicketApiCheckExistTaxRule),
                typeof(UpdateTicketApiCheckHeaderExtraRule),
                typeof(UpdateTicketApiCheckDetailExtraRule),
                typeof(UpdateTicketApiCheckProductTypeRule),
            });

            services.AddConfigValidator<CreateAdjustTicketApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustInvoiceCheckIndexDetailRule),
                typeof(CreateAdjustInvoicePreProcess),
                typeof(CreateAdjustInvoiceCheckExistIdErpRule),
                typeof(CreateAdjustInvoiceCheckLicenseRule),
                typeof(CreateAdjustInvoiceCheckReferenceInvoiceRule),
                typeof(CreateAdjustInvoiceCheckExistTemplateRule),
                typeof(CreateAdjustInvoiceCheckTemplateCanCreateRule),
                typeof(CreateAdjustInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateAdjustInvoiceCheckExistCurrencyRule),
                typeof(CreateAdjustInvoiceCheckExchangeRateRule),
                typeof(CreateAdjustInvoiceCheckPaymentMethodRule),
                typeof(CreateAdjustInvoiceCheckExistTaxRule),
                typeof(CreateAdjustInvoiceCheckHeaderExtraRule),
                typeof(CreateAdjustInvoiceCheckDetailExtraRule),
                typeof(CreateAdjustInvoiceCheckProductTypeRule)
            });

            services.AddConfigValidator<UpdateAdjustTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustTicketApiCheckIndexDetailRule),
                typeof(UpdateAdjustTicketApiPreProcess),
                typeof(UpdateAdjustTicketApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustTicketApiCheckStatusRule),
                typeof(UpdateAdjustTicketApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustTicketApiCheckExistCurrencyRule),
                typeof(UpdateAdjustTicketApiCheckExchangeRateRule),
                typeof(UpdateAdjustTicketApiCheckPaymentMethodRule),
                typeof(UpdateAdjustTicketApiCheckExistTaxRule),
                typeof(UpdateAdjustTicketApiCheckHeaderExtraRule),
                typeof(UpdateAdjustTicketApiCheckDetailExtraRule),
                typeof(UpdateAdjustTicketApiCheckProductTypeRule),
                typeof(UpdateAdjustCheckReferenceInvoiceRule),
            });

            services.AddConfigValidator<CreateReplaceTicketApiRequestModel>(new List<Type>
            {
                typeof(CreateReplaceInvoiceApiCheckIndexDetailRule),
                typeof(CreateReplaceInvoiceApiPreProcess),
                //typeof(CreateReplaceInvoiceApiCheckInvoiceBackDateRule),
                typeof(CreateReplaceTicketApiCheckExistIdErpRule),
                typeof(CreateReplaceInvoiceApiCheckLicenseRule),
                typeof(CreateReplaceInvoiceCheckExistIdErpRule),
                typeof(CreateReplaceInvoiceApiCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceApiCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceApiCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceApiCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceApiCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceApiCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceApiCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceApiCheckPaymentMethodRule),
                //typeof(CreateReplaceInvoiceApiCheckDuplicateBuyerCodeRule),
                typeof(CreateReplaceInvoiceApiCheckExistTaxRule),
                typeof(CreateReplaceInvoiceApiCheckHeaderExtraRule),
                typeof(CreateReplaceTicketApiCheckDetailExtraRule),
                typeof(CreateReplaceTicketApiCheckProductTypeRule),
                //typeof(CreateReplaceInvoiceApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<UpdateReplaceTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateReplaceTicketApiCheckIndexDetailRule),
                typeof(UpdateReplaceTicketApiPreProcess),
                typeof(UpdateReplaceApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateReplaceTicketApiCheckStatusRule),
                typeof(UpdateReplaceTicketApiCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceTicketApiCheckDocumentDateRule),
                typeof(UpdateReplaceTicketApiCheckExistCurrencyRule),
                typeof(UpdateReplaceTicketApiCheckExchangeRateRule),
                typeof(UpdateReplaceTicketApiCheckPaymentMethodRule),
                typeof(UpdateReplaceTicketApiCheckExistTaxRule),
                typeof(UpdateReplaceTicketApiCheckHeaderExtraRule),
                typeof(UpdateReplaceTicketApiCheckDetailExtraRule),
                typeof(UpdateReplaceTicketApiCheckProductTypeRule),
            });


            services.AddConfigValidator<CreateAdjustmentDetailTicketApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustmentDetailTicketApiPreProcess),
                typeof(CreateAdjustmentDetailTicketApiCheckExistIdErpRule),
                typeof(CreateAdjustmentDetailTicketApiCheckLicenseRule),
                typeof(CreateAdjustmentDetailTicketApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentDetailTicketApiCheckExistTemplateRule),
                typeof(CreateAdjustmentDetailTicketApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentDetailTicketApiCheckChangeSerialRule),
                typeof(CreateAdjustmentDetailTicketApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentDetailTicketApiCheckExistTaxRule),
                typeof(CreateAdjustmentDetailTicketApiCheckDetailExtraRule),
                //typeof(CreateAdjustmentDetailTicketApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CreateAdjustmentHeaderTicketApiRequestModel>(new List<Type>
            {
                typeof(CreateAdjustmentHeaderTicketApiPreProcess),
                typeof(CreateAdjustmentHeaderTicketApiCheckExistIdErpRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckLicenseRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckExistTemplateRule),
                typeof(CreateAdjHeaderTicketApiCheckTemplateCreateRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckChangeSerialRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckInvoiceDateRangeRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckHeaderExtraRule),
                typeof(CreateAdjustmentHeaderTicketApiCheckPaymentMethodRule),
                //typeof(CreateAdjustmentHeaderTicketApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<UpdateAdjustmentHeaderTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustmentHeaderTicketApiPreProcess),
                typeof(UpdateAdjHeaderApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentHeaderTicketApiCheckStatusRule),
                typeof(UpdateAdjustmentHeaderTicketApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentHeaderTicketApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentHeaderTicketApiCheckHeaderExtraRule),
                typeof(UpdateAdjustmentHeaderTicketApiCheckPaymentMethodRule),
            });

            services.AddConfigValidator<DeleteTicketApiRequestModel>(new List<Type>
            {
                typeof(DeleteTicketApiPreProcess),
                typeof(DeleteTicketApiCheckStatusRule),
                typeof(DeleteTicketApiCheckInfoErrorRule),
            });

            services.AddConfigValidator<CancelTicketApiRequestModel>(new List<Type>
            {
                typeof(CancelTicketApiPreProcess),
                typeof(CancelTicketApiCheckStatusRule),
            });

            services.AddConfigValidator<TicketApiSignServerRequestModel>(new List<Type>
            {
                typeof(SignTicketApiPreProcess),
                typeof(SignTicketApiCheckSignRule),
            });


            services.AddConfigValidator<UpdateAdjustmentDetailTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateAdjustmentDetailTicketApiPreProcess),
                typeof(UpdateAdjDetailApiCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateAdjustmentDetailTicketApiCheckStatusRule),
                typeof(UpdateAdjustmentDetailTicketApiCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentDetailTicketApiCheckDocumentDateRule),
                typeof(UpdateAdjustmentDetailTicketApiCheckExistTaxRule),
                typeof(UpdateAdjustmentDetailTicketApiCheckDetailExtraRule)
            });


            services.AddConfigValidator<CreateAdjustmentHeaderWithoutTicketNoRequestModel>(new List<Type>
            {
                typeof(CreateAdjHeaderWithoutInvoiceNoTicketApiPreProcess),
                typeof(CreateAdjHeaderWithoutInvoiceNoTicketApiCheckInvoiceRule),
                //typeof(CreateAdjHeaderWithoutInvoiceNoTicketApiCheckInfoErrorRule),
            });


            services.AddConfigValidator<CreateDocumentInfoTicketApiRequestModel>(new List<Type>
            {
                typeof(CreateDocumentInfoTicketApiPreProcess),
                typeof(CreateDocumentInfoTicketApiCheckStatusRule),
                typeof(CreateDocumentInfoTicketApiCheckExistRule),
                typeof(CreateDocumentInfoTicketApiCheckDateRule)
            });

            services.AddConfigValidator<UpdateDocumentInfoTicketApiRequestModel>(new List<Type>
            {
                typeof(UpdateDocumentInfoTicketApiPreProcess),
                typeof(UpdateDocumentInfoTicketApiCheckStatusRule),
                typeof(UpdateDocumentInfoTicketApiCheckDateRule)
            });

            services.AddConfigValidator<DeleteDocumentInfoTicketApiRequestModel>(new List<Type>
            {
                typeof(DeleteDocumentInfoTicketApiPreProcess),
                typeof(DeleteDocumentInfoTicketApiCheckExistRule),
                typeof(DeleteDocumentInfoTicketApiCheckStatusRule)
            });

            services.AddConfigValidator<SendMailTicketApiRequestModel>(new List<Type>
            {
                typeof(SendMailTicketApiPreProcess),
                typeof(SendMailTicketApiCheckSendMailRule),
            });

            services.AddConfigValidator<SendMailTicketWithPrintActionApiRequestModel>(new List<Type>
            {
                typeof(SendMailTicketApiWithPrintActionPreProcess),
                typeof(SendMailTicketApiCheckSendMailWithPrintActionRule),
            });
        }
    }
}
