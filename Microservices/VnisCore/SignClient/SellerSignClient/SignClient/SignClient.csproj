<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{410906F6-6F4C-4137-87B6-95F050EAB5C2}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SignClient</RootNamespace>
    <AssemblyName>SignClient</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <UpdateUrl>https://signclient.vninvoice.vn/</UpdateUrl>
    <TargetCulture>vi</TargetCulture>
    <ProductName>SignClient</ProductName>
    <PublisherName>Hoàng Nguyễn %28Apple Nguyen%29</PublisherName>
    <SuiteName>Version 2.3</SuiteName>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>index.html</WebPage>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <ApplicationRevision>10</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>icon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.4\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Xml, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Xml.4.4.0\lib\net461\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config.cs" />
    <Compile Include="Extensions\Utf8StringWriter.cs" />
    <Compile Include="FormLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormLogin.Designer.cs">
      <DependentUpon>FormLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="HttpClientService.cs" />
    <Compile Include="Models\CertificateModel.cs" />
    <Compile Include="Models\ContextModel.cs" />
    <Compile Include="Models\DLieuInvoiceErrorRequestModel.cs" />
    <Compile Include="Models\ObjectSignatureModel.cs" />
    <Compile Include="Models\ResponseModel.cs" />
    <Compile Include="Models\SignInvoiceErrorModel.cs" />
    <Compile Include="Models\SignModel.cs" />
    <Compile Include="Models\SignTaxReportModel.cs" />
    <Compile Include="Models\DLieuInvoiceErrorRequestModel.cs" />
    <Compile Include="Models\TokenModel.cs" />
    <Compile Include="Models\TypeActionModel.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="FormMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormMain.Designer.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="FormLogin.resx">
      <DependentUpon>FormLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="FormMain.resx">
      <DependentUpon>FormMain.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="icon.ico" />
    <Content Include="Resources\01.png" />
    <Content Include="Resources\02.png" />
    <Content Include="Resources\03.png" />
    <Content Include="Resources\04.png" />
    <Content Include="Resources\05.png" />
    <None Include="Resources\06.png" />
    <Content Include="Resources\07.png" />
    <Content Include="Resources\bc26.png" />
    <Content Include="Resources\close-16x16.png" />
    <Content Include="Resources\close-24x24.png" />
    <Content Include="Resources\icon.ico" />
    <None Include="Resources\logo-vnpay.png" />
    <Content Include="Resources\ok-16x16.png" />
    <Content Include="Resources\ok-24x24.png" />
    <Content Include="Resources\loading.gif" />
    <Content Include="Resources\logo-vnpay.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>