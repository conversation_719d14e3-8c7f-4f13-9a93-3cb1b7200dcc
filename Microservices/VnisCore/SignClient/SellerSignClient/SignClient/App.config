<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="SignClient.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <startup>

    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/>
  </startup>
  <userSettings>
    <SignClient.Properties.Settings>
      <setting name="Username" serializeAs="String">
        <value/>
      </setting>
      <setting name="Password" serializeAs="String">
        <value/>
      </setting>
      <setting name="Endpoint" serializeAs="String">
        <value>https://</value>
      </setting>
      <setting name="FolderXmlInvoiceError32UnSign" serializeAs="String">
        <value>C:/Users/<USER>/Documents/ThuVu/Vnpay/MASS/VINMASS-1025</value>
      </setting>
    </SignClient.Properties.Settings>
  </userSettings>
</configuration>
