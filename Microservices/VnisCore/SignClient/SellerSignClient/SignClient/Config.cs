using SignClient.Models;

namespace SignClient
{
    public static class Config
    {
        public static string Endpoint = null;
        public static TokenModel Token = null;

        public static string UrlLogin = "/api/system/account/login";
        public static string UrlGetContext = "/identity/session";

        public static string UrlGetCertificates = "/api/sign/Certificate/Sign-Client/GetCertificate";
        public static string UrlPostCertificates = "/api/sign/Certificate/Sign-Client/RegisterCertificate";

        public static string UrlGetXmlInvoice01 = "/api/sign/SignClientInvoice01/GetInvoiceXml";
        public static string UrlSaveXmlInvoice01 = "/api/sign/SignClientInvoice01/SaveInvoiceXml";

        public static string UrlGetXmlInvoice02 = "/api/sign/SignClientInvoice02/GetInvoiceXml";
        public static string UrlSaveXmlInvoice02 = "/api/sign/SignClientInvoice02/SaveInvoiceXml";

        public static string UrlGetXmlInvoice03 = "/api/sign/SignClientInvoice03/GetInvoiceXml";
        public static string UrlSaveXmlInvoice03 = "/api/sign/SignClientInvoice03/SaveInvoiceXml";

        public static string UrlGetXmlInvoice04 = "/api/sign/SignClientInvoice04/GetInvoiceXml";
        public static string UrlSaveXmlInvoice04 = "/api/sign/SignClientInvoice04/SaveInvoiceXml";

        public static string UrlGetXmlTicket = "/api/sign/SignClientTicket/getinvoicexml";
        public static string UrlSaveXmlTicket = "/api/sign/SignClientTicket/saveinvoicexml";

        public static string UrlGetXmlPITDocument = "/api/sign/SignClientPitDocument/getinvoicexml";
        public static string UrlSaveXmlPITDocument = "/api/sign/SignClientPitDocument/saveinvoicexml";

        public static string UrlGetXmlPITLetter = "/api/sign/SignClientPitLetter/getinvoicexml";
        public static string UrlSaveXmlPITLetter = "/api/sign/SignClientPitLetter/saveinvoicexml";

        public static string UrlGetXmlRegistration = "/api/sign/SignClientRegistration/GetRegisterXml";
        public static string UrlSaveXmlRegistration = "/api/sign/SignClientRegistration/SaveRegisterXml";

        public static string UrlGetXmlPITDeductionDocumentDeclaration = "/api/sign/SignClientPITDeductionDocumentDeclaration/GetRegisterXml";
        public static string UrlSaveXmlPITDeductionDocumentDeclaration = "/api/sign/SignClientPITDeductionDocumentDeclaration/SaveRegisterXml";

        /// <summary>
        /// hóa đơn sai sót
        /// </summary>
        public static string UrlGetXmlInvoice01Error = "/api/sign/SignClientInvoice01Error/GetInvoiceXml";
        public static string UrlSaveXmlInvoice01Error = "/api/sign/SignClientInvoice01Error/SaveInvoiceXml";

        public static string UrlGetXmlInvoice02Error = "/api/sign/SignClientInvoice02Error/GetInvoiceXml";
        public static string UrlSaveXmlInvoice02Error = "/api/sign/SignClientInvoice02Error/SaveInvoiceXml";

        public static string UrlGetXmlInvoice03Error = "/api/sign/SignClientInvoice03Error/GetInvoiceXml";
        public static string UrlSaveXmlInvoice03Error = "/api/sign/SignClientInvoice03Error/SaveInvoiceXml";

        public static string UrlGetXmlInvoice04Error = "/api/sign/SignClientInvoice04Error/GetInvoiceXml";
        public static string UrlSaveXmlInvoice04Error = "/api/sign/SignClientInvoice04Error/SaveInvoiceXml";

        public static string UrlGetXmlInvoice05Error = "/api/sign/SignClientInvoice05Error/GetInvoiceXml";
        public static string UrlSaveXmlInvoice05Error = "/api/sign/SignClientInvoice05Error/SaveInvoiceXml";

        public static string UrlGetXmlPITDocumentError = "/api/sign/SignClientPITDeductionDocumentError/GetInvoiceXml";
        public static string UrlSaveXmlPITDocumentError = "/api/sign/SignClientPITDeductionDocumentError/SaveInvoiceXml";

        public static string UrlGetXmlInvoiceError32 = "/api/sign/SignClientTbss/GetInvoiceXml";
        public static string UrlSaveXmlInvoiceError32 = "/api/sign/SignClientTbss/SaveInvoiceXml";

        //báo cáo thuế
        public static string UrlGetXmlTaxReport01 = "/api/sign/SignClientTaxReport01/GetInvoiceXml";
        public static string UrlSaveXmlTaxReport01 = "/api/sign/SignClientTaxReport01/SaveInvoiceXml";

        public static string UrlGetXmlTaxReport03 = "/api/sign/SignClientTaxReport03/GetInvoiceXml";
        public static string UrlSaveXmlTaxReport03 = "/api/sign/SignClientTaxReport03/SaveInvoiceXml";

        // Unlock invoice
        public static string UnlockInvoice01 = "/api/sign/SignClientInvoice01/unlock";
    }
}
