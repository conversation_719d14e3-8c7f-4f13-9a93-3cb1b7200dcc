using System.Collections.Generic;
using System.Xml.Serialization;

namespace SignClient.Models
{
    [XmlRoot(ElementName = "DLieu")]
    public class DLieuInvoiceErrorRequestModel
    {
        [XmlElement(ElementName = "TBao")]
        public TBaoModel TBao { get; set; }


        [XmlRoot(ElementName = "TBao")]
        public class TBaoModel
        {
            [XmlElement(ElementName = "DLTBao")]
            public DLTBaoModel DLTBao { get; set; }

            [XmlElement(ElementName = "DSCKS")]
            public DSCKS DSCKS { get; set; }
        }


        [XmlRoot(ElementName = "DSHDon")]
        public class DSHDon
        {
            [XmlElement(ElementName = "HDon")]
            public List<HDonModel> HDon { get; set; }
        }

        [XmlRoot(ElementName = "HDon")]
        public class HDonModel
        {
            /// <summary>
            /// STT
            /// </summary>
            [XmlElement(ElementName = "STT")]
            public short STT { get; set; }

            /// <summary>
            /// Bắt buộc (Trừ trường hợp là hóa đơn không có mã của CQT)
            /// </summary>
            [XmlElement(ElementName = "MCCQT")]
            public string MCCQT { get; set; }

            /// <summary>
            /// Ký hiệu mẫu số hóa đơn
            /// </summary>
            [XmlElement(ElementName = "KHMSHDon")]
            public string KHMSHDon { get; set; }

            /// <summary>
            /// Ký hiệu hóa đơn
            /// </summary>
            [XmlElement(ElementName = "KHHDon")]
            public string KHHDon { get; set; }

            /// <summary>
            /// Số hóa đơn
            /// </summary>
            [XmlElement(ElementName = "SHDon")]
            public string SHDon { get; set; }

            /// <summary>
            /// Ngày (Ngày lập hóa đơn)
            /// </summary>
            [XmlElement(ElementName = "Ngay")]
            public string Ngay { get; set; }

            /// <summary>
            /// Loại áp dụng hóa đơn điện tử
            /// </summary>
            [XmlElement(ElementName = "LADHDDT")]
            public short LADHDDT { get; set; }

            /// <summary>
            /// Tính chất thông báo (Hủy/Điều chỉnh/Thay thế/Giải trình)
            /// </summary>
            [XmlElement(ElementName = "TCTBao")]
            public short TCTBao { get; set; }

            /// <summary>
            /// Lý do
            /// </summary>
            [XmlElement(ElementName = "LDo")]
            public string LDo { get; set; }
        }

        [XmlRoot(ElementName = "DLTBao")]
        public class DLTBaoModel
        {
            [XmlAttribute("Id")]
            public string Data { get; set; }

            /// <summary>
            /// Phiên bản của thông điệp. Trong quy định này giá trị là 2.0.0
            /// </summary>
            /// <value></value>
            [XmlElement("PBan")]
            public string PBan { get; set; }

            /// <summary>
            /// Mẫu số (Mẫu số tờ khai)
            /// </summary>
            /// <value></value>
            [XmlElement("MSo")]
            public string MSo { get; set; }

            /// <summary>
            /// Tên (Tên tờ khai)
            /// </summary>
            /// <value></value>
            [XmlElement("Ten")]
            public string Ten { get; set; }

            /// <summary>
            /// Loại (Loại thông báo)
            /// Số (1: Thông báo hủy/giải trình của NNT, 2: Thông báo hủy/giải trình của NNT theo thông báo của CQT) 
            /// </summary>
            [XmlElement(ElementName = "Loai")]
            public short Loai { get; set; }

            /// <summary>
            /// Số (Số thông báo của CQT)
            /// Bắt buộc (Đối với Loại=2: Thông báo hủy/giải trình của NNT theo thông báo của CQT
            /// </summary>
            //[Required(ErrorMessage = "So không được để trống")]
            [XmlElement(ElementName = "So")]
            public string So { get; set; }

            /// <summary>
            /// Ngày thông báo của CQT
            /// Bắt buộc (Đối với Loại=2: Thông báo hủy/giải trình của NNT theo thông báo của CQT) 
            /// </summary>
            [XmlElement(ElementName = "NTBCCQT")]
            public string NTBCCQT { get; set; }

            /// <summary>
            /// Mã CQT (Mã cơ quan thuế quản lý)
            /// </summary>
            [XmlElement(ElementName = "MCQT")]
            public string MCQT { get; set; }

            /// <summary>
            /// Tên cơ quan thuế
            /// </summary>
            [XmlElement(ElementName = "TCQT")]
            public string TCQT { get; set; }

            /// <summary>
            /// Tên NNT (người nộp thuế)
            /// </summary>
            /// <value></value>
            [XmlElement("TNNT")]
            public string TNNT { get; set; }

            /// <summary>
            /// Mã số thuế (MST của NNT)
            /// </summary>
            /// <value></value>
            [XmlElement("MST")]
            public string MST { get; set; }

            /// <summary>
            /// Mã đơn vị quan hệ ngân sách (Mã số đơn vị có quan hệ với ngân sách của đơn vị bán tài sản công)
            /// Bắt buộc (Đối với đơn vị bán tài sản công không có Mã số thuế ) 
            /// </summary>
            [XmlElement(ElementName = "MDVQHNSach")]
            public string MDVQHNSach { get; set; }

            /// <summary>
            /// Địa danh
            /// </summary>
            [XmlElement(ElementName = "DDanh")]
            public string DDanh { get; set; }

            /// <summary>
            /// Ngày thông báo
            /// </summary>
            [XmlElement(ElementName = "NTBao")]
            public string NTBao { get; set; }

            [XmlElement(ElementName = "DSHDon")]
            public DSHDon DSHDon { get; set; }
        }

        [XmlRoot(ElementName = "DSCKS")]
        public class DSCKS
        {

            [XmlElement(ElementName = "NNT")]
            public CKSNNTModel NNT { get; set; }

            //[XmlElement(ElementName = "CCKSKhac")]
            //public object CCKSKhac { get; set; }
        }
    }


    //public class DSCKSModel
    //{
    //    [XmlRoot(ElementName = "NNT")]
    //    public CKSNNTModel NNT { get; set; }


    //    public object CCKSKhac { get; set; }
    //}

    [XmlRoot(ElementName = "NNT")]

    public class CKSNNTModel
    {
        [XmlElement(Namespace = "http://www.w3.org/2000/09/xmldsig#", ElementName = "Signature")]

        public SignatureNNTModel Signature { get; set; }
    }

    [XmlRoot(ElementName = "Signature")]
    public class SignatureNNTModel
    {
        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "Object")]
        public ObjectNNTModel Object { get; set; }
    }

    [XmlRoot(ElementName = "Object")]
    public class ObjectNNTModel
    {
        [XmlAttribute("Id")]

        public string Id { get; set; }

        public SignaturePropertiesModel SignatureProperties { get; set; }
    }

    public class SignaturePropertiesModel
    {
        public List<SignaturePropertyModel> SignatureProperty { get; set; }
    }

    public class SignaturePropertyModel
    {
        public string Id { get; set; }

        public string Target { get; set; }

        public string SigningTime { get; set; }
    }
}
