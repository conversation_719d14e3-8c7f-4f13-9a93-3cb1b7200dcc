namespace SignClient.Models
{
    public enum TypeActionModel
    {
        Invoice01 = 1,

        Invoice02 = 2,

        Invoice03 = 3,

        Invoice04 = 4,

        Ticket = 5,

        PITDocument = 6,

        Invoice01Error = 7,

        Invoice02Error = 8,

        Invoice03Error = 9,

        Invoice04Error = 10,

        TicketError = 11,

        PITDocumentError = 12,

        TaxReport01 = 13,

        TaxReport03 = 14,

        PITLetter = 15,

        /// <summary>
        /// ký TBSS ngoài hệ thống
        /// </summary>
        InvoiceError32 = 16,

        /// <summary>
        /// Đ<PERSON>ng ký sử dụng chứng từ
        /// </summary>
        _52DKSDCT = 52
    }
}
