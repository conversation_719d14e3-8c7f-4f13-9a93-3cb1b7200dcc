using System.Collections.Generic;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Core.Shared.Extensions;

namespace Core.Shared.Attributes
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class BudgetUnitCodeAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || value is not string source || string.IsNullOrWhiteSpace(source))
                return ValidationResult.Success;

            // Phải có đúng 7 ký tự và toàn bộ là chữ số
            if (source.Length != 7 || !source.All(char.IsDigit))
                return new ValidationResult(ErrorMessage);

            // Ký tự đầu phải là 1 trong các số cho phép
            char firstChar = source[0];
            if (firstChar is '1' or '2' or '3' or '7' or '8' or '9')
                return ValidationResult.Success;

            return new ValidationResult(ErrorMessage);
        }
    }
}
